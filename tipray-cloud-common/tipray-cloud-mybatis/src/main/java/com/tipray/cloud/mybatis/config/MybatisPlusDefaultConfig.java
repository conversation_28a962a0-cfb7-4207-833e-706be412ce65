package com.tipray.cloud.mybatis.config;

import com.baomidou.mybatisplus.autoconfigure.MybatisPlusProperties;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.context.annotation.Configuration;

import javax.annotation.PostConstruct;

/**
 * MyBatis Plus 默认配置
 * 用于设置默认的mapper-locations等配置
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-05-20
 */
@Configuration
@ConditionalOnClass(MybatisPlusProperties.class)
public class MybatisPlusDefaultConfig {

    @Autowired(required = false)
    private MybatisPlusProperties mybatisPlusProperties;

    /**
     * 在bean初始化后设置默认值
     */
    @PostConstruct
    public void setDefaultValues() {
        if (mybatisPlusProperties != null) {
            // 如果没有配置mapper-locations，设置默认值
            String[] mapperLocations = mybatisPlusProperties.getMapperLocations();
            if (mapperLocations == null || mapperLocations.length == 0) {
                mybatisPlusProperties.setMapperLocations(new String[]{"classpath*:/mapper/**/*.xml"});
            }
        }
    }
}
