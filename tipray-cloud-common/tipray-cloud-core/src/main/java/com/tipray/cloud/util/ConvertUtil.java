package com.tipray.cloud.util;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;

import java.util.List;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 通用转换工具类
 * 提供对象之间的转换功能
 *
 * <AUTHOR>
 */
public class ConvertUtil {

    /**
     * 单个对象转换
     *
     * @param source 源对象
     * @param targetClass 目标类型
     * @param <S> 源类型泛型
     * @param <T> 目标类型泛型
     * @return 转换后的对象
     */
    public static <S, T> T convert(S source, Class<T> targetClass) {
        if (source == null) {
            return null;
        }

        try {
            T target = targetClass.getDeclaredConstructor().newInstance();
            BeanUtil.copyProperties(source, target);
            return target;
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 单个对象转换（使用自定义转换函数）
     *
     * @param source 源对象
     * @param converter 转换函数
     * @param <S> 源类型泛型
     * @param <T> 目标类型泛型
     * @return 转换后的对象
     */
    public static <S, T> T convert(S source, Function<S, T> converter) {
        if (source == null) {
            return null;
        }

        try {
            return converter.apply(source);
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 列表转换
     *
     * @param sourceList 源对象列表
     * @param targetClass 目标类型
     * @param <S> 源类型泛型
     * @param <T> 目标类型泛型
     * @return 转换后的对象列表
     */
    public static <S, T> List<T> convertList(List<S> sourceList, Class<T> targetClass) {
        if (CollUtil.isEmpty(sourceList)) {
            return CollUtil.newArrayList();
        }

        return sourceList.stream()
                        .map(source -> convert(source, targetClass))
                        .collect(Collectors.toList());
    }

    /**
     * 列表转换（使用自定义转换函数）
     *
     * @param sourceList 源对象列表
     * @param converter 转换函数
     * @param <S> 源类型泛型
     * @param <T> 目标类型泛型
     * @return 转换后的对象列表
     */
    public static <S, T> List<T> convertList(List<S> sourceList, Function<S, T> converter) {
        if (CollUtil.isEmpty(sourceList)) {
            return CollUtil.newArrayList();
        }

        return sourceList.stream()
                        .map(source -> convert(source, converter))
                        .collect(Collectors.toList());
    }

    /**
     * 安全转换 - 转换失败时返回默认值
     *
     * @param source 源对象
     * @param targetClass 目标类型
     * @param defaultValue 默认值
     * @param <S> 源类型泛型
     * @param <T> 目标类型泛型
     * @return 转换后的对象或默认值
     */
    public static <S, T> T convertSafely(S source, Class<T> targetClass, T defaultValue) {
        T result = convert(source, targetClass);
        return result != null ? result : defaultValue;
    }

    /**
     * 安全转换 - 转换失败时返回默认值（使用自定义转换函数）
     *
     * @param source 源对象
     * @param converter 转换函数
     * @param defaultValue 默认值
     * @param <S> 源类型泛型
     * @param <T> 目标类型泛型
     * @return 转换后的对象或默认值
     */
    public static <S, T> T convertSafely(S source, Function<S, T> converter, T defaultValue) {
        T result = convert(source, converter);
        return result != null ? result : defaultValue;
    }

    /**
     * 条件转换 - 满足条件时才转换
     *
     * @param source 源对象
     * @param targetClass 目标类型
     * @param condition 转换条件
     * @param <S> 源类型泛型
     * @param <T> 目标类型泛型
     * @return 转换后的对象或null
     */
    public static <S, T> T convertIf(S source, Class<T> targetClass, Function<S, Boolean> condition) {
        if (source == null || !condition.apply(source)) {
            return null;
        }

        return convert(source, targetClass);
    }

    /**
     * 双向转换 - 同时支持正向和反向转换
     *
     * @param source 源对象
     * @param targetClass 目标类型
     * @param <S> 源类型泛型
     * @param <T> 目标类型泛型
     * @return 转换结果包装类
     */
    public static <S, T> ConvertResult<S, T> convertBidirectional(S source, Class<T> targetClass) {
        T target = convert(source, targetClass);
        return new ConvertResult<>(source, target);
    }

    /**
     * 转换结果包装类
     */
    public static class ConvertResult<S, T> {
        private final S source;
        private final T target;

        public ConvertResult(S source, T target) {
            this.source = source;
            this.target = target;
        }

        public S getSource() {
            return source;
        }

        public T getTarget() {
            return target;
        }

        public boolean isSuccess() {
            return target != null;
        }
    }

    /**
     * 链式转换 - 支持多级转换
     *
     * @param source 源对象
     * @param <S> 源类型泛型
     * @return 转换链
     */
    public static <S> ConvertChain<S> chain(S source) {
        return new ConvertChain<>(source);
    }

    /**
     * 转换链类
     */
    public static class ConvertChain<S> {
        private final S source;

        public ConvertChain(S source) {
            this.source = source;
        }

        public <T> ConvertChain<T> to(Class<T> targetClass) {
            T target = convert(source, targetClass);
            return new ConvertChain<>(target);
        }

        public <T> ConvertChain<T> to(Function<S, T> converter) {
            T target = convert(source, converter);
            return new ConvertChain<>(target);
        }

        public S get() {
            return source;
        }
    }
}
