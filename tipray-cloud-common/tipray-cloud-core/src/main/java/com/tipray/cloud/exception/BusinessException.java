package com.tipray.cloud.exception;

/**
 * 业务异常类
 * 用于处理业务逻辑相关的异常
 *
 * <AUTHOR>
 */
public class BusinessException extends BaseException {

    public BusinessException(ErrorCode errorCode) {
        super(errorCode);
    }

    public BusinessException(ErrorCode errorCode, String detailMessage) {
        super(errorCode, detailMessage);
    }

    public BusinessException(ErrorCode errorCode, Throwable cause) {
        super(errorCode, cause);
    }

    public BusinessException(ErrorCode errorCode, String detailMessage, Throwable cause) {
        super(errorCode, detailMessage, cause);
    }

    public BusinessException(ErrorCode errorCode, Object... args) {
        super(errorCode, args);
    }

    public BusinessException(String message) {
        super(GlobalErrorCode.BUSINESS_ERROR.getCode(), message);
    }

    public BusinessException(String message, String detailMessage) {
        super(GlobalErrorCode.BUSINESS_ERROR.getCode(), message, detailMessage);
    }

    public BusinessException(String message, Throwable cause) {
        super(GlobalErrorCode.BUSINESS_ERROR.getCode(), message, cause);
    }

    public BusinessException(String message, String detailMessage, Throwable cause) {
        super(GlobalErrorCode.BUSINESS_ERROR.getCode(), message, detailMessage, cause);
    }

    // ========== 静态工厂方法 ==========

    /**
     * 创建业务验证异常
     */
    public static BusinessException validationError(String message) {
        return new BusinessException(GlobalErrorCode.BUSINESS_VALIDATION_ERROR, message);
    }

    /**
     * 创建业务逻辑异常
     */
    public static BusinessException logicError(String message) {
        return new BusinessException(GlobalErrorCode.BUSINESS_LOGIC_ERROR, message);
    }

    /**
     * 创建业务状态异常
     */
    public static BusinessException stateError(String message) {
        return new BusinessException(GlobalErrorCode.BUSINESS_STATE_ERROR, message);
    }

    /**
     * 创建业务权限异常
     */
    public static BusinessException permissionError(String message) {
        return new BusinessException(GlobalErrorCode.BUSINESS_PERMISSION_ERROR, message);
    }

    /**
     * 创建业务资源不存在异常
     */
    public static BusinessException resourceNotFound(String message) {
        return new BusinessException(GlobalErrorCode.BUSINESS_RESOURCE_NOT_FOUND, message);
    }

    /**
     * 创建业务资源冲突异常
     */
    public static BusinessException resourceConflict(String message) {
        return new BusinessException(GlobalErrorCode.BUSINESS_RESOURCE_CONFLICT, message);
    }

    /**
     * 创建业务操作不允许异常
     */
    public static BusinessException operationNotAllowed(String message) {
        return new BusinessException(GlobalErrorCode.BUSINESS_OPERATION_NOT_ALLOWED, message);
    }

    // ========== 支持格式化的静态工厂方法 ==========

    /**
     * 创建数据不存在异常
     */
    public static BusinessException dataNotFound(String dataType) {
        return new BusinessException(GlobalErrorCode.BUSINESS_DATA_NOT_FOUND, dataType);
    }

    /**
     * 创建数据已存在异常
     */
    public static BusinessException dataAlreadyExists(String dataType) {
        return new BusinessException(GlobalErrorCode.BUSINESS_DATA_ALREADY_EXISTS, dataType);
    }

    /**
     * 创建重复数据异常（dataAlreadyExists的别名）
     */
    public static BusinessException duplicateData(String dataType) {
        return new BusinessException(GlobalErrorCode.BUSINESS_DATA_ALREADY_EXISTS, dataType);
    }

    /**
     * 创建数据无效异常
     */
    public static BusinessException dataInvalid(String dataType) {
        return new BusinessException(GlobalErrorCode.BUSINESS_DATA_INVALID, dataType);
    }

    /**
     * 创建状态无效异常
     */
    public static BusinessException statusInvalid(String statusType) {
        return new BusinessException(GlobalErrorCode.BUSINESS_STATUS_INVALID, statusType);
    }

    /**
     * 创建操作失败异常
     */
    public static BusinessException operationFailed(String operation) {
        return new BusinessException(GlobalErrorCode.BUSINESS_OPERATION_FAILED, operation);
    }

    /**
     * 创建配额超限异常
     */
    public static BusinessException quotaExceeded(String quotaType) {
        return new BusinessException(GlobalErrorCode.BUSINESS_QUOTA_EXCEEDED, quotaType);
    }

    /**
     * 创建已过期异常
     */
    public static BusinessException timeExpired(String timeType) {
        return new BusinessException(GlobalErrorCode.BUSINESS_TIME_EXPIRED, timeType);
    }

    /**
     * 创建访问拒绝异常
     */
    public static BusinessException accessDenied(String resource) {
        return new BusinessException(GlobalErrorCode.BUSINESS_ACCESS_DENIED, resource);
    }
}
