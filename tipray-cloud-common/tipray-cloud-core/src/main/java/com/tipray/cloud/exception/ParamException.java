package com.tipray.cloud.exception;

/**
 * 参数异常类
 * 用于处理参数校验相关的异常
 *
 * <AUTHOR>
 */
public class ParamException extends BaseException {

    public ParamException(ErrorCode errorCode) {
        super(errorCode);
    }

    public ParamException(ErrorCode errorCode, String detailMessage) {
        super(errorCode, detailMessage);
    }

    public ParamException(ErrorCode errorCode, Throwable cause) {
        super(errorCode, cause);
    }

    public ParamException(ErrorCode errorCode, String detailMessage, Throwable cause) {
        super(errorCode, detailMessage, cause);
    }

    public ParamException(ErrorCode errorCode, Object... args) {
        super(errorCode, args);
    }

    public ParamException(String message) {
        super(GlobalErrorCode.PARAM_ERROR.getCode(), message);
    }

    public ParamException(String message, String detailMessage) {
        super(GlobalErrorCode.PARAM_ERROR.getCode(), message, detailMessage);
    }

    public ParamException(String message, Throwable cause) {
        super(GlobalErrorCode.PARAM_ERROR.getCode(), message, cause);
    }

    public ParamException(String message, String detailMessage, Throwable cause) {
        super(GlobalErrorCode.PARAM_ERROR.getCode(), message, detailMessage, cause);
    }

    // ========== 静态工厂方法 ==========

    /**
     * 创建参数缺失异常
     */
    public static ParamException missing(String paramName) {
        return new ParamException(GlobalErrorCode.PARAM_MISSING_FIELD, paramName);
    }

    /**
     * 创建参数类型错误异常
     */
    public static ParamException typeError(String paramName, String expectedType) {
        return new ParamException(GlobalErrorCode.PARAM_TYPE_MISMATCH, paramName, expectedType);
    }

    /**
     * 创建参数格式错误异常
     */
    public static ParamException formatError(String paramName, String expectedFormat) {
        return new ParamException(GlobalErrorCode.PARAM_FORMAT_INVALID, paramName, expectedFormat);
    }

    /**
     * 创建参数值错误异常
     */
    public static ParamException valueError(String paramName, String message) {
        return new ParamException(GlobalErrorCode.PARAM_VALUE_INVALID, paramName, message);
    }

    /**
     * 创建参数长度错误异常
     */
    public static ParamException lengthError(String paramName, int minLength, int maxLength) {
        return new ParamException(GlobalErrorCode.PARAM_LENGTH_INVALID, paramName, minLength, maxLength);
    }

    /**
     * 创建参数范围错误异常
     */
    public static ParamException rangeError(String paramName, String minValue, String maxValue) {
        return new ParamException(GlobalErrorCode.PARAM_RANGE_INVALID, paramName, minValue, maxValue);
    }

    /**
     * 创建参数为空异常
     */
    public static ParamException empty(String paramName) {
        return new ParamException(GlobalErrorCode.PARAM_EMPTY_VALUE, paramName);
    }

    /**
     * 创建参数为null异常
     */
    public static ParamException nullValue(String paramName) {
        return new ParamException(GlobalErrorCode.PARAM_NULL_VALUE, paramName);
    }
}
