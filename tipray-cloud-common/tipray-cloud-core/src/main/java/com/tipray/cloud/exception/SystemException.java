package com.tipray.cloud.exception;

/**
 * 系统异常类
 * 用于处理系统级别的异常
 *
 * <AUTHOR>
 */
public class SystemException extends BaseException {

    public SystemException(ErrorCode errorCode) {
        super(errorCode);
    }

    public SystemException(ErrorCode errorCode, String detailMessage) {
        super(errorCode, detailMessage);
    }

    public SystemException(ErrorCode errorCode, Throwable cause) {
        super(errorCode, cause);
    }

    public SystemException(ErrorCode errorCode, String detailMessage, Throwable cause) {
        super(errorCode, detailMessage, cause);
    }

    public SystemException(ErrorCode errorCode, Object... args) {
        super(errorCode, args);
    }

    public SystemException(String message) {
        super(GlobalErrorCode.INTERNAL_SERVER_ERROR.getCode(), message);
    }

    public SystemException(String message, String detailMessage) {
        super(GlobalErrorCode.INTERNAL_SERVER_ERROR.getCode(), message, detailMessage);
    }

    public SystemException(String message, Throwable cause) {
        super(GlobalErrorCode.INTERNAL_SERVER_ERROR.getCode(), message, cause);
    }

    public SystemException(String message, String detailMessage, Throwable cause) {
        super(GlobalErrorCode.INTERNAL_SERVER_ERROR.getCode(), message, detailMessage, cause);
    }

    // ========== 静态工厂方法 ==========

    /**
     * 创建数据库异常
     */
    public static SystemException databaseError(String message) {
        return new SystemException(GlobalErrorCode.DATABASE_ERROR, message);
    }

    /**
     * 创建数据库异常
     */
    public static SystemException databaseError(String message, Throwable cause) {
        return new SystemException(GlobalErrorCode.DATABASE_ERROR, message, cause);
    }

    /**
     * 创建外部服务异常
     */
    public static SystemException externalServiceError(String message) {
        return new SystemException(GlobalErrorCode.EXTERNAL_SERVICE_ERROR, message);
    }

    /**
     * 创建外部服务异常
     */
    public static SystemException externalServiceError(String message, Throwable cause) {
        return new SystemException(GlobalErrorCode.EXTERNAL_SERVICE_ERROR, message, cause);
    }

    /**
     * 创建文件操作异常
     */
    public static SystemException fileError(String message) {
        return new SystemException(GlobalErrorCode.FILE_ERROR, message);
    }

    /**
     * 创建文件操作异常
     */
    public static SystemException fileError(String message, Throwable cause) {
        return new SystemException(GlobalErrorCode.FILE_ERROR, message, cause);
    }

    /**
     * 创建缓存异常
     */
    public static SystemException cacheError(String message) {
        return new SystemException(GlobalErrorCode.CACHE_ERROR, message);
    }

    /**
     * 创建缓存异常
     */
    public static SystemException cacheError(String message, Throwable cause) {
        return new SystemException(GlobalErrorCode.CACHE_ERROR, message, cause);
    }

    /**
     * 创建消息队列异常
     */
    public static SystemException mqError(String message) {
        return new SystemException(GlobalErrorCode.MQ_ERROR, message);
    }

    /**
     * 创建消息队列异常
     */
    public static SystemException mqError(String message, Throwable cause) {
        return new SystemException(GlobalErrorCode.MQ_ERROR, message, cause);
    }

    /**
     * 创建安全异常
     */
    public static SystemException securityError(String message) {
        return new SystemException(GlobalErrorCode.SECURITY_ERROR, message);
    }

    /**
     * 创建安全异常
     */
    public static SystemException securityError(String message, Throwable cause) {
        return new SystemException(GlobalErrorCode.SECURITY_ERROR, message, cause);
    }

    /**
     * 创建配置异常
     */
    public static SystemException configError(String message) {
        return new SystemException(GlobalErrorCode.CONFIG_ERROR, message);
    }

    /**
     * 创建配置异常
     */
    public static SystemException configError(String message, Throwable cause) {
        return new SystemException(GlobalErrorCode.CONFIG_ERROR, message, cause);
    }
}
