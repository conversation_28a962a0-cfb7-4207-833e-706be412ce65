package com.tipray.cloud.exception;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;

/**
 * 异常工具类
 *
 * <AUTHOR>
 */
public class ExceptionUtil {

    /**
     * 断言对象不为null，否则抛出业务异常
     *
     * @param object 对象
     * @param message 异常信息
     */
    public static void assertNotNull(Object object, String message) {
        if (object == null) {
            throw new BusinessException(message);
        }
    }

    /**
     * 断言对象不为null，否则抛出业务异常
     *
     * @param object 对象
     * @param errorCode 错误码
     */
    public static void assertNotNull(Object object, ErrorCode errorCode) {
        if (object == null) {
            throw new BusinessException(errorCode);
        }
    }

    /**
     * 判断对象是否为空，否则抛出业务异常
     *
     * @param object 字符串
     * @param message 异常信息
     */
    public static void assertNotEmpty(Object object, String message) {
        if (ObjectUtil.isEmpty(object)) {
            throw new BusinessException(message);
        }
    }

    /**
     * 断言字符串不为空，否则抛出业务异常
     *
     * @param str 字符串
     * @param errorCode 错误码
     */
    public static void assertNotEmpty(String str, ErrorCode errorCode) {
        if (StrUtil.isEmpty(str)) {
            throw new BusinessException(errorCode);
        }
    }

    /**
     * 断言条件为true，否则抛出业务异常
     *
     * @param condition 条件
     * @param message 异常信息
     */
    public static void assertTrue(boolean condition, String message) {
        if (!condition) {
            throw new BusinessException(message);
        }
    }

    /**
     * 断言条件为true，否则抛出业务异常
     *
     * @param condition 条件
     * @param errorCode 错误码
     */
    public static void assertTrue(boolean condition, ErrorCode errorCode) {
        if (!condition) {
            throw new BusinessException(errorCode);
        }
    }

    /**
     * 断言条件为false，否则抛出业务异常
     *
     * @param condition 条件
     * @param message 异常信息
     */
    public static void assertFalse(boolean condition, String message) {
        if (condition) {
            throw new BusinessException(message);
        }
    }

    /**
     * 断言条件为false，否则抛出业务异常
     *
     * @param condition 条件
     * @param errorCode 错误码
     */
    public static void assertFalse(boolean condition, ErrorCode errorCode) {
        if (condition) {
            throw new BusinessException(errorCode);
        }
    }

    /**
     * 断言两个对象相等，否则抛出业务异常
     *
     * @param obj1 对象1
     * @param obj2 对象2
     * @param message 异常信息
     */
    public static void assertEquals(Object obj1, Object obj2, String message) {
        if (obj1 == null && obj2 == null) {
            return;
        }
        if (obj1 == null || !obj1.equals(obj2)) {
            throw new BusinessException(message);
        }
    }

    /**
     * 断言两个对象相等，否则抛出业务异常
     *
     * @param obj1 对象1
     * @param obj2 对象2
     * @param errorCode 错误码
     */
    public static void assertEquals(Object obj1, Object obj2, ErrorCode errorCode) {
        if (obj1 == null && obj2 == null) {
            return;
        }
        if (obj1 == null || !obj1.equals(obj2)) {
            throw new BusinessException(errorCode);
        }
    }

    /**
     * 断言数字大于0，否则抛出参数异常
     *
     * @param number 数字
     * @param paramName 参数名
     */
    public static void assertPositive(Number number, String paramName) {
        if (number == null || number.doubleValue() <= 0) {
            throw ParamException.valueError(paramName, "必须大于0");
        }
    }

    /**
     * 断言数字大于等于0，否则抛出参数异常
     *
     * @param number 数字
     * @param paramName 参数名
     */
    public static void assertNonNegative(Number number, String paramName) {
        if (number == null || number.doubleValue() < 0) {
            throw ParamException.valueError(paramName, "不能小于0");
        }
    }

    /**
     * 断言数字在指定范围内，否则抛出参数异常
     *
     * @param number 数字
     * @param min 最小值
     * @param max 最大值
     * @param paramName 参数名
     */
    public static void assertRange(Number number, Number min, Number max, String paramName) {
        if (number == null) {
            throw ParamException.nullValue(paramName);
        }
        double value = number.doubleValue();
        double minValue = min.doubleValue();
        double maxValue = max.doubleValue();
        if (value < minValue || value > maxValue) {
            throw ParamException.rangeError(paramName, min.toString(), max.toString());
        }
    }

    /**
     * 断言字符串长度在指定范围内，否则抛出参数异常
     *
     * @param str 字符串
     * @param minLength 最小长度
     * @param maxLength 最大长度
     * @param paramName 参数名
     */
    public static void assertLength(String str, int minLength, int maxLength, String paramName) {
        if (str == null) {
            throw ParamException.nullValue(paramName);
        }
        int length = str.length();
        if (length < minLength || length > maxLength) {
            throw ParamException.lengthError(paramName, minLength, maxLength);
        }
    }

    /**
     * 抛出业务异常
     *
     * @param message 异常信息
     */
    public static void throwBusiness(String message) {
        throw new BusinessException(message);
    }

    /**
     * 抛出业务异常
     *
     * @param errorCode 错误码
     */
    public static void throwBusiness(ErrorCode errorCode) {
        throw new BusinessException(errorCode);
    }

    /**
     * 抛出业务异常
     *
     * @param errorCode 错误码
     * @param detailMessage 详细信息
     */
    public static void throwBusiness(ErrorCode errorCode, String detailMessage) {
        throw new BusinessException(errorCode, detailMessage);
    }

    /**
     * 抛出系统异常
     *
     * @param message 异常信息
     */
    public static void throwSystem(String message) {
        throw new SystemException(message);
    }

    /**
     * 抛出系统异常
     *
     * @param errorCode 错误码
     */
    public static void throwSystem(ErrorCode errorCode) {
        throw new SystemException(errorCode);
    }

    /**
     * 抛出系统异常
     *
     * @param errorCode 错误码
     * @param detailMessage 详细信息
     */
    public static void throwSystem(ErrorCode errorCode, String detailMessage) {
        throw new SystemException(errorCode, detailMessage);
    }

    /**
     * 抛出参数异常
     *
     * @param message 异常信息
     */
    public static void throwParam(String message) {
        throw new ParamException(message);
    }

    /**
     * 抛出参数异常
     *
     * @param errorCode 错误码
     */
    public static void throwParam(ErrorCode errorCode) {
        throw new ParamException(errorCode);
    }

    /**
     * 抛出参数异常
     *
     * @param errorCode 错误码
     * @param detailMessage 详细信息
     */
    public static void throwParam(ErrorCode errorCode, String detailMessage) {
        throw new ParamException(errorCode, detailMessage);
    }

    // ========== 支持格式化的异常抛出方法 ==========

    /**
     * 抛出格式化业务异常
     *
     * @param errorCode 错误码
     * @param args 格式化参数
     */
    public static void throwBusinessFormat(ErrorCode errorCode, Object... args) {
        throw new BusinessException(errorCode, args);
    }

    /**
     * 抛出格式化系统异常
     *
     * @param errorCode 错误码
     * @param args 格式化参数
     */
    public static void throwSystemFormat(ErrorCode errorCode, Object... args) {
        throw new SystemException(errorCode, args);
    }

    /**
     * 抛出格式化参数异常
     *
     * @param errorCode 错误码
     * @param args 格式化参数
     */
    public static void throwParamFormat(ErrorCode errorCode, Object... args) {
        throw new ParamException(errorCode, args);
    }

    // ========== 便捷的断言方法（支持格式化） ==========

    /**
     * 断言数据存在，否则抛出数据不存在异常
     *
     * @param data 数据
     * @param dataType 数据类型
     */
    public static void assertDataExists(Object data, String dataType) {
        if (data == null) {
            throw BusinessException.dataNotFound(dataType);
        }
    }

    /**
     * 断言数据不存在，否则抛出数据已存在异常
     *
     * @param data 数据
     * @param dataType 数据类型
     */
    public static void assertDataNotExists(Object data, String dataType) {
        if (data != null) {
            throw BusinessException.dataAlreadyExists(dataType);
        }
    }

    /**
     * 断言操作成功，否则抛出操作失败异常
     *
     * @param success 是否成功
     * @param operation 操作名称
     */
    public static void assertOperationSuccess(boolean success, String operation) {
        if (!success) {
            throw BusinessException.operationFailed(operation);
        }
    }
}
