package com.tipray.transaction.storage.mapper;

import com.tipray.transaction.storage.entity.StepExecutionHistoryEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 步骤执行历史Mapper接口
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-06-27
 */
@Mapper
public interface StepExecutionHistoryMapper {

    /**
     * 插入执行历史记录
     *
     * @param history 执行历史实体
     * @return 影响行数
     */
    int insert(StepExecutionHistoryEntity history);

    /**
     * 根据步骤ID查询执行历史
     *
     * @param stepId 步骤ID
     * @return 执行历史列表
     */
    List<StepExecutionHistoryEntity> selectByStepId(@Param("stepId") Long stepId);

    /**
     * 根据步骤ID查询执行历史（按执行次数排序）
     *
     * @param stepId 步骤ID
     * @return 执行历史列表
     */
    List<StepExecutionHistoryEntity> selectByStepIdOrderByAttemptNumber(@Param("stepId") Long stepId);

    /**
     * 根据事务ID查询执行历史
     *
     * @param transactionId 事务ID
     * @return 执行历史列表
     */
    List<StepExecutionHistoryEntity> selectByTransactionId(@Param("transactionId") String transactionId);

    /**
     * 根据步骤ID和执行次数查询执行历史
     *
     * @param stepId        步骤ID
     * @param attemptNumber 执行次数
     * @return 执行历史实体
     */
    StepExecutionHistoryEntity selectByStepIdAndAttemptNumber(@Param("stepId") Long stepId,
                                                              @Param("attemptNumber") Integer attemptNumber);

    /**
     * 根据步骤ID删除执行历史
     *
     * @param stepId 步骤ID
     * @return 影响行数
     */
    int deleteByStepId(@Param("stepId") Long stepId);

    /**
     * 根据事务ID删除执行历史
     *
     * @param transactionId 事务ID
     * @return 影响行数
     */
    int deleteByTransactionId(@Param("transactionId") String transactionId);

    /**
     * 统计步骤的执行次数
     *
     * @param stepId 步骤ID
     * @return 执行次数
     */
    int countByStepId(@Param("stepId") Long stepId);

    /**
     * 获取步骤的最新执行记录
     *
     * @param stepId 步骤ID
     * @return 最新的执行历史记录
     */
    StepExecutionHistoryEntity selectLatestByStepId(@Param("stepId") Long stepId);
}
