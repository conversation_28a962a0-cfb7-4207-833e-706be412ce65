package com.tipray.transaction.storage.config;

import com.baomidou.mybatisplus.annotation.DbType;
import com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.PaginationInnerInterceptor;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.transaction.annotation.EnableTransactionManagement;

/**
 * 分布式事务存储配置类
 * 配置MyBatis Plus和数据库相关组件
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-06-20
 */
@Configuration
@EnableTransactionManagement
@MapperScan("com.tipray.transaction.storage.mapper")
@ComponentScan(basePackages = {
        "com.tipray.transaction.storage.impl",
        "com.tipray.transaction.storage.converter"
})
public class TransactionStorageConfiguration {

    /**
     * MyBatis Plus 拦截器配置
     *
     * @return MyBatis Plus拦截器
     */
    @Bean
    public MybatisPlusInterceptor mybatisPlusInterceptor() {
        MybatisPlusInterceptor interceptor = new MybatisPlusInterceptor();

        // 分页插件
        PaginationInnerInterceptor paginationInterceptor = new PaginationInnerInterceptor(DbType.MYSQL);
        paginationInterceptor.setMaxLimit(1000L); // 设置最大分页限制
        paginationInterceptor.setOverflow(false); // 溢出总页数后是否进行处理

        interceptor.addInnerInterceptor(paginationInterceptor);

        return interceptor;
    }
}
