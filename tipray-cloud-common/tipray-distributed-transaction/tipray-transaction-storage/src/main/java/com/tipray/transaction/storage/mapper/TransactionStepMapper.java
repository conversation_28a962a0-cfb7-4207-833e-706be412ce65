package com.tipray.transaction.storage.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.tipray.transaction.storage.dto.StepExecutionStatistics;
import com.tipray.transaction.storage.entity.TransactionStepEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 事务步骤Mapper接口
 * 基于MyBatis Plus的数据访问层
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-06-20
 */
@Mapper
public interface TransactionStepMapper extends BaseMapper<TransactionStepEntity> {

    /**
     * 根据步骤ID查询步骤
     *
     * @param stepId 步骤ID
     * @return 步骤实体
     */
    TransactionStepEntity selectByStepId(@Param("stepId") Long stepId);

    /**
     * 根据事务ID和状态查询步骤
     *
     * @param transactionId 事务ID
     * @param status        步骤状态
     * @return 步骤列表
     */
    List<TransactionStepEntity> selectByTransactionIdAndStatus(@Param("transactionId") String transactionId,
                                                               @Param("status") String status);

    /**
     * 根据状态查询步骤列表
     *
     * @param status 步骤状态
     * @return 步骤列表
     */
    List<TransactionStepEntity> selectByStatus(@Param("status") String status);

    /**
     * 查询指定事务的成功步骤
     *
     * @param transactionId 事务ID
     * @return 成功步骤列表
     */
    List<TransactionStepEntity> selectSuccessfulSteps(@Param("transactionId") String transactionId);

    /**
     * 查询指定事务的失败步骤
     *
     * @param transactionId 事务ID
     * @return 失败步骤列表
     */
    List<TransactionStepEntity> selectFailedSteps(@Param("transactionId") String transactionId);

    /**
     * 查询需要撤销的步骤
     *
     * @param transactionId 事务ID
     * @return 需要撤销的步骤列表（逆序）
     */
    List<TransactionStepEntity> selectStepsToUndo(@Param("transactionId") String transactionId);

    /**
     * 查询超时的步骤列表
     *
     * @param timeoutThreshold 超时阈值时间
     * @return 超时步骤列表
     */
    List<TransactionStepEntity> selectTimeoutSteps(@Param("timeoutThreshold") LocalDateTime timeoutThreshold);

    /**
     * 查询需要重试的步骤列表
     *
     * @param maxRetryCount 最大重试次数
     * @return 需要重试的步骤列表
     */
    List<TransactionStepEntity> selectRetryableSteps(@Param("maxRetryCount") int maxRetryCount);

    /**
     * 统计指定事务的步骤数量
     *
     * @param transactionId 事务ID
     * @return 步骤总数
     */
    int countByTransactionId(@Param("transactionId") String transactionId);

    /**
     * 统计指定事务和状态的步骤数量
     *
     * @param transactionId 事务ID
     * @param status        步骤状态
     * @return 步骤数量
     */
    int countByTransactionIdAndStatus(@Param("transactionId") String transactionId, @Param("status") String status);

    /**
     * 统计指定状态的步骤数量
     *
     * @param status 步骤状态
     * @return 步骤数量
     */
    long countByStatus(@Param("status") String status);

    /**
     * 删除指定事务的所有步骤
     *
     * @param transactionId 事务ID
     * @return 删除的记录数
     */
    int deleteByTransactionId(@Param("transactionId") String transactionId);

    /**
     * 删除指定时间之前的步骤记录
     *
     * @param beforeTime 时间阈值
     * @return 删除的记录数
     */
    int deleteBeforeTime(@Param("beforeTime") LocalDateTime beforeTime);

    /**
     * 批量更新步骤状态
     *
     * @param stepIds    步骤ID列表
     * @param newStatus  新状态
     * @param updateTime 更新时间
     * @return 更新的记录数
     */
    int batchUpdateStatus(@Param("stepIds") List<Long> stepIds,
                          @Param("newStatus") String newStatus,
                          @Param("updateTime") LocalDateTime updateTime);

    /**
     * 批量更新指定事务的步骤状态
     *
     * @param transactionId 事务ID
     * @param oldStatus     原状态
     * @param newStatus     新状态
     * @param updateTime    更新时间
     * @return 更新的记录数
     */
    int batchUpdateStatusByTransaction(@Param("transactionId") String transactionId,
                                       @Param("oldStatus") String oldStatus,
                                       @Param("newStatus") String newStatus,
                                       @Param("updateTime") LocalDateTime updateTime);

    /**
     * 检查步骤是否存在
     *
     * @param stepId 步骤ID
     * @return 存在返回1，不存在返回0
     */
    int existsByStepId(@Param("stepId") Long stepId);

    /**
     * 更新步骤状态和错误信息
     *
     * @param stepId       步骤ID
     * @param status       新状态
     * @param errorMessage 错误信息
     * @param updateTime   更新时间
     * @return 更新的记录数
     */
    int updateStatusAndError(@Param("stepId") Long stepId,
                             @Param("status") String status,
                             @Param("errorMessage") String errorMessage,
                             @Param("updateTime") LocalDateTime updateTime);

    /**
     * 更新步骤结果
     *
     * @param stepId     步骤ID
     * @param stepResult 步骤结果
     * @param updateTime 更新时间
     * @return 更新的记录数
     */
    int updateStepResult(@Param("stepId") Long stepId,
                         @Param("stepResult") String stepResult,
                         @Param("updateTime") LocalDateTime updateTime);

    /**
     * 更新步骤重试次数
     *
     * @param stepId            步骤ID
     * @param currentRetryCount 当前重试次数
     * @param updateTime        更新时间
     * @return 更新的记录数
     */
    int updateRetryCount(@Param("stepId") Long stepId,
                         @Param("currentRetryCount") int currentRetryCount,
                         @Param("updateTime") LocalDateTime updateTime);

    /**
     * 更新步骤时间信息
     *
     * @param stepId     步骤ID
     * @param startTime  开始时间
     * @param endTime    结束时间
     * @param updateTime 更新时间
     * @return 更新的记录数
     */
    int updateTimeInfo(@Param("stepId") Long stepId,
                       @Param("startTime") LocalDateTime startTime,
                       @Param("endTime") LocalDateTime endTime,
                       @Param("updateTime") LocalDateTime updateTime);

    /**
     * 获取指定事务的步骤执行统计
     *
     * @param transactionId 事务ID
     * @return 步骤执行统计信息
     */
    StepExecutionStatistics selectExecutionStatistics(@Param("transactionId") String transactionId);

    // UI相关查询方法

    /**
     * 根据事务ID查询步骤列表
     */
    List<TransactionStepEntity> selectByTransactionId(@Param("transactionId") String transactionId);

    /**
     * 获取步骤执行统计（时间范围）
     */
    List<StepExecutionStatistics> getStepExecutionStatistics(@Param("startTime") LocalDateTime startTime,
                                                             @Param("endTime") LocalDateTime endTime);

    /**
     * 根据创建时间删除步骤记录
     */
    int deleteByCreateTimeBefore(@Param("createTime") LocalDateTime createTime);

    /**
     * 根据时间范围查询不重复的事务ID列表
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 事务ID列表
     */
    List<String> selectDistinctTransactionIdsByTimeRange(@Param("startTime") LocalDateTime startTime,
                                                         @Param("endTime") LocalDateTime endTime);
}
