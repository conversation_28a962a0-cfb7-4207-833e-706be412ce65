//package com.tipray.transaction.storage.impl;
//
//import com.tipray.transaction.storage.converter.TransactionStepConverter;
//import com.tipray.transaction.storage.entity.TransactionStepEntity;
//import com.tipray.transaction.storage.mapper.TransactionStepMapper;
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Repository;
//
//import java.time.LocalDateTime;
//import java.util.ArrayList;
//import java.util.List;
//import java.util.stream.Collectors;
//
/// **
// * 数据库事务步骤存储仓库实现
// * 基于MyBatis Plus的数据库持久化实现
// *
// * <AUTHOR>
// * @version 1.0
// * @since 2025-06-20
// */
//@Repository("databaseTransactionStepRepository")
//public class DatabaseTransactionStepRepository implements TransactionStepRepository {
//
//    private static final Logger logger = LoggerFactory.getLogger(DatabaseTransactionStepRepository.class);
//
//    @Autowired
//    private TransactionStepMapper stepMapper;
//
//    @Autowired
//    private TransactionStepConverter stepConverter;
//
//    @Override
//    public boolean save(TransactionStep step) {
//        if (step == null) {
//            logger.warn("保存步骤失败：步骤对象为空");
//            return false;
//        }
//
//        try {
//            TransactionStepEntity entity = stepConverter.toEntity(step);
//            int result = stepMapper.insert(entity);
//
//            if (result > 0) {
//                logger.debug("[{}] 步骤[{}] 保存成功", step.getTransactionId(), step.getStepId());
//                return true;
//            } else {
//                logger.warn("[{}] 步骤[{}] 保存失败：数据库插入返回0", step.getTransactionId(), step.getStepId());
//                return false;
//            }
//
//        } catch (Exception e) {
//            logger.error("[{}] 步骤[{}] 保存异常: {}", step.getTransactionId(), step.getStepId(), e.getMessage(), e);
//            return false;
//        }
//    }
//
//    @Override
//    public int batchSave(List<TransactionStep> steps) {
//        if (steps == null || steps.isEmpty()) {
//            logger.warn("批量保存步骤失败：步骤列表为空");
//            return 0;
//        }
//
//        int saveCount = 0;
//        for (TransactionStep step : steps) {
//            if (save(step)) {
//                saveCount++;
//            }
//        }
//
//        logger.debug("批量保存步骤完成 - 总数: {}, 成功: {}", steps.size(), saveCount);
//        return saveCount;
//    }
//
//    @Override
//    public boolean update(TransactionStep step) {
//        if (step == null) {
//            logger.warn("更新步骤失败：步骤对象为空");
//            return false;
//        }
//
//        try {
//            TransactionStepEntity entity = stepConverter.toEntity(step);
//            int result = stepMapper.updateById(entity);
//
//            if (result > 0) {
//                logger.debug("[{}] 步骤[{}] 更新成功", step.getTransactionId(), step.getStepId());
//                return true;
//            } else {
//                logger.warn("[{}] 步骤[{}] 更新失败：数据库更新返回0", step.getTransactionId(), step.getStepId());
//                return false;
//            }
//
//        } catch (Exception e) {
//            logger.error("[{}] 步骤[{}] 更新异常: {}", step.getTransactionId(), step.getStepId(), e.getMessage(), e);
//            return false;
//        }
//    }
//
//    @Override
//    public int batchUpdate(List<TransactionStep> steps) {
//        if (steps == null || steps.isEmpty()) {
//            logger.warn("批量更新步骤失败：步骤列表为空");
//            return 0;
//        }
//
//        int updateCount = 0;
//        for (TransactionStep step : steps) {
//            if (update(step)) {
//                updateCount++;
//            }
//        }
//
//        logger.debug("批量更新步骤完成 - 总数: {}, 成功: {}", steps.size(), updateCount);
//        return updateCount;
//    }
//
//    @Override
//    public TransactionStep findByStepId(Long stepId) {
//        if (stepId == null) {
//            logger.warn("查询步骤失败：步骤ID为空");
//            return null;
//        }
//
//        try {
//            TransactionStepEntity entity = stepMapper.selectByStepId(stepId);
//            if (entity != null) {
//                return stepConverter.toDomain(entity);
//            }
//
//            logger.debug("步骤[{}] 不存在", stepId);
//            return null;
//
//        } catch (Exception e) {
//            logger.error("查询步骤[{}] 异常: {}", stepId, e.getMessage(), e);
//            return null;
//        }
//    }
//
//    @Override
//    public List<TransactionStep> findByTransactionId(String transactionId) {
//        if (transactionId == null || transactionId.trim().isEmpty()) {
//            logger.warn("查询事务步骤失败：事务ID为空");
//            return java.util.Collections.emptyList();
//        }
//
//        try {
//            List<TransactionStepEntity> entities = stepMapper.selectByTransactionId(transactionId);
//            return entities.stream()
//                    .map(stepConverter::toDomain)
//                    .collect(Collectors.toList());
//
//        } catch (Exception e) {
//            logger.error("[{}] 查询事务步骤异常: {}", transactionId, e.getMessage(), e);
//            return java.util.Collections.emptyList();
//        }
//    }
//
//    @Override
//    public List<TransactionStep> findByTransactionIdAndStatus(String transactionId, StepStatus status) {
//        if (transactionId == null || transactionId.trim().isEmpty() || status == null) {
//            logger.warn("查询事务步骤失败：参数为空");
//            return java.util.Collections.emptyList();
//        }
//
//        try {
//            List<TransactionStepEntity> entities = stepMapper.selectByTransactionIdAndStatus(transactionId, status.getCode());
//            return entities.stream()
//                    .map(stepConverter::toDomain)
//                    .collect(Collectors.toList());
//
//        } catch (Exception e) {
//            logger.error("[{}] 查询状态[{}]步骤异常: {}", transactionId, status.getCode(), e.getMessage(), e);
//            return java.util.Collections.emptyList();
//        }
//    }
//
//    @Override
//    public List<TransactionStep> findByStatus(StepStatus status) {
//        if (status == null) {
//            logger.warn("查询步骤失败：状态为空");
//            return java.util.Collections.emptyList();
//        }
//
//        try {
//            List<TransactionStepEntity> entities = stepMapper.selectByStatus(status.getCode());
//            return entities.stream()
//                    .map(stepConverter::toDomain)
//                    .collect(Collectors.toList());
//
//        } catch (Exception e) {
//            logger.error("查询状态[{}]步骤异常: {}", status.getCode(), e.getMessage(), e);
//            return java.util.Collections.emptyList();
//        }
//    }
//
//    @Override
//    public List<TransactionStep> findSuccessfulSteps(String transactionId) {
//        return findByTransactionIdAndStatus(transactionId, StepStatus.SUCCESS);
//    }
//
//    @Override
//    public List<TransactionStep> findFailedSteps(String transactionId) {
//        return findByTransactionIdAndStatus(transactionId, StepStatus.FAILED);
//    }
//
//    @Override
//    public List<TransactionStep> findStepsToUndo(String transactionId) {
//        if (transactionId == null || transactionId.trim().isEmpty()) {
//            logger.warn("查询撤销步骤失败：事务ID为空");
//            return java.util.Collections.emptyList();
//        }
//
//        try {
//            List<TransactionStepEntity> entities = stepMapper.selectStepsToUndo(transactionId);
//            return entities.stream()
//                    .map(stepConverter::toDomain)
//                    .collect(Collectors.toList());
//
//        } catch (Exception e) {
//            logger.error("[{}] 查询撤销步骤异常: {}", transactionId, e.getMessage(), e);
//            return java.util.Collections.emptyList();
//        }
//    }
//
//    @Override
//    public List<TransactionStep> findTimeoutSteps(LocalDateTime timeoutThreshold) {
//        if (timeoutThreshold == null) {
//            logger.warn("查询超时步骤失败：时间阈值为空");
//            return java.util.Collections.emptyList();
//        }
//
//        try {
//            List<TransactionStepEntity> entities = stepMapper.selectTimeoutSteps(timeoutThreshold);
//            return entities.stream()
//                    .map(stepConverter::toDomain)
//                    .collect(Collectors.toList());
//
//        } catch (Exception e) {
//            logger.error("查询超时步骤异常: {}", e.getMessage(), e);
//            return java.util.Collections.emptyList();
//        }
//    }
//
//    @Override
//    public List<TransactionStep> findRetryableSteps(int maxRetryCount) {
//        try {
//            List<TransactionStepEntity> entities = stepMapper.selectRetryableSteps(maxRetryCount);
//            return entities.stream()
//                    .map(stepConverter::toDomain)
//                    .collect(Collectors.toList());
//
//        } catch (Exception e) {
//            logger.error("查询可重试步骤异常: {}", e.getMessage(), e);
//            return java.util.Collections.emptyList();
//        }
//    }
//
//    @Override
//    public int countByTransactionId(String transactionId) {
//        if (transactionId == null || transactionId.trim().isEmpty()) {
//            return 0;
//        }
//
//        try {
//            return stepMapper.countByTransactionId(transactionId);
//        } catch (Exception e) {
//            logger.error("[{}] 统计步骤数量异常: {}", transactionId, e.getMessage(), e);
//            return 0;
//        }
//    }
//
//    @Override
//    public int countByTransactionIdAndStatus(String transactionId, StepStatus status) {
//        if (transactionId == null || transactionId.trim().isEmpty() || status == null) {
//            return 0;
//        }
//
//        try {
//            return stepMapper.countByTransactionIdAndStatus(transactionId, status.getCode());
//        } catch (Exception e) {
//            logger.error("[{}] 统计状态[{}]步骤数量异常: {}", transactionId, status.getCode(), e.getMessage(), e);
//            return 0;
//        }
//    }
//
//    @Override
//    public long countByStatus(StepStatus status) {
//        if (status == null) {
//            return 0;
//        }
//
//        try {
//            return stepMapper.countByStatus(status.getCode());
//        } catch (Exception e) {
//            logger.error("统计状态[{}]步骤数量异常: {}", status.getCode(), e.getMessage(), e);
//            return 0;
//        }
//    }
//
//    @Override
//    public int deleteByTransactionId(String transactionId) {
//        if (transactionId == null || transactionId.trim().isEmpty()) {
//            logger.warn("删除事务步骤失败：事务ID为空");
//            return 0;
//        }
//
//        try {
//            int result = stepMapper.deleteByTransactionId(transactionId);
//            logger.info("[{}] 删除事务步骤{}条", transactionId, result);
//            return result;
//
//        } catch (Exception e) {
//            logger.error("[{}] 删除事务步骤异常: {}", transactionId, e.getMessage(), e);
//            return 0;
//        }
//    }
//
//    @Override
//    public int deleteBeforeTime(LocalDateTime beforeTime) {
//        if (beforeTime == null) {
//            logger.warn("删除历史步骤失败：时间参数为空");
//            return 0;
//        }
//
//        try {
//            int result = stepMapper.deleteBeforeTime(beforeTime);
//            logger.info("删除{}之前的历史步骤{}条", beforeTime, result);
//            return result;
//
//        } catch (Exception e) {
//            logger.error("删除历史步骤异常: {}", e.getMessage(), e);
//            return 0;
//        }
//    }
//
//    @Override
//    public int batchUpdateStatus(List<Long> stepIds, StepStatus newStatus) {
//        if (stepIds == null || stepIds.isEmpty() || newStatus == null) {
//            logger.warn("批量更新步骤状态失败：参数为空");
//            return 0;
//        }
//
//        try {
//            int result = stepMapper.batchUpdateStatus(stepIds, newStatus.getCode(), LocalDateTime.now());
//            logger.debug("批量更新{}个步骤状态为{}", result, newStatus.getCode());
//            return result;
//
//        } catch (Exception e) {
//            logger.error("批量更新步骤状态异常: {}", e.getMessage(), e);
//            return 0;
//        }
//    }
//
//    @Override
//    public int batchUpdateStatusByTransaction(String transactionId, StepStatus oldStatus, StepStatus newStatus) {
//        if (transactionId == null || transactionId.trim().isEmpty() || oldStatus == null || newStatus == null) {
//            logger.warn("批量更新事务步骤状态失败：参数为空");
//            return 0;
//        }
//
//        try {
//            int result = stepMapper.batchUpdateStatusByTransaction(transactionId, oldStatus.getCode(), newStatus.getCode(), LocalDateTime.now());
//            logger.debug("[{}] 批量更新步骤状态{}->{}，影响{}条记录", transactionId, oldStatus.getCode(), newStatus.getCode(), result);
//            return result;
//
//        } catch (Exception e) {
//            logger.error("[{}] 批量更新步骤状态异常: {}", transactionId, e.getMessage(), e);
//            return 0;
//        }
//    }
//
//    @Override
//    public boolean exists(Long stepId) {
//        if (stepId == null) {
//            return false;
//        }
//
//        try {
//            return stepMapper.existsByStepId(stepId) > 0;
//        } catch (Exception e) {
//            logger.error("检查步骤[{}]存在性异常: {}", stepId, e.getMessage(), e);
//            return false;
//        }
//    }
//
//    @Override
//    public StepExecutionStatistics getExecutionStatistics(String transactionId) {
//        if (transactionId == null || transactionId.trim().isEmpty()) {
//            return new StepExecutionStatistics();
//        }
//
//        try {
//            com.tipray.transaction.storage.dto.StepExecutionStatistics mapperStats = stepMapper.selectExecutionStatistics(transactionId);
//            if (mapperStats != null) {
//                return new StepExecutionStatistics(
//                        mapperStats.getTotalSteps(),
//                        mapperStats.getSuccessSteps(),
//                        mapperStats.getFailedSteps(),
//                        mapperStats.getPendingSteps(),
//                        mapperStats.getExecutingSteps(),
//                        mapperStats.getUndoingSteps(),
//                        mapperStats.getUndoneSteps(),
//                        mapperStats.getSkippedSteps()
//                );
//            }
//
//            return new StepExecutionStatistics();
//
//        } catch (Exception e) {
//            logger.error("[{}] 获取步骤执行统计异常: {}", transactionId, e.getMessage(), e);
//            return new StepExecutionStatistics();
//        }
//    }
//
//    @Override
//    public List<StepExecutionStatistics> getExecutionStatistics(LocalDateTime startTime, LocalDateTime endTime) {
//        try {
//            // 查询时间范围内的所有事务ID
//            List<String> transactionIds = stepMapper.selectDistinctTransactionIdsByTimeRange(startTime, endTime);
//
//            if (transactionIds == null || transactionIds.isEmpty()) {
//                logger.debug("时间范围内没有找到事务步骤: {} 到 {}", startTime, endTime);
//                return new ArrayList<>();
//            }
//
//            // 为每个事务生成统计信息
//            List<StepExecutionStatistics> statisticsList = new ArrayList<>();
//            for (String transactionId : transactionIds) {
//                StepExecutionStatistics statistics = getExecutionStatistics(transactionId);
//                if (statistics != null) {
//                    statisticsList.add(statistics);
//                }
//            }
//
//            logger.debug("获取时间范围内步骤执行统计成功: {} 到 {}, 事务数: {}",
//                        startTime, endTime, statisticsList.size());
//            return statisticsList;
//
//        } catch (Exception e) {
//            logger.error("获取时间范围内步骤执行统计异常: {} 到 {}", startTime, endTime, e);
//            return new ArrayList<>();
//        }
//    }
//}
