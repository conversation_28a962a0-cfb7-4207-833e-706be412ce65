-- 事务屏障表
-- 用于实现分布式事务的幂等性检查、空补偿检查、悬挂检查等安全机制

CREATE TABLE IF NOT EXISTS `transaction_barrier`
(
    `id`             BIGINT(20)   NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `barrier_key`    VARCHAR(255) NOT NULL COMMENT '屏障键，格式：barrier:{transactionId}:{stepName}:{action}',
    `transaction_id` VARCHAR(64)  NOT NULL COMMENT '事务ID',
    `step_name`      VARCHAR(128) NOT NULL COMMENT '步骤名称',
    `action`         VARCHAR(32)  NOT NULL COMMENT '操作类型：try/confirm/cancel/cancel_retry_1等',
    `create_time`    DATETIME     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`    DATETIME     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `remark`         VARCHAR(512)          DEFAULT NULL COMMENT '备注信息',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_barrier_key` (`barrier_key`) COMMENT '屏障键唯一索引',
    KEY `idx_transaction_id` (`transaction_id`) COMMENT '事务ID索引',
    KEY `idx_step_name` (`step_name`) COMMENT '步骤名称索引',
    KEY `idx_action` (`action`) COMMENT '操作类型索引',
    KEY `idx_create_time` (`create_time`) COMMENT '创建时间索引'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci COMMENT ='事务屏障表';

-- 创建索引优化查询性能
-- 复合索引：事务ID + 步骤名称
CREATE INDEX `idx_transaction_step` ON `transaction_barrier` (`transaction_id`, `step_name`);

-- 复合索引：事务ID + 操作类型
CREATE INDEX `idx_transaction_action` ON `transaction_barrier` (`transaction_id`, `action`);

-- 复合索引：步骤名称 + 操作类型
CREATE INDEX `idx_step_action` ON `transaction_barrier` (`step_name`, `action`);

-- 插入示例数据（可选，用于测试）
-- INSERT INTO transaction_barrier (barrier_key, transaction_id, step_name, action) VALUES
-- ('barrier:tx_001:step1:try', 'tx_001', 'step1', 'try'),
-- ('barrier:tx_001:step1:confirm', 'tx_001', 'step1', 'confirm'),
-- ('barrier:tx_002:step1:try', 'tx_002', 'step1', 'try'),
-- ('barrier:tx_002:step1:cancel', 'tx_002', 'step1', 'cancel');

-- 清理过期数据的存储过程（可选）
DELIMITER $$
CREATE PROCEDURE IF NOT EXISTS `CleanExpiredBarriers`(IN days_to_keep INT)
BEGIN
    DECLARE done INT DEFAULT FALSE;
    DECLARE cleanup_time DATETIME;

    -- 计算清理时间点
    SET cleanup_time = DATE_SUB(NOW(), INTERVAL days_to_keep DAY);

    -- 删除过期的屏障记录
    DELETE
    FROM transaction_barrier
    WHERE create_time < cleanup_time;

    -- 输出清理结果
    SELECT ROW_COUNT() AS cleaned_records, cleanup_time AS cleanup_before_time;
END$$
DELIMITER ;

-- 创建定时清理事件（可选，需要开启事件调度器）
-- SET GLOBAL event_scheduler = ON;
--
-- CREATE EVENT IF NOT EXISTS `event_clean_expired_barriers`
-- ON SCHEDULE EVERY 1 DAY
-- STARTS CURRENT_TIMESTAMP
-- DO
--   CALL CleanExpiredBarriers(7); -- 保留7天内的数据
