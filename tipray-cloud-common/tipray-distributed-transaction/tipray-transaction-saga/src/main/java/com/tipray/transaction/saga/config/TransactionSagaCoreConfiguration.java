package com.tipray.transaction.saga.config;

import com.tipray.transaction.saga.handler.SagaTransactionModeHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * Saga模式核心配置类
 * 负责Saga模式核心组件的Bean配置
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-06-20
 */
@Slf4j
@Configuration
@ConditionalOnProperty(
        prefix = "tipray.transaction.saga",
        name = "enabled",
        havingValue = "true",
        matchIfMissing = true
)
public class TransactionSagaCoreConfiguration {

    @Bean
    @ConditionalOnMissingBean
    public SagaTransactionModeHandler sagaTransactionHandler() {
        log.debug("创建Saga事务处理器 Bean");
        return new SagaTransactionModeHandler();
    }
}
