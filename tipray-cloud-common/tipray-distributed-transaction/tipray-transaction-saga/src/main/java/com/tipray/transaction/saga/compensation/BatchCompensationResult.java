package com.tipray.transaction.saga.compensation;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 批量补偿执行结果
 * 封装多个步骤的补偿执行结果
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-06-20
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class BatchCompensationResult {

    /**
     * 事务ID
     */
    private String transactionId;

    /**
     * 总步骤数
     */
    private int totalSteps;

    /**
     * 成功步骤数
     */
    private int successSteps;

    /**
     * 失败步骤数
     */
    private int failedSteps;

    /**
     * 跳过步骤数
     */
    private int skippedSteps;

    /**
     * 批量执行是否全部成功
     */
    private boolean allSuccess;

    /**
     * 执行开始时间
     */
    private LocalDateTime startTime;

    /**
     * 执行结束时间
     */
    private LocalDateTime endTime;

    /**
     * 总执行耗时（毫秒）
     */
    private long totalExecutionTime;

    /**
     * 各步骤的补偿结果
     */
    @Builder.Default
    private List<CompensationResult> stepResults = new ArrayList<>();

    /**
     * 失败的步骤结果
     */
    private List<CompensationResult> failedResults;

    /**
     * 扩展属性
     */
    private Map<String, Object> extendProperties;

    /**
     * 创建批量补偿结果
     *
     * @param transactionId 事务ID
     * @param stepResults   步骤结果列表
     * @return 批量补偿结果
     */
    public static BatchCompensationResult create(String transactionId, List<CompensationResult> stepResults) {
        if (stepResults == null || stepResults.isEmpty()) {
            return BatchCompensationResult.builder()
                    .transactionId(transactionId)
                    .totalSteps(0)
                    .successSteps(0)
                    .failedSteps(0)
                    .skippedSteps(0)
                    .allSuccess(true)
                    .stepResults(new ArrayList<>())
                    .build();
        }

        int totalSteps = stepResults.size();
        int successSteps = 0;
        int failedSteps = 0;
        long totalExecutionTime = 0;
        LocalDateTime earliestStart = null;
        LocalDateTime latestEnd = null;

        List<CompensationResult> failedResults = new ArrayList<>();

        for (CompensationResult result : stepResults) {
            if (result.isSuccess()) {
                successSteps++;
            } else {
                failedSteps++;
                failedResults.add(result);
            }

            totalExecutionTime += result.getExecutionTime();

            if (result.getStartTime() != null) {
                if (earliestStart == null || result.getStartTime().isBefore(earliestStart)) {
                    earliestStart = result.getStartTime();
                }
            }

            if (result.getEndTime() != null) {
                if (latestEnd == null || result.getEndTime().isAfter(latestEnd)) {
                    latestEnd = result.getEndTime();
                }
            }
        }

        boolean allSuccess = failedSteps == 0;

        return BatchCompensationResult.builder()
                .transactionId(transactionId)
                .totalSteps(totalSteps)
                .successSteps(successSteps)
                .failedSteps(failedSteps)
                .skippedSteps(0)
                .allSuccess(allSuccess)
                .startTime(earliestStart)
                .endTime(latestEnd)
                .totalExecutionTime(totalExecutionTime)
                .stepResults(stepResults)
                .failedResults(failedResults)
                .build();
    }

    /**
     * 添加步骤结果
     *
     * @param result 步骤结果
     */
    public void addStepResult(CompensationResult result) {
        if (stepResults == null) {
            stepResults = new ArrayList<>();
        }
        stepResults.add(result);

        // 更新统计信息
        updateStatistics();
    }

    /**
     * 获取成功的步骤结果
     *
     * @return 成功步骤结果列表
     */
    public List<CompensationResult> getSuccessResults() {
        if (stepResults == null) {
            return new ArrayList<>();
        }

        return stepResults.stream()
                .filter(CompensationResult::isSuccess)
                .collect(Collectors.toList());
    }

    /**
     * 获取失败的步骤结果
     *
     * @return 失败步骤结果列表
     */
    public List<CompensationResult> getFailedResults() {
        if (failedResults != null) {
            return failedResults;
        }

        if (stepResults == null) {
            return new ArrayList<>();
        }

        return stepResults.stream()
                .filter(CompensationResult::isFailure)
                .collect(Collectors.toList());
    }

    /**
     * 根据步骤ID获取结果
     *
     * @param stepId 步骤ID
     * @return 步骤结果，不存在返回null
     */
    public CompensationResult getResultByStepId(Long stepId) {
        if (stepResults == null || stepId == null) {
            return null;
        }

        return stepResults.stream()
                .filter(result -> stepId.equals(result.getStepId()))
                .findFirst()
                .orElse(null);
    }

    /**
     * 获取成功率
     *
     * @return 成功率（0-100）
     */
    public double getSuccessRate() {
        if (totalSteps == 0) {
            return 100.0;
        }
        return (double) successSteps / totalSteps * 100;
    }

    /**
     * 获取失败率
     *
     * @return 失败率（0-100）
     */
    public double getFailureRate() {
        if (totalSteps == 0) {
            return 0.0;
        }
        return (double) failedSteps / totalSteps * 100;
    }

    /**
     * 判断是否有失败的步骤
     *
     * @return true表示有失败步骤
     */
    public boolean hasFailures() {
        return failedSteps > 0;
    }

    /**
     * 获取平均执行时间
     *
     * @return 平均执行时间（毫秒）
     */
    public long getAverageExecutionTime() {
        if (totalSteps == 0) {
            return 0;
        }
        return totalExecutionTime / totalSteps;
    }

    /**
     * 更新统计信息
     */
    private void updateStatistics() {
        if (stepResults == null) {
            return;
        }

        totalSteps = stepResults.size();
        successSteps = (int) stepResults.stream().filter(CompensationResult::isSuccess).count();
        failedSteps = (int) stepResults.stream().filter(CompensationResult::isFailure).count();
        allSuccess = failedSteps == 0;

        totalExecutionTime = stepResults.stream()
                .mapToLong(CompensationResult::getExecutionTime)
                .sum();

        // 更新失败结果列表
        failedResults = getFailedResults();
    }

    /**
     * 添加扩展属性
     *
     * @param key   属性键
     * @param value 属性值
     */
    public void addExtendProperty(String key, Object value) {
        if (extendProperties == null) {
            extendProperties = new java.util.HashMap<>();
        }
        extendProperties.put(key, value);
    }

    /**
     * 获取扩展属性
     *
     * @param key 属性键
     * @return 属性值
     */
    public Object getExtendProperty(String key) {
        return extendProperties != null ? extendProperties.get(key) : null;
    }
}
