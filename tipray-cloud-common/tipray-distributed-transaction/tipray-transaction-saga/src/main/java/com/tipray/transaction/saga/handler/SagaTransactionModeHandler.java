package com.tipray.transaction.saga.handler;

import com.tipray.transaction.core.annotation.DistributedBranchTransaction;
import com.tipray.transaction.core.application.handler.mode.AbstractTransactionModeHandler;
import com.tipray.transaction.core.domain.transaction.BranchTransactionDO;
import com.tipray.transaction.core.domain.transaction.BranchTransationConfig;
import com.tipray.transaction.core.enums.TransactionMode;
import com.tipray.transaction.saga.compensation.CompensationExecutor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import java.lang.reflect.Method;

/**
 * Saga模式事务处理器
 * 继承AbstractTransactionHandler，实现Saga模式特定的事务处理逻辑
 * 负责Saga模式的补偿机制和步骤编排
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-06-20
 */
@Slf4j
public class SagaTransactionModeHandler extends AbstractTransactionModeHandler {

    @Autowired
    private CompensationExecutor compensationExecutor;

    @Override
    public TransactionMode getSupportedMode() {
        return TransactionMode.SAGA;
    }

    @Override
    public String getHandlerName() {
        return "SagaTransactionModeHandler";
    }

    @Override
    public BranchTransationConfig parseBranchTransationConfig(DistributedBranchTransaction annotation, Method method, TransactionMode mode) {
        BranchTransationConfig config = super.parseBranchTransationConfig(annotation, method, mode);

        // 校验Saga模式必须参数
        validateSagaModeRequiredParameters(annotation, method);

        return config;
    }

    @Override
    protected void doBranchRollback(BranchTransactionDO branchTransactionDO, Exception cause) {
        // 通过补偿方法回滚
        compensationExecutor.executeCompensation(branchTransactionDO);
    }


    /**
     * 校验Saga模式必须参数
     *
     * @param annotation 分支事务注解
     * @param method     方法
     */
    private void validateSagaModeRequiredParameters(DistributedBranchTransaction annotation, Method method) {
        String methodName = method.getDeclaringClass().getSimpleName() + "." + method.getName();

        // 1. 步骤名称校验（Saga模式中步骤名称很重要，用于补偿和编排）
        String stepName = annotation.value().trim().isEmpty() ? method.getName() : annotation.value();
        if (stepName.trim().isEmpty()) {
            throw new IllegalArgumentException(String.format(
                    "Saga模式分支事务步骤名称不能为空: %s", methodName));
        }

        // 2. 补偿方法校验（Saga模式的核心特性）
        if (annotation.compensation() == null || annotation.compensation().trim().isEmpty()) {
            log.warn("Saga模式分支事务未指定补偿方法，如果步骤失败将无法进行补偿: {} -> {}",
                    methodName, stepName);
        } else {
            // 校验补偿方法格式
            String compensation = annotation.compensation().trim();
            if (!isValidCompensationMethod(compensation)) {
                throw new IllegalArgumentException(String.format(
                        "Saga模式分支事务补偿方法格式不正确，应为 'methodName' 或 'beanName.methodName': %s -> %s",
                        methodName, compensation));
            }
        }

        // 3. 超时时间校验（必须大于0）
        if (annotation.timeout() <= 0) {
            throw new IllegalArgumentException(String.format(
                    "Saga模式分支事务超时时间必须大于0: %s, 当前值: %d", methodName, annotation.timeout()));
        }

        // 4. 步骤顺序校验（不能为负数）
        if (annotation.order() < 0) {
            throw new IllegalArgumentException(String.format(
                    "Saga模式分支事务步骤顺序不能为负数: %s, 当前值: %d", methodName, annotation.order()));
        }

        // 5. 重试次数校验（不能为负数）
        if (annotation.retryCount() < 0) {
            throw new IllegalArgumentException(String.format(
                    "Saga模式分支事务重试次数不能为负数: %s, 当前值: %d", methodName, annotation.retryCount()));
        }

        // 6. 重试间隔校验（如果有重试，间隔必须大于0）
        if (annotation.retryCount() > 0 && annotation.retryInterval() <= 0) {
            throw new IllegalArgumentException(String.format(
                    "Saga模式分支事务设置了重试次数时，重试间隔必须大于0: %s, 重试次数: %d, 重试间隔: %d",
                    methodName, annotation.retryCount(), annotation.retryInterval()));
        }

        log.debug("Saga模式分支事务参数校验通过: {} -> 步骤: {}, 补偿: {}",
                methodName, stepName, annotation.compensation());
    }

    /**
     * 校验补偿方法格式是否正确
     *
     * @param compensation 补偿方法字符串
     * @return true表示格式正确
     */
    private boolean isValidCompensationMethod(String compensation) {
        if (compensation == null || compensation.trim().isEmpty()) {
            return false;
        }

        compensation = compensation.trim();

        // 格式1: methodName（同一个类中的方法）
        if (!compensation.contains(".")) {
            return isValidMethodName(compensation);
        }

        // 格式2: beanName.methodName
        String[] parts = compensation.split("\\.");
        if (parts.length == 2) {
            return isValidBeanName(parts[0]) && isValidMethodName(parts[1]);
        }

        return false;
    }

    /**
     * 校验方法名是否有效
     */
    private boolean isValidMethodName(String methodName) {
        return methodName != null &&
                methodName.matches("^[a-zA-Z_$][a-zA-Z0-9_$]*$") &&
                methodName.length() > 0;
    }

    /**
     * 校验Bean名称是否有效
     */
    private boolean isValidBeanName(String beanName) {
        return beanName != null &&
                beanName.matches("^[a-zA-Z_$][a-zA-Z0-9_$]*$") &&
                beanName.length() > 0;
    }


//    @Override
//    protected void doCommit(DistributedTransactionDO transaction) throws Exception {
//        String transactionId = transaction.getTransactionId();
//        logger.info("[{}] - [SAGA] 开始提交Saga模式事务", transactionId);
//
//        try {
//            List<TransactionStep> successfulSteps = transaction.getSuccessfulSteps();
//
//            // Saga模式的提交通常不需要特殊操作
//            // 因为每个步骤都是立即提交的
//
//            // 更新事务状态为成功
//            statusManager.updateTransactionStatus(transaction, DistributedTransactionStatus.SUCCESS, null);
//
//            logger.info("[{}] - [SAGA] Saga模式事务提交完成，共{}个步骤", transactionId, successfulSteps.size());
//
//        } catch (Exception e) {
//            logger.error("[{}] - [SAGA] Saga模式事务提交失败: {}", transactionId, e.getMessage(), e);
//            throw new DistributedTransactionException("SAGA_COMMIT_FAILED",
//                "Saga模式事务提交失败", e, transactionId, transaction.getGroupId());
//        }
//    }
//
//    @Override
//    protected void executeStep(DistributedTransactionDO transaction, TransactionStep step) throws Exception {
//        String transactionId = transaction.getTransactionId();
//        Long stepId = step.getStepId();
//
//        logger.info("[{}] - [SAGA] 开始执行步骤[{}]: {}", transactionId, stepId, step.getStepName());
//
//        try {
//            // 更新步骤状态为执行中
//            statusManager.updateStepStatus(step, StepStatus.EXECUTING, null);
//
//            // Saga模式的步骤执行：通过@TransactionStep切面自动处理
//            // 这里主要是状态管理
//
//            // 更新步骤状态为成功
//            statusManager.updateStepStatus(step, StepStatus.SUCCESS, null);
//
//            logger.info("[{}] - [SAGA] 步骤[{}]执行成功", transactionId, stepId);
//
//        } catch (Exception e) {
//            logger.error("[{}] - [SAGA] 步骤[{}]执行失败: {}", transactionId, stepId, e.getMessage(), e);
//
//            // 更新步骤状态为失败
//            statusManager.updateStepStatus(step, StepStatus.FAILED, e);
//
//            throw new DistributedTransactionException("SAGA_STEP_EXECUTE_FAILED",
//                    "Saga模式步骤执行失败: " + step.getStepName(), e, transactionId, transaction.getGroupId());
//        }
//    }
//
//    @Override
//    protected void undoStep(DistributedTransactionDO transaction, TransactionStep step) throws Exception {
//        String transactionId = transaction.getTransactionId();
//        Long stepId = step.getStepId();
//
//        logger.info("[{}] - [SAGA] 开始补偿步骤[{}]: {}", transactionId, stepId, step.getStepName());
//
//        try {
//            // 更新步骤状态为补偿中
//            statusManager.updateStepStatus(step, StepStatus.UNDOING, null);
//
//            // 执行补偿逻辑
//            executeCompensation(transaction, step);
//
//            // 更新步骤状态为已补偿
//            statusManager.updateStepStatus(step, StepStatus.UNDONE, null);
//
//            logger.info("[{}] - [SAGA] 步骤[{}]补偿成功", transactionId, stepId);
//
//        } catch (Exception e) {
//            logger.error("[{}] - [SAGA] 步骤[{}]补偿失败: {}", transactionId, stepId, e.getMessage(), e);
//
//            // 更新步骤状态为补偿失败
//            statusManager.updateStepStatus(step, StepStatus.UNDO_FAILED, e);
//
//            throw new DistributedTransactionException("SAGA_STEP_COMPENSATION_FAILED",
//                    "Saga模式步骤补偿失败: " + step.getStepName(), e, transactionId, transaction.getGroupId());
//        }
//    }
//
//    /**
//     * 执行补偿操作
//     *
//     * @param transaction 事务
//     * @param step 事务步骤
//     * @throws Exception 补偿异常
//     */
//    private void executeCompensation(DistributedTransactionDO transaction, TransactionStep step) throws Exception {
//        String transactionId = transaction.getTransactionId();
//        logger.debug("[{}] - [SAGA] 执行补偿操作: [{}]", transactionId, step.getStepName());
//
//        try {
//            // 通过补偿执行器执行补偿逻辑
//            compensationExecutor.executeCompensation(transaction, step);
//
//            logger.info("[{}] - [SAGA] 补偿操作执行成功: [{}]", transactionId, step.getStepName());
//
//        } catch (Exception e) {
//            logger.error("[{}] - [SAGA] 补偿操作执行失败: [{}], 错误: {}",
//                     transactionId, step.getStepName(), e.getMessage(), e);
//            throw e; // 补偿失败必须抛出异常
//        }
//    }
//
//    // ==================== Saga模式特有逻辑 ====================
//
//    @Override
//    protected void beforeStepExecution(DistributedTransactionDO transaction, TransactionStep step) throws Exception {
//        // Saga模式前置处理：准备步骤执行
//        String transactionId = transaction.getTransactionId();
//        logger.debug("[{}] - [SAGA] 准备执行步骤: {}", transactionId, step.getStepName());
//
//        // 屏障记录已经在checkStepBarrierBeforeExecute中的幂等性检查时插入
//        // 这里可以进行Saga模式特有的前置处理
//    }
//
//    @Override
//    protected void afterStepExecution(DistributedTransactionDO transaction, TransactionStep step, Object result) throws Exception {
//        // Saga模式后置处理：记录步骤执行成功，为后续可能的补偿做准备
//        String transactionId = transaction.getTransactionId();
//        logger.debug("[{}] - [SAGA] 步骤执行完成，记录补偿信息: {}", transactionId, step.getStepName());
//
//        // Saga模式需要记录步骤的执行结果，以便后续补偿时使用
//        // 这里可以记录步骤的执行参数和结果，供补偿方法使用
//    }
//
//    @Override
//    protected void onStepExecutionFailure(DistributedTransactionDO transaction, TransactionStep step, Exception exception) {
//        // Saga模式异常处理：记录失败信息，准备补偿已执行的步骤
//        String transactionId = transaction.getTransactionId();
//        logger.warn("[{}] - [SAGA] 步骤执行失败，准备补偿: {}, 错误: {}",
//            transactionId, step.getStepName(), exception.getMessage());
//
//        // Saga模式的异常处理需要触发补偿流程
//        // 补偿逻辑由SagaTransactionHandler的undo方法统一处理
//    }


}
