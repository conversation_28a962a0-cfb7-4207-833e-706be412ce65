package com.tipray.transaction.saga.compensation;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 补偿执行结果
 * 封装单个步骤的补偿执行结果
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-06-20
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class CompensationResult {

    /**
     * 步骤ID
     */
    private Long stepId;

    /**
     * 补偿是否成功
     */
    private boolean success;

    /**
     * 补偿结果数据
     */
    private Object resultData;

    /**
     * 错误信息
     */
    private String errorMessage;

    /**
     * 异常对象
     */
    private Exception exception;

    /**
     * 执行开始时间
     */
    private LocalDateTime startTime;

    /**
     * 执行结束时间
     */
    private LocalDateTime endTime;

    /**
     * 执行耗时（毫秒）
     */
    private long executionTime;

    /**
     * 重试次数
     */
    private int retryCount;

    /**
     * 补偿方法名称
     */
    private String compensationMethod;

    /**
     * 扩展属性
     */
    private java.util.Map<String, Object> extendProperties;

    /**
     * 创建成功结果
     *
     * @param stepId     步骤ID
     * @param resultData 结果数据
     * @return 成功结果
     */
    public static CompensationResult success(Long stepId, Object resultData) {
        return CompensationResult.builder()
                .stepId(stepId)
                .success(true)
                .resultData(resultData)
                .endTime(LocalDateTime.now())
                .build();
    }

    /**
     * 创建成功结果（带执行时间）
     *
     * @param stepId     步骤ID
     * @param resultData 结果数据
     * @param startTime  开始时间
     * @param endTime    结束时间
     * @return 成功结果
     */
    public static CompensationResult success(Long stepId, Object resultData,
                                             LocalDateTime startTime, LocalDateTime endTime) {
        long executionTime = java.time.Duration.between(startTime, endTime).toMillis();

        return CompensationResult.builder()
                .stepId(stepId)
                .success(true)
                .resultData(resultData)
                .startTime(startTime)
                .endTime(endTime)
                .executionTime(executionTime)
                .build();
    }

    /**
     * 创建失败结果
     *
     * @param stepId       步骤ID
     * @param errorMessage 错误信息
     * @return 失败结果
     */
    public static CompensationResult failure(Long stepId, String errorMessage) {
        return CompensationResult.builder()
                .stepId(stepId)
                .success(false)
                .errorMessage(errorMessage)
                .endTime(LocalDateTime.now())
                .build();
    }

    /**
     * 创建失败结果（带异常）
     *
     * @param stepId       步骤ID
     * @param errorMessage 错误信息
     * @param exception    异常对象
     * @return 失败结果
     */
    public static CompensationResult failure(Long stepId, String errorMessage, Exception exception) {
        return CompensationResult.builder()
                .stepId(stepId)
                .success(false)
                .errorMessage(errorMessage)
                .exception(exception)
                .endTime(LocalDateTime.now())
                .build();
    }

    /**
     * 创建失败结果（带完整信息）
     *
     * @param stepId       步骤ID
     * @param errorMessage 错误信息
     * @param exception    异常对象
     * @param startTime    开始时间
     * @param endTime      结束时间
     * @param retryCount   重试次数
     * @return 失败结果
     */
    public static CompensationResult failure(Long stepId, String errorMessage, Exception exception,
                                             LocalDateTime startTime, LocalDateTime endTime, int retryCount) {
        long executionTime = startTime != null && endTime != null ?
                java.time.Duration.between(startTime, endTime).toMillis() : 0;

        return CompensationResult.builder()
                .stepId(stepId)
                .success(false)
                .errorMessage(errorMessage)
                .exception(exception)
                .startTime(startTime)
                .endTime(endTime)
                .executionTime(executionTime)
                .retryCount(retryCount)
                .build();
    }

    /**
     * 判断是否为成功结果
     *
     * @return true表示成功
     */
    public boolean isSuccess() {
        return success;
    }

    /**
     * 判断是否为失败结果
     *
     * @return true表示失败
     */
    public boolean isFailure() {
        return !success;
    }

    /**
     * 获取执行耗时（毫秒）
     *
     * @return 执行耗时
     */
    public long getExecutionTime() {
        if (executionTime > 0) {
            return executionTime;
        }

        if (startTime != null && endTime != null) {
            return java.time.Duration.between(startTime, endTime).toMillis();
        }

        return 0;
    }

    /**
     * 添加扩展属性
     *
     * @param key   属性键
     * @param value 属性值
     */
    public void addExtendProperty(String key, Object value) {
        if (extendProperties == null) {
            extendProperties = new java.util.HashMap<>();
        }
        extendProperties.put(key, value);
    }

    /**
     * 获取扩展属性
     *
     * @param key 属性键
     * @return 属性值
     */
    public Object getExtendProperty(String key) {
        return extendProperties != null ? extendProperties.get(key) : null;
    }
}
