package com.tipray.transaction.saga.compensation;

import com.tipray.transaction.core.domain.transaction.BranchTransactionDO;

/**
 * 补偿执行器接口
 * 定义Saga模式的补偿执行逻辑
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-06-20
 */
public interface CompensationExecutor {

    /**
     * 执行单个步骤的补偿
     *
     * @return 补偿结果
     * @throws Exception 补偿异常
     */
    CompensationResult executeCompensation(BranchTransactionDO transactionDO);

    /**
     * 检查是否支持指定的补偿方法
     *
     * @param compensationMethod 补偿方法
     * @return true表示支持
     */
    boolean supports(String compensationMethod);

    /**
     * 获取执行器名称
     *
     * @return 执行器名称
     */
    String getExecutorName();
}
