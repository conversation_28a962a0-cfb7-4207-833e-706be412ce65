# 故障排查

本文档提供Tipray分布式事务框架常见问题的排查方法和解决方案，帮助您快速定位和解决生产环境中的问题。

## 🚨 常见问题分类

### 1. 启动问题

- 框架无法启动
- 自动配置失败
- 数据库连接问题

### 2. 事务执行问题

- 事务无法开启
- 事务执行超时
- 事务回滚失败

### 3. 性能问题

- 事务执行缓慢
- 内存占用过高
- 数据库连接池耗尽

### 4. 数据一致性问题

- 数据不一致
- UndoLog异常
- 分支事务状态异常

## 🔍 问题排查流程

### 排查步骤

```
1. 查看应用日志
    ↓
2. 检查配置文件
    ↓
3. 验证数据库状态
    ↓
4. 查看监控界面
    ↓
5. 分析事务详情
    ↓
6. 定位根本原因
    ↓
7. 实施解决方案
```

## 📋 启动问题排查

### 问题1：框架无法启动

#### 症状

```
应用启动时报错：
org.springframework.beans.factory.BeanCreationException: 
Error creating bean with name 'tiprayTransactionAutoConfiguration'
```

#### 排查步骤

1. **检查依赖配置**

```xml
<!-- 确认依赖版本正确 -->
<dependency>
    <groupId>com.tipray</groupId>
    <artifactId>tipray-transaction-spring-boot-starter</artifactId>
    <version>1.0.0</version>
</dependency>
```

2. **检查配置文件**

```yaml
tipray:
  transaction:
    enabled: true  # 确认已启用
    application-name: ${spring.application.name}
```

3. **检查Java版本**

```bash
java -version
# 确认使用Java 1.8+
```

#### 解决方案

```yaml
# 完整的最小配置
tipray:
  transaction:
    enabled: true
    application-name: my-app
    
spring:
  datasource:
    url: ***********************************
    username: root
    password: password
    driver-class-name: com.mysql.cj.jdbc.Driver
```

### 问题2：数据库连接失败

#### 症状

```
java.sql.SQLException: Access denied for user 'root'@'localhost'
```

#### 排查步骤

1. **验证数据库连接**

```bash
mysql -h localhost -u root -p
```

2. **检查数据库配置**

```yaml
spring:
  datasource:
    url: *****************************************************************************
    username: root
    password: correct_password
```

3. **验证数据库表**

```sql
-- 检查必要的表是否存在
SHOW TABLES LIKE 'undo_log';
SHOW TABLES LIKE 'tipray_tx_%';
```

#### 解决方案

```sql
-- 创建必要的表
CREATE TABLE undo_log (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    branch_id BIGINT NOT NULL,
    xid VARCHAR(100) NOT NULL,
    context VARCHAR(128) NOT NULL,
    rollback_info LONGBLOB NOT NULL,
    log_status INT NOT NULL,
    log_created DATETIME NOT NULL,
    log_modified DATETIME NOT NULL,
    UNIQUE KEY ux_undo_log (xid, branch_id)
);
```

### 问题3：自动配置冲突

#### 症状

```
Multiple beans of type 'DistributedTransactionManager' found
```

#### 排查步骤

1. **检查重复配置**

```java
// 检查是否有多个@Bean定义
@Configuration
public class TransactionConfig {
    
    @Bean
    public DistributedTransactionManager transactionManager() {
        // 可能与自动配置冲突
    }
}
```

2. **检查条件注解**

```java
@ConditionalOnMissingBean(DistributedTransactionManager.class)
```

#### 解决方案

```java
@Configuration
public class TransactionConfig {
    
    @Bean
    @Primary  // 指定主要的Bean
    public DistributedTransactionManager customTransactionManager() {
        return new CustomDistributedTransactionManager();
    }
}
```

## ⚡ 事务执行问题排查

### 问题1：事务无法开启

#### 症状

```
@DistributedTransaction注解不生效，事务没有开启
```

#### 排查步骤

1. **检查方法调用方式**

```java
@Service
public class OrderService {
    
    @DistributedTransaction
    public void createOrder() {
        // 必须通过Spring代理调用
        this.processOrder(); // ❌ 错误：直接调用
    }
    
    private void processOrder() {
        // 业务逻辑
    }
}
```

2. **检查方法访问修饰符**

```java
@DistributedTransaction
public void createOrder() {  // ✅ 正确：public方法
    // 业务逻辑
}

@DistributedTransaction
private void createOrder() {  // ❌ 错误：private方法
    // 业务逻辑
}
```

3. **检查异常处理**

```java
@DistributedTransaction
public void createOrder() {
    try {
        businessLogic();
    } catch (Exception e) {
        // ❌ 错误：捕获了异常但没有重新抛出
        log.error("业务异常", e);
    }
}
```

#### 解决方案

```java
@Service
public class OrderService {
    
    @Autowired
    private OrderService self;  // 注入自己，用于内部调用
    
    @DistributedTransaction
    public void createOrder() {
        // 通过代理调用其他事务方法
        self.processOrder();
    }
    
    @DistributedTransaction
    public void processOrder() {
        try {
            businessLogic();
        } catch (BusinessException e) {
            // ✅ 正确：重新抛出异常触发回滚
            throw e;
        }
    }
}
```

### 问题2：事务执行超时

#### 症状

```
TransactionTimeoutException: 事务执行超时，超时时间: 30秒
```

#### 排查步骤

1. **检查超时配置**

```java
@DistributedTransaction(timeoutSeconds = 30)  // 检查是否合理
public void longRunningTransaction() {
    // 长时间运行的业务逻辑
}
```

2. **分析执行时间**

```java
// 添加性能监控
@DistributedTransaction
public void createOrder() {
    long startTime = System.currentTimeMillis();
    
    step1();  // 分析每个步骤的耗时
    log.info("步骤1耗时: {}ms", System.currentTimeMillis() - startTime);
    
    step2();
    log.info("步骤2耗时: {}ms", System.currentTimeMillis() - startTime);
}
```

3. **检查数据库性能**

```sql
-- 查看慢查询
SHOW PROCESSLIST;
SHOW FULL PROCESSLIST;

-- 分析执行计划
EXPLAIN SELECT * FROM orders WHERE user_id = 1001;
```

#### 解决方案

```java
// 方案1：增加超时时间
@DistributedTransaction(timeoutSeconds = 60)
public void longRunningTransaction() {
    // 业务逻辑
}

// 方案2：拆分事务
@DistributedTransaction(timeoutSeconds = 30)
public void createOrder() {
    // 核心业务逻辑
    coreBusinessLogic();
}

@Async
public void asyncProcessing() {
    // 异步处理非核心逻辑
    nonCoreBusinessLogic();
}

// 方案3：优化数据库查询
@DistributedTransaction
public void optimizedTransaction() {
    // 使用索引优化查询
    // 减少不必要的数据库操作
    // 使用批量操作
}
```

### 问题3：事务回滚失败

#### 症状

```
RollbackException: UndoLog回滚失败，数据可能不一致
```

#### 排查步骤

1. **检查UndoLog表**

```sql
-- 查看UndoLog记录
SELECT * FROM undo_log WHERE xid = 'TX_20231201_001';

-- 检查UndoLog状态
SELECT log_status, COUNT(*) FROM undo_log GROUP BY log_status;
```

2. **检查数据变更**

```sql
-- 对比业务数据和UndoLog
SELECT * FROM orders WHERE id = 1001;

-- 检查数据是否被其他事务修改
SELECT * FROM orders WHERE id = 1001 FOR UPDATE;
```

3. **分析回滚日志**

```
2023-12-01 10:30:15 ERROR [tipray-tx-rollback-1] 
UndoExecutor - 回滚失败: 数据已被其他事务修改
原始数据: {"id":1001,"status":"CREATED","amount":100.00}
当前数据: {"id":1001,"status":"PAID","amount":100.00}
```

#### 解决方案

```java
// 方案1：增加重试机制
@DistributedTransaction(
    mode = TransactionMode.AT,
    retryCount = 3,
    retryInterval = 1000
)
public void createOrder() {
    // 业务逻辑
}

// 方案2：使用乐观锁
@Entity
public class Order {
    @Version
    private Long version;  // 乐观锁版本号
    
    // 其他字段...
}

// 方案3：手动处理数据冲突
@DistributedTransaction
public void createOrder() {
    try {
        businessLogic();
    } catch (DataConflictException e) {
        // 处理数据冲突
        handleDataConflict(e);
        throw e;  // 重新抛出触发回滚
    }
}
```

## 📊 性能问题排查

### 问题1：事务执行缓慢

#### 排查步骤

1. **启用性能监控**

```yaml
tipray:
  transaction:
    monitor:
      performance-enabled: true
      
logging:
  level:
    com.tipray.transaction: DEBUG
```

2. **分析监控数据**

```
访问监控界面: http://localhost:8080/tipray-transaction-ui
查看性能指标:
- 平均执行时间
- 95%响应时间
- TPS趋势
```

3. **数据库性能分析**

```sql
-- 查看数据库连接数
SHOW STATUS LIKE 'Threads_connected';

-- 查看慢查询
SHOW VARIABLES LIKE 'slow_query_log';
SHOW VARIABLES LIKE 'long_query_time';
```

#### 解决方案

```yaml
# 优化配置
tipray:
  transaction:
    # 优化线程池
    thread-pool:
      core-pool-size: 20
      maximum-pool-size: 50
      queue-capacity: 200
    
    # 优化AT模式
    at:
      undo-log:
        compression-enabled: true
        cleanup:
          enabled: true
          interval-hours: 1
    
    # 优化存储
    storage:
      database:
        batch-size: 100

# 优化数据库连接池
spring:
  datasource:
    hikari:
      maximum-pool-size: 20
      minimum-idle: 5
      connection-timeout: 30000
```

### 问题2：内存占用过高

#### 排查步骤

1. **内存分析**

```bash
# 生成堆转储
jmap -dump:format=b,file=heap.hprof <pid>

# 分析内存使用
jstat -gc <pid> 1s
```

2. **检查UndoLog清理**

```sql
-- 查看UndoLog数量
SELECT COUNT(*) FROM undo_log;

-- 查看过期UndoLog
SELECT COUNT(*) FROM undo_log 
WHERE log_created < DATE_SUB(NOW(), INTERVAL 1 DAY);
```

#### 解决方案

```yaml
tipray:
  transaction:
    at:
      undo-log:
        # 启用自动清理
        cleanup:
          enabled: true
          interval-hours: 1
          retention-days: 1
          batch-size: 1000
        
        # 启用压缩
        compression-enabled: true
        compression-threshold: 1024
    
    # 优化存储
    storage:
      memory:
        max-size: 10000
        expire-time: 3600
```

## 🔧 数据一致性问题排查

### 问题1：数据不一致

#### 排查步骤

1. **数据对比**

```sql
-- 对比相关表的数据
SELECT o.id, o.status, p.status as payment_status, i.quantity
FROM orders o
LEFT JOIN payments p ON o.id = p.order_id
LEFT JOIN inventory i ON o.product_id = i.product_id
WHERE o.id = 1001;
```

2. **检查事务日志**

```sql
-- 查看事务执行记录
SELECT * FROM tipray_tx_transaction 
WHERE transaction_id = 'TX_20231201_001';

-- 查看分支事务状态
SELECT * FROM tipray_tx_branch_transaction 
WHERE transaction_id = 'TX_20231201_001';
```

3. **分析UndoLog**

```sql
-- 查看UndoLog详情
SELECT xid, branch_id, rollback_info, log_status
FROM undo_log 
WHERE xid = 'TX_20231201_001';
```

#### 解决方案

```java
// 方案1：数据修复工具
@Component
public class DataConsistencyChecker {
    
    public void checkAndRepair(String transactionId) {
        // 检查数据一致性
        List<DataInconsistency> inconsistencies = checkConsistency(transactionId);
        
        // 修复不一致的数据
        for (DataInconsistency inconsistency : inconsistencies) {
            repairData(inconsistency);
        }
    }
}

// 方案2：补偿机制
@Component
public class CompensationService {
    
    @Async
    public void compensateInconsistentData(String transactionId) {
        // 异步补偿不一致的数据
        TransactionAggregate transaction = getTransaction(transactionId);
        if (transaction.getStatus() == TransactionStatus.FAILED) {
            executeCompensation(transaction);
        }
    }
}
```

## 📚 排查工具和命令

### 1. 日志分析命令

```bash
# 查看事务相关日志
grep "DistributedTransaction" application.log

# 查看错误日志
grep "ERROR" application.log | grep "tipray"

# 实时监控日志
tail -f application.log | grep "transaction"
```

### 2. 数据库查询脚本

```sql
-- 事务统计查询
SELECT 
    status,
    COUNT(*) as count,
    AVG(TIMESTAMPDIFF(SECOND, created_time, updated_time)) as avg_duration
FROM tipray_tx_transaction 
WHERE created_time >= DATE_SUB(NOW(), INTERVAL 1 HOUR)
GROUP BY status;

-- UndoLog清理查询
DELETE FROM undo_log 
WHERE log_created < DATE_SUB(NOW(), INTERVAL 7 DAY)
LIMIT 1000;
```

### 3. 监控脚本

```bash
#!/bin/bash
# 事务监控脚本

# 检查应用状态
curl -s http://localhost:8080/actuator/health | jq '.status'

# 检查事务统计
curl -s http://localhost:8080/tipray-transaction-ui/api/statistics | jq '.successRate'

# 检查数据库连接
mysql -h localhost -u root -p -e "SELECT COUNT(*) FROM undo_log;"
```

## 🚨 应急处理方案

### 1. 紧急停止事务

```java
@RestController
public class EmergencyController {
    
    @Autowired
    private DistributedTransactionManager transactionManager;
    
    @PostMapping("/emergency/stop/{transactionId}")
    public ResponseEntity<String> stopTransaction(@PathVariable String transactionId) {
        try {
            transactionManager.rollbackTransaction(transactionId);
            return ResponseEntity.ok("事务已紧急停止");
        } catch (Exception e) {
            return ResponseEntity.status(500).body("停止失败: " + e.getMessage());
        }
    }
}
```

### 2. 数据恢复脚本

```sql
-- 紧急数据恢复脚本
-- 根据UndoLog恢复数据
DELIMITER $$
CREATE PROCEDURE RecoverTransactionData(IN xid VARCHAR(100))
BEGIN
    DECLARE done INT DEFAULT FALSE;
    DECLARE rollback_sql TEXT;
    DECLARE cur CURSOR FOR 
        SELECT rollback_info FROM undo_log WHERE xid = xid;
    DECLARE CONTINUE HANDLER FOR NOT FOUND SET done = TRUE;
    
    OPEN cur;
    read_loop: LOOP
        FETCH cur INTO rollback_sql;
        IF done THEN
            LEAVE read_loop;
        END IF;
        
        -- 执行回滚SQL（需要解析rollback_info）
        -- SET @sql = ExtractRollbackSQL(rollback_sql);
        -- PREPARE stmt FROM @sql;
        -- EXECUTE stmt;
        -- DEALLOCATE PREPARE stmt;
    END LOOP;
    CLOSE cur;
END$$
DELIMITER ;
```

### 3. 监控告警

```yaml
# 告警配置
tipray:
  transaction:
    alert:
      enabled: true
      rules:
        - name: 事务失败率过高
          condition: failure_rate > 10
          action: SEND_EMAIL
          recipients: ["<EMAIL>"]
        
        - name: 事务执行时间过长
          condition: avg_duration > 30000
          action: SEND_DINGTALK
          webhook: "https://oapi.dingtalk.com/robot/send?access_token=xxx"
```

---

**提示**: 故障排查需要结合日志、监控、数据库等多个维度进行分析，建议建立完善的监控和告警机制。
