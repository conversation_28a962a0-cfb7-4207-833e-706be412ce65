# 架构设计

## 🏗️ 整体架构

Tipray分布式事务框架采用去中心化架构设计，每个DLP服务内嵌事务协调器（TC）和事务管理器（TM），消除了传统分布式事务方案中的中心化瓶颈。

```
┌─────────────────────────────────────────────────────────────┐
│                    DLP服务 (协调者)                          │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────────────┐   │
│  │     TM      │  │     TC      │  │   业务服务层        │   │
│  │ 事务管理器   │  │ 事务协调器   │  │                    │   │
│  └─────────────┘  └─────────────┘  └─────────────────────┘   │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │              Tipray分布式事务框架                        │ │
│  └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
                              │
                              │ HTTP调用
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                   云服务 (参与者)                            │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────────────┐   │
│  │     RM      │  │ UndoLog管理  │  │   业务服务层        │   │
│  │ 资源管理器   │  │             │  │                    │   │
│  └─────────────┘  └─────────────┘  └─────────────────────┘   │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │           Tipray事务客户端SDK                            │ │
│  └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

## 🎯 设计原则

### 1. 去中心化原则

- **内嵌TC+TM**: 每个DLP服务内嵌事务协调器，无需独立TC服务
- **自主决策**: 每个DLP服务独立管理事务生命周期
- **水平扩展**: 支持DLP服务的水平扩展，无单点故障

### 2. 单向通信原则

- **适配网络拓扑**: DLP控制台无法接收云服务回调的网络限制
- **主动轮询**: DLP主动查询云服务事务状态
- **异步处理**: 支持异步事务处理，提高吞吐量

### 3. 统一架构原则

- **模式统一**: AT和Saga模式共享核心组件
- **接口统一**: 统一的事务管理接口和编程模型
- **配置统一**: 统一的配置管理和参数体系

### 4. DDD分层原则

- **领域驱动**: 基于DDD的分层架构设计
- **职责分离**: 清晰的模块职责和边界
- **高内聚低耦合**: 模块间松耦合，模块内高内聚

## 📦 模块架构

### 核心模块结构

```
tipray-distributed-transaction/
├── tipray-transaction-core/           # 核心事务管理
│   ├── domain/                       # 领域模型
│   │   ├── aggregate/               # 聚合根
│   │   ├── entity/                  # 实体
│   │   ├── valueobject/             # 值对象
│   │   └── service/                 # 领域服务
│   ├── application/                 # 应用服务
│   │   ├── manager/                 # 事务管理器
│   │   ├── coordinator/             # 事务协调器
│   │   └── handler/                 # 事务处理器
│   ├── infrastructure/              # 基础设施
│   │   ├── persistence/             # 持久化
│   │   ├── config/                  # 配置管理
│   │   └── event/                   # 事件发布
│   └── interface/                   # 接口层
│       ├── annotation/              # 注解
│       └── api/                     # 编程式API
├── tipray-transaction-at/            # AT模式实现
├── tipray-transaction-saga/          # Saga模式实现
├── tipray-transaction-client/        # 客户端SDK
├── tipray-transaction-storage/       # 存储层
└── tipray-transaction-ui/            # 监控界面
```

### 分层职责

#### Domain层（领域层）

- **TransactionAggregate**: 事务聚合根，管理事务生命周期
- **TransactionStep**: 事务步骤实体，表示单个事务操作
- **TransactionStatus**: 事务状态值对象
- **TransactionDomainService**: 事务领域服务

#### Application层（应用层）

- **DistributedTransactionManager**: 统一事务管理器
- **TransactionCoordinator**: 事务协调器
- **AtTransactionHandler**: AT模式事务处理器
- **SagaTransactionHandler**: Saga模式事务处理器

#### Infrastructure层（基础设施层）

- **TransactionStorage**: 事务存储接口
- **TransactionEventPublisher**: 事务事件发布器
- **TiprayTransactionProperties**: 配置属性管理

#### Interface层（接口层）

- **@DistributedTransaction**: 声明式事务注解
- **TransactionTemplate**: 编程式事务模板
- **TransactionCallback**: 事务回调接口

## 🔄 事务流程

### AT模式事务流程

```mermaid
sequenceDiagram
    participant DLP as DLP服务
    participant TM as 事务管理器
    participant TC as 事务协调器
    participant Cloud as 云服务
    participant RM as 资源管理器

    DLP->>TM: 开启全局事务
    TM->>TC: 创建事务上下文
    DLP->>DLP: 执行本地业务
    DLP->>Cloud: HTTP调用云服务
    Cloud->>RM: 注册分支事务
    RM->>RM: 生成UndoLog
    Cloud->>DLP: 返回执行结果
    DLP->>TC: 决策提交/回滚
    TC->>Cloud: 通知分支事务
    RM->>RM: 提交/回滚处理
    Cloud->>TC: 返回处理结果
```

### Saga模式事务流程

```mermaid
sequenceDiagram
    participant DLP as DLP服务
    participant Saga as Saga协调器
    participant Step1 as 步骤1
    participant Step2 as 步骤2
    participant Comp as 补偿器

    DLP->>Saga: 开启Saga事务
    Saga->>Step1: 执行步骤1
    Step1->>Step1: 提交本地事务
    Saga->>Step2: 执行步骤2
    Step2->>Step2: 执行失败
    Saga->>Comp: 触发补偿
    Comp->>Step1: 补偿步骤1
    Step1->>Step1: 执行补偿逻辑
```

## 🎨 设计模式

### 1. 策略模式

```java
// 事务处理器策略
public interface TransactionHandler {
    void handle(DistributedTransactionDO transaction);
}

// AT模式处理器
@Component
public class AtTransactionHandler implements TransactionHandler {
    // AT模式具体实现
}

// Saga模式处理器
@Component
public class SagaTransactionHandler implements TransactionHandler {
    // Saga模式具体实现
}
```

### 2. 模板方法模式

```java
public abstract class AbstractTransactionHandler {
    
    public final void handle(DistributedTransactionDO transaction) {
        beforeHandle(transaction);
        doHandle(transaction);
        afterHandle(transaction);
    }
    
    protected abstract void doHandle(DistributedTransactionDO transaction);
    
    protected void beforeHandle(DistributedTransactionDO transaction) {
        // 通用前置处理
    }
    
    protected void afterHandle(DistributedTransactionDO transaction) {
        // 通用后置处理
    }
}
```

### 3. 观察者模式

```java
// 事务事件发布
@Component
public class TransactionEventPublisher {
    
    public void publishTransactionStarted(TransactionStartedEvent event) {
        // 发布事务开始事件
    }
    
    public void publishTransactionCompleted(TransactionCompletedEvent event) {
        // 发布事务完成事件
    }
}

// 事务事件监听
@EventListener
public void onTransactionStarted(TransactionStartedEvent event) {
    // 处理事务开始事件
}
```

### 4. 工厂模式

```java
@Component
public class TransactionHandlerFactory {
    
    private final Map<TransactionMode, TransactionHandler> handlerMap;
    
    public TransactionHandler getHandler(TransactionMode mode) {
        return handlerMap.get(mode);
    }
}
```

## 🔧 技术选型

### 核心技术栈

- **Java 8**: 基础运行环境
- **Spring Boot 2.7.x**: 应用框架
- **Spring Framework**: 依赖注入和AOP
- **Hutool**: 工具类库
- **Lombok**: 代码生成
- **Alibaba TTL**: 线程上下文传递

### 存储技术

- **MySQL**: 主要数据存储
- **Redis**: 缓存和分布式锁（可选）
- **H2**: 测试环境数据库

### 监控技术

- **Micrometer**: 指标收集
- **Spring Boot Actuator**: 健康检查
- **自定义Web UI**: 事务监控界面

## 🚀 性能设计

### 1. 异步处理

- **异步持久化**: 事务信息异步保存到数据库
- **异步清理**: 定时清理过期事务数据
- **异步补偿**: Saga模式支持异步补偿

### 2. 批量操作

- **批量提交**: 支持批量提交分支事务
- **批量回滚**: 支持批量回滚分支事务
- **批量清理**: 批量清理过期数据

### 3. 连接复用

- **HTTP连接池**: 复用HTTP连接减少开销
- **数据库连接池**: 高效的数据库连接管理
- **线程池**: 专用线程池处理事务任务

### 4. 缓存优化

- **本地缓存**: 缓存事务状态和配置信息
- **分布式缓存**: Redis缓存热点数据
- **查询优化**: 优化数据库查询性能

## 📊 扩展性设计

### 1. 插件化架构

- **事务处理器插件**: 支持自定义事务处理器
- **存储插件**: 支持多种存储实现
- **监控插件**: 支持自定义监控指标

### 2. 配置化设计

- **动态配置**: 支持运行时配置更新
- **环境隔离**: 支持多环境配置
- **参数调优**: 丰富的性能调优参数

### 3. 接口抽象

- **统一接口**: 核心功能通过接口抽象
- **SPI机制**: 支持服务提供者接口扩展
- **回调机制**: 支持自定义回调处理

## 📚 下一步

- [业务背景](业务背景.md) - 了解框架解决的业务问题
- [术语表](术语表.md) - 掌握核心术语和概念
- [快速开始](../02-用户文档/快速开始.md) - 开始使用框架
- [架构详解](../04-开发者指南/架构详解.md) - 深入了解技术实现

---

**提示**: 这个架构设计支持高并发、高可用的分布式事务处理，适合大规模微服务环境。
