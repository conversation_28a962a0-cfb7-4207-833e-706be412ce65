# 核心组件

本文档详细介绍Tipray分布式事务框架的核心组件架构、接口设计和扩展机制，帮助开发者深入理解框架内部实现。

## 🏗️ 组件架构图

```
┌─────────────────────────────────────────────────────────────┐
│                    Interface Layer                          │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────┐  │
│  │   @DistTx注解   │  │ TransactionAPI  │  │  WebUI接口  │  │
│  └─────────────────┘  └─────────────────┘  └─────────────┘  │
└─────────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────────┐
│                  Application Layer                          │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────┐  │
│  │ TransactionMgr  │  │  Coordinator    │  │ EventHandler│  │
│  └─────────────────┘  └─────────────────┘  └─────────────┘  │
└─────────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────────┐
│                   Domain Layer                              │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────┐  │
│  │ TransactionAgg  │  │   StepEntity    │  │ StatusValue │  │
│  └─────────────────┘  └─────────────────┘  └─────────────┘  │
└─────────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────────┐
│                Infrastructure Layer                         │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────┐  │
│  │   Storage       │  │   EventBus      │  │   Config    │  │
│  └─────────────────┘  └─────────────────┘  └─────────────┘  │
└─────────────────────────────────────────────────────────────┘
```

## 🎯 核心接口

### 1. 事务管理器接口

#### DistributedTransactionManager

```java
/**
 * 分布式事务管理器核心接口
 * 负责事务的生命周期管理
 */
public interface DistributedTransactionManager {
    
    /**
     * 开启全局事务
     * @param transactionInfo 事务信息
     * @return 全局事务ID
     */
    String beginTransaction(TransactionInfo transactionInfo);
    
    /**
     * 提交全局事务
     * @param transactionId 全局事务ID
     * @return 提交结果
     */
    TransactionResult commitTransaction(String transactionId);
    
    /**
     * 回滚全局事务
     * @param transactionId 全局事务ID
     * @return 回滚结果
     */
    TransactionResult rollbackTransaction(String transactionId);
    
    /**
     * 查询事务状态
     * @param transactionId 全局事务ID
     * @return 事务状态
     */
    TransactionStatus getTransactionStatus(String transactionId);
    
    /**
     * 注册事务监听器
     * @param listener 事务监听器
     */
    void registerTransactionListener(TransactionListener listener);
}
```

#### 默认实现

```java
@Component
public class DefaultDistributedTransactionManager implements DistributedTransactionManager {
    
    private final TransactionCoordinator coordinator;
    private final TransactionStorage storage;
    private final TransactionEventPublisher eventPublisher;
    private final List<TransactionListener> listeners;
    
    public DefaultDistributedTransactionManager(
            TransactionCoordinator coordinator,
            TransactionStorage storage,
            TransactionEventPublisher eventPublisher) {
        this.coordinator = coordinator;
        this.storage = storage;
        this.eventPublisher = eventPublisher;
        this.listeners = new CopyOnWriteArrayList<>();
    }
    
    @Override
    public String beginTransaction(TransactionInfo transactionInfo) {
        // 1. 生成全局事务ID
        String transactionId = generateTransactionId();
        
        // 2. 创建事务聚合根
        TransactionAggregate transaction = new TransactionAggregate(
            transactionId, 
            transactionInfo.getName(),
            transactionInfo.getMode(),
            transactionInfo.getTimeout()
        );
        
        // 3. 持久化事务信息
        storage.saveTransaction(transaction);
        
        // 4. 发布事务开始事件
        eventPublisher.publishTransactionStarted(
            new TransactionStartedEvent(transactionId, transactionInfo)
        );
        
        // 5. 通知监听器
        listeners.forEach(listener -> listener.onTransactionStarted(transactionId));
        
        return transactionId;
    }
    
    @Override
    public TransactionResult commitTransaction(String transactionId) {
        try {
            // 1. 获取事务信息
            TransactionAggregate transaction = storage.getTransaction(transactionId);
            if (transaction == null) {
                return TransactionResult.failure("事务不存在: " + transactionId);
            }
            
            // 2. 检查事务状态
            if (!transaction.canCommit()) {
                return TransactionResult.failure("事务状态不允许提交: " + transaction.getStatus());
            }
            
            // 3. 委托协调器执行提交
            TransactionResult result = coordinator.commit(transaction);
            
            // 4. 更新事务状态
            if (result.isSuccess()) {
                transaction.markAsCommitted();
                storage.updateTransaction(transaction);
                
                // 5. 发布事务提交成功事件
                eventPublisher.publishTransactionCommitted(
                    new TransactionCommittedEvent(transactionId, result)
                );
                
                // 6. 通知监听器
                listeners.forEach(listener -> listener.onTransactionCommitted(transactionId));
            } else {
                transaction.markAsFailed(result.getErrorMessage());
                storage.updateTransaction(transaction);
                
                // 发布事务提交失败事件
                eventPublisher.publishTransactionFailed(
                    new TransactionFailedEvent(transactionId, result.getErrorMessage())
                );
            }
            
            return result;
            
        } catch (Exception e) {
            log.error("提交事务失败: {}", transactionId, e);
            return TransactionResult.failure("提交事务异常: " + e.getMessage());
        }
    }
    
    // 其他方法实现...
}
```

### 2. 事务协调器接口

#### TransactionCoordinator

```java
/**
 * 事务协调器接口
 * 负责协调分支事务的执行
 */
public interface TransactionCoordinator {
    
    /**
     * 注册分支事务
     * @param branchTransaction 分支事务信息
     * @return 分支事务ID
     */
    String registerBranch(BranchTransaction branchTransaction);
    
    /**
     * 提交全局事务
     * @param transaction 全局事务
     * @return 提交结果
     */
    TransactionResult commit(TransactionAggregate transaction);
    
    /**
     * 回滚全局事务
     * @param transaction 全局事务
     * @return 回滚结果
     */
    TransactionResult rollback(TransactionAggregate transaction);
    
    /**
     * 查询分支事务状态
     * @param branchId 分支事务ID
     * @return 分支事务状态
     */
    BranchTransactionStatus getBranchStatus(String branchId);
}
```

#### AT模式协调器实现

```java
@Component
@ConditionalOnProperty(name = "tipray.transaction.at.enabled", havingValue = "true")
public class AtTransactionCoordinator implements TransactionCoordinator {
    
    private final AtServiceRegistry serviceRegistry;
    private final AtHttpClient httpClient;
    private final TransactionStorage storage;
    
    @Override
    public String registerBranch(BranchTransaction branchTransaction) {
        // 1. 生成分支事务ID
        String branchId = generateBranchId();
        branchTransaction.setBranchId(branchId);
        
        // 2. 持久化分支事务信息
        storage.saveBranchTransaction(branchTransaction);
        
        // 3. 注册到服务注册表
        serviceRegistry.registerBranch(branchTransaction);
        
        return branchId;
    }
    
    @Override
    public TransactionResult commit(TransactionAggregate transaction) {
        List<BranchTransaction> branches = storage.getBranchTransactions(transaction.getTransactionId());
        
        // 并行提交所有分支事务（使用TTL线程池确保上下文传播）
        ExecutorService executor = poolManager.getTransactionExecutor();
        List<CompletableFuture<BranchCommitResult>> futures = branches.stream()
            .map(branch -> CompletableFuture.supplyAsync(() -> commitBranch(branch), executor))
            .collect(Collectors.toList());
        
        // 等待所有分支事务提交完成
        CompletableFuture<Void> allFutures = CompletableFuture.allOf(
            futures.toArray(new CompletableFuture[0])
        );
        
        try {
            allFutures.get(transaction.getTimeout(), TimeUnit.SECONDS);
            
            // 检查提交结果
            List<BranchCommitResult> results = futures.stream()
                .map(CompletableFuture::join)
                .collect(Collectors.toList());
            
            boolean allSuccess = results.stream().allMatch(BranchCommitResult::isSuccess);
            
            if (allSuccess) {
                return TransactionResult.success("所有分支事务提交成功");
            } else {
                String errorMessage = results.stream()
                    .filter(result -> !result.isSuccess())
                    .map(BranchCommitResult::getErrorMessage)
                    .collect(Collectors.joining("; "));
                return TransactionResult.failure("部分分支事务提交失败: " + errorMessage);
            }
            
        } catch (TimeoutException e) {
            return TransactionResult.failure("分支事务提交超时");
        } catch (Exception e) {
            return TransactionResult.failure("分支事务提交异常: " + e.getMessage());
        }
    }
    
    private BranchCommitResult commitBranch(BranchTransaction branch) {
        try {
            // 调用云服务的提交接口
            CommitRequest request = new CommitRequest(
                branch.getTransactionId(),
                branch.getBranchId(),
                branch.getResourceId()
            );
            
            CommitResponse response = httpClient.commit(branch.getServiceUrl(), request);
            
            if (response.isSuccess()) {
                // 更新分支事务状态
                branch.markAsCommitted();
                storage.updateBranchTransaction(branch);
                return BranchCommitResult.success();
            } else {
                return BranchCommitResult.failure(response.getErrorMessage());
            }
            
        } catch (Exception e) {
            log.error("提交分支事务失败: {}", branch.getBranchId(), e);
            return BranchCommitResult.failure("提交分支事务异常: " + e.getMessage());
        }
    }
    
    // 其他方法实现...
}
```

### 3. 存储接口

#### TransactionStorage

```java
/**
 * 事务存储接口
 * 支持多种存储实现：内存、数据库、Redis
 */
public interface TransactionStorage {
    
    /**
     * 保存事务信息
     * @param transaction 事务聚合根
     */
    void saveTransaction(TransactionAggregate transaction);
    
    /**
     * 更新事务信息
     * @param transaction 事务聚合根
     */
    void updateTransaction(TransactionAggregate transaction);
    
    /**
     * 获取事务信息
     * @param transactionId 事务ID
     * @return 事务聚合根
     */
    TransactionAggregate getTransaction(String transactionId);
    
    /**
     * 删除事务信息
     * @param transactionId 事务ID
     */
    void deleteTransaction(String transactionId);
    
    /**
     * 查询事务列表
     * @param query 查询条件
     * @return 事务列表
     */
    List<TransactionAggregate> queryTransactions(TransactionQuery query);
    
    /**
     * 保存分支事务信息
     * @param branchTransaction 分支事务
     */
    void saveBranchTransaction(BranchTransaction branchTransaction);
    
    /**
     * 获取分支事务列表
     * @param transactionId 全局事务ID
     * @return 分支事务列表
     */
    List<BranchTransaction> getBranchTransactions(String transactionId);
}
```

#### 数据库存储实现

```java
@Component
@ConditionalOnProperty(name = "tipray.transaction.storage.type", havingValue = "DATABASE")
public class DatabaseTransactionStorage implements TransactionStorage {
    
    private final TransactionMapper transactionMapper;
    private final BranchTransactionMapper branchTransactionMapper;
    private final TransactionConverter converter;
    
    @Override
    public void saveTransaction(TransactionAggregate transaction) {
        TransactionDO transactionDO = converter.toDataObject(transaction);
        transactionMapper.insert(transactionDO);
    }
    
    @Override
    public void updateTransaction(TransactionAggregate transaction) {
        TransactionDO transactionDO = converter.toDataObject(transaction);
        transactionMapper.updateById(transactionDO);
    }
    
    @Override
    public TransactionAggregate getTransaction(String transactionId) {
        TransactionDO transactionDO = transactionMapper.selectByTransactionId(transactionId);
        if (transactionDO == null) {
            return null;
        }
        return converter.toDomainObject(transactionDO);
    }
    
    @Override
    public List<TransactionAggregate> queryTransactions(TransactionQuery query) {
        List<TransactionDO> transactionDOs = transactionMapper.selectByQuery(query);
        return transactionDOs.stream()
            .map(converter::toDomainObject)
            .collect(Collectors.toList());
    }
    
    // 其他方法实现...
}
```

## 🎨 领域模型

### 1. 事务聚合根

#### TransactionAggregate

```java
/**
 * 事务聚合根
 * 管理事务的完整生命周期
 */
public class TransactionAggregate {
    
    private final String transactionId;
    private final String name;
    private final TransactionMode mode;
    private final int timeoutSeconds;
    private TransactionStatus status;
    private LocalDateTime createdTime;
    private LocalDateTime updatedTime;
    private String errorMessage;
    private int retryCount;
    
    // 分支事务列表（AT模式）
    private List<BranchTransaction> branches;
    
    // 事务步骤列表（Saga模式）
    private List<TransactionStep> steps;
    
    public TransactionAggregate(String transactionId, String name, 
                               TransactionMode mode, int timeoutSeconds) {
        this.transactionId = transactionId;
        this.name = name;
        this.mode = mode;
        this.timeoutSeconds = timeoutSeconds;
        this.status = TransactionStatus.INIT;
        this.createdTime = LocalDateTime.now();
        this.updatedTime = LocalDateTime.now();
        this.retryCount = 0;
        this.branches = new ArrayList<>();
        this.steps = new ArrayList<>();
    }
    
    /**
     * 开始执行事务
     */
    public void start() {
        if (status != TransactionStatus.INIT) {
            throw new IllegalStateException("事务状态不允许开始执行: " + status);
        }
        this.status = TransactionStatus.EXECUTING;
        this.updatedTime = LocalDateTime.now();
    }
    
    /**
     * 标记事务为已提交
     */
    public void markAsCommitted() {
        if (status != TransactionStatus.EXECUTING) {
            throw new IllegalStateException("事务状态不允许提交: " + status);
        }
        this.status = TransactionStatus.SUCCESS;
        this.updatedTime = LocalDateTime.now();
    }
    
    /**
     * 标记事务为失败
     */
    public void markAsFailed(String errorMessage) {
        this.status = TransactionStatus.FAILED;
        this.errorMessage = errorMessage;
        this.updatedTime = LocalDateTime.now();
    }
    
    /**
     * 检查是否可以提交
     */
    public boolean canCommit() {
        return status == TransactionStatus.EXECUTING;
    }
    
    /**
     * 检查是否可以回滚
     */
    public boolean canRollback() {
        return status == TransactionStatus.EXECUTING || status == TransactionStatus.FAILED;
    }
    
    /**
     * 添加分支事务（AT模式）
     */
    public void addBranch(BranchTransaction branch) {
        if (mode != TransactionMode.AT) {
            throw new IllegalStateException("只有AT模式才能添加分支事务");
        }
        this.branches.add(branch);
    }
    
    /**
     * 添加事务步骤（Saga模式）
     */
    public void addStep(TransactionStep step) {
        if (mode != TransactionMode.SAGA) {
            throw new IllegalStateException("只有Saga模式才能添加事务步骤");
        }
        this.steps.add(step);
    }
    
    /**
     * 检查是否超时
     */
    public boolean isTimeout() {
        return Duration.between(createdTime, LocalDateTime.now()).getSeconds() > timeoutSeconds;
    }
    
    // getter方法...
}
```

### 2. 分支事务实体

#### BranchTransaction

```java
/**
 * 分支事务实体（AT模式）
 */
public class BranchTransaction {
    
    private String branchId;
    private String transactionId;
    private String resourceId;
    private String serviceUrl;
    private BranchTransactionStatus status;
    private LocalDateTime registeredTime;
    private LocalDateTime committedTime;
    private String errorMessage;
    
    public BranchTransaction(String transactionId, String resourceId, String serviceUrl) {
        this.transactionId = transactionId;
        this.resourceId = resourceId;
        this.serviceUrl = serviceUrl;
        this.status = BranchTransactionStatus.REGISTERED;
        this.registeredTime = LocalDateTime.now();
    }
    
    /**
     * 标记为已提交
     */
    public void markAsCommitted() {
        this.status = BranchTransactionStatus.COMMITTED;
        this.committedTime = LocalDateTime.now();
    }
    
    /**
     * 标记为已回滚
     */
    public void markAsRolledBack() {
        this.status = BranchTransactionStatus.ROLLBACKED;
        this.committedTime = LocalDateTime.now();
    }
    
    /**
     * 标记为失败
     */
    public void markAsFailed(String errorMessage) {
        this.status = BranchTransactionStatus.FAILED;
        this.errorMessage = errorMessage;
    }
    
    // getter和setter方法...
}
```

### 3. 事务步骤实体

#### TransactionStep

```java
/**
 * 事务步骤实体（Saga模式）
 */
public class TransactionStep {
    
    private String stepId;
    private String transactionId;
    private String stepName;
    private String compensationName;
    private int stepOrder;
    private StepStatus status;
    private LocalDateTime startTime;
    private LocalDateTime endTime;
    private String errorMessage;
    private Object stepResult;
    
    public TransactionStep(String transactionId, String stepName, 
                          String compensationName, int stepOrder) {
        this.stepId = generateStepId();
        this.transactionId = transactionId;
        this.stepName = stepName;
        this.compensationName = compensationName;
        this.stepOrder = stepOrder;
        this.status = StepStatus.PENDING;
    }
    
    /**
     * 开始执行步骤
     */
    public void start() {
        this.status = StepStatus.EXECUTING;
        this.startTime = LocalDateTime.now();
    }
    
    /**
     * 标记步骤成功
     */
    public void markAsSuccess(Object result) {
        this.status = StepStatus.SUCCESS;
        this.stepResult = result;
        this.endTime = LocalDateTime.now();
    }
    
    /**
     * 标记步骤失败
     */
    public void markAsFailed(String errorMessage) {
        this.status = StepStatus.FAILED;
        this.errorMessage = errorMessage;
        this.endTime = LocalDateTime.now();
    }
    
    /**
     * 开始补偿
     */
    public void startCompensation() {
        this.status = StepStatus.COMPENSATING;
    }
    
    /**
     * 标记补偿完成
     */
    public void markAsCompensated() {
        this.status = StepStatus.COMPENSATED;
    }
    
    // getter和setter方法...
}
```

## 🔧 扩展机制

### 1. 事务处理器扩展

#### TransactionHandler接口

```java
/**
 * 事务处理器接口
 * 支持自定义事务处理逻辑
 */
public interface TransactionHandler {
    
    /**
     * 支持的事务模式
     */
    TransactionMode supportedMode();
    
    /**
     * 处理事务
     * @param transaction 事务聚合根
     * @param context 执行上下文
     * @return 处理结果
     */
    TransactionResult handle(TransactionAggregate transaction, TransactionContext context);
    
    /**
     * 处理优先级
     * 数值越小优先级越高
     */
    default int getOrder() {
        return 0;
    }
}
```

#### 自定义处理器示例

```java
@Component
public class CustomTransactionHandler implements TransactionHandler {
    
    @Override
    public TransactionMode supportedMode() {
        return TransactionMode.CUSTOM;
    }
    
    @Override
    public TransactionResult handle(TransactionAggregate transaction, TransactionContext context) {
        // 自定义事务处理逻辑
        try {
            // 执行自定义逻辑
            executeCustomLogic(transaction, context);
            return TransactionResult.success("自定义事务处理成功");
        } catch (Exception e) {
            return TransactionResult.failure("自定义事务处理失败: " + e.getMessage());
        }
    }
    
    private void executeCustomLogic(TransactionAggregate transaction, TransactionContext context) {
        // 实现自定义事务逻辑
    }
    
    @Override
    public int getOrder() {
        return 100; // 较低优先级
    }
}
```

### 2. 存储扩展

#### 自定义存储实现

```java
@Component
@ConditionalOnProperty(name = "tipray.transaction.storage.type", havingValue = "CUSTOM")
public class CustomTransactionStorage implements TransactionStorage {
    
    // 实现自定义存储逻辑
    // 例如：MongoDB、Elasticsearch等
    
    @Override
    public void saveTransaction(TransactionAggregate transaction) {
        // 自定义保存逻辑
    }
    
    // 其他方法实现...
}
```

### 3. 事件监听器扩展

#### TransactionListener接口

```java
/**
 * 事务监听器接口
 * 支持监听事务生命周期事件
 */
public interface TransactionListener {
    
    /**
     * 事务开始时触发
     */
    void onTransactionStarted(String transactionId);
    
    /**
     * 事务提交时触发
     */
    void onTransactionCommitted(String transactionId);
    
    /**
     * 事务回滚时触发
     */
    void onTransactionRolledBack(String transactionId);
    
    /**
     * 事务失败时触发
     */
    void onTransactionFailed(String transactionId, String errorMessage);
}
```

#### 自定义监听器示例

```java
@Component
public class CustomTransactionListener implements TransactionListener {
    
    private final MetricsCollector metricsCollector;
    private final AlertService alertService;
    
    @Override
    public void onTransactionStarted(String transactionId) {
        metricsCollector.incrementTransactionStarted();
        log.info("事务开始: {}", transactionId);
    }
    
    @Override
    public void onTransactionCommitted(String transactionId) {
        metricsCollector.incrementTransactionCommitted();
        log.info("事务提交成功: {}", transactionId);
    }
    
    @Override
    public void onTransactionFailed(String transactionId, String errorMessage) {
        metricsCollector.incrementTransactionFailed();
        alertService.sendAlert("事务失败", "事务ID: " + transactionId + ", 错误: " + errorMessage);
        log.error("事务失败: {}, 错误: {}", transactionId, errorMessage);
    }
    
    // 其他方法实现...
}
```

## 📚 组件使用示例

### 1. 自定义事务管理器

```java
@Configuration
public class CustomTransactionConfig {
    
    @Bean
    @Primary
    public DistributedTransactionManager customTransactionManager(
            TransactionCoordinator coordinator,
            TransactionStorage storage,
            TransactionEventPublisher eventPublisher) {
        
        return new CustomDistributedTransactionManager(coordinator, storage, eventPublisher);
    }
}
```

### 2. 扩展存储实现

```java
@Configuration
@ConditionalOnProperty(name = "tipray.transaction.storage.type", havingValue = "REDIS")
public class RedisStorageConfig {
    
    @Bean
    public TransactionStorage redisTransactionStorage(RedisTemplate<String, Object> redisTemplate) {
        return new RedisTransactionStorage(redisTemplate);
    }
}
```

### 3. 注册自定义监听器

```java
@Configuration
public class TransactionListenerConfig {
    
    @Bean
    public TransactionListener metricsTransactionListener() {
        return new MetricsTransactionListener();
    }
    
    @Bean
    public TransactionListener auditTransactionListener() {
        return new AuditTransactionListener();
    }
}
```

---

**提示**: 核心组件采用了良好的抽象设计，支持灵活的扩展和定制，可以根据具体需求进行个性化实现。
