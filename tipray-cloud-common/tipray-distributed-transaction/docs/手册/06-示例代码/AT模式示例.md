# AT模式示例

本文档提供完整的AT模式使用示例，展示如何在实际项目中使用Tipray分布式事务框架的AT模式。

## 🎯 示例场景

### 业务场景：电商订单系统

- **DLP服务**: 订单管理服务（协调者）
- **云服务1**: 库存服务（参与者）
- **云服务2**: 支付服务（参与者）
- **云服务3**: 积分服务（参与者）

### 业务流程

1. 创建订单记录
2. 调用库存服务扣减库存
3. 调用支付服务处理支付
4. 调用积分服务增加积分
5. 更新订单状态为已完成

## 🏗️ 项目结构

```
ecommerce-order-system/
├── order-service/              # DLP服务（协调者）
│   ├── src/main/java/
│   │   ├── controller/
│   │   ├── service/
│   │   ├── entity/
│   │   ├── mapper/
│   │   └── client/            # 云服务客户端
│   └── pom.xml
├── inventory-service/          # 云服务（参与者）
├── payment-service/           # 云服务（参与者）
├── points-service/            # 云服务（参与者）
└── common/                    # 公共模块
```

## 📦 DLP服务实现（协调者）

### 1. 依赖配置

```xml
<!-- pom.xml -->
<dependencies>
    <!-- Tipray分布式事务 -->
    <dependency>
        <groupId>com.tipray</groupId>
        <artifactId>tipray-transaction-spring-boot-starter</artifactId>
        <version>1.0.0</version>
    </dependency>
    
    <!-- Spring Boot -->
    <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-web</artifactId>
    </dependency>
    
    <!-- 数据库 -->
    <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-jdbc</artifactId>
    </dependency>
    <dependency>
        <groupId>mysql</groupId>
        <artifactId>mysql-connector-java</artifactId>
    </dependency>
    
    <!-- MyBatis -->
    <dependency>
        <groupId>org.mybatis.spring.boot</groupId>
        <artifactId>mybatis-spring-boot-starter</artifactId>
        <version>2.3.1</version>
    </dependency>
</dependencies>
```

### 2. 配置文件

```yaml
# application.yml
server:
  port: 8080

spring:
  application:
    name: order-service
  
  datasource:
    url: ****************************************************************************************
    username: root
    password: password
    driver-class-name: com.mysql.cj.jdbc.Driver

# Tipray分布式事务配置
tipray:
  transaction:
    enabled: true
    application-name: ${spring.application.name}
    
    # AT模式配置
    at:
      enabled: true
      default-timeout: 30
      
      # 服务映射配置
      service:
        mappings:
          inventory-service:
            base-url: http://inventory-service:8081
            commit-path: /api/transaction/commit
            rollback-path: /api/transaction/rollback
            connect-timeout: 5000
            read-timeout: 30000
          
          payment-service:
            base-url: http://payment-service:8082
            commit-path: /api/transaction/commit
            rollback-path: /api/transaction/rollback
            connect-timeout: 5000
            read-timeout: 30000
          
          points-service:
            base-url: http://points-service:8083
            commit-path: /api/transaction/commit
            rollback-path: /api/transaction/rollback
            connect-timeout: 5000
            read-timeout: 30000
    
    # 存储配置
    storage:
      type: DATABASE
      database:
        auto-create-table: true
    
    # 监控界面
    ui:
      enabled: true
      path: /transaction-ui

# MyBatis配置
mybatis:
  mapper-locations: classpath:mapper/*.xml
  configuration:
    map-underscore-to-camel-case: true
```

### 3. 实体类

```java
// Order.java
package com.example.order.entity;

import java.math.BigDecimal;
import java.time.LocalDateTime;

public class Order {
    private Long id;
    private String orderNo;
    private Long userId;
    private Long productId;
    private Integer quantity;
    private BigDecimal amount;
    private String status; // CREATED, PROCESSING, COMPLETED, FAILED
    private LocalDateTime createdTime;
    private LocalDateTime updatedTime;
    
    // 构造方法
    public Order() {}
    
    public Order(String orderNo, Long userId, Long productId, Integer quantity, BigDecimal amount) {
        this.orderNo = orderNo;
        this.userId = userId;
        this.productId = productId;
        this.quantity = quantity;
        this.amount = amount;
        this.status = "CREATED";
        this.createdTime = LocalDateTime.now();
        this.updatedTime = LocalDateTime.now();
    }
    
    // getter和setter方法
    public Long getId() { return id; }
    public void setId(Long id) { this.id = id; }
    
    public String getOrderNo() { return orderNo; }
    public void setOrderNo(String orderNo) { this.orderNo = orderNo; }
    
    public Long getUserId() { return userId; }
    public void setUserId(Long userId) { this.userId = userId; }
    
    public Long getProductId() { return productId; }
    public void setProductId(Long productId) { this.productId = productId; }
    
    public Integer getQuantity() { return quantity; }
    public void setQuantity(Integer quantity) { this.quantity = quantity; }
    
    public BigDecimal getAmount() { return amount; }
    public void setAmount(BigDecimal amount) { this.amount = amount; }
    
    public String getStatus() { return status; }
    public void setStatus(String status) { this.status = status; }
    
    public LocalDateTime getCreatedTime() { return createdTime; }
    public void setCreatedTime(LocalDateTime createdTime) { this.createdTime = createdTime; }
    
    public LocalDateTime getUpdatedTime() { return updatedTime; }
    public void setUpdatedTime(LocalDateTime updatedTime) { this.updatedTime = updatedTime; }
}
```

### 4. Mapper接口

```java
// OrderMapper.java
package com.example.order.mapper;

import com.example.order.entity.Order;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface OrderMapper {
    
    int insert(Order order);
    
    int updateStatus(@Param("id") Long id, @Param("status") String status);
    
    Order selectById(@Param("id") Long id);
    
    Order selectByOrderNo(@Param("orderNo") String orderNo);
}
```

### 5. Mapper XML

```xml
<!-- mapper/OrderMapper.xml -->
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.example.order.mapper.OrderMapper">
    
    <insert id="insert" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO orders (order_no, user_id, product_id, quantity, amount, status, created_time, updated_time)
        VALUES (#{orderNo}, #{userId}, #{productId}, #{quantity}, #{amount}, #{status}, #{createdTime}, #{updatedTime})
    </insert>
    
    <update id="updateStatus">
        UPDATE orders 
        SET status = #{status}, updated_time = NOW()
        WHERE id = #{id}
    </update>
    
    <select id="selectById" resultType="Order">
        SELECT id, order_no, user_id, product_id, quantity, amount, status, created_time, updated_time
        FROM orders
        WHERE id = #{id}
    </select>
    
    <select id="selectByOrderNo" resultType="Order">
        SELECT id, order_no, user_id, product_id, quantity, amount, status, created_time, updated_time
        FROM orders
        WHERE order_no = #{orderNo}
    </select>
    
</mapper>
```

### 6. 云服务客户端

```java
// InventoryServiceClient.java
package com.example.order.client;

import com.tipray.transaction.at.annotation.AtService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

@Component
public class InventoryServiceClient {
    
    @Autowired
    private RestTemplate restTemplate;
    
    /**
     * 扣减库存
     */
    @AtService(value = "inventory-service", url = "http://inventory-service:8081/api")
    public void decreaseStock(Long productId, Integer quantity) {
        InventoryRequest request = new InventoryRequest(productId, quantity);
        
        ResponseEntity<InventoryResponse> response = restTemplate.postForEntity(
            "/inventory/decrease", 
            request, 
            InventoryResponse.class
        );
        
        if (!response.getBody().isSuccess()) {
            throw new RuntimeException("库存扣减失败: " + response.getBody().getMessage());
        }
    }
    
    // 请求和响应类
    public static class InventoryRequest {
        private Long productId;
        private Integer quantity;
        
        public InventoryRequest(Long productId, Integer quantity) {
            this.productId = productId;
            this.quantity = quantity;
        }
        
        // getter和setter
        public Long getProductId() { return productId; }
        public void setProductId(Long productId) { this.productId = productId; }
        
        public Integer getQuantity() { return quantity; }
        public void setQuantity(Integer quantity) { this.quantity = quantity; }
    }
    
    public static class InventoryResponse {
        private boolean success;
        private String message;
        
        // getter和setter
        public boolean isSuccess() { return success; }
        public void setSuccess(boolean success) { this.success = success; }
        
        public String getMessage() { return message; }
        public void setMessage(String message) { this.message = message; }
    }
}
```

```java
// PaymentServiceClient.java
package com.example.order.client;

import com.tipray.transaction.at.annotation.AtService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import java.math.BigDecimal;

@Component
public class PaymentServiceClient {
    
    @Autowired
    private RestTemplate restTemplate;
    
    /**
     * 处理支付
     */
    @AtService(value = "payment-service", url = "http://payment-service:8082/api")
    public void processPayment(Long userId, BigDecimal amount, String orderNo) {
        PaymentRequest request = new PaymentRequest(userId, amount, orderNo);
        
        ResponseEntity<PaymentResponse> response = restTemplate.postForEntity(
            "/payment/process", 
            request, 
            PaymentResponse.class
        );
        
        if (!response.getBody().isSuccess()) {
            throw new RuntimeException("支付处理失败: " + response.getBody().getMessage());
        }
    }
    
    // 请求和响应类
    public static class PaymentRequest {
        private Long userId;
        private BigDecimal amount;
        private String orderNo;
        
        public PaymentRequest(Long userId, BigDecimal amount, String orderNo) {
            this.userId = userId;
            this.amount = amount;
            this.orderNo = orderNo;
        }
        
        // getter和setter
        public Long getUserId() { return userId; }
        public void setUserId(Long userId) { this.userId = userId; }
        
        public BigDecimal getAmount() { return amount; }
        public void setAmount(BigDecimal amount) { this.amount = amount; }
        
        public String getOrderNo() { return orderNo; }
        public void setOrderNo(String orderNo) { this.orderNo = orderNo; }
    }
    
    public static class PaymentResponse {
        private boolean success;
        private String message;
        private String paymentId;
        
        // getter和setter
        public boolean isSuccess() { return success; }
        public void setSuccess(boolean success) { this.success = success; }
        
        public String getMessage() { return message; }
        public void setMessage(String message) { this.message = message; }
        
        public String getPaymentId() { return paymentId; }
        public void setPaymentId(String paymentId) { this.paymentId = paymentId; }
    }
}
```

### 7. 业务服务

```java
// OrderService.java
package com.example.order.service;

import com.example.order.client.InventoryServiceClient;
import com.example.order.client.PaymentServiceClient;
import com.example.order.client.PointsServiceClient;
import com.example.order.entity.Order;
import com.example.order.mapper.OrderMapper;
import com.tipray.transaction.core.application.annotation.DistributedTransaction;
import com.tipray.transaction.core.enums.TransactionMode;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.UUID;

@Service
public class OrderService {
    
    @Autowired
    private OrderMapper orderMapper;
    
    @Autowired
    private InventoryServiceClient inventoryServiceClient;
    
    @Autowired
    private PaymentServiceClient paymentServiceClient;
    
    @Autowired
    private PointsServiceClient pointsServiceClient;
    
    /**
     * AT模式创建订单
     * 强一致性保证：要么全部成功，要么全部回滚
     */
    @DistributedTransaction(
        mode = TransactionMode.AT,
        name = "创建订单",
        timeoutSeconds = 30,
        description = "电商订单创建流程"
    )
    public String createOrder(Long userId, Long productId, Integer quantity, BigDecimal amount) {
        // 生成订单号
        String orderNo = "ORDER_" + System.currentTimeMillis() + "_" + UUID.randomUUID().toString().substring(0, 8);
        
        try {
            // 步骤1：创建订单记录
            Order order = new Order(orderNo, userId, productId, quantity, amount);
            orderMapper.insert(order);
            
            // 步骤2：扣减库存（调用云服务）
            inventoryServiceClient.decreaseStock(productId, quantity);
            
            // 步骤3：处理支付（调用云服务）
            paymentServiceClient.processPayment(userId, amount, orderNo);
            
            // 步骤4：增加积分（调用云服务）
            int points = amount.intValue(); // 1元=1积分
            pointsServiceClient.addPoints(userId, points, orderNo);
            
            // 步骤5：更新订单状态为已完成
            orderMapper.updateStatus(order.getId(), "COMPLETED");
            
            return orderNo;
            
        } catch (Exception e) {
            // 任何步骤失败，框架会自动回滚所有操作
            throw new RuntimeException("订单创建失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 查询订单
     */
    public Order getOrder(String orderNo) {
        return orderMapper.selectByOrderNo(orderNo);
    }
}
```

### 8. 控制器

```java
// OrderController.java
package com.example.order.controller;

import com.example.order.entity.Order;
import com.example.order.service.OrderService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;

@RestController
@RequestMapping("/api/order")
public class OrderController {
    
    @Autowired
    private OrderService orderService;
    
    /**
     * 创建订单
     */
    @PostMapping("/create")
    public Map<String, Object> createOrder(
            @RequestParam Long userId,
            @RequestParam Long productId,
            @RequestParam Integer quantity,
            @RequestParam BigDecimal amount) {
        
        Map<String, Object> result = new HashMap<>();
        try {
            String orderNo = orderService.createOrder(userId, productId, quantity, amount);
            result.put("success", true);
            result.put("orderNo", orderNo);
            result.put("message", "订单创建成功");
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "订单创建失败: " + e.getMessage());
        }
        return result;
    }
    
    /**
     * 查询订单
     */
    @GetMapping("/{orderNo}")
    public Map<String, Object> getOrder(@PathVariable String orderNo) {
        Map<String, Object> result = new HashMap<>();
        try {
            Order order = orderService.getOrder(orderNo);
            if (order != null) {
                result.put("success", true);
                result.put("order", order);
            } else {
                result.put("success", false);
                result.put("message", "订单不存在");
            }
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "查询失败: " + e.getMessage());
        }
        return result;
    }
}
```

### 9. 配置类

```java
// AppConfig.java
package com.example.order.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.client.RestTemplate;

@Configuration
public class AppConfig {
    
    @Bean
    public RestTemplate restTemplate() {
        return new RestTemplate();
    }
}
```

### 10. 启动类

```java
// OrderServiceApplication.java
package com.example.order;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;

@SpringBootApplication
public class OrderServiceApplication {
    public static void main(String[] args) {
        SpringApplication.run(OrderServiceApplication.class, args);
    }
}
```

## 📊 数据库脚本

### 订单服务数据库

```sql
-- 创建订单数据库
CREATE DATABASE order_db DEFAULT CHARSET=utf8mb4;

USE order_db;

-- 订单表
CREATE TABLE orders (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    order_no VARCHAR(64) NOT NULL UNIQUE,
    user_id BIGINT NOT NULL,
    product_id BIGINT NOT NULL,
    quantity INT NOT NULL,
    amount DECIMAL(10,2) NOT NULL,
    status VARCHAR(20) NOT NULL DEFAULT 'CREATED',
    created_time DATETIME NOT NULL,
    updated_time DATETIME NOT NULL,
    INDEX idx_user_id (user_id),
    INDEX idx_order_no (order_no),
    INDEX idx_status (status)
);

-- UndoLog表（AT模式必需）
CREATE TABLE undo_log (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    branch_id BIGINT NOT NULL,
    xid VARCHAR(100) NOT NULL,
    context VARCHAR(128) NOT NULL,
    rollback_info LONGBLOB NOT NULL,
    log_status INT NOT NULL,
    log_created DATETIME NOT NULL,
    log_modified DATETIME NOT NULL,
    UNIQUE KEY ux_undo_log (xid, branch_id)
);

-- 分布式事务表（框架自动创建，这里仅供参考）
CREATE TABLE tipray_tx_transaction (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    transaction_id VARCHAR(100) NOT NULL UNIQUE,
    group_id VARCHAR(100) NOT NULL,
    status VARCHAR(20) NOT NULL,
    mode VARCHAR(10) NOT NULL,
    timeout_seconds INT NOT NULL,
    retry_count INT NOT NULL DEFAULT 0,
    created_time DATETIME NOT NULL,
    updated_time DATETIME NOT NULL,
    INDEX idx_group_id (group_id),
    INDEX idx_status (status)
);
```

## 🧪 测试验证

### 1. 成功场景测试

```bash
# 创建订单（成功）
curl -X POST "http://localhost:8080/api/order/create?userId=1001&productId=2001&quantity=2&amount=199.99"

# 预期返回：
# {
#   "success": true,
#   "orderNo": "ORDER_1640995200000_a1b2c3d4",
#   "message": "订单创建成功"
# }

# 查询订单
curl "http://localhost:8080/api/order/ORDER_1640995200000_a1b2c3d4"

# 预期返回：
# {
#   "success": true,
#   "order": {
#     "id": 1,
#     "orderNo": "ORDER_1640995200000_a1b2c3d4",
#     "userId": 1001,
#     "productId": 2001,
#     "quantity": 2,
#     "amount": 199.99,
#     "status": "COMPLETED",
#     "createdTime": "2023-12-31T10:00:00",
#     "updatedTime": "2023-12-31T10:00:05"
#   }
# }
```

### 2. 回滚场景测试

```bash
# 创建订单（库存不足，触发回滚）
curl -X POST "http://localhost:8080/api/order/create?userId=1001&productId=2001&quantity=999&amount=199.99"

# 预期返回：
# {
#   "success": false,
#   "message": "订单创建失败: 库存扣减失败: 库存不足"
# }

# 验证数据库中没有创建订单记录
# 验证所有云服务的数据都已回滚
```

### 3. 监控界面查看

访问: http://localhost:8080/transaction-ui

可以看到：

- 事务执行历史
- 成功/失败统计
- 事务详细信息
- 各个步骤的执行状态

## 📚 关键要点

### 1. AT模式特点

- ✅ **自动回滚**: 无需编写回滚逻辑
- ✅ **强一致性**: 保证ACID特性
- ✅ **透明化**: 对业务代码侵入性小
- ⚠️ **性能开销**: UndoLog会增加存储和性能开销

### 2. 注意事项

- 确保所有参与的云服务都集成了tipray-transaction-client
- 合理设置超时时间，避免长时间锁定资源
- 定期清理UndoLog数据
- 监控事务执行情况和性能指标

### 3. 最佳实践

- 保持事务边界尽可能小
- 避免在事务中执行耗时操作
- 合理处理业务异常
- 使用监控界面跟踪事务状态

---

**提示**: 这个示例展示了AT模式在实际项目中的完整使用方式，可以作为您项目的参考模板。
