# 注解使用

本文档详细介绍Tipray分布式事务框架提供的注解使用方法，帮助您快速掌握声明式事务编程。

## 🎯 核心注解

### @DistributedTransaction

#### 基本用法

```java
@DistributedTransaction
public void simpleTransaction() {
    // 使用默认配置的分布式事务
    businessLogic();
}
```

#### 完整配置

```java
@DistributedTransaction(
    mode = TransactionMode.AT,           // 事务模式
    name = "创建订单",                    // 事务名称
    timeoutSeconds = 30,                 // 超时时间（秒）
    retryCount = 3,                      // 重试次数
    retryInterval = 1000,                // 重试间隔（毫秒）
    description = "电商订单创建流程",      // 事务描述
    rollbackFor = {BusinessException.class}, // 回滚异常类型
    noRollbackFor = {ValidationException.class} // 不回滚异常类型
)
public void createOrder(OrderRequest request) {
    // 业务逻辑
}
```

#### 属性说明

| 属性               | 类型              | 默认值  | 说明           |
|------------------|-----------------|------|--------------|
| `mode`           | TransactionMode | AT   | 事务模式：AT/Saga |
| `name`           | String          | ""   | 事务名称，用于监控和日志 |
| `timeoutSeconds` | int             | 30   | 事务超时时间（秒）    |
| `retryCount`     | int             | 0    | 失败重试次数       |
| `retryInterval`  | long            | 1000 | 重试间隔（毫秒）     |
| `description`    | String          | ""   | 事务描述信息       |
| `rollbackFor`    | Class<?>[]      | {}   | 触发回滚的异常类型    |
| `noRollbackFor`  | Class<?>[]      | {}   | 不触发回滚的异常类型   |

### @AtService

#### 基本用法

```java
@Component
public class CloudServiceClient {
    
    @AtService("inventory-service")
    public void decreaseStock(Long productId, Integer quantity) {
        // HTTP调用云服务逻辑
        InventoryRequest request = new InventoryRequest(productId, quantity);
        ResponseEntity<InventoryResponse> response = restTemplate.postForEntity(
            "/api/inventory/decrease", 
            request, 
            InventoryResponse.class
        );
        
        if (!response.getBody().isSuccess()) {
            throw new BusinessException("库存扣减失败");
        }
    }
}
```

#### 完整配置

```java
@AtService(
    value = "inventory-service",              // 服务名称
    url = "http://inventory-service:8081",    // 服务URL
    commitPath = "/api/transaction/commit",   // 提交路径
    rollbackPath = "/api/transaction/rollback", // 回滚路径
    connectTimeout = 5000,                    // 连接超时（毫秒）
    readTimeout = 30000,                      // 读取超时（毫秒）
    retryCount = 3,                          // 重试次数
    retryInterval = 1000                     // 重试间隔（毫秒）
)
public void callInventoryService(InventoryRequest request) {
    // 云服务调用逻辑
}
```

#### 属性说明

| 属性               | 类型     | 默认值                         | 说明         |
|------------------|--------|-----------------------------|------------|
| `value`          | String | ""                          | 服务名称（必填）   |
| `url`            | String | ""                          | 服务基础URL    |
| `commitPath`     | String | "/api/transaction/commit"   | 提交接口路径     |
| `rollbackPath`   | String | "/api/transaction/rollback" | 回滚接口路径     |
| `connectTimeout` | int    | 5000                        | 连接超时时间（毫秒） |
| `readTimeout`    | int    | 30000                       | 读取超时时间（毫秒） |
| `retryCount`     | int    | 0                           | 重试次数       |
| `retryInterval`  | long   | 1000                        | 重试间隔（毫秒）   |

### @TransactionStep（Saga模式）

#### 基本用法

```java
@DistributedTransaction(mode = TransactionMode.SAGA)
public void processOrder(OrderRequest request) {
    createOrder(request);
    reserveInventory(request);
    processPayment(request);
}

@TransactionStep(compensation = "cancelOrder")
public void createOrder(OrderRequest request) {
    // 创建订单逻辑
    Order order = new Order(request);
    orderService.save(order);
}

public void cancelOrder(OrderRequest request) {
    // 补偿逻辑：取消订单
    orderService.cancel(request.getOrderId());
}
```

#### 完整配置

```java
@TransactionStep(
    name = "创建订单",                        // 步骤名称
    compensation = "cancelOrder",            // 补偿方法名
    timeout = 30,                           // 步骤超时时间（秒）
    retryCount = 3,                         // 重试次数
    retryInterval = 1000,                   // 重试间隔（毫秒）
    async = false,                          // 是否异步执行
    order = 1,                              // 执行顺序
    description = "在数据库中创建订单记录"     // 步骤描述
)
public void createOrder(OrderRequest request) {
    // 业务逻辑
}
```

#### 属性说明

| 属性              | 类型      | 默认值   | 说明        |
|-----------------|---------|-------|-----------|
| `name`          | String  | ""    | 步骤名称      |
| `compensation`  | String  | ""    | 补偿方法名（必填） |
| `timeout`       | int     | 30    | 步骤超时时间（秒） |
| `retryCount`    | int     | 0     | 重试次数      |
| `retryInterval` | long    | 1000  | 重试间隔（毫秒）  |
| `async`         | boolean | false | 是否异步执行    |
| `order`         | int     | 0     | 执行顺序      |
| `description`   | String  | ""    | 步骤描述      |

## 💻 使用示例

### 1. AT模式完整示例

```java
@Service
public class OrderService {
    
    @Autowired
    private OrderMapper orderMapper;
    
    @Autowired
    private InventoryServiceClient inventoryServiceClient;
    
    @Autowired
    private PaymentServiceClient paymentServiceClient;
    
    /**
     * AT模式订单创建
     */
    @DistributedTransaction(
        mode = TransactionMode.AT,
        name = "创建订单",
        timeoutSeconds = 60,
        description = "电商订单创建流程，包含库存扣减和支付处理",
        rollbackFor = {BusinessException.class, SystemException.class}
    )
    public String createOrder(CreateOrderRequest request) {
        // 1. 参数验证
        validateRequest(request);
        
        // 2. 创建订单记录
        Order order = new Order();
        order.setUserId(request.getUserId());
        order.setProductId(request.getProductId());
        order.setQuantity(request.getQuantity());
        order.setAmount(request.getAmount());
        order.setStatus("CREATED");
        orderMapper.insert(order);
        
        // 3. 调用库存服务扣减库存
        inventoryServiceClient.decreaseStock(request.getProductId(), request.getQuantity());
        
        // 4. 调用支付服务处理支付
        paymentServiceClient.processPayment(request.getUserId(), request.getAmount(), order.getOrderNo());
        
        // 5. 更新订单状态
        order.setStatus("COMPLETED");
        orderMapper.updateStatus(order.getId(), "COMPLETED");
        
        return order.getOrderNo();
    }
    
    private void validateRequest(CreateOrderRequest request) {
        if (request.getQuantity() <= 0) {
            throw new ValidationException("商品数量必须大于0");
        }
        if (request.getAmount().compareTo(BigDecimal.ZERO) <= 0) {
            throw new ValidationException("订单金额必须大于0");
        }
    }
}

@Component
public class InventoryServiceClient {
    
    @Autowired
    private RestTemplate restTemplate;
    
    @AtService(
        value = "inventory-service",
        url = "http://inventory-service:8081",
        connectTimeout = 3000,
        readTimeout = 10000,
        retryCount = 2
    )
    public void decreaseStock(Long productId, Integer quantity) {
        InventoryDecreaseRequest request = new InventoryDecreaseRequest(productId, quantity);
        
        ResponseEntity<ApiResponse> response = restTemplate.postForEntity(
            "/api/inventory/decrease",
            request,
            ApiResponse.class
        );
        
        if (!response.getBody().isSuccess()) {
            throw new BusinessException("库存扣减失败: " + response.getBody().getMessage());
        }
    }
}
```

### 2. Saga模式完整示例

```java
@Service
public class OrderProcessService {
    
    @Autowired
    private OrderService orderService;
    
    @Autowired
    private InventoryService inventoryService;
    
    @Autowired
    private PaymentService paymentService;
    
    @Autowired
    private NotificationService notificationService;
    
    /**
     * Saga模式订单处理流程
     */
    @DistributedTransaction(
        mode = TransactionMode.SAGA,
        name = "订单处理流程",
        timeoutSeconds = 120,
        description = "包含订单创建、库存预留、支付处理、通知发送的完整流程"
    )
    public void processOrder(OrderRequest request) {
        // 步骤按顺序执行，每个步骤都有对应的补偿方法
        createOrder(request);
        reserveInventory(request);
        processPayment(request);
        sendNotification(request);
    }
    
    @TransactionStep(
        name = "创建订单",
        compensation = "cancelOrder",
        timeout = 10,
        order = 1,
        description = "在数据库中创建订单记录"
    )
    public void createOrder(OrderRequest request) {
        Order order = new Order();
        order.setUserId(request.getUserId());
        order.setProductId(request.getProductId());
        order.setQuantity(request.getQuantity());
        order.setAmount(request.getAmount());
        order.setStatus("CREATED");
        orderService.save(order);
        
        // 将订单ID保存到请求中，供后续步骤使用
        request.setOrderId(order.getId());
    }
    
    public void cancelOrder(OrderRequest request) {
        if (request.getOrderId() != null) {
            orderService.cancel(request.getOrderId());
            log.info("订单已取消: {}", request.getOrderId());
        }
    }
    
    @TransactionStep(
        name = "预留库存",
        compensation = "releaseInventory",
        timeout = 15,
        order = 2,
        retryCount = 2,
        description = "预留商品库存"
    )
    public void reserveInventory(OrderRequest request) {
        InventoryReservation reservation = inventoryService.reserve(
            request.getProductId(), 
            request.getQuantity()
        );
        request.setReservationId(reservation.getId());
    }
    
    public void releaseInventory(OrderRequest request) {
        if (request.getReservationId() != null) {
            inventoryService.release(request.getReservationId());
            log.info("库存预留已释放: {}", request.getReservationId());
        }
    }
    
    @TransactionStep(
        name = "处理支付",
        compensation = "refundPayment",
        timeout = 30,
        order = 3,
        retryCount = 1,
        description = "处理用户支付"
    )
    public void processPayment(OrderRequest request) {
        Payment payment = paymentService.pay(
            request.getUserId(),
            request.getAmount(),
            request.getOrderId()
        );
        request.setPaymentId(payment.getId());
    }
    
    public void refundPayment(OrderRequest request) {
        if (request.getPaymentId() != null) {
            paymentService.refund(request.getPaymentId());
            log.info("支付已退款: {}", request.getPaymentId());
        }
    }
    
    @TransactionStep(
        name = "发送通知",
        compensation = "sendCancelNotification",
        timeout = 5,
        order = 4,
        async = true,
        description = "发送订单确认通知"
    )
    public void sendNotification(OrderRequest request) {
        notificationService.sendOrderConfirmation(
            request.getUserId(),
            request.getOrderId()
        );
    }
    
    public void sendCancelNotification(OrderRequest request) {
        notificationService.sendOrderCancellation(
            request.getUserId(),
            request.getOrderId()
        );
    }
}
```

### 3. 异常处理示例

```java
@Service
public class OrderService {
    
    /**
     * 自定义异常回滚规则
     */
    @DistributedTransaction(
        mode = TransactionMode.AT,
        rollbackFor = {
            BusinessException.class,      // 业务异常回滚
            SystemException.class,        // 系统异常回滚
            RuntimeException.class        // 运行时异常回滚
        },
        noRollbackFor = {
            ValidationException.class,    // 验证异常不回滚
            WarningException.class        // 警告异常不回滚
        }
    )
    public void createOrderWithCustomRollback(OrderRequest request) {
        try {
            // 业务逻辑
            processOrder(request);
            
        } catch (ValidationException e) {
            // 验证异常，记录日志但不回滚事务
            log.warn("订单验证失败，但不影响事务: {}", e.getMessage());
            throw e;
            
        } catch (BusinessException e) {
            // 业务异常，回滚事务
            log.error("业务处理失败，事务回滚: {}", e.getMessage());
            throw e;
            
        } catch (Exception e) {
            // 其他异常，包装后抛出
            log.error("系统异常，事务回滚: {}", e.getMessage(), e);
            throw new SystemException("系统处理异常", e);
        }
    }
}
```

### 4. 条件执行示例

```java
@Service
public class ConditionalTransactionService {
    
    @DistributedTransaction(mode = TransactionMode.SAGA)
    public void conditionalProcess(ProcessRequest request) {
        // 必须执行的步骤
        validateRequest(request);
        
        // 条件执行
        if (request.needsApproval()) {
            requestApproval(request);
        }
        
        // 根据用户类型选择不同处理方式
        if (request.isVipUser()) {
            processVipOrder(request);
        } else {
            processNormalOrder(request);
        }
        
        // 最终步骤
        finalizeProcess(request);
    }
    
    @TransactionStep(compensation = "skipValidation")
    public void validateRequest(ProcessRequest request) {
        // 验证逻辑
    }
    
    @TransactionStep(compensation = "cancelApproval")
    public void requestApproval(ProcessRequest request) {
        // 审批逻辑
    }
    
    @TransactionStep(compensation = "cancelVipOrder")
    public void processVipOrder(ProcessRequest request) {
        // VIP订单处理
    }
    
    @TransactionStep(compensation = "cancelNormalOrder")
    public void processNormalOrder(ProcessRequest request) {
        // 普通订单处理
    }
    
    // 补偿方法...
}
```

## 🚨 注意事项

### 1. 方法调用限制

```java
@Service
public class OrderService {
    
    // ❌ 错误：private方法不会被代理
    @DistributedTransaction
    private void createOrder() {
        // 不会生效
    }
    
    // ❌ 错误：final方法不能被代理
    @DistributedTransaction
    public final void createOrder() {
        // 不会生效
    }
    
    // ❌ 错误：static方法不能被代理
    @DistributedTransaction
    public static void createOrder() {
        // 不会生效
    }
    
    // ✅ 正确：public非final方法
    @DistributedTransaction
    public void createOrder() {
        // 正常工作
    }
}
```

### 2. 内部调用问题

```java
@Service
public class OrderService {
    
    public void publicMethod() {
        // ❌ 错误：内部直接调用不会触发事务
        this.transactionalMethod();
    }
    
    @DistributedTransaction
    public void transactionalMethod() {
        // 事务不会生效
    }
}

// 解决方案1：注入自己
@Service
public class OrderService {
    
    @Autowired
    private OrderService self;
    
    public void publicMethod() {
        // ✅ 正确：通过代理调用
        self.transactionalMethod();
    }
    
    @DistributedTransaction
    public void transactionalMethod() {
        // 事务正常工作
    }
}

// 解决方案2：使用AopContext
@Service
@EnableAspectJAutoProxy(exposeProxy = true)
public class OrderService {
    
    public void publicMethod() {
        // ✅ 正确：获取代理对象调用
        OrderService proxy = (OrderService) AopContext.currentProxy();
        proxy.transactionalMethod();
    }
    
    @DistributedTransaction
    public void transactionalMethod() {
        // 事务正常工作
    }
}
```

### 3. 异常处理注意事项

```java
@Service
public class OrderService {
    
    @DistributedTransaction
    public void createOrder() {
        try {
            businessLogic();
        } catch (Exception e) {
            // ❌ 错误：捕获异常但不重新抛出，事务不会回滚
            log.error("业务异常", e);
            return;
        }
    }
    
    @DistributedTransaction
    public void createOrderCorrect() {
        try {
            businessLogic();
        } catch (BusinessException e) {
            // ✅ 正确：重新抛出异常，触发事务回滚
            log.error("业务异常，事务回滚", e);
            throw e;
        } catch (Exception e) {
            // ✅ 正确：包装后抛出，触发事务回滚
            throw new SystemException("系统异常", e);
        }
    }
}
```

## 📚 最佳实践

### 1. 注解配置建议

- 明确指定事务模式（AT/Saga）
- 设置合理的超时时间
- 提供有意义的事务名称和描述
- 根据业务需求配置重试策略

### 2. 异常处理建议

- 明确定义业务异常和系统异常
- 合理配置rollbackFor和noRollbackFor
- 避免捕获异常后不重新抛出

### 3. 性能优化建议

- 保持事务边界尽可能小
- 避免在事务中执行耗时操作
- 合理使用异步执行（Saga模式）

---

**提示**: 注解是框架的核心功能，正确使用注解是保证分布式事务正常工作的关键。
