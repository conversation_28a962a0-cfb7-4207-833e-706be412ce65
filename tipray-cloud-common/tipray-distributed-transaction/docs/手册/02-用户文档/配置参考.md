# 配置参考

本文档详细介绍Tipray分布式事务框架的所有配置项，帮助您根据实际需求进行个性化配置。

## 📋 配置概览

### 配置层次结构

```yaml
tipray:
  transaction:
    # 基础配置
    enabled: true
    application-name: my-app
    
    # AT模式配置
    at:
      enabled: true
      # AT模式详细配置...
    
    # Saga模式配置
    saga:
      enabled: true
      # Saga模式详细配置...
    
    # 存储配置
    storage:
      type: DATABASE
      # 存储详细配置...
    
    # 监控配置
    ui:
      enabled: true
      # 监控界面配置...
    
    # 性能配置
    thread-pool:
      # 线程池配置...
    
    # 其他配置...
```

## 🔧 基础配置

### 核心配置项

```yaml
tipray:
  transaction:
    # 是否启用分布式事务框架
    enabled: true
    
    # 应用名称，用于标识事务来源
    application-name: ${spring.application.name:tipray-app}
    
    # 资源ID，用于标识资源管理器
    resource-id: ${tipray.transaction.application-name}
```

### 配置说明

| 配置项                | 类型      | 默认值               | 说明        |
|--------------------|---------|-------------------|-----------|
| `enabled`          | boolean | true              | 是否启用分布式事务 |
| `application-name` | String  | tipray-app        | 应用名称      |
| `resource-id`      | String  | 同application-name | 资源标识符     |

## 🎯 AT模式配置

### 基础配置

```yaml
tipray:
  transaction:
    at:
      # 是否启用AT模式
      enabled: true
      
      # 默认连接超时时间（毫秒）
      default-connect-timeout: 5000
      
      # 默认读取超时时间（毫秒）
      default-read-timeout: 30000
      
      # 默认执行超时时间（秒）
      default-timeout: 30
```

### 数据源代理配置

```yaml
tipray:
  transaction:
    at:
      data-source-proxy:
        # 是否启用数据源代理
        enabled: true
        
        # 是否启用全局锁
        global-lock-enabled: true
        
        # 全局锁超时时间（毫秒）
        global-lock-timeout: 30000
        
        # 代理类型：AT
        proxy-type: AT
```

### UndoLog配置

```yaml
tipray:
  transaction:
    at:
      undo-log:
        # UndoLog表名
        table-name: undo_log
        
        # 是否启用压缩
        compression-enabled: true
        
        # 压缩阈值（字节）
        compression-threshold: 4096
        
        # 最大数据大小（字节）
        max-data-size: 4194304  # 4MB
        
        # 序列化器类型：jackson/kryo
        serializer: jackson
        
        # 是否自动创建表
        auto-create-table: false
        
        # 清理配置
        cleanup:
          enabled: true
          interval-hours: 24
          retention-days: 7
          batch-size: 1000
```

### 服务映射配置

```yaml
tipray:
  transaction:
    at:
      service:
        mappings:
          # 服务名称
          user-service:
            base-url: http://user-service
            protocol: http
            port: 8080
            commit-path: /api/transaction/commit
            rollback-path: /api/transaction/rollback
            connect-timeout: 5000
            read-timeout: 30000
          
          order-service:
            base-url: http://order-service
            protocol: http
            port: 8081
            commit-path: /api/transaction/commit
            rollback-path: /api/transaction/rollback
            connect-timeout: 5000
            read-timeout: 30000
```

## 🔄 Saga模式配置

### 基础配置

```yaml
tipray:
  transaction:
    saga:
      # 是否启用Saga模式
      enabled: true
      
      # 默认补偿超时时间（秒）
      default-compensation-timeout: 30
      
      # 默认执行超时时间（秒）
      default-timeout: 60
```

### 补偿配置

```yaml
tipray:
  transaction:
    saga:
      compensation:
        # 是否启用异步补偿
        async-enabled: true
        
        # 补偿重试次数
        retry-count: 3
        
        # 补偿重试间隔（毫秒）
        retry-interval: 1000
        
        # 补偿超时时间（秒）
        timeout: 30
        
        # 是否启用补偿日志
        log-enabled: true
```

### 编排器配置

```yaml
tipray:
  transaction:
    saga:
      orchestrator:
        # 编排器类型：SIMPLE/COMPLEX
        type: SIMPLE
        
        # 是否启用并行执行
        parallel-enabled: false
        
        # 最大并行度
        max-parallelism: 5
        
        # 步骤超时时间（秒）
        step-timeout: 30
```

## 💾 存储配置

### 数据库存储

```yaml
tipray:
  transaction:
    storage:
      # 存储类型：MEMORY/DATABASE/REDIS
      type: DATABASE
      
      database:
        # 表前缀
        table-prefix: tipray_tx_
        
        # 是否自动创建表
        auto-create-table: true
        
        # 数据源名称（多数据源时使用）
        datasource-name: primary
        
        # 批量操作大小
        batch-size: 100
        
        # 查询超时时间（秒）
        query-timeout: 30
```

### Redis存储

```yaml
tipray:
  transaction:
    storage:
      type: REDIS
      
      redis:
        # Redis键前缀
        key-prefix: "tipray:tx:"
        
        # 数据过期时间（秒）
        expire-time: 3600
        
        # 序列化器：JSON/KRYO
        serializer: JSON
        
        # 连接池配置
        pool:
          max-active: 8
          max-idle: 8
          min-idle: 0
          max-wait: -1
```

### 内存存储

```yaml
tipray:
  transaction:
    storage:
      type: MEMORY
      
      memory:
        # 最大存储条数
        max-size: 10000
        
        # 数据过期时间（秒）
        expire-time: 3600
        
        # 清理间隔（秒）
        cleanup-interval: 300
```

## 🧵 线程池配置

### 核心线程池

```yaml
tipray:
  transaction:
    thread-pool:
      # 核心线程数
      core-pool-size: 10
      
      # 最大线程数
      maximum-pool-size: 50
      
      # 线程空闲时间（秒）
      keep-alive-time: 60
      
      # 队列容量
      queue-capacity: 200
      
      # 线程名前缀
      thread-name-prefix: "tipray-tx-"
      
      # 拒绝策略：ABORT/CALLER_RUNS/DISCARD/DISCARD_OLDEST
      reject-policy: CALLER_RUNS
```

### 异步处理线程池

```yaml
tipray:
  transaction:
    async:
      thread-pool:
        core-pool-size: 5
        maximum-pool-size: 20
        keep-alive-time: 60
        queue-capacity: 100
        thread-name-prefix: "tipray-async-"
```

## ⏱️ 超时配置

### 全局超时配置

```yaml
tipray:
  transaction:
    timeout:
      # 全局事务超时时间（秒）
      global-timeout: 60
      
      # 分支事务超时时间（秒）
      branch-timeout: 30
      
      # 补偿超时时间（秒）
      compensation-timeout: 30
      
      # 清理超时时间（秒）
      cleanup-timeout: 10
```

### HTTP超时配置

```yaml
tipray:
  transaction:
    http:
      # 连接超时时间（毫秒）
      connect-timeout: 5000
      
      # 读取超时时间（毫秒）
      read-timeout: 30000
      
      # 写入超时时间（毫秒）
      write-timeout: 30000
      
      # 连接池配置
      pool:
        max-connections: 100
        max-connections-per-host: 20
        keep-alive-duration: 300
```

## 🔄 重试配置

### 全局重试配置

```yaml
tipray:
  transaction:
    retry:
      # 默认重试次数
      default-count: 3
      
      # 默认重试间隔（毫秒）
      default-interval: 1000
      
      # 最大重试间隔（毫秒）
      max-interval: 10000
      
      # 重试倍数
      multiplier: 2.0
      
      # 是否启用随机化
      randomization-enabled: true
```

### 特定操作重试配置

```yaml
tipray:
  transaction:
    retry:
      operations:
        commit:
          count: 5
          interval: 500
          max-interval: 5000
        
        rollback:
          count: 3
          interval: 1000
          max-interval: 10000
        
        compensation:
          count: 3
          interval: 2000
          max-interval: 20000
```

## 📊 监控配置

### 基础监控配置

```yaml
tipray:
  transaction:
    monitor:
      # 是否启用监控
      enabled: true
      
      # 监控检查间隔（毫秒）
      check-interval: 30000
      
      # 是否启用性能监控
      performance-enabled: true
      
      # 是否启用健康检查
      health-check-enabled: true
      
      # 是否启用指标收集
      metrics-enabled: true
```

### UI界面配置

```yaml
tipray:
  transaction:
    ui:
      # 是否启用UI界面
      enabled: true
      
      # UI访问路径
      path: /tipray-transaction-ui
      
      # 页面标题
      title: "Tipray分布式事务监控"
      
      # 刷新间隔（秒）
      refresh-interval: 3
      
      # 分页大小
      page-size: 15
      
      # 数据保留天数
      data-retention-days: 7
      
      # 是否启用认证
      auth-enabled: false
      
      # 认证用户名
      username: admin
      
      # 认证密码
      password: admin123
```

## 🔧 高级配置

### 开发环境配置

```yaml
tipray:
  transaction:
    dev:
      # 是否启用开发模式
      enabled: false
      
      # 是否启用调试日志
      debug-log-enabled: false
      
      # 是否启用性能监控
      performance-monitor-enabled: false
      
      # 是否启用SQL日志
      sql-log-enabled: false
```

### 安全配置

```yaml
tipray:
  transaction:
    security:
      # 是否启用安全验证
      enabled: false
      
      # 密钥
      secret-key: "tipray-secret-key"
      
      # 令牌过期时间（秒）
      token-expire-time: 3600
      
      # 是否启用IP白名单
      ip-whitelist-enabled: false
      
      # IP白名单
      ip-whitelist:
        - "127.0.0.1"
        - "***********/24"
```

## 📝 配置示例

### 生产环境配置

```yaml
tipray:
  transaction:
    enabled: true
    application-name: ${spring.application.name}
    
    at:
      enabled: true
      default-timeout: 60
      data-source-proxy:
        enabled: true
        global-lock-timeout: 30000
      undo-log:
        cleanup:
          enabled: true
          interval-hours: 6
          retention-days: 3
    
    saga:
      enabled: true
      default-timeout: 120
      compensation:
        async-enabled: true
        retry-count: 5
    
    storage:
      type: DATABASE
      database:
        auto-create-table: false
        batch-size: 200
    
    thread-pool:
      core-pool-size: 20
      maximum-pool-size: 100
      queue-capacity: 500
    
    monitor:
      enabled: true
      check-interval: 10000
    
    ui:
      enabled: true
      data-retention-days: 30
```

### 开发环境配置

```yaml
tipray:
  transaction:
    enabled: true
    application-name: ${spring.application.name}-dev
    
    at:
      enabled: true
      undo-log:
        auto-create-table: true
        cleanup:
          retention-days: 1
    
    saga:
      enabled: true
    
    storage:
      type: MEMORY
      memory:
        max-size: 1000
        expire-time: 1800
    
    ui:
      enabled: true
      refresh-interval: 1
      data-retention-days: 1
    
    dev:
      enabled: true
      debug-log-enabled: true
      sql-log-enabled: true
```

## 📚 配置最佳实践

### 1. 环境隔离

- 使用Spring Profile区分不同环境配置
- 生产环境关闭调试功能
- 开发环境启用详细日志

### 2. 性能优化

- 根据业务量调整线程池大小
- 合理设置超时时间
- 启用数据压缩和批量操作

### 3. 监控告警

- 生产环境必须启用监控
- 设置合理的数据保留期
- 配置必要的安全认证

### 4. 存储选择

- 开发环境可使用内存存储
- 生产环境建议使用数据库存储
- 高性能场景可考虑Redis存储

---

**提示**: 配置项支持Spring Boot的配置文件热更新，部分配置修改后需要重启应用才能生效。
