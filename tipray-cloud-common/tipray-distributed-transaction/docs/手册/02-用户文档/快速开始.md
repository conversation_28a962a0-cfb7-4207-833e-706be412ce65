# 快速开始

本指南将帮助您在5分钟内快速体验Tipray分布式事务框架的核心功能。

## 🎯 前置条件

### 环境要求

- **JDK**: 1.8+
- **Maven**: 3.6+
- **MySQL**: 5.7+ (用于存储事务信息和UndoLog)
- **IDE**: IntelliJ IDEA 或 Eclipse

### 数据库准备

```sql
-- 创建测试数据库
CREATE DATABASE tipray_transaction_demo DEFAULT CHARSET=utf8mb4;

-- 创建业务表
USE tipray_transaction_demo;
CREATE TABLE user_account (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id VARCHAR(64) NOT NULL,
    balance DECIMAL(10,2) NOT NULL DEFAULT 0.00,
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- 创建UndoLog表（AT模式必需）
CREATE TABLE undo_log (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    branch_id BIGINT NOT NULL,
    xid VARCHAR(100) NOT NULL,
    context VARCHAR(128) NOT NULL,
    rollback_info LONGBLOB NOT NULL,
    log_status INT NOT NULL,
    log_created DATETIME NOT NULL,
    log_modified DATETIME NOT NULL,
    UNIQUE KEY ux_undo_log (xid, branch_id)
);

-- 插入测试数据
INSERT INTO user_account (user_id, balance) VALUES ('user001', 1000.00);
INSERT INTO user_account (user_id, balance) VALUES ('user002', 500.00);
```

## 🚀 创建项目

### 1. 创建Spring Boot项目

```xml
<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0">
    <modelVersion>4.0.0</modelVersion>
    
    <groupId>com.example</groupId>
    <artifactId>tipray-transaction-demo</artifactId>
    <version>1.0.0</version>
    <packaging>jar</packaging>
    
    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>2.7.18</version>
        <relativePath/>
    </parent>
    
    <properties>
        <java.version>1.8</java.version>
        <tipray.version>1.0.0</tipray.version>
    </properties>
    
    <dependencies>
        <!-- Spring Boot Starter -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
        
        <!-- Tipray分布式事务 -->
        <dependency>
            <groupId>com.tipray</groupId>
            <artifactId>tipray-transaction-spring-boot-starter</artifactId>
            <version>${tipray.version}</version>
        </dependency>
        
        <!-- 数据库相关 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-jdbc</artifactId>
        </dependency>
        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
            <scope>runtime</scope>
        </dependency>
        
        <!-- MyBatis -->
        <dependency>
            <groupId>org.mybatis.spring.boot</groupId>
            <artifactId>mybatis-spring-boot-starter</artifactId>
            <version>2.3.1</version>
        </dependency>
    </dependencies>
</project>
```

### 2. 配置文件

```yaml
# application.yml
server:
  port: 8080

spring:
  application:
    name: tipray-transaction-demo
  
  # 数据源配置
  datasource:
    url: ************************************************************************************************************************************
    username: root
    password: your_password
    driver-class-name: com.mysql.cj.jdbc.Driver

# Tipray分布式事务配置
tipray:
  transaction:
    # 启用分布式事务
    enabled: true
    # 应用名称
    application-name: ${spring.application.name}
    
    # AT模式配置
    at:
      enabled: true
      # UndoLog表名
      undo-log:
        table-name: undo_log
        auto-create-table: false
    
    # Saga模式配置
    saga:
      enabled: true
    
    # 存储配置
    storage:
      type: DATABASE
      database:
        table-prefix: tipray_tx_
        auto-create-table: true
    
    # 监控界面配置
    ui:
      enabled: true
      path: /tipray-transaction-ui
      title: "Tipray分布式事务监控 - Demo"

# MyBatis配置
mybatis:
  mapper-locations: classpath:mapper/*.xml
  type-aliases-package: com.example.demo.entity
  configuration:
    map-underscore-to-camel-case: true

# 日志配置
logging:
  level:
    com.tipray.transaction: DEBUG
    com.example.demo: DEBUG
```

## 📝 编写代码

### 1. 实体类

```java
// UserAccount.java
package com.example.demo.entity;

import java.math.BigDecimal;
import java.time.LocalDateTime;

public class UserAccount {
    private Long id;
    private String userId;
    private BigDecimal balance;
    private LocalDateTime createdTime;
    private LocalDateTime updatedTime;
    
    // getter和setter方法
    public Long getId() { return id; }
    public void setId(Long id) { this.id = id; }
    
    public String getUserId() { return userId; }
    public void setUserId(String userId) { this.userId = userId; }
    
    public BigDecimal getBalance() { return balance; }
    public void setBalance(BigDecimal balance) { this.balance = balance; }
    
    public LocalDateTime getCreatedTime() { return createdTime; }
    public void setCreatedTime(LocalDateTime createdTime) { this.createdTime = createdTime; }
    
    public LocalDateTime getUpdatedTime() { return updatedTime; }
    public void setUpdatedTime(LocalDateTime updatedTime) { this.updatedTime = updatedTime; }
}
```

### 2. Mapper接口

```java
// UserAccountMapper.java
package com.example.demo.mapper;

import com.example.demo.entity.UserAccount;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface UserAccountMapper {
    
    UserAccount selectByUserId(@Param("userId") String userId);
    
    int updateBalance(@Param("userId") String userId, @Param("balance") BigDecimal balance);
    
    int insert(UserAccount account);
}
```

### 3. Mapper XML

```xml
<!-- mapper/UserAccountMapper.xml -->
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.example.demo.mapper.UserAccountMapper">
    
    <select id="selectByUserId" resultType="UserAccount">
        SELECT id, user_id, balance, created_time, updated_time
        FROM user_account
        WHERE user_id = #{userId}
    </select>
    
    <update id="updateBalance">
        UPDATE user_account 
        SET balance = #{balance}, updated_time = NOW()
        WHERE user_id = #{userId}
    </update>
    
    <insert id="insert" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO user_account (user_id, balance)
        VALUES (#{userId}, #{balance})
    </insert>
    
</mapper>
```

### 4. 业务服务

```java
// AccountService.java
package com.example.demo.service;

import com.example.demo.entity.UserAccount;
import com.example.demo.mapper.UserAccountMapper;
import com.tipray.transaction.core.application.annotation.DistributedTransaction;
import com.tipray.transaction.core.enums.TransactionMode;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;

@Service
public class AccountService {
    
    @Autowired
    private UserAccountMapper userAccountMapper;
    
    /**
     * AT模式转账示例
     * 强一致性保证，任何步骤失败都会自动回滚
     */
    @DistributedTransaction(
        mode = TransactionMode.AT,
        name = "转账交易",
        timeoutSeconds = 30
    )
    public void transferMoney(String fromUserId, String toUserId, BigDecimal amount) {
        // 1. 检查余额
        UserAccount fromAccount = userAccountMapper.selectByUserId(fromUserId);
        if (fromAccount == null) {
            throw new RuntimeException("转出账户不存在: " + fromUserId);
        }
        if (fromAccount.getBalance().compareTo(amount) < 0) {
            throw new RuntimeException("余额不足");
        }
        
        // 2. 扣减转出账户余额
        BigDecimal newFromBalance = fromAccount.getBalance().subtract(amount);
        userAccountMapper.updateBalance(fromUserId, newFromBalance);
        
        // 3. 增加转入账户余额
        UserAccount toAccount = userAccountMapper.selectByUserId(toUserId);
        if (toAccount == null) {
            throw new RuntimeException("转入账户不存在: " + toUserId);
        }
        
        BigDecimal newToBalance = toAccount.getBalance().add(amount);
        userAccountMapper.updateBalance(toUserId, newToBalance);
        
        // 4. 模拟业务异常（用于测试回滚）
        if (amount.compareTo(new BigDecimal("999")) > 0) {
            throw new RuntimeException("转账金额过大，交易被拒绝");
        }
    }
    
    /**
     * 查询账户余额
     */
    public UserAccount getAccount(String userId) {
        return userAccountMapper.selectByUserId(userId);
    }
}
```

### 5. 控制器

```java
// AccountController.java
package com.example.demo.controller;

import com.example.demo.entity.UserAccount;
import com.example.demo.service.AccountService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;

@RestController
@RequestMapping("/api/account")
public class AccountController {
    
    @Autowired
    private AccountService accountService;
    
    /**
     * 转账接口
     */
    @PostMapping("/transfer")
    public Map<String, Object> transfer(
            @RequestParam String fromUserId,
            @RequestParam String toUserId,
            @RequestParam BigDecimal amount) {
        
        Map<String, Object> result = new HashMap<>();
        try {
            accountService.transferMoney(fromUserId, toUserId, amount);
            result.put("success", true);
            result.put("message", "转账成功");
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "转账失败: " + e.getMessage());
        }
        return result;
    }
    
    /**
     * 查询账户余额
     */
    @GetMapping("/balance/{userId}")
    public Map<String, Object> getBalance(@PathVariable String userId) {
        Map<String, Object> result = new HashMap<>();
        try {
            UserAccount account = accountService.getAccount(userId);
            if (account != null) {
                result.put("success", true);
                result.put("userId", account.getUserId());
                result.put("balance", account.getBalance());
            } else {
                result.put("success", false);
                result.put("message", "账户不存在");
            }
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "查询失败: " + e.getMessage());
        }
        return result;
    }
}
```

### 6. 启动类

```java
// DemoApplication.java
package com.example.demo;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;

@SpringBootApplication
public class DemoApplication {
    public static void main(String[] args) {
        SpringApplication.run(DemoApplication.class, args);
    }
}
```

## 🧪 测试验证

### 1. 启动应用

```bash
mvn spring-boot:run
```

启动成功后，控制台会显示：

```
Tipray分布式事务框架启动 | 应用: tipray-transaction-demo | AT: true | Saga: true
```

### 2. 测试成功场景

```bash
# 查询初始余额
curl "http://localhost:8080/api/account/balance/user001"
# 返回: {"success":true,"userId":"user001","balance":1000.00}

curl "http://localhost:8080/api/account/balance/user002"
# 返回: {"success":true,"userId":"user002","balance":500.00}

# 执行转账（成功）
curl -X POST "http://localhost:8080/api/account/transfer?fromUserId=user001&toUserId=user002&amount=100"
# 返回: {"success":true,"message":"转账成功"}

# 验证余额变化
curl "http://localhost:8080/api/account/balance/user001"
# 返回: {"success":true,"userId":"user001","balance":900.00}

curl "http://localhost:8080/api/account/balance/user002"
# 返回: {"success":true,"userId":"user002","balance":600.00}
```

### 3. 测试回滚场景

```bash
# 执行转账（触发回滚）
curl -X POST "http://localhost:8080/api/account/transfer?fromUserId=user001&toUserId=user002&amount=1000"
# 返回: {"success":false,"message":"转账失败: 转账金额过大，交易被拒绝"}

# 验证余额未变化（回滚成功）
curl "http://localhost:8080/api/account/balance/user001"
# 返回: {"success":true,"userId":"user001","balance":900.00}

curl "http://localhost:8080/api/account/balance/user002"
# 返回: {"success":true,"userId":"user002","balance":600.00}
```

### 4. 查看监控界面

访问: http://localhost:8080/tipray-transaction-ui

可以看到：

- 事务执行历史
- 成功/失败统计
- 事务详细信息
- 性能指标

### 5. 查看UndoLog

```sql
-- 查看UndoLog表（事务提交后会被清理）
SELECT * FROM undo_log;

-- 查看事务记录表
SELECT * FROM tipray_tx_transaction ORDER BY created_time DESC LIMIT 10;
```

## 🔍 工作原理解析

### AT模式执行流程

1. **开启全局事务**: `@DistributedTransaction`注解触发
2. **执行业务SQL**: 自动生成前后镜像到UndoLog
3. **提交本地事务**: 业务数据和UndoLog一起提交
4. **决策阶段**: 根据业务执行结果决定提交或回滚
5. **清理UndoLog**: 提交成功后异步清理UndoLog

### 回滚机制

当业务异常时：

1. **解析UndoLog**: 获取数据变更的前后镜像
2. **生成回滚SQL**: 根据前镜像生成反向SQL
3. **执行回滚**: 执行回滚SQL恢复数据
4. **清理UndoLog**: 回滚完成后清理UndoLog

## 🎉 恭喜！

您已经成功完成了Tipray分布式事务框架的快速体验！

### 您学到了什么：

1. ✅ 如何添加Tipray依赖和配置
2. ✅ 如何使用`@DistributedTransaction`注解
3. ✅ AT模式的自动回滚机制
4. ✅ 如何查看事务监控界面
5. ✅ UndoLog的工作原理

### 下一步建议：

- [配置参考](配置参考.md) - 了解更多配置选项
- [AT模式](../03-事务模式/AT模式.md) - 深入学习AT模式
- [Saga模式](../03-事务模式/Saga模式.md) - 学习Saga模式
- [监控界面](监控界面.md) - 掌握监控界面使用

## 🚨 常见问题

### 1. 启动失败

- 检查数据库连接配置
- 确认MySQL服务已启动
- 验证数据库和表是否创建成功

### 2. 事务不生效

- 确认方法是public的
- 检查是否通过Spring容器调用
- 验证`@DistributedTransaction`注解配置

### 3. 回滚失败

- 检查UndoLog表是否存在
- 确认数据库支持事务
- 查看错误日志定位问题

---

**提示**: 这个示例展示了AT模式的基本用法，实际项目中还可以结合云服务调用、Saga模式等更复杂的场景。
