# AT模式

AT模式（Automatic Transaction Mode）是Tipray分布式事务框架提供的强一致性事务模式，基于2PC+UndoLog机制实现自动事务管理。

## 🎯 核心概念

### 什么是AT模式

AT模式是一种**自动化**的分布式事务模式，它通过以下机制保证强一致性：

- **自动生成UndoLog**: 拦截SQL执行，自动记录数据变更的前后镜像
- **两阶段提交**: 采用经典的2PC协议保证事务一致性
- **全局锁机制**: 防止并发事务的脏写问题
- **自动回滚**: 基于UndoLog自动生成回滚SQL

### 适用场景

- ✅ 需要强一致性保证的业务场景
- ✅ 基于关系型数据库的应用
- ✅ 对业务代码侵入性要求低的场景
- ✅ 事务链路相对简单的场景

### 不适用场景

- ❌ 长时间运行的业务流程
- ❌ 对性能要求极高的场景
- ❌ 不支持事务的存储系统
- ❌ 复杂的异步业务流程

## 🏗️ 工作原理

### 整体架构

```
┌─────────────────────────────────────────────────────────────┐
│                    DLP服务 (协调者)                          │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────────────┐   │
│  │     TM      │  │     TC      │  │   业务服务层        │   │
│  │ 事务管理器   │  │ 事务协调器   │  │                    │   │
│  └─────────────┘  └─────────────┘  └─────────────────────┘   │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │              AT事务处理器                               │ │
│  └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
                              │
                              │ HTTP调用
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                   云服务 (参与者)                            │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────────────┐   │
│  │     RM      │  │ UndoLog管理  │  │   业务服务层        │   │
│  │ 资源管理器   │  │             │  │                    │   │
│  └─────────────┘  └─────────────┘  └─────────────────────┘   │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │           数据源代理 + 连接代理                          │ │
│  └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

### 执行流程

#### 一阶段：业务执行 + UndoLog记录

```mermaid
sequenceDiagram
    participant App as 业务应用
    participant TM as 事务管理器
    participant TC as 事务协调器
    participant RM as 资源管理器
    participant DB as 数据库

    App->>TM: @DistributedTransaction
    TM->>TC: 开启全局事务
    TC->>TC: 生成全局XID
    App->>RM: 执行业务SQL
    RM->>DB: 查询前镜像
    RM->>DB: 执行业务SQL
    RM->>DB: 查询后镜像
    RM->>DB: 插入UndoLog
    RM->>TC: 注册分支事务
    RM->>DB: 提交本地事务
    RM->>TC: 报告分支状态
```

#### 二阶段：提交或回滚

```mermaid
sequenceDiagram
    participant TC as 事务协调器
    participant RM as 资源管理器
    participant DB as 数据库

    alt 全局提交
        TC->>RM: 发送提交指令
        RM->>DB: 删除UndoLog
        RM->>TC: 返回提交结果
    else 全局回滚
        TC->>RM: 发送回滚指令
        RM->>DB: 查询UndoLog
        RM->>RM: 生成回滚SQL
        RM->>DB: 执行回滚SQL
        RM->>DB: 删除UndoLog
        RM->>TC: 返回回滚结果
    end
```

## 💻 使用方式

### 1. 基础使用

#### 注解方式

```java
@Service
public class OrderService {
    
    @Autowired
    private OrderMapper orderMapper;
    
    @Autowired
    private InventoryService inventoryService;
    
    /**
     * AT模式分布式事务
     */
    @DistributedTransaction(mode = TransactionMode.AT)
    public void createOrder(CreateOrderRequest request) {
        // 1. 创建订单
        Order order = new Order();
        order.setUserId(request.getUserId());
        order.setProductId(request.getProductId());
        order.setQuantity(request.getQuantity());
        order.setStatus("CREATED");
        orderMapper.insert(order);
        
        // 2. 扣减库存（可能是云服务调用）
        inventoryService.decreaseStock(request.getProductId(), request.getQuantity());
        
        // 3. 更新订单状态
        order.setStatus("CONFIRMED");
        orderMapper.updateStatus(order.getId(), "CONFIRMED");
        
        // 如果任何步骤失败，整个事务自动回滚
    }
}
```

#### 编程式使用

```java
@Service
public class PaymentService {
    
    @Autowired
    private TransactionTemplate transactionTemplate;
    
    public void processPayment(PaymentRequest request) {
        transactionTemplate.execute(TransactionMode.AT, () -> {
            // 1. 扣减账户余额
            accountService.deductBalance(request.getUserId(), request.getAmount());
            
            // 2. 创建支付记录
            paymentMapper.insert(createPaymentRecord(request));
            
            // 3. 调用第三方支付
            thirdPartyPaymentService.pay(request);
            
            return "支付成功";
        });
    }
}
```

### 2. 云服务调用

#### @AtService注解

```java
@Service
public class CloudServiceClient {
    
    /**
     * 调用云服务的库存服务
     */
    @AtService(value = "inventory-service", url = "http://inventory-service/api")
    public void decreaseStock(String productId, Integer quantity) {
        // HTTP调用逻辑，事务上下文自动传递
        InventoryRequest request = new InventoryRequest(productId, quantity);
        
        ResponseEntity<InventoryResponse> response = restTemplate.postForEntity(
            "/inventory/decrease", 
            request, 
            InventoryResponse.class
        );
        
        if (!response.getBody().isSuccess()) {
            throw new BusinessException("库存扣减失败: " + response.getBody().getMessage());
        }
    }
    
    /**
     * 调用云服务的用户服务
     */
    @AtService(value = "user-service", url = "http://user-service/api")
    public UserInfo getUserInfo(String userId) {
        return restTemplate.getForObject("/user/" + userId, UserInfo.class);
    }
}
```

### 3. 云服务端配置

#### 添加客户端依赖

```xml
<dependency>
    <groupId>com.tipray</groupId>
    <artifactId>tipray-transaction-client-spring-boot-starter</artifactId>
    <version>1.0.0</version>
</dependency>
```

#### 云服务配置

```yaml
tipray:
  transaction:
    client:
      # 启用客户端
      enabled: true
      
      # 应用名称
      application-name: inventory-service
      
      # AT模式配置
      at:
        enabled: true
        data-source-proxy:
          enabled: true
        undo-log:
          table-name: undo_log
          auto-create-table: true
```

#### 云服务事务接口

```java
@RestController
@RequestMapping("/api/transaction")
public class TransactionController {
    
    @Autowired
    private ResourceManager resourceManager;
    
    /**
     * 提交分支事务
     */
    @PostMapping("/commit")
    public ResponseEntity<Void> commit(@RequestBody CommitRequest request) {
        try {
            resourceManager.commitBranchTransaction(
                request.getXid(), 
                request.getBranchId(), 
                request.getResourceId()
            );
            return ResponseEntity.ok().build();
        } catch (Exception e) {
            return ResponseEntity.status(500).build();
        }
    }
    
    /**
     * 回滚分支事务
     */
    @PostMapping("/rollback")
    public ResponseEntity<Void> rollback(@RequestBody RollbackRequest request) {
        try {
            resourceManager.rollbackBranchTransaction(
                request.getXid(), 
                request.getBranchId(), 
                request.getResourceId()
            );
            return ResponseEntity.ok().build();
        } catch (Exception e) {
            return ResponseEntity.status(500).build();
        }
    }
}
```

## 🔒 全局锁机制

### 写隔离

AT模式通过全局锁保证写隔离：

```java
// 事务1
@DistributedTransaction(mode = TransactionMode.AT)
public void updateAccount1() {
    // 获取account表id=1记录的全局锁
    accountMapper.updateBalance(1L, new BigDecimal("100"));
    // 本地事务提交，释放本地锁，保持全局锁
}

// 事务2（并发执行）
@DistributedTransaction(mode = TransactionMode.AT)
public void updateAccount2() {
    // 尝试获取account表id=1记录的全局锁
    // 由于事务1持有全局锁，事务2等待
    accountMapper.updateBalance(1L, new BigDecimal("200"));
}
```

### 读隔离

默认读未提交，可通过SELECT FOR UPDATE实现读已提交：

```java
@DistributedTransaction(mode = TransactionMode.AT)
public void readCommittedExample() {
    // 普通查询：读未提交
    Account account1 = accountMapper.selectById(1L);
    
    // 加锁查询：读已提交
    Account account2 = accountMapper.selectByIdForUpdate(1L);
}
```

## 📊 UndoLog详解

### UndoLog表结构

```sql
CREATE TABLE undo_log (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    branch_id BIGINT NOT NULL COMMENT '分支事务ID',
    xid VARCHAR(100) NOT NULL COMMENT '全局事务ID',
    context VARCHAR(128) NOT NULL COMMENT '上下文信息',
    rollback_info LONGBLOB NOT NULL COMMENT '回滚信息',
    log_status INT NOT NULL COMMENT '日志状态',
    log_created DATETIME NOT NULL COMMENT '创建时间',
    log_modified DATETIME NOT NULL COMMENT '修改时间',
    UNIQUE KEY ux_undo_log (xid, branch_id)
);
```

### UndoLog内容示例

```json
{
    "branchId": *********,
    "undoItems": [{
        "afterImage": {
            "rows": [{
                "fields": [{
                    "name": "id",
                    "type": 4,
                    "value": 1
                }, {
                    "name": "balance",
                    "type": 3,
                    "value": 900.00
                }]
            }],
            "tableName": "account"
        },
        "beforeImage": {
            "rows": [{
                "fields": [{
                    "name": "id",
                    "type": 4,
                    "value": 1
                }, {
                    "name": "balance",
                    "type": 3,
                    "value": 1000.00
                }]
            }],
            "tableName": "account"
        },
        "sqlType": "UPDATE"
    }],
    "xid": "*************:8091:*************:1"
}
```

### 支持的SQL类型

- ✅ **INSERT**: 记录插入的数据，回滚时执行DELETE
- ✅ **UPDATE**: 记录更新前后的数据，回滚时恢复原值
- ✅ **DELETE**: 记录删除的数据，回滚时执行INSERT
- ❌ **DDL**: 不支持表结构变更的回滚
- ❌ **复杂查询**: 不支持存储过程、函数等复杂SQL

## ⚙️ 配置详解

### AT模式专用配置

```yaml
tipray:
  transaction:
    at:
      # 基础配置
      enabled: true
      default-timeout: 30
      
      # 数据源代理配置
      data-source-proxy:
        enabled: true
        global-lock-enabled: true
        global-lock-timeout: 30000
        proxy-type: AT
      
      # UndoLog配置
      undo-log:
        table-name: undo_log
        compression-enabled: true
        compression-threshold: 4096
        max-data-size: 4194304
        serializer: jackson
        auto-create-table: false
        cleanup:
          enabled: true
          interval-hours: 24
          retention-days: 7
          batch-size: 1000
      
      # 服务映射配置
      service:
        mappings:
          inventory-service:
            base-url: http://inventory-service
            commit-path: /api/transaction/commit
            rollback-path: /api/transaction/rollback
            connect-timeout: 5000
            read-timeout: 30000
```

## 🚨 注意事项

### 1. 数据库要求

- 必须支持ACID事务
- 建议使用InnoDB存储引擎
- 需要创建UndoLog表

### 2. SQL限制

- 不支持DDL语句
- 不支持存储过程和函数
- 复杂的多表关联更新需要谨慎使用

### 3. 性能考虑

- UndoLog会增加存储开销
- 全局锁可能影响并发性能
- 建议定期清理过期的UndoLog

### 4. 异常处理

- 业务异常会触发事务回滚
- 网络异常会重试或超时回滚
- 数据冲突会等待或超时失败

## 📈 性能优化

### 1. UndoLog优化

```yaml
tipray:
  transaction:
    at:
      undo-log:
        # 启用压缩减少存储空间
        compression-enabled: true
        compression-threshold: 1024
        
        # 及时清理过期数据
        cleanup:
          enabled: true
          interval-hours: 6
          retention-days: 1
```

### 2. 全局锁优化

```yaml
tipray:
  transaction:
    at:
      data-source-proxy:
        # 合理设置锁超时时间
        global-lock-timeout: 10000
```

### 3. 连接池优化

```yaml
spring:
  datasource:
    hikari:
      maximum-pool-size: 20
      minimum-idle: 5
      connection-timeout: 30000
      idle-timeout: 600000
      max-lifetime: 1800000
```

## 📚 最佳实践

### 1. 事务边界设计

- 保持事务尽可能短小
- 避免在事务中执行耗时操作
- 合理设置超时时间

### 2. 异常处理

```java
@DistributedTransaction(mode = TransactionMode.AT)
public void businessMethod() {
    try {
        // 业务逻辑
        doBusinessLogic();
    } catch (BusinessException e) {
        // 业务异常，事务回滚
        throw e;
    } catch (Exception e) {
        // 系统异常，包装后抛出
        throw new SystemException("系统异常", e);
    }
}
```

### 3. 监控告警

- 监控事务成功率
- 监控事务执行时间
- 监控UndoLog清理情况
- 设置异常告警

---

**提示**: AT模式适合大多数基于数据库的业务场景，提供了强一致性保证和良好的开发体验。
