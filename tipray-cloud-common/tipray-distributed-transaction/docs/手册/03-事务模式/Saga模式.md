# Saga模式

Saga模式是Tipray分布式事务框架提供的最终一致性事务模式，基于补偿机制实现长事务的分布式协调。

## 🎯 核心概念

### 什么是Saga模式

Saga模式是一种**补偿式**的分布式事务模式，它通过以下机制保证最终一致性：

- **立即提交**: 每个事务步骤立即提交本地事务
- **补偿机制**: 失败时执行补偿操作撤销已完成的步骤
- **事件驱动**: 支持异步执行和事件驱动架构
- **业务补偿**: 通过业务逻辑实现数据一致性

### 理论基础

Saga模式基于1987年Hector & Kenneth发表的论文《Sagas》，将长事务分解为一系列短事务。

### 适用场景

- ✅ 长时间运行的业务流程
- ✅ 跨多个服务的复杂业务编排
- ✅ 对性能要求较高的场景
- ✅ 包含第三方服务或遗留系统的场景
- ✅ 可以接受最终一致性的业务

### 不适用场景

- ❌ 需要强一致性保证的场景
- ❌ 补偿逻辑复杂或无法实现的场景
- ❌ 对数据隔离性要求很高的场景

## 🏗️ 工作原理

### 整体架构

```
┌─────────────────────────────────────────────────────────────┐
│                    DLP服务 (协调者)                          │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────────────┐   │
│  │ Saga管理器  │  │ Saga编排器  │  │   业务服务层        │   │
│  │             │  │             │  │                    │   │
│  └─────────────┘  └─────────────┘  └─────────────────────┘   │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │              Saga事务处理器                             │ │
│  └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
                              │
                              │ 业务调用
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                   业务步骤执行                               │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────────────┐   │
│  │   步骤1     │  │   步骤2     │  │      步骤N          │   │
│  │ + 补偿1     │  │ + 补偿2     │  │    + 补偿N          │   │
│  └─────────────┘  └─────────────┘  └─────────────────────┘   │
└─────────────────────────────────────────────────────────────┘
```

### 执行流程

#### 正常执行流程

```mermaid
sequenceDiagram
    participant App as 业务应用
    participant Saga as Saga协调器
    participant Step1 as 步骤1
    participant Step2 as 步骤2
    participant Step3 as 步骤3

    App->>Saga: @DistributedTransaction(SAGA)
    Saga->>Step1: 执行步骤1
    Step1->>Step1: 提交本地事务
    Step1->>Saga: 返回成功
    Saga->>Step2: 执行步骤2
    Step2->>Step2: 提交本地事务
    Step2->>Saga: 返回成功
    Saga->>Step3: 执行步骤3
    Step3->>Step3: 提交本地事务
    Step3->>Saga: 返回成功
    Saga->>App: 事务完成
```

#### 补偿执行流程

```mermaid
sequenceDiagram
    participant App as 业务应用
    participant Saga as Saga协调器
    participant Step1 as 步骤1
    participant Step2 as 步骤2
    participant Step3 as 步骤3
    participant Comp as 补偿器

    App->>Saga: @DistributedTransaction(SAGA)
    Saga->>Step1: 执行步骤1
    Step1->>Step1: 提交本地事务
    Step1->>Saga: 返回成功
    Saga->>Step2: 执行步骤2
    Step2->>Step2: 提交本地事务
    Step2->>Saga: 返回成功
    Saga->>Step3: 执行步骤3
    Step3->>Step3: 执行失败
    Step3->>Saga: 返回失败
    Saga->>Comp: 触发补偿
    Comp->>Step2: 补偿步骤2
    Comp->>Step1: 补偿步骤1
    Saga->>App: 事务回滚完成
```

## 💻 使用方式

### 1. 基础使用

#### 注解方式

```java
@Service
public class OrderProcessService {
    
    @Autowired
    private OrderService orderService;
    
    @Autowired
    private PaymentService paymentService;
    
    @Autowired
    private InventoryService inventoryService;
    
    @Autowired
    private NotificationService notificationService;
    
    /**
     * Saga模式订单处理流程
     */
    @DistributedTransaction(mode = TransactionMode.SAGA)
    public void processOrder(OrderRequest request) {
        // 步骤1: 创建订单
        createOrder(request);
        
        // 步骤2: 扣减库存
        decreaseInventory(request);
        
        // 步骤3: 处理支付
        processPayment(request);
        
        // 步骤4: 发送通知
        sendNotification(request);
    }
    
    @TransactionStep(compensation = "cancelOrder")
    public void createOrder(OrderRequest request) {
        Order order = new Order();
        order.setUserId(request.getUserId());
        order.setProductId(request.getProductId());
        order.setQuantity(request.getQuantity());
        order.setStatus("CREATED");
        orderService.createOrder(order);
        
        // 立即提交本地事务
    }
    
    public void cancelOrder(OrderRequest request) {
        // 补偿逻辑：取消订单
        orderService.cancelOrder(request.getOrderId());
    }
    
    @TransactionStep(compensation = "restoreInventory")
    public void decreaseInventory(OrderRequest request) {
        inventoryService.decrease(request.getProductId(), request.getQuantity());
    }
    
    public void restoreInventory(OrderRequest request) {
        // 补偿逻辑：恢复库存
        inventoryService.increase(request.getProductId(), request.getQuantity());
    }
    
    @TransactionStep(compensation = "refundPayment")
    public void processPayment(OrderRequest request) {
        paymentService.pay(request.getUserId(), request.getAmount());
    }
    
    public void refundPayment(OrderRequest request) {
        // 补偿逻辑：退款
        paymentService.refund(request.getUserId(), request.getAmount());
    }
    
    @TransactionStep(compensation = "sendCancelNotification")
    public void sendNotification(OrderRequest request) {
        notificationService.sendOrderConfirmation(request.getUserId(), request.getOrderId());
    }
    
    public void sendCancelNotification(OrderRequest request) {
        // 补偿逻辑：发送取消通知
        notificationService.sendOrderCancellation(request.getUserId(), request.getOrderId());
    }
}
```

#### 编程式使用

```java
@Service
public class BusinessProcessService {
    
    @Autowired
    private SagaTransactionTemplate sagaTemplate;
    
    public void executeBusinessProcess(BusinessRequest request) {
        sagaTemplate.execute(context -> {
            // 步骤1
            context.addStep("step1", () -> {
                return businessService.step1(request);
            }, (result) -> {
                businessService.compensateStep1(request, result);
            });
            
            // 步骤2
            context.addStep("step2", () -> {
                return businessService.step2(request);
            }, (result) -> {
                businessService.compensateStep2(request, result);
            });
            
            // 步骤3
            context.addStep("step3", () -> {
                return businessService.step3(request);
            }, (result) -> {
                businessService.compensateStep3(request, result);
            });
            
            return "业务流程执行完成";
        });
    }
}
```

### 2. 高级特性

#### 条件执行

```java
@DistributedTransaction(mode = TransactionMode.SAGA)
public void conditionalProcess(ProcessRequest request) {
    // 步骤1: 总是执行
    validateRequest(request);
    
    // 步骤2: 条件执行
    if (request.needsApproval()) {
        requestApproval(request);
    }
    
    // 步骤3: 根据条件选择不同的处理方式
    if (request.isVipUser()) {
        processVipOrder(request);
    } else {
        processNormalOrder(request);
    }
}
```

#### 并行执行

```java
@DistributedTransaction(mode = TransactionMode.SAGA)
public void parallelProcess(ProcessRequest request) {
    // 步骤1: 串行执行
    prepareData(request);

    // 获取TTL包装的线程池，确保事务上下文传播
    ExecutorService executor = poolManager.getAsyncTaskExecutor();

    // 步骤2-4: 并行执行（使用TTL线程池）
    CompletableFuture<Void> future1 = CompletableFuture.runAsync(() -> {
        processInventory(request);
    }, executor);

    CompletableFuture<Void> future2 = CompletableFuture.runAsync(() -> {
        processPayment(request);
    }, executor);

    CompletableFuture<Void> future3 = CompletableFuture.runAsync(() -> {
        processNotification(request);
    }, executor);

    // 等待所有并行步骤完成
    CompletableFuture.allOf(future1, future2, future3).join();

    // 步骤5: 串行执行
    finalizeProcess(request);
}
```

#### 异步补偿

```java
@TransactionStep(
    compensation = "asyncCompensateOrder",
    async = true,
    timeout = 60
)
public void createOrderAsync(OrderRequest request) {
    // 异步执行的业务步骤
    orderService.createOrderAsync(request);
}

@Async
public void asyncCompensateOrder(OrderRequest request) {
    // 异步执行的补偿逻辑
    orderService.cancelOrderAsync(request);
}
```

### 3. 状态机模式

#### 定义状态机

```java
@Component
public class OrderSagaStateMachine {
    
    @SagaState("CREATED")
    public void handleCreated(OrderSagaContext context) {
        // 处理订单创建状态
        context.transitionTo("INVENTORY_CHECKED");
    }
    
    @SagaState("INVENTORY_CHECKED")
    public void handleInventoryChecked(OrderSagaContext context) {
        if (context.hasEnoughInventory()) {
            context.transitionTo("PAYMENT_PROCESSED");
        } else {
            context.transitionTo("FAILED");
        }
    }
    
    @SagaState("PAYMENT_PROCESSED")
    public void handlePaymentProcessed(OrderSagaContext context) {
        context.transitionTo("COMPLETED");
    }
    
    @SagaState("FAILED")
    public void handleFailed(OrderSagaContext context) {
        // 执行补偿逻辑
        context.compensate();
    }
    
    @SagaState("COMPLETED")
    public void handleCompleted(OrderSagaContext context) {
        // 发送完成通知
        notificationService.sendCompletion(context.getOrderId());
    }
}
```

## ⚙️ 配置详解

### Saga模式专用配置

```yaml
tipray:
  transaction:
    saga:
      # 基础配置
      enabled: true
      default-timeout: 60
      default-compensation-timeout: 30
      
      # 补偿配置
      compensation:
        async-enabled: true
        retry-count: 3
        retry-interval: 1000
        timeout: 30
        log-enabled: true
        
        # 补偿策略：FORWARD/BACKWARD
        strategy: BACKWARD
        
        # 补偿模式：SYNC/ASYNC
        mode: ASYNC
      
      # 编排器配置
      orchestrator:
        type: SIMPLE
        parallel-enabled: true
        max-parallelism: 5
        step-timeout: 30
        
        # 状态持久化
        state-persistence-enabled: true
        
        # 事件发布
        event-publish-enabled: true
      
      # 状态机配置
      state-machine:
        enabled: false
        initial-state: INIT
        final-states: [COMPLETED, FAILED]
        
        # 状态转换超时
        transition-timeout: 10
```

## 🔄 补偿策略

### 1. 向前补偿 (Forward Compensation)

```java
@DistributedTransaction(mode = TransactionMode.SAGA)
public void forwardCompensationExample(ProcessRequest request) {
    try {
        step1(request);
        step2(request);
        step3(request); // 失败
    } catch (Exception e) {
        // 向前补偿：尝试修复问题继续执行
        fixStep3Issue(request);
        step3(request); // 重试
    }
}
```

### 2. 向后补偿 (Backward Compensation)

```java
@DistributedTransaction(mode = TransactionMode.SAGA)
public void backwardCompensationExample(ProcessRequest request) {
    step1(request);     // 成功
    step2(request);     // 成功
    step3(request);     // 失败
    
    // 向后补偿：撤销已完成的步骤
    // 自动调用 compensateStep2() 和 compensateStep1()
}
```

### 3. 混合补偿策略

```java
@TransactionStep(
    compensation = "compensateComplexStep",
    compensationStrategy = CompensationStrategy.MIXED
)
public void complexStep(ProcessRequest request) {
    // 复杂的业务步骤
    complexBusinessLogic(request);
}

public void compensateComplexStep(ProcessRequest request) {
    // 先尝试向前补偿
    if (canFixIssue(request)) {
        fixIssue(request);
        return;
    }
    
    // 向前补偿失败，执行向后补偿
    rollbackComplexStep(request);
}
```

## 📊 监控和可观测性

### 1. Saga执行监控

```java
@Component
public class SagaMonitor {
    
    @EventListener
    public void onSagaStarted(SagaStartedEvent event) {
        log.info("Saga事务开始: {}", event.getSagaId());
        metricsCollector.incrementSagaStarted();
    }
    
    @EventListener
    public void onSagaCompleted(SagaCompletedEvent event) {
        log.info("Saga事务完成: {}, 耗时: {}ms", 
            event.getSagaId(), event.getDuration());
        metricsCollector.recordSagaDuration(event.getDuration());
    }
    
    @EventListener
    public void onSagaFailed(SagaFailedEvent event) {
        log.error("Saga事务失败: {}, 原因: {}", 
            event.getSagaId(), event.getFailureReason());
        metricsCollector.incrementSagaFailed();
    }
    
    @EventListener
    public void onCompensationStarted(CompensationStartedEvent event) {
        log.warn("开始补偿: {}, 步骤: {}", 
            event.getSagaId(), event.getStepName());
        metricsCollector.incrementCompensationStarted();
    }
}
```

### 2. 自定义指标

```java
@Component
public class SagaMetrics {
    
    private final MeterRegistry meterRegistry;
    private final Counter sagaStartedCounter;
    private final Counter sagaCompletedCounter;
    private final Counter sagaFailedCounter;
    private final Timer sagaDurationTimer;
    
    public SagaMetrics(MeterRegistry meterRegistry) {
        this.meterRegistry = meterRegistry;
        this.sagaStartedCounter = Counter.builder("saga.started")
            .description("Saga事务开始次数")
            .register(meterRegistry);
        this.sagaCompletedCounter = Counter.builder("saga.completed")
            .description("Saga事务完成次数")
            .register(meterRegistry);
        this.sagaFailedCounter = Counter.builder("saga.failed")
            .description("Saga事务失败次数")
            .register(meterRegistry);
        this.sagaDurationTimer = Timer.builder("saga.duration")
            .description("Saga事务执行时间")
            .register(meterRegistry);
    }
    
    public void incrementSagaStarted() {
        sagaStartedCounter.increment();
    }
    
    public void recordSagaDuration(long duration) {
        sagaDurationTimer.record(duration, TimeUnit.MILLISECONDS);
    }
}
```

## 🚨 注意事项

### 1. 补偿逻辑设计

- 补偿操作必须是幂等的
- 补偿逻辑要能处理部分失败的情况
- 考虑补偿操作本身失败的处理

### 2. 数据一致性

- Saga只保证最终一致性
- 中间状态可能被其他事务看到
- 需要设计合理的业务状态

### 3. 性能考虑

- 步骤数量不宜过多
- 避免长时间运行的步骤
- 合理使用异步执行

### 4. 异常处理

```java
@TransactionStep(
    compensation = "compensateWithRetry",
    retryCount = 3,
    retryInterval = 1000
)
public void stepWithRetry(ProcessRequest request) {
    try {
        businessService.process(request);
    } catch (RetryableException e) {
        // 可重试异常，框架会自动重试
        throw e;
    } catch (NonRetryableException e) {
        // 不可重试异常，直接触发补偿
        throw new SagaException("业务处理失败", e);
    }
}
```

## 📈 性能优化

### 1. 异步执行

```yaml
tipray:
  transaction:
    saga:
      compensation:
        async-enabled: true
      orchestrator:
        parallel-enabled: true
        max-parallelism: 10
```

### 2. 状态持久化优化

```yaml
tipray:
  transaction:
    saga:
      orchestrator:
        state-persistence-enabled: true
        # 批量持久化
        batch-persistence-enabled: true
        batch-size: 100
        batch-timeout: 1000
```

### 3. 事件发布优化

```yaml
tipray:
  transaction:
    saga:
      orchestrator:
        event-publish-enabled: true
        # 异步事件发布
        async-event-publish: true
        event-buffer-size: 1000
```

## 📚 最佳实践

### 1. 步骤设计原则

- 每个步骤应该是原子的
- 步骤之间应该松耦合
- 设计清晰的补偿逻辑

### 2. 状态管理

- 使用明确的业务状态
- 避免中间状态的歧义
- 提供状态查询接口

### 3. 错误处理

- 区分可重试和不可重试错误
- 设计合理的重试策略
- 记录详细的错误日志

### 4. 监控告警

- 监控Saga执行成功率
- 监控补偿执行情况
- 设置关键步骤的告警

---

**提示**: Saga模式适合复杂的业务流程编排，提供了良好的性能和扩展性，但需要仔细设计补偿逻辑。
