# 事务传播机制重构方案

## 🎯 **重构目标**

实现完整的事务传播机制，支持嵌套事务、事务挂起恢复、传播行为配置等功能。

### **当前问题分析**

1. **缺乏传播机制**：无法处理嵌套事务和事务传播
2. **上下文管理混乱**：事务上下文传递不规范
3. **挂起恢复缺失**：无法挂起和恢复事务
4. **传播行为单一**：只支持简单的事务开启，缺乏多样化的传播行为
5. **线程安全问题**：多线程环境下事务上下文可能混乱
6. **性能问题**：频繁的事务创建和销毁影响性能

## 📋 **传播机制设计**

### **1. 传播行为定义**

```java
/**
 * 事务传播行为枚举
 * 定义事务在不同场景下的传播方式
 */
public enum TransactionPropagation {

    /**
     * 必需的事务
     * 如果当前存在事务，则加入该事务；如果当前没有事务，则创建一个新事务
     */
    REQUIRED("必需", "支持当前事务，如果不存在则创建新事务"),

    /**
     * 支持事务
     * 如果当前存在事务，则加入该事务；如果当前没有事务，则以非事务方式执行
     */
    SUPPORTS("支持", "支持当前事务，如果不存在则非事务执行"),

    /**
     * 强制事务
     * 如果当前存在事务，则加入该事务；如果当前没有事务，则抛出异常
     */
    MANDATORY("强制", "支持当前事务，如果不存在则抛出异常"),

    /**
     * 需要新事务
     * 创建一个新事务，如果当前存在事务，则挂起当前事务
     */
    REQUIRES_NEW("需要新事务", "创建新事务，如果当前存在事务则挂起"),

    /**
     * 不支持事务
     * 以非事务方式执行，如果当前存在事务，则挂起当前事务
     */
    NOT_SUPPORTED("不支持", "非事务执行，如果当前存在事务则挂起"),

    /**
     * 从不使用事务
     * 以非事务方式执行，如果当前存在事务，则抛出异常
     */
    NEVER("从不", "非事务执行，如果当前存在事务则抛出异常"),

    /**
     * 嵌套事务
     * 如果当前存在事务，则在嵌套事务内执行；如果当前没有事务，则创建一个新事务
     */
    NESTED("嵌套", "如果当前存在事务则嵌套执行，否则创建新事务");

    private final String displayName;
    private final String description;

    TransactionPropagation(String displayName, String description) {
        this.displayName = displayName;
        this.description = description;
    }

    public String getDisplayName() {
        return displayName;
    }

    public String getDescription() {
        return description;
    }
}
```

### **2. 事务上下文管理**

```java
/**
 * 事务上下文
 * 包含事务的所有相关信息
 */
public class TransactionContext {

    private final String transactionId;
    private final String groupId;
    private final TransactionMode mode;
    private final TransactionPropagation propagation;
    private final boolean readOnly;
    private final int timeout;
    private final long startTime;
    private final String initiator;
    private final Map<String, Object> attributes;

    // 嵌套事务相关
    private final TransactionContext parent;
    private final List<TransactionContext> children;
    private final int nestingLevel;

    // 挂起恢复相关
    private volatile boolean suspended;
    private volatile long suspendTime;
    private volatile String suspendReason;

    private TransactionContext(Builder builder) {
        this.transactionId = builder.transactionId;
        this.groupId = builder.groupId;
        this.mode = builder.mode;
        this.propagation = builder.propagation;
        this.readOnly = builder.readOnly;
        this.timeout = builder.timeout;
        this.startTime = System.currentTimeMillis();
        this.initiator = builder.initiator;
        this.attributes = new ConcurrentHashMap<>(builder.attributes);
        this.parent = builder.parent;
        this.children = new CopyOnWriteArrayList<>();
        this.nestingLevel = parent != null ? parent.nestingLevel + 1 : 0;
        this.suspended = false;
    }

    /**
     * 创建子事务上下文
     */
    public TransactionContext createChild(String childTransactionId, TransactionMode childMode) {
        TransactionContext child = TransactionContext.builder()
                .transactionId(childTransactionId)
                .groupId(this.groupId)
                .mode(childMode)
                .propagation(TransactionPropagation.NESTED)
                .parent(this)
                .timeout(this.timeout)
                .initiator(this.initiator)
                .build();

        this.children.add(child);
        return child;
    }

    /**
     * 挂起事务
     */
    public void suspend(String reason) {
        this.suspended = true;
        this.suspendTime = System.currentTimeMillis();
        this.suspendReason = reason;
    }

    /**
     * 恢复事务
     */
    public void resume() {
        this.suspended = false;
        this.suspendTime = 0;
        this.suspendReason = null;
    }

    /**
     * 判断是否为根事务
     */
    public boolean isRoot() {
        return parent == null;
    }

    /**
     * 获取根事务上下文
     */
    public TransactionContext getRoot() {
        TransactionContext current = this;
        while (current.parent != null) {
            current = current.parent;
        }
        return current;
    }

    public static Builder builder() {
        return new Builder();
    }

    public static class Builder {
        private String transactionId;
        private String groupId;
        private TransactionMode mode = TransactionMode.AT;
        private TransactionPropagation propagation = TransactionPropagation.REQUIRED;
        private boolean readOnly = false;
        private int timeout = 30;
        private String initiator;
        private Map<String, Object> attributes = new HashMap<>();
        private TransactionContext parent;

        public Builder transactionId(String transactionId) {
            this.transactionId = transactionId;
            return this;
        }

        public Builder groupId(String groupId) {
            this.groupId = groupId;
            return this;
        }

        public Builder mode(TransactionMode mode) {
            this.mode = mode;
            return this;
        }

        public Builder propagation(TransactionPropagation propagation) {
            this.propagation = propagation;
            return this;
        }

        public Builder readOnly(boolean readOnly) {
            this.readOnly = readOnly;
            return this;
        }

        public Builder timeout(int timeout) {
            this.timeout = timeout;
            return this;
        }

        public Builder initiator(String initiator) {
            this.initiator = initiator;
            return this;
        }

        public Builder attribute(String key, Object value) {
            this.attributes.put(key, value);
            return this;
        }

        public Builder parent(TransactionContext parent) {
            this.parent = parent;
            return this;
        }

        public TransactionContext build() {
            return new TransactionContext(this);
        }
    }
}
```

### **3. 事务上下文持有者**

```java
/**
 * 事务上下文持有者
 * 使用ThreadLocal管理当前线程的事务上下文
 */
public class TransactionContextHolder {

    private static final TransmittableThreadLocal<TransactionContext> CONTEXT_HOLDER =
            new TransmittableThreadLocal<>();

    private static final TransmittableThreadLocal<Deque<TransactionContext>> SUSPENDED_CONTEXTS =
            new TransmittableThreadLocal<Deque<TransactionContext>>() {
                @Override
                protected Deque<TransactionContext> initialValue() {
                    return new ArrayDeque<>();
                }
            };

    /**
     * 获取当前事务上下文
     */
    public static TransactionContext getCurrentContext() {
        return CONTEXT_HOLDER.get();
    }

    /**
     * 设置当前事务上下文
     */
    public static void setCurrentContext(TransactionContext context) {
        if (context != null) {
            CONTEXT_HOLDER.set(context);
        } else {
            CONTEXT_HOLDER.remove();
        }
    }

    /**
     * 挂起当前事务上下文
     */
    public static TransactionContext suspendCurrentContext(String reason) {
        TransactionContext current = getCurrentContext();
        if (current != null) {
            current.suspend(reason);
            SUSPENDED_CONTEXTS.get().push(current);
            CONTEXT_HOLDER.remove();
        }
        return current;
    }

    /**
     * 恢复挂起的事务上下文
     */
    public static void resumeSuspendedContext() {
        Deque<TransactionContext> suspended = SUSPENDED_CONTEXTS.get();
        if (!suspended.isEmpty()) {
            TransactionContext context = suspended.pop();
            context.resume();
            setCurrentContext(context);
        }
    }

    /**
     * 清理当前线程的所有事务上下文
     */
    public static void clear() {
        CONTEXT_HOLDER.remove();
        SUSPENDED_CONTEXTS.remove();
    }

    /**
     * 判断当前是否存在事务
     */
    public static boolean hasTransaction() {
        return getCurrentContext() != null;
    }

    /**
     * 获取当前事务ID
     */
    public static String getCurrentTransactionId() {
        TransactionContext context = getCurrentContext();
        return context != null ? context.getTransactionId() : null;
    }

    /**
     * 获取当前事务的嵌套级别
     */
    public static int getCurrentNestingLevel() {
        TransactionContext context = getCurrentContext();
        return context != null ? context.getNestingLevel() : 0;
    }
}
```

### **4. 传播行为处理器**

```java
/**
 * 传播行为处理器接口
 */
public interface PropagationHandler {

    /**
     * 判断是否支持该传播行为
     */
    boolean supports(TransactionPropagation propagation);

    /**
     * 处理传播行为
     */
    PropagationResult handle(TransactionPropagationContext propagationContext);
}

/**
 * REQUIRED传播行为处理器
 */
@Component
public class RequiredPropagationHandler implements PropagationHandler {

    private final TransactionManager transactionManager;

    @Override
    public boolean supports(TransactionPropagation propagation) {
        return propagation == TransactionPropagation.REQUIRED;
    }

    @Override
    public PropagationResult handle(TransactionPropagationContext propagationContext) {
        TransactionContext currentContext = TransactionContextHolder.getCurrentContext();

        if (currentContext != null) {
            // 当前存在事务，加入该事务
            return PropagationResult.joinExisting(currentContext);
        } else {
            // 当前不存在事务，创建新事务
            TransactionContext newContext = createNewTransaction(propagationContext);
            TransactionContextHolder.setCurrentContext(newContext);
            return PropagationResult.createNew(newContext);
        }
    }

    private TransactionContext createNewTransaction(TransactionPropagationContext propagationContext) {
        String transactionId = transactionManager.generateTransactionId();

        return TransactionContext.builder()
                .transactionId(transactionId)
                .groupId(propagationContext.getGroupId())
                .mode(propagationContext.getMode())
                .propagation(propagationContext.getPropagation())
                .timeout(propagationContext.getTimeout())
                .initiator(propagationContext.getInitiator())
                .build();
    }
}

/**
 * REQUIRES_NEW传播行为处理器
 */
@Component
public class RequiresNewPropagationHandler implements PropagationHandler {

    private final TransactionManager transactionManager;

    @Override
    public boolean supports(TransactionPropagation propagation) {
        return propagation == TransactionPropagation.REQUIRES_NEW;
    }

    @Override
    public PropagationResult handle(TransactionPropagationContext propagationContext) {
        // 挂起当前事务（如果存在）
        TransactionContext suspendedContext = TransactionContextHolder
                .suspendCurrentContext("REQUIRES_NEW传播行为");

        // 创建新事务
        TransactionContext newContext = createNewTransaction(propagationContext);
        TransactionContextHolder.setCurrentContext(newContext);

        return PropagationResult.createNewWithSuspended(newContext, suspendedContext);
    }

    private TransactionContext createNewTransaction(TransactionPropagationContext propagationContext) {
        String transactionId = transactionManager.generateTransactionId();

        return TransactionContext.builder()
                .transactionId(transactionId)
                .groupId(propagationContext.getGroupId())
                .mode(propagationContext.getMode())
                .propagation(propagationContext.getPropagation())
                .timeout(propagationContext.getTimeout())
                .initiator(propagationContext.getInitiator())
                .build();
    }
}

/**
 * NESTED传播行为处理器
 */
@Component
public class NestedPropagationHandler implements PropagationHandler {

    private final TransactionManager transactionManager;

    @Override
    public boolean supports(TransactionPropagation propagation) {
        return propagation == TransactionPropagation.NESTED;
    }

    @Override
    public PropagationResult handle(TransactionPropagationContext propagationContext) {
        TransactionContext currentContext = TransactionContextHolder.getCurrentContext();

        if (currentContext != null) {
            // 当前存在事务，创建嵌套事务
            String nestedTransactionId = transactionManager.generateTransactionId();
            TransactionContext nestedContext = currentContext.createChild(
                    nestedTransactionId, propagationContext.getMode()
            );

            TransactionContextHolder.setCurrentContext(nestedContext);
            return PropagationResult.createNested(nestedContext, currentContext);
        } else {
            // 当前不存在事务，创建新事务
            TransactionContext newContext = createNewTransaction(propagationContext);
            TransactionContextHolder.setCurrentContext(newContext);
            return PropagationResult.createNew(newContext);
        }
    }

    private TransactionContext createNewTransaction(TransactionPropagationContext propagationContext) {
        String transactionId = transactionManager.generateTransactionId();

        return TransactionContext.builder()
                .transactionId(transactionId)
                .groupId(propagationContext.getGroupId())
                .mode(propagationContext.getMode())
                .propagation(propagationContext.getPropagation())
                .timeout(propagationContext.getTimeout())
                .initiator(propagationContext.getInitiator())
                .build();
    }
}
```

### **5. 传播管理器**

```java
/**
 * 事务传播管理器
 * 统一管理所有传播行为的处理
 */
@Component
public class TransactionPropagationManager {

    private final Map<TransactionPropagation, PropagationHandler> handlers;
    private final TransactionPropagationMetrics metrics;
    private final TransactionPropagationLogger logger;

    public TransactionPropagationManager(List<PropagationHandler> handlerList,
                                         TransactionPropagationMetrics metrics,
                                         TransactionPropagationLogger logger) {
        this.handlers = handlerList.stream()
                .collect(Collectors.toMap(
                        handler -> Arrays.stream(TransactionPropagation.values())
                                .filter(handler::supports)
                                .findFirst()
                                .orElseThrow(() -> new IllegalStateException("处理器未指定支持的传播行为")),
                        Function.identity()
                ));
        this.metrics = metrics;
        this.logger = logger;
    }

    /**
     * 处理事务传播
     */
    public PropagationResult handlePropagation(TransactionPropagationContext propagationContext) {
        TransactionPropagation propagation = propagationContext.getPropagation();

        // 记录传播处理开始
        long startTime = System.currentTimeMillis();
        logger.logPropagationStart(propagationContext);

        try {
            // 获取对应的处理器
            PropagationHandler handler = handlers.get(propagation);
            if (handler == null) {
                throw new UnsupportedPropagationException(
                        "不支持的传播行为: " + propagation
                );
            }

            // 执行传播处理
            PropagationResult result = handler.handle(propagationContext);

            // 记录成功指标
            long duration = System.currentTimeMillis() - startTime;
            metrics.recordPropagationSuccess(propagation, duration);
            logger.logPropagationSuccess(propagationContext, result, duration);

            return result;

        } catch (Exception e) {
            // 记录失败指标
            long duration = System.currentTimeMillis() - startTime;
            metrics.recordPropagationFailure(propagation, duration);
            logger.logPropagationFailure(propagationContext, e, duration);

            throw new TransactionPropagationException(
                    "事务传播处理失败: " + propagation, e
            );
        }
    }

    /**
     * 清理传播上下文
     */
    public void cleanupPropagation(PropagationResult propagationResult) {
        try {
            switch (propagationResult.getType()) {
                case CREATE_NEW:
                    // 清理新创建的事务上下文
                    TransactionContextHolder.setCurrentContext(null);
                    break;

                case CREATE_NEW_WITH_SUSPENDED:
                    // 恢复挂起的事务上下文
                    TransactionContextHolder.setCurrentContext(null);
                    TransactionContextHolder.resumeSuspendedContext();
                    break;

                case CREATE_NESTED:
                    // 恢复父事务上下文
                    TransactionContext parentContext = propagationResult.getParentContext();
                    TransactionContextHolder.setCurrentContext(parentContext);
                    break;

                case JOIN_EXISTING:
                    // 加入现有事务，无需特殊清理
                    break;

                default:
                    logger.logUnknownPropagationType(propagationResult.getType());
            }

        } catch (Exception e) {
            logger.logCleanupFailure(propagationResult, e);
        }
    }

    /**
     * 获取支持的传播行为
     */
    public Set<TransactionPropagation> getSupportedPropagations() {
        return handlers.keySet();
    }
}

/**
 * 传播结果
 */
public class PropagationResult {

    private final PropagationType type;
    private final TransactionContext transactionContext;
    private final TransactionContext parentContext;
    private final TransactionContext suspendedContext;
    private final boolean newTransactionCreated;

    private PropagationResult(PropagationType type,
                              TransactionContext transactionContext,
                              TransactionContext parentContext,
                              TransactionContext suspendedContext,
                              boolean newTransactionCreated) {
        this.type = type;
        this.transactionContext = transactionContext;
        this.parentContext = parentContext;
        this.suspendedContext = suspendedContext;
        this.newTransactionCreated = newTransactionCreated;
    }

    public static PropagationResult joinExisting(TransactionContext context) {
        return new PropagationResult(PropagationType.JOIN_EXISTING, context, null, null, false);
    }

    public static PropagationResult createNew(TransactionContext context) {
        return new PropagationResult(PropagationType.CREATE_NEW, context, null, null, true);
    }

    public static PropagationResult createNewWithSuspended(TransactionContext newContext,
                                                           TransactionContext suspendedContext) {
        return new PropagationResult(PropagationType.CREATE_NEW_WITH_SUSPENDED,
                newContext, null, suspendedContext, true);
    }

    public static PropagationResult createNested(TransactionContext nestedContext,
                                                 TransactionContext parentContext) {
        return new PropagationResult(PropagationType.CREATE_NESTED,
                nestedContext, parentContext, null, true);
    }

    // getters...

    public enum PropagationType {
        JOIN_EXISTING,
        CREATE_NEW,
        CREATE_NEW_WITH_SUSPENDED,
        CREATE_NESTED
    }
}
```
