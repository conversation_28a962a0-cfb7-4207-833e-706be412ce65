# tipray-distributed-transaction 务实重构方案

## 🎯 **重构原则**

基于对现有项目的深度分析，制定以下重构原则：

1. **保留核心价值**：保留现有设计中的核心功能和业务逻辑
2. **渐进式优化**：在现有基础上进行渐进式改进，不推翻重建
3. **针对性解决**：重点解决代码结构、扩展性、监控等具体问题
4. **业务导向**：始终以DLP业务场景为导向，不过度通用化

## 📊 **重构范围界定**

### **保留不变的部分**

- ✅ 自动装配逻辑（主配置器 + Import模式）
- ✅ 事务屏障机制（防空回滚、防悬挂）
- ✅ 异步持久化机制
- ✅ 本地事务和分支事务统一管理的核心思路
- ✅ 现有的业务逻辑和功能特性

### **需要重构的部分**

- 🔧 状态机设计（参考Seata优化）
- 🔧 DistributedTransactionManager代码结构
- 🔧 异常处理体系
- 🔧 事务传播机制
- 🔧 监控和扩展能力

## 🏗️ **第一阶段：状态机优化（参考Seata）**

### **1. 优化状态定义**

```java
/**
 * 分布式事务状态枚举
 * 参考Seata的GlobalStatus，结合DLP业务场景优化
 */
public enum DistributedTransactionStatus {

    // ========== 初始阶段 ==========
    /**
     * 未知状态 - 通常在事务开始前使用
     */
    UNKNOWN(0, "未知状态", false, false),

    /**
     * 开始状态 - 全局事务开始，可以接受新的分支注册
     */
    BEGIN(1, "开始", false, false),

    // ========== 执行阶段 ==========
    /**
     * 执行中状态 - 事务正在执行业务逻辑
     */
    EXECUTING(2, "执行中", false, false),

    /**
     * 等待中状态 - 事务暂时等待某些条件满足
     */
    WAITING(3, "等待中", false, false),

    /**
     * 暂停状态 - 事务被手动暂停或因异常暂停
     */
    PAUSED(4, "暂停", false, false),

    // ========== 提交阶段 ==========
    /**
     * 提交中状态 - 两阶段提交进行中
     */
    COMMITTING(5, "提交中", false, false),

    /**
     * 提交重试中状态 - 提交失败后重试中
     */
    COMMIT_RETRYING(6, "提交重试中", false, false),

    /**
     * 异步提交中状态 - 异步提交进行中（主要用于Saga模式）
     */
    ASYNC_COMMITTING(7, "异步提交中", false, false),

    // ========== 回滚阶段 ==========
    /**
     * 回滚中状态 - 事务回滚进行中
     */
    ROLLBACKING(8, "回滚中", false, false),

    /**
     * 回滚重试中状态 - 回滚失败后重试中
     */
    ROLLBACK_RETRYING(9, "回滚重试中", false, false),

    /**
     * 超时回滚中状态 - 因超时触发的回滚进行中
     */
    TIMEOUT_ROLLBACKING(10, "超时回滚中", false, false),

    /**
     * 超时回滚重试中状态 - 超时回滚失败后重试中
     */
    TIMEOUT_ROLLBACK_RETRYING(11, "超时回滚重试中", false, false),

    // ========== 终态 ==========
    /**
     * 已提交状态 - 全局事务成功提交完成
     */
    COMMITTED(12, "已提交", true, false),

    /**
     * 提交失败状态 - 两阶段提交失败
     */
    COMMIT_FAILED(13, "提交失败", true, true),

    /**
     * 已回滚状态 - 全局事务成功回滚完成
     */
    ROLLBACKED(14, "已回滚", true, false),

    /**
     * 回滚失败状态 - 回滚失败
     */
    ROLLBACK_FAILED(15, "回滚失败", true, true),

    /**
     * 超时回滚状态 - 因超时成功回滚完成
     */
    TIMEOUT_ROLLBACKED(16, "超时回滚", true, false),

    /**
     * 超时回滚失败状态 - 超时回滚失败
     */
    TIMEOUT_ROLLBACK_FAILED(17, "超时回滚失败", true, true),

    /**
     * 已完成状态 - 事务完成，资源已清理
     */
    FINISHED(18, "已完成", true, false),

    // ========== 特殊状态（DLP特色） ==========
    /**
     * 需要人工干预状态 - 自动处理失败，需要人工介入
     */
    MANUAL_INTERVENTION_REQUIRED(19, "需要人工干预", true, true),

    /**
     * 已取消状态 - 事务被手动取消
     */
    CANCELLED(20, "已取消", true, false);

    private final int code;
    private final String description;
    private final boolean isTerminal;  // 是否为终态
    private final boolean needsIntervention;  // 是否需要人工干预

    DistributedTransactionStatus(int code, String description, boolean isTerminal, boolean needsIntervention) {
        this.code = code;
        this.description = description;
        this.isTerminal = isTerminal;
        this.needsIntervention = needsIntervention;
    }

    /**
     * 判断是否可以转换到目标状态
     */
    public boolean canTransitionTo(DistributedTransactionStatus target) {
        return TRANSITION_RULES.getOrDefault(this, Collections.emptySet()).contains(target);
    }

    /**
     * 获取下一个可能的状态
     */
    public Set<DistributedTransactionStatus> getNextPossibleStatuses() {
        return TRANSITION_RULES.getOrDefault(this, Collections.emptySet());
    }

    // 状态转换规则映射
    private static final Map<DistributedTransactionStatus, Set<DistributedTransactionStatus>> TRANSITION_RULES;

    static {
        Map<DistributedTransactionStatus, Set<DistributedTransactionStatus>> rules = new HashMap<>();

        // UNKNOWN状态转换
        rules.put(UNKNOWN, Set.of(BEGIN, CANCELLED));

        // BEGIN状态转换
        rules.put(BEGIN, Set.of(EXECUTING, CANCELLED, ROLLBACKING));

        // EXECUTING状态转换
        rules.put(EXECUTING, Set.of(
                COMMITTING, ROLLBACKING, WAITING, PAUSED,
                TIMEOUT_ROLLBACKING, CANCELLED
        ));

        // WAITING状态转换
        rules.put(WAITING, Set.of(
                EXECUTING, COMMITTING, ROLLBACKING,
                TIMEOUT_ROLLBACKING, CANCELLED
        ));

        // PAUSED状态转换
        rules.put(PAUSED, Set.of(
                EXECUTING, ROLLBACKING, CANCELLED
        ));

        // COMMITTING状态转换
        rules.put(COMMITTING, Set.of(
                COMMITTED, COMMIT_FAILED, COMMIT_RETRYING,
                ROLLBACKING, ASYNC_COMMITTING
        ));

        // COMMIT_RETRYING状态转换
        rules.put(COMMIT_RETRYING, Set.of(
                COMMITTED, COMMIT_FAILED, ROLLBACKING
        ));

        // ASYNC_COMMITTING状态转换
        rules.put(ASYNC_COMMITTING, Set.of(
                COMMITTED, COMMIT_FAILED
        ));

        // ROLLBACKING状态转换
        rules.put(ROLLBACKING, Set.of(
                ROLLBACKED, ROLLBACK_FAILED, ROLLBACK_RETRYING
        ));

        // ROLLBACK_RETRYING状态转换
        rules.put(ROLLBACK_RETRYING, Set.of(
                ROLLBACKED, ROLLBACK_FAILED, MANUAL_INTERVENTION_REQUIRED
        ));

        // TIMEOUT_ROLLBACKING状态转换
        rules.put(TIMEOUT_ROLLBACKING, Set.of(
                TIMEOUT_ROLLBACKED, TIMEOUT_ROLLBACK_FAILED, TIMEOUT_ROLLBACK_RETRYING
        ));

        // TIMEOUT_ROLLBACK_RETRYING状态转换
        rules.put(TIMEOUT_ROLLBACK_RETRYING, Set.of(
                TIMEOUT_ROLLBACKED, TIMEOUT_ROLLBACK_FAILED, MANUAL_INTERVENTION_REQUIRED
        ));

        // 终态不能再转换（除了清理操作）
        rules.put(COMMITTED, Set.of(FINISHED));
        rules.put(ROLLBACKED, Set.of(FINISHED));
        rules.put(TIMEOUT_ROLLBACKED, Set.of(FINISHED));
        rules.put(CANCELLED, Set.of(FINISHED));

        // 失败状态可以转换为需要人工干预
        rules.put(COMMIT_FAILED, Set.of(MANUAL_INTERVENTION_REQUIRED, ROLLBACKING));
        rules.put(ROLLBACK_FAILED, Set.of(MANUAL_INTERVENTION_REQUIRED));
        rules.put(TIMEOUT_ROLLBACK_FAILED, Set.of(MANUAL_INTERVENTION_REQUIRED));

        // 人工干预状态可以转换为任何状态（人工操作）
        rules.put(MANUAL_INTERVENTION_REQUIRED, Set.of(
                ROLLBACKING, COMMITTING, CANCELLED, FINISHED
        ));

        TRANSITION_RULES = Collections.unmodifiableMap(rules);
    }
}
```

### **2. 状态转换管理器**

```java
/**
 * 事务状态转换管理器
 * 管理状态转换的规则和逻辑
 */
@Component
public class TransactionStatusTransitionManager {

    private final TransactionStatusStorage statusStorage;
    private final TransactionEventPublisher eventPublisher;
    private final TransactionStatusMetrics statusMetrics;

    /**
     * 执行状态转换
     */
    public boolean transitionStatus(String transactionId,
                                    DistributedTransactionStatus targetStatus,
                                    String reason,
                                    Exception exception) {

        // 1. 获取当前状态
        DistributedTransactionStatus currentStatus = statusStorage.getCurrentStatus(transactionId);
        if (currentStatus == null) {
            log.warn("事务不存在，无法转换状态: {}", transactionId);
            return false;
        }

        // 2. 检查转换是否合法
        if (!currentStatus.canTransitionTo(targetStatus)) {
            log.warn("非法状态转换: {} -> {}, 事务ID: {}",
                    currentStatus, targetStatus, transactionId);
            return false;
        }

        // 3. 执行状态转换
        try {
            StatusTransitionContext context = StatusTransitionContext.builder()
                    .transactionId(transactionId)
                    .fromStatus(currentStatus)
                    .toStatus(targetStatus)
                    .reason(reason)
                    .exception(exception)
                    .timestamp(System.currentTimeMillis())
                    .build();

            // 4. 持久化状态变更
            statusStorage.updateStatus(transactionId, targetStatus, context);

            // 5. 发布状态变更事件
            eventPublisher.publishStatusChanged(context);

            // 6. 记录指标
            statusMetrics.recordStatusTransition(currentStatus, targetStatus);

            log.info("状态转换成功: {} -> {}, 事务ID: {}, 原因: {}",
                    currentStatus, targetStatus, transactionId, reason);

            return true;

        } catch (Exception e) {
            log.error("状态转换失败: {} -> {}, 事务ID: {}",
                    currentStatus, targetStatus, transactionId, e);
            return false;
        }
    }

    /**
     * 批量状态转换（用于分布式协调）
     */
    public Map<String, Boolean> batchTransitionStatus(
            Map<String, DistributedTransactionStatus> transitionMap,
            String reason) {

        Map<String, Boolean> results = new HashMap<>();

        // 按事务ID排序，避免死锁
        List<String> sortedTransactionIds = transitionMap.keySet().stream()
                .sorted()
                .collect(Collectors.toList());

        for (String transactionId : sortedTransactionIds) {
            DistributedTransactionStatus targetStatus = transitionMap.get(transactionId);
            boolean success = transitionStatus(transactionId, targetStatus, reason, null);
            results.put(transactionId, success);
        }

        return results;
    }

    /**
     * 获取需要人工干预的事务
     */
    public List<String> getTransactionsNeedingIntervention() {
        return statusStorage.findTransactionsByStatus(
                DistributedTransactionStatus.MANUAL_INTERVENTION_REQUIRED
        );
    }

    /**
     * 获取可以清理的已完成事务
     */
    public List<String> getFinishedTransactions(Duration retentionPeriod) {
        return statusStorage.findFinishedTransactionsBefore(
                System.currentTimeMillis() - retentionPeriod.toMillis()
        );
    }
}
```

## 🏗️ **第二阶段：DistributedTransactionManager重构**

### **1. 职责分离和代码结构优化**

```java
/**
 * 分布式事务管理器 - 重构后的版本
 * 职责：统一协调本地事务和分布式事务
 */
@Component
public class DistributedTransactionManager {

    // 核心组件
    private final LocalTransactionCoordinator localTransactionCoordinator;
    private final DistributedTransactionCoordinator distributedTransactionCoordinator;
    private final TransactionStatusTransitionManager statusTransitionManager;
    private final TransactionPropagationManager propagationManager;
    private final TransactionExceptionHandler exceptionHandler;

    /**
     * 执行分布式事务（主入口方法）
     */
    public <T> T execute(TransactionDefinition definition, TransactionCallback<T> callback) {
        // 1. 处理事务传播
        PropagationResult propagationResult = propagationManager.handlePropagation(definition);

        try {
            // 2. 开始事务
            TransactionContext context = beginTransaction(definition, propagationResult);

            // 3. 执行业务逻辑
            T result = executeBusinessLogic(context, callback);

            // 4. 提交事务
            commitTransaction(context);

            return result;

        } catch (Exception e) {
            // 5. 处理异常和回滚
            handleExceptionAndRollback(propagationResult, e);
            throw e;

        } finally {
            // 6. 清理传播上下文
            propagationManager.cleanupPropagation(propagationResult);
        }
    }

    private TransactionContext beginTransaction(TransactionDefinition definition,
                                                PropagationResult propagationResult) {

        if (propagationResult.isNewTransactionRequired()) {
            // 需要开启新事务
            return doBeginTransaction(definition);
        } else {
            // 加入现有事务
            return propagationResult.getExistingContext();
        }
    }

    private TransactionContext doBeginTransaction(TransactionDefinition definition) {
        String transactionId = generateTransactionId();

        try {
            // 1. 创建事务上下文
            TransactionContext context = TransactionContext.builder()
                    .transactionId(transactionId)
                    .groupId(definition.getGroupId())
                    .mode(definition.getMode())
                    .propagation(definition.getPropagation())
                    .timeout(definition.getTimeout())
                    .build();

            // 2. 开始本地事务
            localTransactionCoordinator.begin(context);

            // 3. 开始分布式事务
            distributedTransactionCoordinator.begin(context);

            // 4. 状态转换：UNKNOWN -> BEGIN
            statusTransitionManager.transitionStatus(
                    transactionId, DistributedTransactionStatus.BEGIN,
                    "事务开始", null
            );

            // 5. 设置事务上下文
            TransactionContextHolder.setCurrentContext(context);

            log.info("分布式事务开始: {}", transactionId);
            return context;

        } catch (Exception e) {
            log.error("开始分布式事务失败: {}", transactionId, e);
            throw new TransactionBeginException("开始分布式事务失败", e);
        }
    }

    private <T> T executeBusinessLogic(TransactionContext context, TransactionCallback<T> callback) {
        String transactionId = context.getTransactionId();

        try {
            // 状态转换：BEGIN -> EXECUTING
            statusTransitionManager.transitionStatus(
                    transactionId, DistributedTransactionStatus.EXECUTING,
                    "开始执行业务逻辑", null
            );

            // 执行业务逻辑
            return callback.doInTransaction(context);

        } catch (Exception e) {
            log.error("业务逻辑执行失败: {}", transactionId, e);
            throw e;
        }
    }

    private void commitTransaction(TransactionContext context) {
        String transactionId = context.getTransactionId();

        try {
            // 状态转换：EXECUTING -> COMMITTING
            statusTransitionManager.transitionStatus(
                    transactionId, DistributedTransactionStatus.COMMITTING,
                    "开始提交事务", null
            );

            // 1. 提交本地事务
            localTransactionCoordinator.commit(context);

            // 2. 提交分布式事务
            distributedTransactionCoordinator.commit(context);

            // 状态转换：COMMITTING -> COMMITTED
            statusTransitionManager.transitionStatus(
                    transactionId, DistributedTransactionStatus.COMMITTED,
                    "事务提交成功", null
            );

            log.info("分布式事务提交成功: {}", transactionId);

        } catch (Exception e) {
            log.error("分布式事务提交失败: {}", transactionId, e);

            // 状态转换：COMMITTING -> COMMIT_FAILED
            statusTransitionManager.transitionStatus(
                    transactionId, DistributedTransactionStatus.COMMIT_FAILED,
                    "事务提交失败", e
            );

            throw new TransactionCommitException("分布式事务提交失败", e);
        }
    }

    private void handleExceptionAndRollback(PropagationResult propagationResult, Exception e) {
        if (!propagationResult.isNewTransactionRequired()) {
            // 不是新事务，不需要回滚
            return;
        }

        TransactionContext context = TransactionContextHolder.getCurrentContext();
        if (context == null) {
            return;
        }

        String transactionId = context.getTransactionId();

        try {
            // 检查是否需要回滚
            if (exceptionHandler.shouldRollback(e, context)) {
                rollbackTransaction(context, e);
            } else {
                // 不需要回滚，仍然提交
                commitTransaction(context);
            }

        } catch (Exception rollbackException) {
            log.error("回滚处理失败: {}", transactionId, rollbackException);
        }
    }

    private void rollbackTransaction(TransactionContext context, Exception cause) {
        String transactionId = context.getTransactionId();

        try {
            // 状态转换：* -> ROLLBACKING
            statusTransitionManager.transitionStatus(
                    transactionId, DistributedTransactionStatus.ROLLBACKING,
                    "开始回滚事务", cause
            );

            // 1. 回滚分布式事务
            distributedTransactionCoordinator.rollback(context, cause);

            // 2. 回滚本地事务
            localTransactionCoordinator.rollback(context, cause);

            // 状态转换：ROLLBACKING -> ROLLBACKED
            statusTransitionManager.transitionStatus(
                    transactionId, DistributedTransactionStatus.ROLLBACKED,
                    "事务回滚成功", null
            );

            log.info("分布式事务回滚成功: {}", transactionId);

        } catch (Exception e) {
            log.error("分布式事务回滚失败: {}", transactionId, e);

            // 状态转换：ROLLBACKING -> ROLLBACK_FAILED
            statusTransitionManager.transitionStatus(
                    transactionId, DistributedTransactionStatus.ROLLBACK_FAILED,
                    "事务回滚失败", e
            );
        }
    }

    private String generateTransactionId() {
        return "tx-" + System.currentTimeMillis() + "-" +
                ThreadLocalRandom.current().nextInt(10000);
    }
}
```
