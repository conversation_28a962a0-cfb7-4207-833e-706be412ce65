# Tipray分布式事务框架日志使用示例

## 概述

本文档展示了如何在各个类中使用slf4j Logger配合LogFormatUtils工具类，实现统一的日志格式输出。

## 设计原则

1. **直接使用slf4j Logger** - 保持日志来源的清晰性
2. **统一格式** - 通过LogFormatUtils提供统一的日志消息格式
3. **结构化信息** - 使用键值对形式组织上下文信息
4. **性能友好** - 避免字符串拼接的性能开销

## 日志格式规范

### 统一格式模板
```
[TX-ID|BRANCH-ID] [PHASE] - MESSAGE [CONTEXT]
```

### 字段说明
- **TX-ID**: 事务ID，无事务时显示"----"
- **BRANCH-ID**: 分支ID，无分支时显示"----"
- **PHASE**: 事务阶段标识（BEGIN, REGISTER, EXEC, STATE, COMMIT, ROLLBACK, PERF, ERROR等）
- **MESSAGE**: 主要信息描述
- **CONTEXT**: 键值对形式的上下文信息

## 使用示例

### 1. 事务开始日志

```java
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import com.tipray.transaction.core.logging.LogFormatUtils;

@Service
public class TransactionService {
    private static final Logger log = LoggerFactory.getLogger(TransactionService.class);
    
    public void beginTransaction(String mode, String method) {
        log.info(LogFormatUtils.buildBeginMessage(mode, method));
    }
}
```

输出示例：
```
[tx-1753790590777-9855|----] [BEGIN] - 事务开始 [mode=AT, method=processData]
```

### 2. 分支注册日志

```java
@Service
public class BranchTransactionService {
    private static final Logger log = LoggerFactory.getLogger(BranchTransactionService.class);
    
    public void registerBranch(String service, String method) {
        log.info(LogFormatUtils.buildRegisterMessage(service, method));
    }
}
```

输出示例：
```
[tx-1753790590777-9855|1753790590774255] [REGISTER] - 分支注册 [service=LOCAL, method=processData]
```

### 3. 状态转换日志

```java
@Service
public class StateMachineService {
    private static final Logger log = LoggerFactory.getLogger(StateMachineService.class);
    
    public void stateTransition(Object fromStatus, Object toStatus, String event) {
        long startTime = System.currentTimeMillis();
        // 执行状态转换逻辑
        log.info(LogFormatUtils.buildStateMessage(fromStatus, toStatus, event, startTime));
    }
}
```

输出示例：
```
[tx-1753790590777-9855|1753790590774255] [STATE] - UNKNOWN -> REGISTERED [event=REGISTER, cost=5ms]
```

### 4. 事务提交日志

```java
@Service
public class TransactionCommitService {
    private static final Logger log = LoggerFactory.getLogger(TransactionCommitService.class);
    
    public void commitTransaction(int branchCount) {
        long startTime = System.currentTimeMillis();
        // 执行提交逻辑
        log.info(LogFormatUtils.buildCommitMessage(branchCount, startTime));
    }
}
```

输出示例：
```
[tx-1753790590777-9855|----] [COMMIT] - 事务提交成功 [branches=3, total=218ms]
```

### 5. 错误日志

```java
@Service
public class ErrorHandlingService {
    private static final Logger log = LoggerFactory.getLogger(ErrorHandlingService.class);
    
    public void handleError(String action, Exception e) {
        log.error(LogFormatUtils.buildErrorMessage(action, e), e);
    }
}
```

输出示例：
```
[tx-1753790590777-9855|1753790590774255] [ERROR] - 分支执行失败 [error=SQLException, msg=Connection timeout]
```

### 6. 性能日志

```java
@Service
public class PerformanceMonitorService {
    private static final Logger log = LoggerFactory.getLogger(PerformanceMonitorService.class);
    
    public void monitorPerformance(String phase, String metric, Object value) {
        long startTime = System.currentTimeMillis();
        // 执行监控逻辑
        log.info(LogFormatUtils.buildPerfMessage(phase, startTime, metric, value));
    }
}
```

输出示例：
```
[tx-1753790590777-9855|1753790590774255] [PERF] - 数据库操作性能 [rows=1000, cost=150ms]
```

### 7. 调试日志

```java
@Service
public class DebugService {
    private static final Logger log = LoggerFactory.getLogger(DebugService.class);
    
    public void debugOperation(String action, String param1, Object value1) {
        log.debug(LogFormatUtils.buildDebugMessage(action, param1, value1));
    }
}
```

输出示例：
```
[tx-1753790590777-9855|1753790590774255] [DEBUG] - 参数验证 [param1=value1]
```

### 8. 警告日志

```java
@Service
public class WarningService {
    private static final Logger log = LoggerFactory.getLogger(WarningService.class);
    
    public void handleWarning(String action, String reason) {
        log.warn(LogFormatUtils.buildWarnMessage(action, reason));
    }
}
```

输出示例：
```
[tx-1753790590777-9855|1753790590774255] [WARN] - 重试操作 [reason=网络超时]
```

### 9. 执行日志

```java
@Service
public class ExecutionService {
    private static final Logger log = LoggerFactory.getLogger(ExecutionService.class);
    
    public void executeStep(String action, int step, String timeout) {
        log.info(LogFormatUtils.buildExecMessage(action, "step", step, "timeout", timeout));
    }
}
```

输出示例：
```
[tx-1753790590777-9855|1753790590774255] [EXEC] - 开始执行 [step=1, timeout=30s]
```

## 最佳实践

### 1. Logger命名规范
```java
// 推荐：使用当前类名
private static final Logger log = LoggerFactory.getLogger(TransactionService.class);

// 不推荐：使用字符串
private static final Logger log = LoggerFactory.getLogger("TransactionService");
```

### 2. 日志级别选择
```java
// TRACE - 详细的调试信息
log.trace(LogFormatUtils.buildDebugMessage("方法进入", "param", value));

// DEBUG - 开发调试信息
log.debug(LogFormatUtils.buildDebugMessage("状态变更", "from", oldStatus, "to", newStatus));

// INFO - 关键业务流程
log.info(LogFormatUtils.buildBeginMessage(mode, method));

// WARN - 异常但可恢复的情况
log.warn(LogFormatUtils.buildWarnMessage("重试操作", "网络超时"));

// ERROR - 错误和异常
log.error(LogFormatUtils.buildErrorMessage("事务提交", e), e);
```

### 3. 性能考虑
```java
// 推荐：使用条件判断避免不必要的字符串拼接
if (log.isDebugEnabled()) {
    log.debug(LogFormatUtils.buildDebugMessage("详细调试", "param", expensiveOperation()));
}

// 推荐：使用占位符避免字符串拼接
log.info("{}", LogFormatUtils.buildBeginMessage(mode, method));
```

### 4. 异常处理
```java
try {
    // 业务逻辑
} catch (Exception e) {
    log.error(LogFormatUtils.buildErrorMessage("业务执行", e), e);
    throw e;
}
```

### 5. 上下文信息规范
```java
// 推荐：使用有意义的键值对
log.info(LogFormatUtils.buildExecMessage("数据库操作", 
    "table", "user", 
    "operation", "INSERT", 
    "rows", 1));

// 不推荐：使用无意义的参数
log.info(LogFormatUtils.buildExecMessage("操作", "param1", "value1"));
```

## 配置示例

### Logback配置
```xml
<configuration>
    <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>[%-5level] [%d{yyyy-MM-dd HH:mm:ss.SSS}] [%thread] %msg%n</pattern>
        </encoder>
    </appender>
    
    <!-- 分布式事务日志 -->
    <logger name="com.tipray.transaction" level="INFO" additivity="false">
        <appender-ref ref="CONSOLE"/>
    </logger>
    
    <root level="INFO">
        <appender-ref ref="CONSOLE"/>
    </root>
</configuration>
```

### 应用配置
```yaml
tipray:
  transaction:
    logging:
      level: INFO              # 日志级别
      enable-performance: true # 是否启用性能日志
      max-context-length: 200  # 上下文信息最大长度

logging:
  level:
    com.tipray.transaction: INFO
    root: INFO
  pattern:
    console: "[%-5level] [%d{yyyy-MM-dd HH:mm:ss.SSS}] [%thread] %msg%n"
```

## 日志输出效果

### 正常流程示例
```
[INFO ] [2025-01-29 20:03:10.777] [http-exec-9] [tx-1753790590777-9855|----] [BEGIN] - 事务开始 [mode=AT, method=processData]
[INFO ] [2025-01-29 20:03:10.784] [http-exec-9] [tx-1753790590777-9855|1753790590774255] [REGISTER] - 分支注册 [service=LOCAL, method=processData]
[INFO ] [2025-01-29 20:03:10.785] [http-exec-9] [tx-1753790590777-9855|1753790590774255] [EXEC] - 开始执行 [step=1, timeout=30s]
[INFO ] [2025-01-29 20:03:10.890] [http-exec-9] [tx-1753790590777-9855|1753790590774255] [EXEC] - 执行完成 [result=SUCCESS, cost=105ms]
[INFO ] [2025-01-29 20:03:10.995] [http-exec-9] [tx-1753790590777-9855|----] [COMMIT] - 事务提交成功 [branches=3, total=218ms]
```

### 异常流程示例
```
[ERROR] [2025-01-29 20:03:11.123] [http-exec-9] [tx-1753790590777-9855|1753790590774255] [ERROR] - 分支执行失败 [error=SQLException, msg=Connection timeout]
[WARN ] [2025-01-29 20:03:11.234] [http-exec-9] [tx-1753790590777-9855|----] [ROLLBACK] - 事务回滚 [reason=分支执行失败, cause=SQLException]
```

这种设计既保持了slf4j Logger的原有特性（可以清楚看到日志来源），又通过LogFormatUtils提供了统一的日志格式，让整个分布式事务框架的日志输出更加规范和美观。 