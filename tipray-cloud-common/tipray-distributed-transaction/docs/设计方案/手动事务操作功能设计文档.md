# 手动事务操作功能设计文档

## 1. 功能概述

为tipray-distributed-transaction框架增加手动事务操作功能，支持管理员或运维人员对分布式事务进行人工干预，包括：

- 事务重试
- 事务回滚
- 事务提交
- 取消事务
- 查看事务日志

## 2. 业务场景分析

### 2.1 事务重试场景

- **适用状态**: FAILED、UNDO_FAILED、TIMEOUT、CANCELLED
- **业务需求**: 当事务因为临时性问题（网络抖动、服务暂时不可用）失败时，需要重新执行
- **实现方式**: 重新执行原事务的所有步骤，保持原有的事务参数和配置

### 2.2 事务回滚场景

- **适用状态**: SUCCESS、COMMITTED、PARTIAL_FAILED
- **业务需求**: 已成功的事务需要撤销其影响，恢复到事务执行前的状态
- **实现方式**:
    - AT模式：执行UndoLog回滚
    - Saga模式：执行补偿操作

### 2.3 事务提交场景

- **适用状态**: PREPARING、WAITING
- **业务需求**: 对于处于准备提交状态的事务，强制进行提交
- **实现方式**: 跳过等待阶段，直接执行提交逻辑

### 2.4 取消事务场景

- **适用状态**: EXECUTING、PENDING、WAITING、PAUSED
- **业务需求**: 停止正在执行或等待执行的事务
- **实现方式**: 设置取消标志，停止后续步骤执行

## 3. 技术架构设计

### 3.1 整体架构

```
UI层 (transactions.html/js)
    ↓
Controller层 (TransactionManualOperationController)
    ↓  
Service层 (TransactionManualOperationService)
    ↓
Manager层 (DistributedTransactionManager)
    ↓
Repository层 (TransactionRepository)
```

### 3.2 核心组件设计

#### 3.2.1 TransactionManualOperationController

```java

@RestController
@RequestMapping("/api/transaction/manual")
public class TransactionManualOperationController {

    @PostMapping("/retry/{transactionId}")
    public ApiResponse<String> retryTransaction(@PathVariable String transactionId);

    @PostMapping("/rollback/{transactionId}")
    public ApiResponse<String> rollbackTransaction(@PathVariable String transactionId);

    @PostMapping("/commit/{transactionId}")
    public ApiResponse<String> commitTransaction(@PathVariable String transactionId);

    @PostMapping("/cancel/{transactionId}")
    public ApiResponse<String> cancelTransaction(@PathVariable String transactionId);

    @GetMapping("/logs/{transactionId}")
    public ApiResponse<List<TransactionLogInfo>> getTransactionLogs(@PathVariable String transactionId);
}
```

#### 3.2.2 TransactionManualOperationService

```java
public interface TransactionManualOperationService {
    
    /**
     * 手动重试事务
     */
    ManualOperationResult retryTransaction(String transactionId, String operatorId);
    
    /**
     * 手动回滚事务  
     */
    ManualOperationResult rollbackTransaction(String transactionId, String operatorId);
    
    /**
     * 手动提交事务
     */
    ManualOperationResult commitTransaction(String transactionId, String operatorId);
    
    /**
     * 取消事务
     */
    ManualOperationResult cancelTransaction(String transactionId, String operatorId);
    
    /**
     * 获取事务操作日志
     */
    List<TransactionLogInfo> getTransactionLogs(String transactionId);
}
```

### 3.3 数据模型设计

#### 3.3.1 手动操作记录表

```sql
CREATE TABLE tipray_transaction_manual_operation (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    transaction_id VARCHAR(64) NOT NULL COMMENT '事务ID',
    operation_type VARCHAR(32) NOT NULL COMMENT '操作类型：RETRY/ROLLBACK/COMMIT/CANCEL',
    operator_id VARCHAR(64) NOT NULL COMMENT '操作人ID',
    operator_name VARCHAR(128) COMMENT '操作人姓名',
    operation_time DATETIME NOT NULL COMMENT '操作时间',
    operation_reason TEXT COMMENT '操作原因',
    operation_result VARCHAR(32) NOT NULL COMMENT '操作结果：SUCCESS/FAILED',
    error_message TEXT COMMENT '错误信息',
    create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    update_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

#### 3.3.2 事务日志表

```sql  
CREATE TABLE tipray_transaction_log (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    transaction_id VARCHAR(64) NOT NULL COMMENT '事务ID',
    step_id BIGINT COMMENT '步骤ID',
    log_level VARCHAR(16) NOT NULL COMMENT '日志级别：DEBUG/INFO/WARN/ERROR',
    log_message TEXT NOT NULL COMMENT '日志内容',
    log_time DATETIME NOT NULL COMMENT '日志时间',
    thread_name VARCHAR(128) COMMENT '线程名称',
    class_name VARCHAR(256) COMMENT '类名',
    method_name VARCHAR(128) COMMENT '方法名',
    create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP
);
```

## 4. 核心问题解决方案

### 4.1 事务执行上下文完整记录

#### 4.1.1 增强的事务执行上下文

```java

@Data
public class TransactionExecutionContext {
    // 基础信息
    private String transactionId;
    private String groupId;
    private TransactionMode mode;

    // 执行环境信息
    private String businessClassName;      // 业务类名
    private String businessMethodName;     // 业务方法名
    private String businessMethodSignature; // 方法签名
    private Object[] businessMethodArgs;   // 方法参数（序列化后）
    private String executionType;          // ANNOTATION/PROGRAMMATIC

    // 调用栈信息
    private List<StackTraceInfo> callStack; // 完整调用栈
    private String initiatorThread;         // 发起线程
    private Map<String, Object> contextData; // 上下文数据

    // 执行环境
    private String springProfileActive;     // Spring环境
    private Map<String, String> systemProperties; // 系统属性
    private String jvmInfo;                // JVM信息
}
```

#### 4.1.2 调用栈信息记录

```java

@Data
public class StackTraceInfo {
    private String className;
    private String methodName;
    private String fileName;
    private int lineNumber;
    private boolean isBusinessMethod;      // 是否为业务方法
    private boolean isFrameworkMethod;     // 是否为框架方法
}
```

### 4.2 统一的事务信息收集器

#### 4.2.1 TransactionContextCollector

```java

@Component
public class TransactionContextCollector {

    /**
     * 收集注解式事务上下文
     */
    public TransactionExecutionContext collectAnnotationContext(
            ProceedingJoinPoint joinPoint, DistributedTransaction annotation) {

        TransactionExecutionContext context = new TransactionExecutionContext();

        // 基础信息
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        context.setBusinessClassName(signature.getDeclaringType().getName());
        context.setBusinessMethodName(signature.getName());
        context.setBusinessMethodSignature(signature.toString());
        context.setBusinessMethodArgs(serializeArgs(joinPoint.getArgs()));
        context.setExecutionType("ANNOTATION");

        // 调用栈信息
        context.setCallStack(collectCallStack());
        context.setInitiatorThread(Thread.currentThread().getName());

        // 环境信息
        collectEnvironmentInfo(context);

        return context;
    }

    /**
     * 收集编程式事务上下文
     */
    public TransactionExecutionContext collectProgrammaticContext(
            String groupId, TransactionMode mode, String transactionName) {

        TransactionExecutionContext context = new TransactionExecutionContext();

        // 通过调用栈分析找到业务方法
        StackTraceElement[] stackTrace = Thread.currentThread().getStackTrace();
        StackTraceElement businessMethod = findBusinessMethod(stackTrace);

        if (businessMethod != null) {
            context.setBusinessClassName(businessMethod.getClassName());
            context.setBusinessMethodName(businessMethod.getMethodName());
            context.setBusinessMethodSignature(reconstructMethodSignature(businessMethod));
        }

        context.setExecutionType("PROGRAMMATIC");
        context.setCallStack(collectCallStack());
        context.setInitiatorThread(Thread.currentThread().getName());

        collectEnvironmentInfo(context);

        return context;
    }
}
```

### 4.3 增强的事务数据模型

#### 4.3.1 扩展事务表结构

```sql
-- 在现有事务表中增加字段
ALTER TABLE tipray_distributed_transaction ADD COLUMN business_class_name VARCHAR(512) COMMENT '业务类名';
ALTER TABLE tipray_distributed_transaction ADD COLUMN business_method_name VARCHAR(256) COMMENT '业务方法名';
ALTER TABLE tipray_distributed_transaction ADD COLUMN business_method_signature TEXT COMMENT '方法签名';
ALTER TABLE tipray_distributed_transaction ADD COLUMN business_method_args TEXT COMMENT '方法参数(JSON)';
ALTER TABLE tipray_distributed_transaction ADD COLUMN execution_type VARCHAR(32) COMMENT '执行类型:ANNOTATION/PROGRAMMATIC';
ALTER TABLE tipray_distributed_transaction ADD COLUMN call_stack TEXT COMMENT '调用栈信息(JSON)';
ALTER TABLE tipray_distributed_transaction ADD COLUMN initiator_thread VARCHAR(128) COMMENT '发起线程';
ALTER TABLE tipray_distributed_transaction ADD COLUMN execution_context TEXT COMMENT '执行上下文(JSON)';
```

### 4.4 手动操作实现细节

#### 4.4.1 事务重试实现

```java

@Service
public class TransactionManualOperationServiceImpl implements TransactionManualOperationService {

    @Override
    public ManualOperationResult retryTransaction(String transactionId, String operatorId) {
        // 1. 获取原事务信息
        DistributedTransactionDO originalTransaction = transactionRepository.findByTransactionId(transactionId);
        if (originalTransaction == null) {
            return ManualOperationResult.failed("事务不存在");
        }

        // 2. 状态检查
        if (!canRetry(originalTransaction.getStatus())) {
            return ManualOperationResult.failed("当前状态不支持重试");
        }

        // 3. 恢复执行上下文
        TransactionExecutionContext executionContext = restoreExecutionContext(originalTransaction);

        // 4. 根据执行类型选择重试策略
        if ("ANNOTATION".equals(executionContext.getExecutionType())) {
            return retryAnnotationTransaction(originalTransaction, executionContext, operatorId);
        } else {
            return retryProgrammaticTransaction(originalTransaction, executionContext, operatorId);
        }
    }

    /**
     * 重试注解式事务
     */
    private ManualOperationResult retryAnnotationTransaction(
            DistributedTransactionDO originalTransaction,
            TransactionExecutionContext context,
            String operatorId) {

        try {
            // 1. 创建新的事务实例（保持原有配置）
            DistributedTransactionDO newTransaction = cloneTransaction(originalTransaction);
            newTransaction.setStatus(DistributedTransactionStatus.PENDING);

            // 2. 通过反射重新调用业务方法
            Object result = invokeBusinessMethod(context);

            // 3. 记录操作日志
            recordManualOperation(originalTransaction.getTransactionId(),
                    "RETRY", operatorId, "SUCCESS", null);

            return ManualOperationResult.success("事务重试成功", result);

        } catch (Exception e) {
            recordManualOperation(originalTransaction.getTransactionId(),
                    "RETRY", operatorId, "FAILED", e.getMessage());
            return ManualOperationResult.failed("事务重试失败: " + e.getMessage());
        }
    }

    /**
     * 通过反射调用业务方法
     */
    private Object invokeBusinessMethod(TransactionExecutionContext context) throws Exception {
        // 1. 获取业务类实例
        Class<?> businessClass = Class.forName(context.getBusinessClassName());
        Object businessInstance = applicationContext.getBean(businessClass);

        // 2. 获取方法
        Method businessMethod = findMethod(businessClass, context);

        // 3. 恢复方法参数
        Object[] args = deserializeArgs(context.getBusinessMethodArgs());

        // 4. 调用方法
        return businessMethod.invoke(businessInstance, args);
    }
}
```

### 4.2 事务回滚实现

1. **状态检查**: 验证事务是否可回滚
2. **模式判断**: 根据事务模式选择回滚策略
3. **AT模式回滚**:
    - 查询UndoLog记录
    - 执行反向SQL操作
    - 清理UndoLog
4. **Saga模式回滚**:
    - 按逆序执行补偿操作
    - 更新步骤状态
5. **状态更新**: 更新事务状态为UNDONE

### 4.3 事务提交实现

1. **状态检查**: 验证事务是否处于可提交状态
2. **资源检查**: 确认所有资源锁定状态
3. **提交执行**: 调用各参与方的提交接口
4. **状态更新**: 更新事务状态为COMMITTED
5. **资源释放**: 释放相关资源和锁

### 4.4 取消事务实现

1. **状态检查**: 验证事务是否可取消
2. **停止执行**: 设置取消标志，中断正在执行的步骤
3. **资源清理**: 清理已分配的资源
4. **状态更新**: 更新事务状态为CANCELLED

## 5. 安全控制

### 5.1 权限控制

- 操作权限验证：只有具备相应权限的用户才能执行手动操作
- 操作审计：记录所有手动操作的详细信息
- 操作确认：重要操作需要二次确认

### 5.2 并发控制

- 乐观锁：使用版本号防止并发修改
- 状态互斥：同一事务同时只能有一个手动操作
- 超时控制：手动操作设置超时时间

## 6. 监控告警

### 6.1 操作监控

- 手动操作成功率统计
- 操作响应时间监控
- 异常操作告警

### 6.2 审计日志

- 操作人员追踪
- 操作时间记录
- 操作结果统计

## 7. 前端交互设计

### 7.1 操作按钮

- 根据事务状态动态显示可用操作
- 操作前显示确认对话框
- 操作过程显示进度提示

### 7.2 操作结果反馈

- 成功操作显示成功提示
- 失败操作显示详细错误信息
- 操作完成后自动刷新事务状态

## 8. 配置参数

```properties
# 手动操作配置
tipray.transaction.manual.enabled=true
tipray.transaction.manual.timeout=30000
tipray.transaction.manual.retry.max-attempts=3
tipray.transaction.manual.audit.enabled=true
```

## 9. 实施计划

### 阶段一：基础功能实现

1. 创建数据表结构
2. 实现Service层核心逻辑
3. 实现Controller层API接口

### 阶段二：前端集成

1. 修改事务列表页面操作按钮
2. 实现操作确认对话框
3. 集成操作结果反馈

### 阶段三：完善功能

1. 添加权限控制
2. 实现审计日志
3. 添加监控告警

### 阶段四：测试验证

1. 单元测试
2. 集成测试
3. 压力测试

这个设计文档涵盖了手动事务操作功能的完整实现方案，包括技术架构、数据模型、实现细节、安全控制等各个方面。您觉得这个设计方案如何？有需要调整或补充的地方吗？
