# 简洁超时监控器使用示例

## 概述

`TransactionTimeoutMonitor` 是一个简洁的超时监控器，支持对 lambda 方法进行超时监控。

## 核心特性

1. **简单易用**：只需传入 lambda 方法和超时时间
2. **同步监控**：支持同步超时检查，超时时抛出异常
3. **异步监控**：支持异步超时检查，超时时执行回调
4. **超时检查**：支持简单的超时状态检查

## 使用示例

### 1. 基本用法

```java
@Autowired
private TransactionTimeoutMonitor timeoutMonitor;

// 监控方法执行，超时时抛出异常
try {
    String result = timeoutMonitor.executeWithTimeout(
        () -> {
            // 你的业务逻辑
            return remoteService.call();
        },
        5000L, // 5秒超时
        "远程服务调用"
    );
    
    System.out.println("调用成功: " + result);
    
} catch (TimeoutException e) {
    System.err.println("调用超时: " + e.getMessage());
}
```

### 2. Runnable 方法监控

```java
// 监控无返回值的方法
try {
    timeoutMonitor.executeWithTimeout(
        () -> {
            // 你的业务逻辑
            sendNotification();
        },
        3000L, // 3秒超时
        "发送通知"
    );
    
    System.out.println("通知发送成功");
    
} catch (TimeoutException e) {
    System.err.println("通知发送超时: " + e.getMessage());
}
```

### 3. 异步监控

```java
// 异步监控，超时时执行回调
CompletableFuture<String> future = timeoutMonitor.executeWithTimeoutAsync(
    () -> {
        // 你的业务逻辑
        return longRunningOperation();
    },
    10000L, // 10秒超时
    "长时间运行的操作",
    () -> {
        // 超时回调
        System.err.println("操作超时，执行清理逻辑");
        cleanup();
    }
);

// 处理结果
future.whenComplete((result, throwable) -> {
    if (throwable != null) {
        System.err.println("操作失败: " + throwable.getMessage());
    } else {
        System.out.println("操作成功: " + result);
    }
});
```

### 4. 简单超时检查

```java
// 记录开始时间
long startTime = System.currentTimeMillis();

// 执行业务逻辑
doSomething();

// 检查是否超时
boolean isTimeout = timeoutMonitor.isTimeout(
    startTime,
    5000L, // 5秒超时
    "业务操作"
);

if (isTimeout) {
    System.err.println("操作超时，需要处理");
    handleTimeout();
}
```

### 5. 在分布式事务中使用

```java
@Service
public class OrderService {
    
    @Autowired
    private TransactionTimeoutMonitor timeoutMonitor;
    
    @DistributedTransaction
    public void createOrder(Order order) {
        try {
            // 监控库存扣减操作
            timeoutMonitor.executeWithTimeout(
                () -> {
                    inventoryService.deductStock(order.getItems());
                },
                3000L, // 3秒超时
                "库存扣减"
            );
            
            // 监控支付操作
            PaymentResult paymentResult = timeoutMonitor.executeWithTimeout(
                () -> {
                    return paymentService.pay(order.getAmount());
                },
                5000L, // 5秒超时
                "支付处理"
            );
            
            // 保存订单
            orderRepository.save(order);
            
        } catch (TimeoutException e) {
            log.error("订单创建超时: {}", e.getMessage());
            throw new BusinessException("订单创建超时，请稍后重试");
        }
    }
}
```

### 6. 结合重试机制使用

```java
@Service
public class RemoteCallService {
    
    @Autowired
    private TransactionTimeoutMonitor timeoutMonitor;
    
    @Autowired
    private RetryManager retryManager;
    
    public String callRemoteService(String request) {
        return retryManager.executeWithRetry(() -> {
            // 在重试中使用超时监控
            return timeoutMonitor.executeWithTimeout(
                () -> {
                    return httpClient.post("/api/service", request);
                },
                2000L, // 每次调用2秒超时
                "远程服务调用"
            );
        }, "远程服务调用", 3); // 最多重试3次
    }
}
```

## 异常处理

### 1. TimeoutException
当操作超时时抛出，包含详细的超时信息：

```java
try {
    result = timeoutMonitor.executeWithTimeout(operation, 5000L, "操作名称");
} catch (TimeoutException e) {
    // e.getMessage() 包含: "操作执行超时: 操作名称 (超时时间: 5000ms)"
    log.error("操作超时: {}", e.getMessage());
}
```

### 2. InterruptedException
当操作被中断时，会自动处理中断状态并抛出 RuntimeException。

### 3. ExecutionException
当操作执行过程中发生异常时，会将原始异常包装后抛出。

## 日志输出

监控器会自动输出相关日志：

```
DEBUG - 操作执行成功: 远程服务调用
WARN  - 操作执行超时: 远程服务调用 - 超时时间: 5000ms
WARN  - 异步操作执行超时: 长时间操作 - 超时时间: 10000ms
WARN  - 检测到超时: 业务操作 - 执行时间: 6000ms, 超时限制: 5000ms
```

## 最佳实践

1. **合理设置超时时间**：根据业务特性设置合适的超时时间
2. **提供清晰的操作名称**：便于日志分析和问题排查
3. **异常处理**：妥善处理 TimeoutException，提供用户友好的错误信息
4. **结合重试**：对于网络调用等可重试操作，结合重试机制使用
5. **异步操作**：对于长时间运行的操作，考虑使用异步监控

## 配置说明

超时监控器使用独立的线程池，无需额外配置。线程池会自动管理，使用守护线程，不会阻止应用程序关闭。

如果需要调整线程池配置，可以通过继承 `TransactionTimeoutMonitor` 类并重写构造方法来实现。
