# Tipray分布式事务框架日志规范

## 概述

本文档定义了tipray-distributed-transaction框架的统一日志打印规范，旨在提供美观、易读、便于排查问题的日志输出格式。

## 设计原则

### 1. 统一性
- 所有日志使用统一的格式和工具类
- 保持一致的命名规范和输出风格

### 2. 可读性
- 简洁明了的信息展示
- 关键信息突出显示
- 合理的信息分层

### 3. 实用性
- 便于问题排查和性能分析
- 支持日志系统集成（ELK等）
- 可配置的详细程度

### 4. 非冗余性
- 避免重复信息
- 精简ID显示
- 结构化上下文信息

## 日志格式规范

### 统一格式模板

```
[LEVEL] [TIMESTAMP] [THREAD] [TX-ID] [BRANCH-ID] [PHASE] - MESSAGE [CONTEXT]
```

### 字段说明

| 字段 | 说明 | 示例 | 备注 |
|------|------|------|------|
| LEVEL | 日志级别 | INFO, WARN, ERROR | 标准slf4j级别 |
| TIMESTAMP | 时间戳 | 2025-01-29 20:03:10.777 | 由logback自动添加 |
| THREAD | 线程名 | http-exec-9 | 由logback自动添加 |
| TX-ID | 事务ID | tx-1753790590777-9855 | 完整事务ID |
| BRANCH-ID | 分支ID | 1753790590774255 | 完整分支ID，无分支显示---- |
| PHASE | 事务阶段 | BEGIN, EXEC, COMMIT等 | 标识当前操作阶段 |
| MESSAGE | 主要信息 | 事务开始, 分支注册等 | 简洁描述当前操作 |
| CONTEXT | 上下文 | [mode=AT, cost=10ms] | 键值对形式的补充信息 |

## 日志级别定义

### TRACE级别
- 详细的调试信息
- 方法进入/退出
- 参数详细信息
- 仅在开发调试时启用

### DEBUG级别
- 开发调试信息
- 状态变更详情
- 内部处理逻辑
- 开发环境建议启用

### INFO级别
- 关键业务流程
- 事务生命周期节点
- 重要状态变更
- 生产环境推荐级别

### WARN级别
- 异常但可恢复的情况
- 重试操作
- 降级处理
- 需要关注但不影响业务

### ERROR级别
- 错误和异常
- 事务失败
- 系统异常
- 需要立即处理

## 事务阶段标识

| 阶段 | 说明 | 使用场景 |
|------|------|----------|
| BEGIN | 事务开始 | 事务初始化 |
| REGISTER | 分支注册 | 分支事务注册 |
| EXEC | 执行阶段 | 业务逻辑执行 |
| STATE | 状态转换 | 状态机状态变更 |
| COMMIT | 提交阶段 | 事务提交操作 |
| ROLLBACK | 回滚阶段 | 事务回滚操作 |
| PERF | 性能监控 | 性能指标记录 |
| ERROR | 错误处理 | 异常和错误 |

## 日志输出示例

### 正常流程
```
[INFO ] [2025-01-29 20:03:10.777] [http-exec-9] [tx-1753790590777-9855] [----] [BEGIN] - 事务开始 [mode=AT, method=processData]
[INFO ] [2025-01-29 20:03:10.784] [http-exec-9] [tx-1753790590777-9855] [1753790590774255] [REGISTER] - 分支注册 [service=LOCAL, method=processData]
[INFO ] [2025-01-29 20:03:10.785] [http-exec-9] [tx-1753790590777-9855] [1753790590774255] [EXEC] - 开始执行 [step=1, timeout=30s]
[INFO ] [2025-01-29 20:03:10.890] [http-exec-9] [tx-1753790590777-9855] [1753790590774255] [EXEC] - 执行完成 [result=SUCCESS, cost=105ms]
[INFO ] [2025-01-29 20:03:10.784] [http-exec-9] [tx-1753790590777-9855] [1753790590774255] [STATE] - UNKNOWN -> REGISTERED [event=REGISTER, cost=0ms]
[INFO ] [2025-01-29 20:03:10.995] [http-exec-9] [tx-1753790590777-9855] [----] [COMMIT] - 事务提交成功 [branches=3, total=218ms]
```

### 异常流程
```
[ERROR] [2025-01-29 20:03:11.123] [http-exec-9] [tx-1753790590777-9855] [1753790590774255] [ERROR] - 分支执行失败 [error=SQLException, msg=Connection timeout]
[WARN ] [2025-01-29 20:03:11.234] [http-exec-9] [tx-1753790590777-9855] [----] [ROLLBACK] - 事务回滚 [reason=分支执行失败, cause=SQLException]
```

## 配置说明

### 应用配置
```yaml
tipray:
  transaction:
    logging:
      level: INFO              # 日志级别：TRACE, DEBUG, INFO, WARN, ERROR
      enable-performance: true # 是否启用性能日志
      enable-state-detail: false # 是否启用详细状态日志
      max-context-length: 200  # 上下文信息最大长度
      enable-method-trace: false # 是否启用方法追踪
```

### Logback配置示例
```xml
<configuration>
    <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>[%-5level] [%d{yyyy-MM-dd HH:mm:ss.SSS}] [%thread] %msg%n</pattern>
        </encoder>
    </appender>
    
    <!-- 分布式事务日志 -->
    <logger name="com.tipray.transaction" level="INFO" additivity="false">
        <appender-ref ref="CONSOLE"/>
    </logger>
    
    <root level="INFO">
        <appender-ref ref="CONSOLE"/>
    </root>
</configuration>
```

## 使用规范

### 1. 统一使用TransactionLogger
- 禁止直接使用slf4j Logger
- 所有日志通过TransactionLogger工具类输出
- 保持日志格式的一致性

### 2. 上下文信息规范
- 使用键值对形式：`[key1=value1, key2=value2]`
- 关键信息优先：耗时、状态、错误类型等
- 避免敏感信息：密码、个人信息等

### 3. 错误日志规范
- 包含错误类型和简要描述
- 提供足够的上下文信息
- 避免堆栈信息冗余

### 4. 性能日志规范
- 记录关键操作耗时
- 包含相关性能指标
- 便于性能分析和优化

## 最佳实践

### 1. 日志级别选择
- 生产环境建议使用INFO级别
- 开发环境可使用DEBUG级别
- 问题排查时临时启用TRACE级别

### 2. 信息完整性
- 确保事务ID在整个生命周期中一致
- 分支ID与事务ID关联清晰
- 时间线信息完整

### 3. 性能考虑
- 避免在高频路径上输出过多日志
- 合理使用日志级别控制输出
- 考虑异步日志输出

### 4. 监控集成
- 日志格式便于ELK等系统解析
- 支持基于关键字的告警配置
- 便于日志统计和分析

## 工具类使用

所有日志输出必须通过 `TransactionLogger` 工具类，该类提供以下主要方法：

### 基础日志方法
- `logTransactionBegin()` - 事务开始日志
- `logBranchRegister()` - 分支注册日志
- `logBranchExecute()` - 分支执行日志
- `logStateTransition()` - 状态转换日志
- `logTransactionCommit()` - 事务提交日志
- `logTransactionRollback()` - 事务回滚日志
- `logError()` - 错误日志
- `logPerformance()` - 性能日志

### 自动计算耗时的便捷方法
为了简化耗时计算，提供了以下便捷方法：

- `logTransactionCommitWithStartTime(txId, branchCount, startTime)` - 自动计算事务总耗时
- `logBranchCommitWithStartTime(txId, branchId, startTime)` - 自动计算分支提交耗时
- `logBranchRollbackWithStartTime(txId, branchId, startTime)` - 自动计算分支回滚耗时
- `logPerformanceWithStartTime(txId, phase, startTime, metrics...)` - 自动计算性能耗时

这些方法支持两种时间类型：
- `LocalDateTime` - 适用于从 `TransactionContext.getStartTime()` 获取的时间
- `long` - 适用于 `System.currentTimeMillis()` 获取的毫秒时间戳

### 使用示例
```java
// 使用 LocalDateTime（推荐）
transactionLogger.logTransactionCommitWithStartTime(txId, branchCount, context.getStartTime());

// 使用毫秒时间戳
long startTime = System.currentTimeMillis();
// ... 执行业务逻辑 ...
transactionLogger.logBranchCommitWithStartTime(txId, branchId, startTime);
```

详细API文档请参考工具类源码注释。
