# Tipray 分布式事务框架文档

## 📖 文档导航

### 概述

- [什么是Tipray分布式事务](手册/01-概述/什么是Tipray分布式事务.md) - 框架介绍和核心概念
- [架构设计](手册/01-概述/架构设计.md) - 整体架构和设计思想
- [业务背景](手册/01-概述/业务背景.md) - 解决的业务问题和应用场景
- [术语表](手册/01-概述/术语表.md) - 核心术语和概念解释

### 用户文档

- [快速开始](手册/02-用户文档/快速开始.md) - 5分钟上手指南
- [安装配置](手册/02-用户文档/安装配置.md) - 详细安装和配置说明
- [注解使用](手册/02-用户文档/注解使用.md) - @DistributedTransaction注解详解
- [配置参考](手册/02-用户文档/配置参考.md) - 完整配置项说明
- [监控界面](手册/02-用户文档/监控界面.md) - Web UI使用指南

### 事务模式

- [AT模式](手册/03-事务模式/AT模式.md) - 强一致性事务模式
- [Saga模式](手册/03-事务模式/Saga模式.md) - 最终一致性事务模式
- [模式对比](手册/03-事务模式/模式对比.md) - 两种模式的对比和选择

### 开发者指南

- [核心组件](手册/04-开发者指南/核心组件.md) - 核心组件和接口详解

### 最佳实践

- [故障排查](手册/05-最佳实践/故障排查.md) - 常见问题和解决方案

### 示例代码

- [AT模式示例](手册/06-示例代码/AT模式示例.md) - AT模式完整示例

## 🚀 快速导航

### 新手入门

1. [什么是Tipray分布式事务](手册/01-概述/什么是Tipray分布式事务.md) - 了解框架基本概念
2. [快速开始](手册/02-用户文档/快速开始.md) - 快速体验框架功能
3. [AT模式](手册/03-事务模式/AT模式.md) - 学习最常用的事务模式

### 深入使用

1. [配置参考](手册/02-用户文档/配置参考.md) - 掌握所有配置选项
2. [核心组件](手册/04-开发者指南/核心组件.md) - 深入理解框架设计
3. [监控界面](手册/02-用户文档/监控界面.md) - 掌握监控和运维

### 问题解决

1. [故障排查](手册/05-最佳实践/故障排查.md) - 解决常见问题
2. [注解使用](手册/02-用户文档/注解使用.md) - 掌握正确的注解用法

## 📋 版本信息

- **当前版本**: 1.0.0
- **Java版本**: 1.8+
- **Spring Boot版本**: 2.7.x
- **数据库支持**: MySQL 5.7+, PostgreSQL 9.6+
- **缓存支持**: Redis 3.0+ (可选)

## 🔧 技术支持

- **GitHub**: [tipray-distributed-transaction](https://github.com/tipray/tipray-distributed-transaction)
- **文档更新**: 本文档随框架版本同步更新
- **反馈建议**: 欢迎通过Issue提交问题和建议

## 📝 文档贡献

本文档采用Markdown格式编写，欢迎贡献：

1. 发现错误或不准确的内容
2. 补充缺失的使用场景
3. 提供更好的示例代码
4. 改进文档结构和表达

---

**注意**: 本文档基于框架1.0.0版本编写，使用前请确认版本兼容性。
