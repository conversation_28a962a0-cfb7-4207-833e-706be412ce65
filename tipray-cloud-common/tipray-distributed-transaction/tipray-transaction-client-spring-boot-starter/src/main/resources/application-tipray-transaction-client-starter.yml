# Tipray分布式事务客户端Starter默认配置
# 这个配置文件会被自动加载，提供合理的默认值

tipray:
  transaction:
    client:
      # 启用事务客户端
      enabled: true
      
      # 客户端标识
      client-id: ${spring.application.name:tipray-client}-${random.int(1000,9999)}
      application-name: ${spring.application.name:unknown}
      
      # AT模式配置（默认启用）
      at:
        enabled: true
        resource-id: ${spring.application.name:tipray}-at-resource
        sql-parse-cache: true
        sql-parse-cache-size: 1000
        table-meta-cache: true
        table-meta-cache-expire-minutes: 30
      
      # UndoLog配置
      undo-log:
        table-name: undo_log
        compression-enabled: true
        compression-threshold: 4096
        serializer: jackson
        auto-create-table: true
        cleanup:
          enabled: true
          interval-hours: 24
          retention-days: 7
          batch-size: 1000
      
      # 数据源代理配置
      data-source-proxy:
        enabled: true
        global-lock-enabled: true
        global-lock-timeout: 30000
      
      # 网络配置
      network:
        connect-timeout: 5000
        read-timeout: 10000
        retry-count: 3
        retry-interval: 1000
      
      # 监控配置
      monitor:
        enabled: true
        port: ${server.port:8080}
        path: /transaction
        health-check-enabled: true
        metrics-enabled: true

# 日志配置优化
logging:
  level:
    com.tipray.transaction.client: INFO
    io.seata: WARN
    root: INFO
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"

# 管理端点配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics
      base-path: /actuator
  endpoint:
    health:
      show-details: when-authorized
  health:
    defaults:
      enabled: true
