package com.tipray.transaction.client.infrastructure.datasource;

import com.tipray.transaction.client.infrastructure.context.TransactionContextHolder;
import com.tipray.transaction.core.config.propertie.TiprayTransactionClientProperties;
import io.seata.core.context.RootContext;
import io.seata.rm.datasource.ConnectionProxy;
import io.seata.rm.datasource.DataSourceProxy;
import io.seata.rm.datasource.undo.UndoLogManagerFactory;
import lombok.extern.slf4j.Slf4j;

import java.sql.Connection;
import java.sql.SQLException;

import static com.tipray.transaction.client.infrastructure.context.TransactionContextHolder.getGlobalTransactionId;

/**
 * Tipray 连接代理 - 基于 Seata ConnectionProxy
 * <p>
 * 完全基于 Seata 的 ConnectionProxy 实现，确保与 Seata 的完全兼容性
 * 同时集成 Tipray 的事务上下文管理
 * <p>
 * 主要特性：
 * 1. 直接继承 Seata 的 ConnectionProxy
 * 2. 使用 Seata 的成熟 UndoLog 生成机制
 * 3. 完全兼容 Seata 的 SQL 解析和镜像生成
 * 4. 集成 Tipray 的事务上下文管理
 * 5. 自动优化非事务场景的性能
 *
 * <AUTHOR>
 * @version 2.0
 * @since 2025-01-27
 */
@Slf4j
public class TiprayConnectionProxy extends ConnectionProxy {

    private final TiprayTransactionClientProperties properties;
    private final String resourceId;

    /**
     * UndoLog 是否已刷新标志
     */
    private volatile boolean undoLogFlushed = false;

    /**
     * 构造函数
     */
    public TiprayConnectionProxy(DataSourceProxy dataSourceProxy, Connection targetConnection,
                                 TiprayTransactionClientProperties properties) {
        super(dataSourceProxy, targetConnection);
        this.properties = properties;
        this.resourceId = dataSourceProxy.getResourceId();

        String tiprayTxId = getGlobalTransactionId();
        String seataTxId = RootContext.getXID();
        boolean inSeataTransaction = RootContext.inGlobalTransaction();

        log.debug("创建Tipray连接代理: resourceId={}, tiprayTxId={}, seataTxId={}",
                resourceId, tiprayTxId, seataTxId);
    }

    /**
     * 重写 bind 方法，集成 Tipray 事务上下文
     */
    @Override
    public void bind(String xid) {
        // 重置连接状态，防止连接复用时的状态污染
        resetConnectionState();

        // 检查是否在 Tipray 全局事务中
        String tiprayTxId = getGlobalTransactionId();
        String seataTxId = RootContext.getXID();

        log.debug("事务绑定: xid={}, tiprayTxId={}, resourceId={}", xid, tiprayTxId, resourceId);

        if (tiprayTxId != null && tiprayTxId.equals(xid)) {
            // 使用 Tipray 事务上下文，同时设置 Seata 的 RootContext
            RootContext.bind(xid);
            super.bind(xid);
            // 设置 branchId（使用 Tipray 的 branchId）
            setBranchIdToContext(TransactionContextHolder.getBranchId());
            log.debug("绑定Tipray事务上下文: globalTxId={}, branchId={}, resourceId={}",
                    xid, TransactionContextHolder.getBranchId(), resourceId);
        } else if (tiprayTxId != null) {
            // 如果有不同的全局事务ID，使用 Tipray 的，同时设置 Seata 的 RootContext
            RootContext.bind(tiprayTxId);
            super.bind(tiprayTxId);
            // 设置 branchId（使用 Tipray 的 branchId）
            setBranchIdToContext(TransactionContextHolder.getBranchId());
            log.debug("使用Tipray事务上下文: globalTxId={}, branchId={}, resourceId={}",
                    tiprayTxId, TransactionContextHolder.getBranchId(), resourceId);
        } else {
            // 检查是否在 Seata 事务中
            if (seataTxId != null && !seataTxId.trim().isEmpty()) {
                super.bind(seataTxId);
                log.debug("使用Seata事务上下文: globalTxId={}, resourceId={}", seataTxId, resourceId);
            } else {
                // 没有全局事务，不绑定
                log.debug("没有全局事务，跳过绑定: resourceId={}", resourceId);
            }
        }
    }

    /**
     * 重写 checkLock 方法，集成锁检查逻辑
     */
    @Override
    public void checkLock(String lockKeys) throws SQLException {
        // 如果没有全局事务，跳过锁检查
        if (!isInGlobalTransaction()) {
            log.debug("没有全局事务，跳过锁检查: resourceId={}", resourceId);
            return;
        }

        // 调用父类的锁检查逻辑
        super.checkLock(lockKeys);
        log.debug("锁检查完成: lockKeys={}, resourceId={}", lockKeys, resourceId);
    }

    /**
     * 重写 commit 方法，只生成 UndoLog 但不向 TC 注册分支事务
     */
    @Override
    public void commit() throws SQLException {
        if (isInGlobalTransaction()) {
            String currentTxId = getCurrentGlobalTransactionId();
            log.debug("提交事务: globalTxId={}, resourceId={}", currentTxId, resourceId);

            try {
                // 检查是否已经刷新过 UndoLog，避免重复刷新
                if (!undoLogFlushed) {
                    synchronized (this) {
                        if (!undoLogFlushed) {
                            try {
                                log.debug("开始刷新UndoLog到数据库");
                                UndoLogManagerFactory.getUndoLogManager(getDbType()).flushUndoLogs(this);
                                undoLogFlushed = true;
                                log.debug("UndoLog刷新完成");
                            } catch (Exception undoLogException) {
                                log.debug("UndoLog刷新异常（可能没有UndoLog）: {}", undoLogException.getMessage());
                            }
                        } else {
                            log.debug("UndoLog已刷新，跳过");
                        }
                    }
                } else {
                    log.debug("=== UndoLog刷新 === UndoLog已刷新，跳过");
                }

                // 直接提交本地事务
                getTargetConnection().commit();
                log.debug("事务提交成功: globalTxId={}, resourceId={}", currentTxId, resourceId);
            } catch (SQLException e) {
                log.error("事务提交失败: globalTxId={}, resourceId={}", currentTxId, resourceId, e);

                // 提交失败时，尝试回滚以确保连接状态一致
                try {
                    log.warn("提交失败，尝试回滚以清理连接状态");
                    getTargetConnection().rollback();
                } catch (SQLException rollbackEx) {
                    log.error("回滚清理失败，连接状态不明: {}", rollbackEx.getMessage());
                    e.addSuppressed(rollbackEx);
                }
                throw e;
            }
        } else {
            // 没有全局事务，直接提交
            try {
                getTargetConnection().commit();
                log.debug("直接提交本地事务: resourceId={}", resourceId);
            } catch (SQLException e) {
                log.error("本地事务提交失败: resourceId={}", resourceId, e);
                // 尝试回滚清理状态
                try {
                    getTargetConnection().rollback();
                } catch (SQLException rollbackEx) {
                    log.error("本地事务回滚清理失败: {}", rollbackEx.getMessage());
                    e.addSuppressed(rollbackEx);
                }
                throw e;
            }
        }
    }

    /**
     * 重写 rollback 方法，避免向 TC 报告分支状态
     * 增强对DruidPooledConnection的transactionInfo为空问题的处理
     */
    @Override
    public void rollback() throws SQLException {
        if (isInGlobalTransaction()) {
            String currentTxId = getCurrentGlobalTransactionId();
            log.debug("回滚事务: globalTxId={}, resourceId={}", currentTxId, resourceId);

            try {
                // 检查底层连接类型，针对DruidPooledConnection做特殊处理
                Connection targetConn = getTargetConnection();
                if (targetConn.getClass().getName().contains("DruidPooledConnection")) {
                    log.debug("检测到DruidPooledConnection，增强事务回滚保护: globalTxId={}, resourceId={}",
                             currentTxId, resourceId);

                    // 使用反射检查transactionInfo状态
                    try {
                        java.lang.reflect.Field transactionInfoField = targetConn.getClass().getDeclaredField("transactionInfo");
                        transactionInfoField.setAccessible(true);
                        Object transactionInfo = transactionInfoField.get(targetConn);

                        if (transactionInfo == null) {
                            log.warn("检测到DruidPooledConnection的transactionInfo为空，这可能导致回滚失败: globalTxId={}, resourceId={}",
                                    currentTxId, resourceId);
                            // 尝试重新设置事务信息或使用备用回滚方式
                            // 这里可以根据具体情况实现恢复逻辑
                        }
                    } catch (Exception reflectionException) {
                        log.debug("无法通过反射检查transactionInfo状态: {}", reflectionException.getMessage());
                    }
                }

                // 直接回滚本地事务，不向 TC 报告分支状态
                targetConn.rollback();
                log.debug("事务回滚成功: globalTxId={}, resourceId={}", currentTxId, resourceId);
            } catch (SQLException e) {
                // 增强错误信息，帮助诊断transactionInfo为空问题
                String errorMsg = String.format("事务回滚失败: globalTxId=%s, resourceId=%s, 错误信息=%s",
                                               currentTxId, resourceId, e.getMessage());
                if (e.getMessage() != null && e.getMessage().contains("transactionInfo")) {
                    errorMsg += " [可能原因：DruidPooledConnection的transactionInfo被提前清理]";
                }
                log.error(errorMsg, e);
                throw e;
            }
        } else {
            // 没有全局事务，直接回滚
            try {
                getTargetConnection().rollback();
                log.debug("直接回滚本地事务: resourceId={}", resourceId);
            } catch (SQLException e) {
                log.error("本地事务回滚失败: resourceId={}", resourceId, e);
                // 回滚失败是严重问题，但无法进一步清理，只能抛出异常
                throw e;
            }
        }
    }

    /**
     * 检查是否在全局事务中
     */
    public boolean isInGlobalTransaction() {
        String tiprayTxId = getGlobalTransactionId();
        String seataTxId = RootContext.getXID();

        return (tiprayTxId != null && !tiprayTxId.trim().isEmpty()) ||
                (seataTxId != null && !seataTxId.trim().isEmpty());
    }

    /**
     * 获取当前全局事务ID
     */
    public String getCurrentGlobalTransactionId() {
        String tiprayTxId = getGlobalTransactionId();
        if (tiprayTxId != null && !tiprayTxId.trim().isEmpty()) {
            return tiprayTxId;
        }

        return RootContext.getXID();
    }

    /**
     * 检查是否需要生成UndoLog
     */
    public boolean shouldGenerateUndoLog() {
        return isInGlobalTransaction();
    }

    /**
     * 获取配置属性
     */
    public TiprayTransactionClientProperties getProperties() {
        return properties;
    }

    /**
     * 获取资源ID
     */
    public String getResourceId() {
        return resourceId;
    }

    /**
     * 重写 prepareStatement 方法，优化非事务场景
     * <p>
     * 关键修复：确保非事务场景下的PreparedStatement也被正确管理
     */
    @Override
    public java.sql.PreparedStatement prepareStatement(String sql) throws SQLException {
        log.debug("prepareStatement: sql={}, 全局事务ID={}", sql, getGlobalTransactionId());

        // 在全局事务中，使用 Seata 的代理
        if (isInGlobalTransaction()) {
            log.debug("在全局事务中，使用Seata代理PreparedStatement");
            java.sql.PreparedStatement statement = super.prepareStatement(sql);
            log.debug("Seata代理PreparedStatement创建完成: {}", statement.getClass().getSimpleName());
            return statement;
        }

        // 非事务场景：也要确保PreparedStatement被正确管理
        log.debug("不在全局事务中，使用原始PreparedStatement");
        return getTargetConnection().prepareStatement(sql);
    }

    /**
     * 重写 prepareStatement 方法（带参数）
     */
    @Override
    public java.sql.PreparedStatement prepareStatement(String sql, int autoGeneratedKeys) throws SQLException {
        if (!isInGlobalTransaction()) {
            return getTargetConnection().prepareStatement(sql, autoGeneratedKeys);
        }

        return super.prepareStatement(sql, autoGeneratedKeys);
    }

    /**
     * 重写 prepareStatement 方法（带列索引）
     */
    @Override
    public java.sql.PreparedStatement prepareStatement(String sql, int[] columnIndexes) throws SQLException {
        if (!isInGlobalTransaction()) {
            return getTargetConnection().prepareStatement(sql, columnIndexes);
        }

        return super.prepareStatement(sql, columnIndexes);
    }

    /**
     * 重写 prepareStatement 方法（带列名）
     */
    @Override
    public java.sql.PreparedStatement prepareStatement(String sql, String[] columnNames) throws SQLException {
        if (!isInGlobalTransaction()) {
            return getTargetConnection().prepareStatement(sql, columnNames);
        }

        return super.prepareStatement(sql, columnNames);
    }

    /**
     * 重写 createStatement 方法
     */
    @Override
    public java.sql.Statement createStatement() throws SQLException {
        if (!isInGlobalTransaction()) {
            return getTargetConnection().createStatement();
        }

        return super.createStatement();
    }

    /**
     * 重写 createStatement 方法（带参数）
     */
    @Override
    public java.sql.Statement createStatement(int resultSetType, int resultSetConcurrency) throws SQLException {
        if (!isInGlobalTransaction()) {
            return getTargetConnection().createStatement(resultSetType, resultSetConcurrency);
        }

        return super.createStatement(resultSetType, resultSetConcurrency);
    }

    /**
     * 重写 createStatement 方法（带更多参数）
     */
    @Override
    public java.sql.Statement createStatement(int resultSetType, int resultSetConcurrency, int resultSetHoldability) throws SQLException {
        if (!isInGlobalTransaction()) {
            return getTargetConnection().createStatement(resultSetType, resultSetConcurrency, resultSetHoldability);
        }

        return super.createStatement(resultSetType, resultSetConcurrency, resultSetHoldability);
    }

    @Override
    public String toString() {
        return "TiprayConnectionProxy{" +
                "resourceId='" + resourceId + '\'' +
                ", inGlobalTransaction=" + isInGlobalTransaction() +
                ", currentTxId='" + getCurrentGlobalTransactionId() + '\'' +
                '}';
    }

    /**
     * 使用反射设置 branchId 到 ConnectionContext
     */
    private void setBranchIdToContext(Long branchId) {
        if (branchId == null) {
            return;
        }

        try {
            java.lang.reflect.Method setBranchIdMethod = getContext().getClass().getDeclaredMethod("setBranchId", Long.class);
            setBranchIdMethod.setAccessible(true);
            setBranchIdMethod.invoke(getContext(), branchId);
            log.debug("=== 事务绑定 === 成功设置branchId: {}", branchId);
        } catch (Exception e) {
            log.warn("=== 事务绑定 === 设置branchId失败: {}", e.getMessage());
        }
    }

    /**
     * 重写 close 方法，确保连接正确释放
     */
    @Override
    public void close() throws SQLException {
        String currentTxId = getCurrentGlobalTransactionId();
        log.debug("关闭连接: globalTxId={}, resourceId={}", currentTxId, resourceId);

        try {
            // 调用父类的 close 方法
            super.close();
            log.debug("连接关闭成功: globalTxId={}, resourceId={}", currentTxId, resourceId);
        } catch (SQLException e) {
            log.error("连接关闭失败: globalTxId={}, resourceId={}", currentTxId, resourceId, e);
            // 即使关闭失败，也要尝试关闭底层连接
            try {
                getTargetConnection().close();
                log.debug("底层连接强制关闭成功");
            } catch (SQLException ex) {
                log.error("底层连接强制关闭失败: {}", ex.getMessage());
            }
            throw e;
        }
    }

    /**
     * 重置连接状态，防止连接复用时的状态污染
     */
    private void resetConnectionState() {
        undoLogFlushed = false;
        log.debug("连接状态已重置: resourceId={}", resourceId);
    }
}
