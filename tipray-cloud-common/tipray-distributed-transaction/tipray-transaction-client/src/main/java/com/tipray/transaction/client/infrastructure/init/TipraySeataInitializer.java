package com.tipray.transaction.client.infrastructure.init;

import com.tipray.transaction.core.config.propertie.TiprayTransactionClientProperties;
import io.seata.config.ConfigurationFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Component;

import java.lang.reflect.Field;

/**
 * Tipray Seata 初始化器
 * <p>
 * 负责初始化 Seata 的配置，避免复杂的配置依赖
 * <p>
 * 主要特性：
 * 1. 设置 Seata 的配置工厂
 * 2. 初始化必要的 Seata 组件
 * 3. 避免 Seata 的配置中心依赖
 * 4. 提供基本的错误处理
 *
 * <AUTHOR>
 * @version 2.0
 * @since 2025-01-27
 */
@Slf4j
public class TipraySeataInitializer implements InitializingBean {

    private static final Object INIT_LOCK = new Object();
    private static volatile boolean initialized = false;

    // 静态初始化块，在类加载时就设置系统属性
    static {
        try {
            // 设置基本的系统属性
            System.setProperty("seata.config.type", "file");
            System.setProperty("seata.registry.type", "file");
            System.setProperty("seata.config.file.name", "file.conf");
            System.setProperty("seata.registry.file.name", "registry.conf");

            // 设置更多必要的配置
            System.setProperty("config.type", "file");
            System.setProperty("registry.type", "file");
            System.setProperty("config.file.name", "file.conf");
            System.setProperty("registry.file.name", "registry.conf");

            // 设置应用相关配置
            System.setProperty("seata.application.id", "tipray-transaction-client");
            System.setProperty("seata.tx.service.group", "tipray_tx_group");

            // 设置客户端配置
            System.setProperty("client.undo.logTable", "undo_log");
            System.setProperty("client.undo.compress.enable", "true");
            System.setProperty("client.undo.compress.type", "gzip");
            System.setProperty("client.undo.compress.threshold", "4k");  // 使用单字符单位格式
            System.setProperty("client.undo.logSerialization", "jackson");
            System.setProperty("client.rm.reportSuccessEnable", "false");
            System.setProperty("client.rm.tableMetaCheckEnable", "false");
        } catch (Exception e) {
            System.err.println("静态块初始化Seata配置失败: " + e.getMessage());
        }
    }

    private final TiprayTransactionClientProperties properties;

    public TipraySeataInitializer(TiprayTransactionClientProperties properties) {
        this.properties = properties;
    }

    /**
     * 静态初始化方法，确保 Seata 配置在任何地方都能被正确初始化
     */
    public static void ensureSeataInitialized() {
        if (!initialized) {
            synchronized (INIT_LOCK) {
                if (!initialized) {
                    try {
                        // 设置基本的系统属性
                        System.setProperty("seata.config.type", "file");
                        System.setProperty("seata.registry.type", "file");
                        System.setProperty("seata.config.file.name", "file.conf");
                        System.setProperty("seata.registry.file.name", "registry.conf");

                        // 设置更多必要的配置
                        System.setProperty("config.type", "file");
                        System.setProperty("registry.type", "file");
                        System.setProperty("config.file.name", "file.conf");
                        System.setProperty("registry.file.name", "registry.conf");

                        // 设置应用相关配置
                        System.setProperty("seata.application.id", "tipray-transaction-client");
                        System.setProperty("seata.tx.service.group", "tipray_tx_group");

                        // 设置客户端配置
                        System.setProperty("client.undo.logTable", "undo_log");
                        System.setProperty("client.undo.compress.enable", "true");
                        System.setProperty("client.undo.compress.type", "gzip");
                        System.setProperty("client.undo.compress.threshold", "4k");  // 使用单字符单位格式
                        System.setProperty("client.undo.logSerialization", "jackson");
                        System.setProperty("client.rm.reportSuccessEnable", "false");
                        System.setProperty("client.rm.tableMetaCheckEnable", "false");
                        initialized = true;
                    } catch (Exception e) {
                        log.warn("静态初始化Seata配置失败: {}", e.getMessage());
                    }
                }
            }
        }
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        try {
            // 首先设置系统属性，确保 Seata 能找到配置类型
            setSystemProperties();

            // 初始化 Seata 配置
            initializeSeataConfiguration();
        } catch (Exception e) {
            log.error("Tipray Seata配置初始化失败", e);
            // 不抛出异常，允许应用继续启动
        }
    }

    /**
     * 初始化 Seata 配置
     */
    private void initializeSeataConfiguration() {
        try {
            // 创建 Tipray 配置实例
            TipraySeataConfigurationFactory.TipraySeataConfiguration configuration =
                    (TipraySeataConfigurationFactory.TipraySeataConfiguration)
                            TipraySeataConfigurationFactory.getInstance(properties);

            // 尝试设置到 Seata 的 ConfigurationFactory 中
            setSeataConfigurationFactory(configuration);

            log.info("Seata配置工厂设置成功: {}", configuration);

        } catch (Exception e) {
            log.warn("设置Seata配置工厂失败，使用默认配置: {}", e.getMessage());
        }
    }

    /**
     * 设置 Seata 配置工厂
     */
    private void setSeataConfigurationFactory(TipraySeataConfigurationFactory.TipraySeataConfiguration configuration) {
        try {
            // 尝试多种可能的字段名
            Class<?> configFactoryClass = ConfigurationFactory.class;
            Field instanceField = null;

            // 尝试不同的字段名
            String[] possibleFieldNames = {
                    "CURRENT_FILE_INSTANCE",
                    "FILE_INSTANCE",
                    "instance",
                    "INSTANCE",
                    "configuration"
            };

            for (String fieldName : possibleFieldNames) {
                try {
                    instanceField = configFactoryClass.getDeclaredField(fieldName);
                    break;
                } catch (NoSuchFieldException e) {
                    // 继续尝试下一个字段名
                }
            }

            if (instanceField != null) {
                instanceField.setAccessible(true);
                instanceField.set(null, configuration);
                log.debug("通过反射设置Seata配置工厂成功: 字段名={}", instanceField.getName());
            } else {
                log.debug("未找到合适的配置字段，跳过反射设置");
            }

        } catch (Exception e) {
            log.debug("通过反射设置Seata配置工厂失败: {}", e.getMessage());
        }
    }

    /**
     * 设置系统属性
     */
    private void setSystemProperties() {
        try {
            // 首先设置 Seata 配置类型，这是最关键的
            setSystemPropertyIfNotExists("seata.config.type", "file");

            // 设置注册中心类型
            setSystemPropertyIfNotExists("seata.registry.type", "file");

            // 设置配置文件名（虽然我们不使用文件，但需要设置避免报错）
            setSystemPropertyIfNotExists("seata.config.file.name", "file.conf");
            setSystemPropertyIfNotExists("seata.registry.file.name", "registry.conf");

            // 设置 UndoLog 相关属性
            setSystemPropertyIfNotExists("client.undo.logTable", properties.getUndoLog().getTableName());
            setSystemPropertyIfNotExists("client.undo.compress.enable", String.valueOf(properties.getUndoLog().isCompressionEnabled()));
            setSystemPropertyIfNotExists("client.undo.compress.type", "gzip");
            // 将字节数转换为带单位的格式
            String thresholdValue = formatByteSize(properties.getUndoLog().getCompressionThreshold());
            setSystemPropertyIfNotExists("client.undo.compress.threshold", thresholdValue);

            // 设置序列化器
            setSystemPropertyIfNotExists("client.undo.logSerialization", "jackson");

            // 设置数据源相关属性
            setSystemPropertyIfNotExists("client.rm.datasource.autoCommit", "true");
            setSystemPropertyIfNotExists("client.rm.reportSuccessEnable", "false");
            setSystemPropertyIfNotExists("client.rm.tableMetaCheckEnable", "false");

            // 设置锁相关属性
            setSystemPropertyIfNotExists("client.rm.lock.retryInterval", "10");
            setSystemPropertyIfNotExists("client.rm.lock.retryTimes", "30");
            setSystemPropertyIfNotExists("client.rm.lock.retryPolicyBranchRollbackOnConflict", "true");

            // 设置应用名称
            setSystemPropertyIfNotExists("seata.application.id", properties.getApplicationName());
            setSystemPropertyIfNotExists("seata.tx.service.group", properties.getApplicationName() + "_tx_group");

            log.debug("系统属性设置完成");

        } catch (Exception e) {
            log.warn("设置系统属性失败: {}", e.getMessage());
        }
    }

    /**
     * 设置系统属性（如果不存在）
     */
    private void setSystemPropertyIfNotExists(String key, String value) {
        if (System.getProperty(key) == null) {
            System.setProperty(key, value);
            log.debug("设置系统属性: {}={}", key, value);
        } else {
            log.debug("系统属性已存在，跳过: {}={}", key, System.getProperty(key));
        }
    }

    /**
     * 将字节数格式化为 Seata 期望的单字符单位格式
     */
    private String formatByteSize(int bytes) {
        if (bytes >= 1024 * 1024 * 1024) {
            return (bytes / (1024 * 1024 * 1024)) + "g";
        } else if (bytes >= 1024 * 1024) {
            return (bytes / (1024 * 1024)) + "m";
        } else if (bytes >= 1024) {
            return (bytes / 1024) + "k";
        } else {
            // 对于小于1024的值，直接使用字节数加上'k'单位，避免无单位的情况
            return "1k";
        }
    }

    /**
     * 检查 Seata 配置是否正常
     */
    public boolean checkSeataConfiguration() {
        try {
            // 尝试获取配置
            io.seata.config.Configuration config = ConfigurationFactory.getInstance();
            String undoLogTable = config.getConfig("client.undo.logTable", "undo_log");

            log.debug("Seata配置检查成功: undoLogTable={}", undoLogTable);
            return true;

        } catch (Exception e) {
            log.warn("Seata配置检查失败: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 获取配置属性
     */
    public TiprayTransactionClientProperties getProperties() {
        return properties;
    }

    @Override
    public String toString() {
        return "TipraySeataInitializer{" +
                "undoLogTable='" + properties.getUndoLog().getTableName() + '\'' +
                ", compressionEnabled=" + properties.getUndoLog().isCompressionEnabled() +
                '}';
    }
}
