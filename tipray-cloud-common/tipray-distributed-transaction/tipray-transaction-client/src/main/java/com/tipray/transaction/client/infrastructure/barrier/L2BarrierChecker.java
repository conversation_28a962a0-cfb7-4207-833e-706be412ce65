package com.tipray.transaction.client.infrastructure.barrier;

import com.tipray.transaction.client.domain.barrier.BarrierCheckResult;
import com.tipray.transaction.client.domain.barrier.BarrierInfo;
import com.tipray.transaction.client.infrastructure.barrier.persistence.ParticipantBarrierStorage;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * L2屏障检查器（参与方屏障）
 * 负责控制业务执行层面的屏障检查
 * 在云服务端维护屏障记录，仅在AT模式下启用
 *
 * <AUTHOR>
 * @version 2.0
 * @since 2025-01-23
 */
public class L2BarrierChecker {

    private static final Logger log = LoggerFactory.getLogger(L2BarrierChecker.class);

    @Autowired
    private ParticipantBarrierStorage barrierStorage;

    /**
     * 设置屏障存储（用于测试）
     */
    public void setBarrierStorage(ParticipantBarrierStorage barrierStorage) {
        this.barrierStorage = barrierStorage;
    }

    /**
     * L2屏障检查 - 控制是否执行业务逻辑
     * 实现DTM核心算法的"两个insert判断"
     * 注意：此方法应该在事务内调用，确保屏障记录和业务逻辑的一致性
     *
     * @param barrierInfo 屏障信息
     * @return 屏障检查结果
     */
    public BarrierCheckResult checkL2Barrier(BarrierInfo barrierInfo) {
        if (barrierInfo == null) {
            log.warn("L2屏障检查失败：屏障信息不能为空");
            return BarrierCheckResult.error("屏障信息不能为空", "L2");
        }

        // L2屏障仅在AT模式下启用
        if (!barrierInfo.isAtMode()) {
            log.debug("L2屏障跳过：非AT模式 - mode={}", barrierInfo.getMode());
            return BarrierCheckResult.passL2();
        }

        String gid = barrierInfo.getGid();
        String branchId = barrierInfo.getBranchId();
        String operation = barrierInfo.getOperation();

        try {
            // AT模式的L2屏障检查
            return handleAtL2Barrier(barrierInfo);

        } catch (Exception e) {
            log.error("L2屏障检查异常: gid={}, branchId={}, operation={}", gid, branchId, operation, e);
            // 异常情况下，为了安全起见，允许执行
            return BarrierCheckResult.passL2();
        }
    }

    /**
     * AT模式L2屏障处理
     * 处理try/confirm/cancel操作的屏障逻辑
     * 实现DTM标准的"两个insert判断"算法
     */
    private BarrierCheckResult handleAtL2Barrier(BarrierInfo barrierInfo) {
        String gid = barrierInfo.getGid();
        String branchId = barrierInfo.getBranchId();
        String operation = barrierInfo.getOperation();

        // DTM核心算法：两个insert判断

        if ("cancel".equals(operation)) {
            // cancel操作的处理逻辑
            return handleAtCancelOperationL2(gid, branchId);
        } else if ("try".equals(operation)) {
            // try操作的处理逻辑
            return handleAtTryOperationL2(gid, branchId);
        } else {
            // confirm等其他操作：只做幂等控制
            boolean currentInserted = barrierStorage.insertBarrier(gid, branchId, operation);
            if (!currentInserted) {
                log.debug("L2屏障幂等控制: gid={}, branchId={}, operation={}", gid, branchId, operation);
                return BarrierCheckResult.idempotentL2("业务已执行，幂等控制");
            }
            log.debug("L2屏障检查通过: gid={}, branchId={}, operation={}", gid, branchId, operation);
            return BarrierCheckResult.passL2();
        }
    }

    /**
     * 处理AT模式的cancel操作（L2屏障）
     */
    private BarrierCheckResult handleAtCancelOperationL2(String gid, String branchId) {
        // 1. 插入cancel记录
        boolean cancelInserted = barrierStorage.insertBarrier(gid, branchId, "cancel");
        if (!cancelInserted) {
            // cancel操作已执行过，幂等控制
            log.debug("L2屏障幂等控制: gid={}, branchId={}, operation=cancel", gid, branchId);
            return BarrierCheckResult.idempotentL2("cancel业务已执行，幂等控制");
        }

        // 2. 尝试插入try记录，如果成功说明try未执行（空回滚）
        boolean tryInserted = barrierStorage.insertBarrier(gid, branchId, "try");
        if (tryInserted) {
            log.warn("L2屏障检测到AT空回滚: gid={}, branchId={}", gid, branchId);
            return BarrierCheckResult.emptyRollbackL2("AT空回滚：try业务未执行");
        }

        // 3. try记录已存在，说明try已执行，正常回滚
        log.debug("L2屏障检查通过: gid={}, branchId={}, operation=cancel", gid, branchId);
        return BarrierCheckResult.passL2();
    }

    /**
     * 处理AT模式的try操作（L2屏障）
     */
    private BarrierCheckResult handleAtTryOperationL2(String gid, String branchId) {
        // 1. 检查cancel是否已执行
        boolean cancelExists = barrierStorage.existsBarrier(gid, branchId, "cancel");
        if (cancelExists) {
            // 悬挂：cancel已执行，try操作不应执行
            log.warn("L2屏障检测到AT悬挂: gid={}, branchId={}", gid, branchId);
            return BarrierCheckResult.hangingL2("AT悬挂：cancel已执行");
        }

        // 2. 插入try记录
        boolean tryInserted = barrierStorage.insertBarrier(gid, branchId, "try");
        if (!tryInserted) {
            // try操作已执行过，幂等控制
            log.debug("L2屏障幂等控制: gid={}, branchId={}, operation=try", gid, branchId);
            return BarrierCheckResult.idempotentL2("try业务已执行，幂等控制");
        }

        // 3. try记录插入成功，正常执行
        log.debug("L2屏障检查通过: gid={}, branchId={}, operation=try", gid, branchId);
        return BarrierCheckResult.passL2();
    }

    /**
     * 清理事务屏障记录
     *
     * @param gid 全局事务ID
     */
    public void cleanupBarrier(String gid) {
        try {
            int deletedCount = barrierStorage.deleteBarrier(gid);
            log.debug("L2屏障清理完成: gid={}, 删除记录数={}", gid, deletedCount);
        } catch (Exception e) {
            log.error("L2屏障清理失败: gid={}", gid, e);
        }
    }

    /**
     * 检查是否需要启用L2屏障
     *
     * @param barrierInfo 屏障信息
     * @return true表示需要启用L2屏障
     */
    public boolean shouldEnableL2Barrier(BarrierInfo barrierInfo) {
        // L2屏障仅在AT模式下启用
        return barrierInfo != null && barrierInfo.isAtMode();
    }
}
