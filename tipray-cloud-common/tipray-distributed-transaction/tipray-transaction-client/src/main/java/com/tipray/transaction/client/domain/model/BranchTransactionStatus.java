package com.tipray.transaction.client.domain.model;

/**
 * 分支事务状态枚举
 * <p>
 * 定义分支事务的所有可能状态和状态流转规则
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-01-27
 */
public enum BranchTransactionStatus {

    /**
     * 已注册 - 分支事务已注册到全局事务中
     */
    REGISTERED(1, "已注册"),

    /**
     * 已提交 - 分支事务已成功提交
     */
    COMMITTED(2, "已提交"),

    /**
     * 已回滚 - 分支事务已成功回滚
     */
    ROLLBACKED(3, "已回滚"),

    /**
     * 提交失败 - 分支事务提交失败
     */
    COMMIT_FAILED(4, "提交失败"),

    /**
     * 回滚失败 - 分支事务回滚失败
     */
    ROLLBACK_FAILED(5, "回滚失败");

    private final int code;
    private final String description;

    BranchTransactionStatus(int code, String description) {
        this.code = code;
        this.description = description;
    }

    /**
     * 根据状态码获取状态枚举
     *
     * @param code 状态码
     * @return 状态枚举
     */
    public static BranchTransactionStatus fromCode(int code) {
        for (BranchTransactionStatus status : values()) {
            if (status.code == code) {
                return status;
            }
        }
        throw new IllegalArgumentException("未知的分支事务状态码: " + code);
    }

    /**
     * 获取状态码
     *
     * @return 状态码
     */
    public int getCode() {
        return code;
    }

    /**
     * 获取状态描述
     *
     * @return 状态描述
     */
    public String getDescription() {
        return description;
    }

    /**
     * 检查是否可以从当前状态转换到目标状态
     *
     * @param targetStatus 目标状态
     * @return 是否可以转换
     */
    public boolean canTransitionTo(BranchTransactionStatus targetStatus) {
        switch (this) {
            case REGISTERED:
                return targetStatus == COMMITTED ||
                        targetStatus == ROLLBACKED ||
                        targetStatus == COMMIT_FAILED ||
                        targetStatus == ROLLBACK_FAILED;
            case COMMITTED:
            case ROLLBACKED:
                return false; // 终态，不能再转换
            case COMMIT_FAILED:
                return targetStatus == ROLLBACKED || targetStatus == ROLLBACK_FAILED;
            case ROLLBACK_FAILED:
                return false; // 回滚失败是最终状态
            default:
                return false;
        }
    }

    /**
     * 检查是否为终态
     *
     * @return 是否为终态
     */
    public boolean isFinalState() {
        return this == COMMITTED || this == ROLLBACKED || this == ROLLBACK_FAILED;
    }

    /**
     * 检查是否为成功状态
     *
     * @return 是否为成功状态
     */
    public boolean isSuccessState() {
        return this == COMMITTED || this == ROLLBACKED;
    }

    /**
     * 检查是否为失败状态
     *
     * @return 是否为失败状态
     */
    public boolean isFailureState() {
        return this == COMMIT_FAILED || this == ROLLBACK_FAILED;
    }

    @Override
    public String toString() {
        return description + "(" + code + ")";
    }
}
