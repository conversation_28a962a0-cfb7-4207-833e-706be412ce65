package com.tipray.transaction.client.infrastructure.barrier.persistence.impl;

import com.tipray.transaction.client.infrastructure.barrier.persistence.ParticipantBarrierStorage;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 内存参与方屏障存储实现（L2屏障）
 * 使用ConcurrentHashMap实现线程安全的内存存储
 *
 * <AUTHOR>
 * @version 2.0
 * @since 2025-01-23
 */
public class DefaultParticipantBarrierStorage implements ParticipantBarrierStorage {

    private static final Logger log = LoggerFactory.getLogger(DefaultParticipantBarrierStorage.class);

    /**
     * 屏障记录存储
     * Key: gid:branchId:operation
     * Value: BarrierRecord
     */
    private final ConcurrentHashMap<String, BarrierRecord> barrierMap = new ConcurrentHashMap<>();

    @Override
    public boolean insertBarrier(String gid, String branchId, String operation) {
        if (gid == null || branchId == null || operation == null) {
            log.warn("L2屏障插入失败：参数不能为空 - gid={}, branchId={}, operation={}", gid, branchId, operation);
            return false;
        }

        String barrierKey = buildBarrierKey(gid, branchId, operation);
        BarrierRecord record = new BarrierRecord(gid, branchId, operation);

        // 使用putIfAbsent确保原子性，如果已存在则返回false
        BarrierRecord existing = barrierMap.putIfAbsent(barrierKey, record);
        boolean success = (existing == null);

        if (success) {
            log.debug("L2屏障记录插入成功: gid={}, branchId={}, operation={}", gid, branchId, operation);
        } else {
            log.debug("L2屏障记录已存在，插入失败: gid={}, branchId={}, operation={}", gid, branchId, operation);
        }

        return success;
    }

    @Override
    public boolean existsBarrier(String gid, String branchId, String operation) {
        if (gid == null || branchId == null || operation == null) {
            log.warn("L2屏障检查失败：参数不能为空 - gid={}, branchId={}, operation={}", gid, branchId, operation);
            return false;
        }

        String barrierKey = buildBarrierKey(gid, branchId, operation);
        boolean exists = barrierMap.containsKey(barrierKey);

        log.debug("L2屏障记录检查: gid={}, branchId={}, operation={} -> {}",
                gid, branchId, operation, exists ? "存在" : "不存在");

        return exists;
    }

    @Override
    public int deleteBarrier(String gid) {
        if (gid == null) {
            log.warn("L2屏障删除失败：事务ID不能为空");
            return 0;
        }

        // 删除指定事务ID的所有屏障记录
        int deletedCount = 0;
        String prefix = gid + ":";

        for (String key : barrierMap.keySet()) {
            if (key.startsWith(prefix)) {
                if (barrierMap.remove(key) != null) {
                    deletedCount++;
                }
            }
        }

        log.debug("L2屏障记录删除完成: gid={}, 删除数量={}", gid, deletedCount);
        return deletedCount;
    }

    @Override
    public int cleanExpiredBarriers(LocalDateTime beforeTime) {
        if (beforeTime == null) {
            log.warn("L2屏障清理失败：过期时间不能为空");
            return 0;
        }

        int cleanedCount = 0;

        for (String key : barrierMap.keySet()) {
            BarrierRecord record = barrierMap.get(key);
            if (record != null && record.getCreateTime().isBefore(beforeTime)) {
                if (barrierMap.remove(key) != null) {
                    cleanedCount++;
                }
            }
        }

        log.debug("L2屏障过期记录清理完成: beforeTime={}, 清理数量={}", beforeTime, cleanedCount);
        return cleanedCount;
    }

    @Override
    public int getBarrierCount() {
        return barrierMap.size();
    }

    @Override
    public int getBarrierCount(String gid) {
        if (gid == null) {
            return 0;
        }

        String prefix = gid + ":";
        int count = 0;

        for (String key : barrierMap.keySet()) {
            if (key.startsWith(prefix)) {
                count++;
            }
        }

        return count;
    }

    @Override
    public void clearAll() {
        int oldSize = barrierMap.size();
        barrierMap.clear();
        log.debug("L2屏障记录全部清理: 清理前={}, 清理后={}", oldSize, barrierMap.size());
    }

    /**
     * 构建屏障键
     */
    private String buildBarrierKey(String gid, String branchId, String operation) {
        return gid + ":" + branchId + ":" + operation;
    }

    /**
     * 屏障记录
     */
    private static class BarrierRecord {
        private final String gid;
        private final String branchId;
        private final String operation;
        private final LocalDateTime createTime;

        public BarrierRecord(String gid, String branchId, String operation) {
            this.gid = gid;
            this.branchId = branchId;
            this.operation = operation;
            this.createTime = LocalDateTime.now();
        }

        public String getGid() {
            return gid;
        }

        public String getBranchId() {
            return branchId;
        }

        public String getOperation() {
            return operation;
        }

        public LocalDateTime getCreateTime() {
            return createTime;
        }

        @Override
        public String toString() {
            return String.format("BarrierRecord{gid='%s', branchId='%s', operation='%s', createTime=%s}",
                    gid, branchId, operation, createTime);
        }
    }
}
