package com.tipray.transaction.client.application.task;

import com.tipray.transaction.client.domain.service.UndoLogManagerService;
import com.tipray.transaction.core.config.propertie.TiprayTransactionClientProperties;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;

import javax.sql.DataSource;
import java.sql.Connection;
import java.sql.SQLException;
import java.time.LocalDateTime;

/**
 * UndoLog清理任务
 * <p>
 * 定期清理过期的UndoLog记录
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-01-27
 */
@Slf4j
public class UndoLogCleanupTask {

    @Autowired
    private TiprayTransactionClientProperties properties;

    @Autowired
    private UndoLogManagerService undoLogManagerService;

    @Autowired(required = false)
    private DataSource dataSource;

    /**
     * 清理过期UndoLog
     * <p>
     * 每小时执行一次，可通过配置调整
     */
    @Scheduled(fixedRateString = "#{${tipray.transaction.client.undo-log.cleanup.interval-hours:24} * 3600000}")
    public void cleanupExpiredUndoLogs() {
        if (dataSource == null) {
            log.debug("数据源未配置，跳过UndoLog清理");
            return;
        }

        TiprayTransactionClientProperties.UndoLogConfig.CleanupConfig cleanupConfig =
                properties.getUndoLog().getCleanup();

        if (!cleanupConfig.isEnabled()) {
            log.debug("UndoLog清理功能已禁用");
            return;
        }

        log.info("开始清理过期UndoLog记录");

        try (Connection connection = dataSource.getConnection()) {
            // 设置手动提交事务
            connection.setAutoCommit(false);

            try {
                // 计算过期时间
                LocalDateTime expireTime = LocalDateTime.now().minusDays(cleanupConfig.getRetentionDays());

                int totalDeleted = 0;
                int batchSize = cleanupConfig.getBatchSize();

                // 分批删除
                while (true) {
                    int deletedCount = undoLogManagerService.deleteExpiredUndoLogs(expireTime, batchSize, connection);
                    totalDeleted += deletedCount;

                    if (deletedCount < batchSize) {
                        // 没有更多记录需要删除
                        break;
                    }

                    // 避免长时间占用数据库连接
                    if (totalDeleted >= 10000) {
                        log.info("本次清理已删除{}条记录，暂停清理", totalDeleted);
                        break;
                    }
                }

                // 提交事务
                connection.commit();

                if (totalDeleted > 0) {
                    log.info("UndoLog清理完成: 删除{}条过期记录，过期时间: {}", totalDeleted, expireTime);
                } else {
                    log.debug("没有过期的UndoLog记录需要清理");
                }

            } catch (Exception e) {
                // 回滚事务
                connection.rollback();
                throw e;
            }

        } catch (SQLException e) {
            log.error("UndoLog清理失败: {}", e.getMessage(), e);
        } catch (Exception e) {
            log.error("UndoLog清理异常: {}", e.getMessage(), e);
        }
    }

    /**
     * 统计UndoLog记录数
     * <p>
     * 每天执行一次
     */
    @Scheduled(cron = "0 0 2 * * ?") // 每天凌晨2点执行
    public void statisticsUndoLogs() {
        if (dataSource == null) {
            log.debug("数据源未配置，跳过UndoLog统计");
            return;
        }

        try (Connection connection = dataSource.getConnection()) {
            long count = undoLogManagerService.countUndoLogs(connection);
            log.info("UndoLog统计: 当前记录数 = {}", count);

            // 如果记录数过多，发出警告
            if (count > 100000) {
                log.warn("UndoLog记录数过多: {}，建议检查清理配置", count);
            }

        } catch (SQLException e) {
            log.error("UndoLog统计失败: {}", e.getMessage(), e);
        } catch (Exception e) {
            log.error("UndoLog统计异常: {}", e.getMessage(), e);
        }
    }

    /**
     * 检查UndoLog表是否存在，如果不存在则创建
     * <p>
     * 应用启动后执行一次
     */
    @Scheduled(initialDelay = 10000, fixedDelay = Long.MAX_VALUE) // 启动10秒后执行一次
    public void ensureUndoLogTable() {
        if (dataSource == null) {
            log.debug("数据源未配置，跳过UndoLog表检查");
            return;
        }

        if (!properties.getUndoLog().isAutoCreateTable()) {
            log.debug("自动创建表功能已禁用");
            return;
        }

        try (Connection connection = dataSource.getConnection()) {
            boolean exists = undoLogManagerService.undoLogTableExists(connection);

            if (!exists) {
                log.info("UndoLog表不存在，开始创建");
                undoLogManagerService.createUndoLogTable(connection);
                log.info("UndoLog表创建成功: {}", undoLogManagerService.getUndoLogTableName());
            } else {
                log.debug("UndoLog表已存在: {}", undoLogManagerService.getUndoLogTableName());
            }

        } catch (SQLException e) {
            log.error("UndoLog表检查失败: {}", e.getMessage(), e);
        } catch (Exception e) {
            log.error("UndoLog表检查异常: {}", e.getMessage(), e);
        }
    }

    /**
     * 手动触发清理
     *
     * @return 清理结果
     */
    public CleanupResult manualCleanup() {
        CleanupResult result = new CleanupResult();
        result.setStartTime(LocalDateTime.now());

        if (dataSource == null) {
            result.setSuccess(false);
            result.setMessage("数据源未配置");
            return result;
        }

        try (Connection connection = dataSource.getConnection()) {
            // 设置手动提交事务
            connection.setAutoCommit(false);

            try {
                TiprayTransactionClientProperties.UndoLogConfig.CleanupConfig cleanupConfig =
                        properties.getUndoLog().getCleanup();

                LocalDateTime expireTime = LocalDateTime.now().minusDays(cleanupConfig.getRetentionDays());
                int deletedCount = undoLogManagerService.deleteExpiredUndoLogs(expireTime, cleanupConfig.getBatchSize(), connection);

                // 提交事务
                connection.commit();

                result.setSuccess(true);
                result.setDeletedCount(deletedCount);
                result.setMessage("手动清理完成");
                result.setExpireTime(expireTime);

            } catch (Exception e) {
                // 回滚事务
                connection.rollback();
                throw e;
            }

        } catch (Exception e) {
            result.setSuccess(false);
            result.setMessage("清理失败: " + e.getMessage());
            log.error("手动清理UndoLog失败: {}", e.getMessage(), e);
        }

        result.setEndTime(LocalDateTime.now());
        return result;
    }

    /**
     * 清理结果
     */
    public static class CleanupResult {
        private boolean success;
        private String message;
        private int deletedCount;
        private LocalDateTime startTime;
        private LocalDateTime endTime;
        private LocalDateTime expireTime;

        // Getters and Setters
        public boolean isSuccess() {
            return success;
        }

        public void setSuccess(boolean success) {
            this.success = success;
        }

        public String getMessage() {
            return message;
        }

        public void setMessage(String message) {
            this.message = message;
        }

        public int getDeletedCount() {
            return deletedCount;
        }

        public void setDeletedCount(int deletedCount) {
            this.deletedCount = deletedCount;
        }

        public LocalDateTime getStartTime() {
            return startTime;
        }

        public void setStartTime(LocalDateTime startTime) {
            this.startTime = startTime;
        }

        public LocalDateTime getEndTime() {
            return endTime;
        }

        public void setEndTime(LocalDateTime endTime) {
            this.endTime = endTime;
        }

        public LocalDateTime getExpireTime() {
            return expireTime;
        }

        public void setExpireTime(LocalDateTime expireTime) {
            this.expireTime = expireTime;
        }
    }
}
