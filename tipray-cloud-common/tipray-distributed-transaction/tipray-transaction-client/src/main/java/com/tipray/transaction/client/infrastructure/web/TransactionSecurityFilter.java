package com.tipray.transaction.client.infrastructure.web;

import com.tipray.transaction.client.infrastructure.security.ClientSecurityUtils;
import com.tipray.transaction.core.util.TransactionSecurityUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.annotation.Order;
import org.springframework.util.StreamUtils;

import javax.servlet.*;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 事务安全验证过滤器
 * <p>
 * 替代TransactionSecurityInterceptor，避免WebMvcConfigurer冲突
 * 对事务相关接口进行安全验证，防止非法请求
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-01-27
 */
@Slf4j
@Order(1) // 最高优先级，在事务过滤器之前执行
public class TransactionSecurityFilter implements Filter {

    /**
     * 缓存清理间隔（毫秒）
     */
    private static final long CACHE_CLEANUP_INTERVAL = 300000L; // 5分钟
    /**
     * 需要安全验证的路径
     */
    private static final String[] SECURE_PATHS = {
            "/api/transaction/commit",
            "/api/transaction/rollback"
    };
    /**
     * 防重复请求缓存
     * key: 请求唯一标识
     * value: 请求时间戳
     */
    private final ConcurrentHashMap<String, Long> requestCache = new ConcurrentHashMap<>();
    /**
     * 上次清理时间
     */
    private volatile long lastCleanupTime = System.currentTimeMillis();

    @Override
    public void init(FilterConfig filterConfig) throws ServletException {
        log.info("事务安全验证过滤器初始化完成");
    }

    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain)
            throws IOException, ServletException {

        if (!(request instanceof HttpServletRequest) || !(response instanceof HttpServletResponse)) {
            chain.doFilter(request, response);
            return;
        }

        HttpServletRequest httpRequest = (HttpServletRequest) request;
        HttpServletResponse httpResponse = (HttpServletResponse) response;
        String requestPath = httpRequest.getRequestURI();

        // 检查是否需要安全验证
        if (!needsSecurityValidation(requestPath)) {
            chain.doFilter(request, response);
            return;
        }

        try {
            // 执行安全验证
            if (!validateSecurity(httpRequest)) {
                log.warn("安全验证失败: path={}, remoteAddr={}", requestPath, httpRequest.getRemoteAddr());
                sendSecurityError(httpResponse, "安全验证失败", "SECURITY_VALIDATION_FAILED");
                return;
            }

            // 定期清理过期缓存
            cleanupExpiredCache();

            // 继续过滤器链
            chain.doFilter(request, response);

        } catch (Exception e) {
            log.error("安全验证异常: path={}, error={}", requestPath, e.getMessage(), e);
            sendSecurityError(httpResponse, "安全验证异常", "SECURITY_VALIDATION_ERROR");
        }
    }

    @Override
    public void destroy() {
        log.info("事务安全验证过滤器销毁");
        requestCache.clear();
    }

    /**
     * 检查路径是否需要安全验证
     *
     * @param requestPath 请求路径
     * @return 是否需要验证
     */
    private boolean needsSecurityValidation(String requestPath) {
        if (requestPath == null) {
            return false;
        }

        for (String securePath : SECURE_PATHS) {
            if (requestPath.equals(securePath)) {
                return true;
            }
        }

        return false;
    }

    /**
     * 执行安全验证
     *
     * @param request HTTP请求
     * @return 验证结果
     */
    private boolean validateSecurity(HttpServletRequest request) throws IOException {
        // 提取安全验证信息
        ClientSecurityUtils.SecurityInfo securityInfo = ClientSecurityUtils.extractSecurityInfo(request);

        // 验证安全信息完整性
        ClientSecurityUtils.ValidationResult validationResult = ClientSecurityUtils.validateSecurityInfo(securityInfo);
        if (!validationResult.isValid()) {
            log.warn("安全信息验证失败: {}", validationResult.getErrorMessage());
            return false;
        }

        // 解析参数
        Long branchId = Long.parseLong(securityInfo.getBranchId());
        long timestamp = Long.parseLong(securityInfo.getTimestamp());

        // 验证时间戳
        if (!TransactionSecurityUtils.isTimestampValid(timestamp)) {
            log.warn("时间戳验证失败: timestamp={}, current={}", timestamp, System.currentTimeMillis());
            return false;
        }

        // 读取请求体
        String requestBody = readRequestBody(request);

        // 验证签名
        if (!TransactionSecurityUtils.verifySignature(securityInfo.getTransactionId(), branchId, securityInfo.getOperation(),
                timestamp, securityInfo.getNonce(), requestBody, securityInfo.getSignature())) {
            log.warn("签名验证失败: transactionId={}, branchId={}, operation={}",
                    securityInfo.getTransactionId(), branchId, securityInfo.getOperation());
            return false;
        }

        // 防重复请求验证
        if (!validateDuplicateRequest(securityInfo.getTransactionId(), branchId, securityInfo.getOperation(), securityInfo.getNonce())) {
            log.warn("重复请求检测失败: transactionId={}, branchId={}, operation={}, nonce={}",
                    securityInfo.getTransactionId(), branchId, securityInfo.getOperation(), securityInfo.getNonce());
            return false;
        }

        // 记录验证成功日志
        ClientSecurityUtils.logSecurityValidation(securityInfo, validationResult, request.getRequestURI());
        return true;
    }

    /**
     * 读取请求体
     *
     * @param request HTTP请求
     * @return 请求体内容
     */
    private String readRequestBody(HttpServletRequest request) throws IOException {
        try {
            // 如果是CachedBodyHttpServletRequest，可以重复读取
            if (request instanceof CachedBodyHttpServletRequest) {
                return ((CachedBodyHttpServletRequest) request).getCachedBodyAsString();
            }

            // 普通请求，尝试读取一次（但这种情况下可能会失败，因为请求体可能已经被读取过）
            return StreamUtils.copyToString(request.getInputStream(), java.nio.charset.StandardCharsets.UTF_8);
        } catch (Exception e) {
            log.warn("读取请求体失败: {}", e.getMessage());
            return "";
        }
    }

    /**
     * 验证重复请求
     *
     * @param transactionId 事务ID
     * @param branchId      分支ID
     * @param operation     操作类型
     * @param nonce         随机数
     * @return 验证结果
     */
    private boolean validateDuplicateRequest(String transactionId, Long branchId, String operation, String nonce) {
        String requestKey = String.format("%s:%s:%s:%s", transactionId, branchId, operation, nonce);
        long currentTime = System.currentTimeMillis();

        // 检查是否重复请求
        Long existingTime = requestCache.get(requestKey);
        if (existingTime != null) {
            return false; // 重复请求
        }

        // 记录请求
        requestCache.put(requestKey, currentTime);
        return true;
    }

    /**
     * 清理过期缓存
     */
    private void cleanupExpiredCache() {
        long currentTime = System.currentTimeMillis();
        if (currentTime - lastCleanupTime > CACHE_CLEANUP_INTERVAL) {
            synchronized (this) {
                if (currentTime - lastCleanupTime > CACHE_CLEANUP_INTERVAL) {
                    long expireTime = currentTime - CACHE_CLEANUP_INTERVAL;
                    requestCache.entrySet().removeIf(entry -> entry.getValue() < expireTime);
                    lastCleanupTime = currentTime;
                    log.debug("清理过期安全验证缓存，当前缓存大小: {}", requestCache.size());
                }
            }
        }
    }

    /**
     * 发送安全错误响应
     *
     * @param response  HTTP响应
     * @param message   错误消息
     * @param errorCode 错误代码
     */
    private void sendSecurityError(HttpServletResponse response, String message, String errorCode) throws IOException {
        response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
        response.setContentType("application/json;charset=UTF-8");
        String errorJson = String.format("{\"success\":false,\"message\":\"%s\",\"errorCode\":\"%s\"}", message, errorCode);
        response.getWriter().write(errorJson);
    }
}
