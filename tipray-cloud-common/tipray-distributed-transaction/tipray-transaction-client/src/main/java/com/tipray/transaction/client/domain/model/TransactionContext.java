package com.tipray.transaction.client.domain.model;

import lombok.Data;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * 事务上下文值对象
 * <p>
 * 包含分布式事务执行过程中的上下文信息
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-01-27
 */
@Data
public class TransactionContext {

    /**
     * 全局事务ID
     */
    private String globalTransactionId;

    /**
     * 分支事务ID
     */
    private Long branchId;

    /**
     * 事务类型
     */
    private BranchTransactionType transactionType;

    /**
     * 资源ID
     */
    private String resourceId;

    /**
     * 应用数据
     */
    private String applicationData;

    /**
     * 客户端ID
     */
    private String clientId;

    /**
     * 用户编号
     */
    private String userNo;

    /**
     * 扩展属性
     */
    private Map<String, Object> attributes;

    /**
     * 构造函数
     */
    public TransactionContext() {
        this.attributes = new HashMap<>();
    }

    /**
     * 构造函数
     *
     * @param globalTransactionId 全局事务ID
     * @param branchId            分支事务ID
     * @param transactionType     事务类型
     */
    public TransactionContext(String globalTransactionId, Long branchId, BranchTransactionType transactionType) {
        this();
        this.globalTransactionId = globalTransactionId;
        this.branchId = branchId;
        this.transactionType = transactionType;
    }

    /**
     * 设置属性
     *
     * @param key   属性键
     * @param value 属性值
     */
    public void setAttribute(String key, Object value) {
        if (attributes == null) {
            attributes = new HashMap<>();
        }
        attributes.put(key, value);
    }

    /**
     * 获取属性
     *
     * @param key 属性键
     * @return 属性值
     */
    public Object getAttribute(String key) {
        return attributes != null ? attributes.get(key) : null;
    }

    /**
     * 获取属性（指定类型）
     *
     * @param key  属性键
     * @param type 属性类型
     * @param <T>  类型参数
     * @return 属性值
     */
    @SuppressWarnings("unchecked")
    public <T> T getAttribute(String key, Class<T> type) {
        Object value = getAttribute(key);
        if (value != null && type.isInstance(value)) {
            return (T) value;
        }
        return null;
    }

    /**
     * 移除属性
     *
     * @param key 属性键
     * @return 被移除的属性值
     */
    public Object removeAttribute(String key) {
        return attributes != null ? attributes.remove(key) : null;
    }

    /**
     * 检查是否包含属性
     *
     * @param key 属性键
     * @return 是否包含
     */
    public boolean hasAttribute(String key) {
        return attributes != null && attributes.containsKey(key);
    }

    /**
     * 清空所有属性
     */
    public void clearAttributes() {
        if (attributes != null) {
            attributes.clear();
        }
    }

    /**
     * 获取事务标识
     *
     * @return 事务标识
     */
    public String getTransactionKey() {
        return globalTransactionId + ":" + branchId;
    }

    /**
     * 检查上下文是否有效
     *
     * @return 是否有效
     */
    public boolean isValid() {
        return globalTransactionId != null &&
                !globalTransactionId.trim().isEmpty() &&
                branchId != null &&
                transactionType != null;
    }

    /**
     * 检查是否为AT模式
     *
     * @return 是否为AT模式（client模块始终为true）
     */
    public boolean isAtMode() {
        return transactionType == BranchTransactionType.AT;
    }

    /**
     * 复制上下文（深拷贝）
     *
     * @return 新的上下文实例
     */
    public TransactionContext copy() {
        TransactionContext copy = new TransactionContext();
        copy.globalTransactionId = this.globalTransactionId;
        copy.branchId = this.branchId;
        copy.transactionType = this.transactionType;
        copy.resourceId = this.resourceId;
        copy.applicationData = this.applicationData;
        copy.clientId = this.clientId;
        copy.userNo = this.userNo;

        if (this.attributes != null) {
            copy.attributes = new HashMap<>(this.attributes);
        }

        return copy;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        TransactionContext that = (TransactionContext) o;
        return Objects.equals(globalTransactionId, that.globalTransactionId) &&
                Objects.equals(branchId, that.branchId);
    }

    @Override
    public int hashCode() {
        return Objects.hash(globalTransactionId, branchId);
    }

    @Override
    public String toString() {
        return "TransactionContext{" +
                "globalTransactionId='" + globalTransactionId + '\'' +
                ", branchId=" + branchId +
                ", transactionType=" + transactionType +
                ", resourceId='" + resourceId + '\'' +
                ", clientId='" + clientId + '\'' +
                ", userNo='" + userNo + '\'' +
                '}';
    }
}
