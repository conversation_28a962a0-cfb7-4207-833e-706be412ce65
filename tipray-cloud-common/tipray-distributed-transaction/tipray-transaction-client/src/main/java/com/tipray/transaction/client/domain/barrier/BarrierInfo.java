package com.tipray.transaction.client.domain.barrier;

import com.tipray.transaction.client.domain.model.BranchTransactionType;

/**
 * 屏障信息
 * 封装屏障检查所需的所有信息
 *
 * <AUTHOR>
 * @version 2.0
 * @since 2025-01-23
 */
public class BarrierInfo {

    /**
     * 全局事务ID
     */
    private String gid;

    /**
     * 分支事务ID
     */
    private String branchId;

    /**
     * 操作类型
     * AT模式：try/confirm/cancel
     */
    private String operation;

    /**
     * 事务模式
     */
    private BranchTransactionType mode;

    /**
     * 请求时间戳
     */
    private long timestamp;

    /**
     * 默认构造函数
     */
    public BarrierInfo() {
    }

    /**
     * 构造函数
     */
    public BarrierInfo(String gid, String branchId, String operation, BranchTransactionType mode) {
        this.gid = gid;
        this.branchId = branchId;
        this.operation = operation;
        this.mode = mode;
        this.timestamp = System.currentTimeMillis();
    }

    /**
     * 创建Builder
     */
    public static Builder builder() {
        return new Builder();
    }

    /**
     * 构建屏障键
     * 格式：gid:branchId:operation
     */
    public String buildBarrierKey() {
        return gid + ":" + branchId + ":" + operation;
    }

    /**
     * 判断是否为AT模式
     */
    public boolean isAtMode() {
        return mode != null && mode == BranchTransactionType.AT;
    }

    /**
     * 判断是否为try操作
     */
    public boolean isTryOperation() {
        return "try".equalsIgnoreCase(operation);
    }

    /**
     * 判断是否为confirm操作
     */
    public boolean isConfirmOperation() {
        return "confirm".equalsIgnoreCase(operation);
    }

    /**
     * 判断是否为cancel操作
     */
    public boolean isCancelOperation() {
        return "cancel".equalsIgnoreCase(operation);
    }

    /**
     * 获取对应的try操作屏障信息
     */
    public BarrierInfo getTryBarrierInfo() {
        return new BarrierInfo(gid, branchId, "try", mode);
    }

    // Getters and Setters
    public String getGid() {
        return gid;
    }

    public void setGid(String gid) {
        this.gid = gid;
    }

    public String getBranchId() {
        return branchId;
    }

    public void setBranchId(String branchId) {
        this.branchId = branchId;
    }

    public String getOperation() {
        return operation;
    }

    public void setOperation(String operation) {
        this.operation = operation;
    }

    public BranchTransactionType getMode() {
        return mode;
    }

    public void setMode(BranchTransactionType mode) {
        this.mode = mode;
    }

    public long getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(long timestamp) {
        this.timestamp = timestamp;
    }

    @Override
    public String toString() {
        return String.format("BarrierInfo{gid='%s', branchId='%s', operation='%s', mode=%s, timestamp=%d}",
                gid, branchId, operation, mode, timestamp);
    }

    /**
     * Builder类
     */
    public static class Builder {
        private String gid;
        private String branchId;
        private String operation;
        private BranchTransactionType mode;
        private long timestamp = System.currentTimeMillis();

        public Builder gid(String gid) {
            this.gid = gid;
            return this;
        }

        public Builder branchId(String branchId) {
            this.branchId = branchId;
            return this;
        }

        public Builder operation(String operation) {
            this.operation = operation;
            return this;
        }

        public Builder mode(BranchTransactionType mode) {
            this.mode = mode;
            return this;
        }

        public Builder timestamp(long timestamp) {
            this.timestamp = timestamp;
            return this;
        }

        public BarrierInfo build() {
            BarrierInfo barrierInfo = new BarrierInfo();
            barrierInfo.gid = this.gid;
            barrierInfo.branchId = this.branchId;
            barrierInfo.operation = this.operation;
            barrierInfo.mode = this.mode;
            barrierInfo.timestamp = this.timestamp;
            return barrierInfo;
        }
    }
}
