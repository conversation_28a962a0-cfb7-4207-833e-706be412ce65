package com.tipray.transaction.client.infrastructure.barrier.persistence;

import java.time.LocalDateTime;

/**
 * 参与方屏障存储接口（L2屏障）
 * 在云服务端维护屏障记录
 *
 * <AUTHOR>
 * @version 2.0
 * @since 2025-01-23
 */
public interface ParticipantBarrierStorage {

    /**
     * 插入屏障记录（原子操作）
     *
     * @param gid       全局事务ID
     * @param branchId  分支事务ID
     * @param operation 操作类型
     * @return true-插入成功，false-记录已存在
     */
    boolean insertBarrier(String gid, String branchId, String operation);

    /**
     * 检查屏障记录是否存在
     *
     * @param gid       全局事务ID
     * @param branchId  分支事务ID
     * @param operation 操作类型
     * @return true表示存在
     */
    boolean existsBarrier(String gid, String branchId, String operation);

    /**
     * 删除指定事务的所有屏障记录
     *
     * @param gid 全局事务ID
     * @return 删除的记录数量
     */
    int deleteBarrier(String gid);

    /**
     * 清理过期屏障记录
     *
     * @param beforeTime 过期时间点
     * @return 清理的记录数量
     */
    int cleanExpiredBarriers(LocalDateTime beforeTime);

    /**
     * 获取屏障记录总数
     *
     * @return 记录总数
     */
    int getBarrierCount();

    /**
     * 获取指定事务的屏障记录数量
     *
     * @param gid 全局事务ID
     * @return 记录数量
     */
    int getBarrierCount(String gid);

    /**
     * 清理所有屏障记录
     */
    void clearAll();
}
