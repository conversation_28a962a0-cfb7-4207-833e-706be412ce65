package com.tipray.transaction.client.config;

import com.tipray.transaction.client.infrastructure.barrier.L2BarrierChecker;
import com.tipray.transaction.client.infrastructure.web.CachedBodyFilter;
import com.tipray.transaction.client.infrastructure.web.DistributedTransactionFilter;
import com.tipray.transaction.client.infrastructure.web.TransactionSecurityFilter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnWebApplication;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * 事务客户端Web配置类
 * 负责客户端Web相关组件的Bean配置
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-05-20
 */
@Slf4j
@Configuration
@ConditionalOnWebApplication
public class TransactionClientWebConfiguration {

    /**
     * 配置缓存请求体过滤器
     *
     * @return FilterRegistrationBean实例
     */
    /**
     * 配置事务安全验证过滤器
     * <p>
     * 当启用Filter模式时生效
     */
    @Bean
    public FilterRegistrationBean<TransactionSecurityFilter> transactionSecurityFilterRegistration() {
        FilterRegistrationBean<TransactionSecurityFilter> registration = new FilterRegistrationBean<>();
        registration.setFilter(new TransactionSecurityFilter());
        // 只拦截需要安全验证的路径，不要拦截所有请求
        registration.addUrlPatterns("/api/transaction/commit", "/api/transaction/rollback");
        registration.setOrder(1); // 高优先级
        registration.setName("transactionSecurityFilter");

        return registration;
    }

    /**
     * 配置分布式事务过滤器
     * <p>
     * 当启用Filter模式时生效
     */
    @Bean
    @ConditionalOnBean(L2BarrierChecker.class)
    public FilterRegistrationBean<DistributedTransactionFilter> distributedTransactionFilterRegistration(L2BarrierChecker l2BarrierChecker) {
        log.debug("配置分布式事务过滤器");
        FilterRegistrationBean<DistributedTransactionFilter> registration = new FilterRegistrationBean<>();
        registration.setFilter(new DistributedTransactionFilter(l2BarrierChecker));
        registration.addUrlPatterns("/*"); // 拦截所有请求，内部会判断是否需要处理
        registration.setOrder(2); // 在安全过滤器之后
        registration.setName("distributedTransactionFilter");

        return registration;
    }

    /**
     * 配置请求体缓存过滤器
     * <p>
     * 当启用安全验证时生效
     */
    @Bean
    public FilterRegistrationBean<CachedBodyFilter> cachedBodyFilterRegistration() {
        log.debug("配置请求体缓存过滤器");

        FilterRegistrationBean<CachedBodyFilter> registration = new FilterRegistrationBean<>();
        registration.setFilter(new CachedBodyFilter());
        registration.addUrlPatterns("/transaction/commit", "/transaction/rollback");
        registration.setOrder(0); // 最高优先级，确保在其他过滤器之前执行
        registration.setName("cachedBodyFilter");

        return registration;
    }
}
