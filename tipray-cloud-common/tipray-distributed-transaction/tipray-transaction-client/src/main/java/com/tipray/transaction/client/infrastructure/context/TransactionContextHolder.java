package com.tipray.transaction.client.infrastructure.context;

import com.alibaba.ttl.TransmittableThreadLocal;
import com.tipray.transaction.client.domain.model.TransactionContext;
import lombok.extern.slf4j.Slf4j;

/**
 * 事务上下文持有者
 * <p>
 * 使用ThreadLocal管理当前线程的事务上下文
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-01-27
 */
@Slf4j
public class TransactionContextHolder {

    /**
     * 事务上下文ThreadLocal - 使用TransmittableThreadLocal支持异步传递
     */
    private static final TransmittableThreadLocal<TransactionContext> CONTEXT_HOLDER = new TransmittableThreadLocal<>();

    /**
     * 全局事务ID ThreadLocal - 使用TransmittableThreadLocal支持异步传递
     */
    private static final TransmittableThreadLocal<String> GLOBAL_TRANSACTION_ID_HOLDER = new TransmittableThreadLocal<>();

    /**
     * 分支事务ID ThreadLocal - 使用TransmittableThreadLocal支持异步传递
     */
    private static final TransmittableThreadLocal<Long> BRANCH_ID_HOLDER = new TransmittableThreadLocal<>();

    /**
     * 用户编号 ThreadLocal - 使用TransmittableThreadLocal支持异步传递
     */
    private static final TransmittableThreadLocal<String> USER_NO_HOLDER = new TransmittableThreadLocal<>();

    /**
     * 获取事务上下文
     *
     * @return 事务上下文
     */
    public static TransactionContext getContext() {
        return CONTEXT_HOLDER.get();
    }

    /**
     * 设置事务上下文
     *
     * @param context 事务上下文
     */
    public static void setContext(TransactionContext context) {
        if (context != null) {
            CONTEXT_HOLDER.set(context);
            GLOBAL_TRANSACTION_ID_HOLDER.set(context.getGlobalTransactionId());
            BRANCH_ID_HOLDER.set(context.getBranchId());
            USER_NO_HOLDER.set(context.getUserNo());

            log.debug("设置事务上下文: globalTransactionId={}, branchId={}, userNo={}",
                    context.getGlobalTransactionId(), context.getBranchId(), context.getUserNo());
        } else {
            clear();
        }
    }

    /**
     * 获取全局事务ID
     *
     * @return 全局事务ID
     */
    public static String getGlobalTransactionId() {
        return GLOBAL_TRANSACTION_ID_HOLDER.get();
    }

    /**
     * 设置全局事务ID
     *
     * @param globalTransactionId 全局事务ID
     */
    public static void setGlobalTransactionId(String globalTransactionId) {
        GLOBAL_TRANSACTION_ID_HOLDER.set(globalTransactionId);

        // 同步更新上下文
        TransactionContext context = CONTEXT_HOLDER.get();
        if (context != null) {
            context.setGlobalTransactionId(globalTransactionId);
        }

        log.debug("设置全局事务ID: {}", globalTransactionId);
    }

    /**
     * 获取分支事务ID
     *
     * @return 分支事务ID
     */
    public static Long getBranchId() {
        return BRANCH_ID_HOLDER.get();
    }

    /**
     * 设置分支事务ID
     *
     * @param branchId 分支事务ID
     */
    public static void setBranchId(Long branchId) {
        BRANCH_ID_HOLDER.set(branchId);

        // 同步更新上下文
        TransactionContext context = CONTEXT_HOLDER.get();
        if (context != null) {
            context.setBranchId(branchId);
        }

        log.debug("设置分支事务ID: {}", branchId);
    }

    /**
     * 获取用户编号
     *
     * @return 用户编号
     */
    public static String getUserNo() {
        return USER_NO_HOLDER.get();
    }

    /**
     * 设置用户编号
     *
     * @param userNo 用户编号
     */
    public static void setUserNo(String userNo) {
        USER_NO_HOLDER.set(userNo);

        // 同步更新上下文
        TransactionContext context = CONTEXT_HOLDER.get();
        if (context != null) {
            context.setUserNo(userNo);
        }

        log.debug("设置用户编号: {}", userNo);
    }

    /**
     * 检查是否在事务中
     *
     * @return 是否在事务中
     */
    public static boolean inTransaction() {
        return GLOBAL_TRANSACTION_ID_HOLDER.get() != null;
    }

    /**
     * 检查是否在分支事务中
     *
     * @return 是否在分支事务中
     */
    public static boolean inBranchTransaction() {
        return GLOBAL_TRANSACTION_ID_HOLDER.get() != null && BRANCH_ID_HOLDER.get() != null;
    }

    /**
     * 获取事务标识
     *
     * @return 事务标识
     */
    public static String getTransactionKey() {
        String globalTransactionId = GLOBAL_TRANSACTION_ID_HOLDER.get();
        Long branchId = BRANCH_ID_HOLDER.get();

        if (globalTransactionId != null && branchId != null) {
            return globalTransactionId + ":" + branchId;
        } else if (globalTransactionId != null) {
            return globalTransactionId;
        } else {
            return null;
        }
    }

    /**
     * 创建新的事务上下文
     *
     * @param globalTransactionId 全局事务ID
     * @param branchId            分支事务ID
     * @param transactionType     事务类型
     * @return 事务上下文
     */
    public static TransactionContext createContext(String globalTransactionId, Long branchId,
                                                   com.tipray.transaction.client.domain.model.BranchTransactionType transactionType) {
        TransactionContext context = new TransactionContext(globalTransactionId, branchId, transactionType);
        context.setUserNo(USER_NO_HOLDER.get());

        setContext(context);
        return context;
    }

    /**
     * 复制当前上下文
     *
     * @return 复制的上下文
     */
    public static TransactionContext copyContext() {
        TransactionContext current = CONTEXT_HOLDER.get();
        return current != null ? current.copy() : null;
    }

    /**
     * 清理事务上下文
     */
    public static void clear() {
        TransactionContext context = CONTEXT_HOLDER.get();
        if (context != null) {
            log.debug("清理事务上下文: globalTransactionId={}, branchId={}",
                    context.getGlobalTransactionId(), context.getBranchId());
        }

        CONTEXT_HOLDER.remove();
        GLOBAL_TRANSACTION_ID_HOLDER.remove();
        BRANCH_ID_HOLDER.remove();
        USER_NO_HOLDER.remove();
    }

    /**
     * 清理全部ThreadLocal（用于防止内存泄漏）
     */
    public static void clearAll() {
        clear();
        log.debug("清理所有ThreadLocal变量");
    }

    /**
     * 获取当前线程的事务信息摘要
     *
     * @return 事务信息摘要
     */
    public static String getTransactionSummary() {
        String globalTransactionId = GLOBAL_TRANSACTION_ID_HOLDER.get();
        Long branchId = BRANCH_ID_HOLDER.get();
        String userNo = USER_NO_HOLDER.get();

        StringBuilder summary = new StringBuilder();
        summary.append("Transaction[");

        if (globalTransactionId != null) {
            summary.append("globalTxId=").append(globalTransactionId);
        }

        if (branchId != null) {
            if (globalTransactionId != null) {
                summary.append(", ");
            }
            summary.append("branchId=").append(branchId);
        }

        if (userNo != null) {
            if (globalTransactionId != null || branchId != null) {
                summary.append(", ");
            }
            summary.append("userNo=").append(userNo);
        }

        if (globalTransactionId == null && branchId == null && userNo == null) {
            summary.append("none");
        }

        summary.append("]");
        return summary.toString();
    }

    /**
     * 执行带事务上下文的操作
     *
     * @param context 事务上下文
     * @param action  操作
     * @param <T>     返回类型
     * @return 操作结果
     * @throws Exception 操作异常
     */
    public static <T> T executeWithContext(TransactionContext context, ContextAction<T> action) throws Exception {
        TransactionContext originalContext = copyContext();
        try {
            setContext(context);
            return action.execute();
        } finally {
            setContext(originalContext);
        }
    }

    /**
     * 上下文操作接口
     *
     * @param <T> 返回类型
     */
    @FunctionalInterface
    public interface ContextAction<T> {
        T execute() throws Exception;
    }
}
