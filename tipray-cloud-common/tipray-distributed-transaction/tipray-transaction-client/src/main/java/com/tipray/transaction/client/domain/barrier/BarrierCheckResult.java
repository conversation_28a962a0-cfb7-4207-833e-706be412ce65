package com.tipray.transaction.client.domain.barrier;

/**
 * 屏障检查结果
 * 封装屏障检查的结果信息
 *
 * <AUTHOR>
 * @version 2.0
 * @since 2025-01-23
 */
public class BarrierCheckResult {

    /**
     * 是否被屏障拦截
     */
    private final boolean blocked;

    /**
     * 屏障状态
     */
    private final BarrierStatus status;

    /**
     * 详细信息
     */
    private final String message;

    /**
     * 屏障层级（L2）
     */
    private final String level;

    /**
     * 构造函数
     */
    private BarrierCheckResult(boolean blocked, BarrierStatus status, String message, String level) {
        this.blocked = blocked;
        this.status = status;
        this.message = message;
        this.level = level;
    }

    /**
     * L2屏障检查通过
     */
    public static BarrierCheckResult passL2() {
        return new BarrierCheckResult(false, BarrierStatus.PASS, "L2屏障检查通过", "L2");
    }

    /**
     * L2幂等控制
     */
    public static BarrierCheckResult idempotentL2(String message) {
        return new BarrierCheckResult(true, BarrierStatus.IDEMPOTENT, message, "L2");
    }

    /**
     * L2空回滚
     */
    public static BarrierCheckResult emptyRollbackL2(String message) {
        return new BarrierCheckResult(true, BarrierStatus.EMPTY_ROLLBACK, message, "L2");
    }

    /**
     * L2悬挂
     */
    public static BarrierCheckResult hangingL2(String message) {
        return new BarrierCheckResult(true, BarrierStatus.HANGING, message, "L2");
    }

    /**
     * 屏障异常
     */
    public static BarrierCheckResult error(String message, String level) {
        return new BarrierCheckResult(false, BarrierStatus.ERROR, message, level);
    }

    // Getters
    public boolean isBlocked() {
        return blocked;
    }

    public BarrierStatus getStatus() {
        return status;
    }

    public String getMessage() {
        return message;
    }

    public String getLevel() {
        return level;
    }

    /**
     * 判断是否为幂等控制
     */
    public boolean isIdempotent() {
        return status == BarrierStatus.IDEMPOTENT;
    }

    /**
     * 判断是否为空回滚
     */
    public boolean isEmptyRollback() {
        return status == BarrierStatus.EMPTY_ROLLBACK;
    }

    /**
     * 判断是否为悬挂
     */
    public boolean isHanging() {
        return status == BarrierStatus.HANGING;
    }

    /**
     * 判断是否为L2屏障结果
     */
    public boolean isL2Result() {
        return "L2".equals(level);
    }

    @Override
    public String toString() {
        return String.format("BarrierCheckResult{blocked=%s, status=%s, message='%s', level='%s'}",
                blocked, status, message, level);
    }

    /**
     * 屏障状态枚举
     */
    public enum BarrierStatus {
        /**
         * 通过
         */
        PASS,

        /**
         * 幂等控制
         */
        IDEMPOTENT,

        /**
         * 空回滚
         */
        EMPTY_ROLLBACK,

        /**
         * 悬挂
         */
        HANGING,

        /**
         * 错误
         */
        ERROR
    }
}
