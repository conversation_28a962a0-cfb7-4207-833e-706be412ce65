package com.tipray.transaction.client.infrastructure.init;

import com.tipray.transaction.core.config.propertie.TiprayTransactionClientProperties;
import io.seata.config.Configuration;
import io.seata.config.ConfigurationChangeListener;
import lombok.extern.slf4j.Slf4j;

import java.time.Duration;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Tipray Seata 配置工厂
 * <p>
 * 提供简化的 Seata 配置实现，避免复杂的配置依赖
 * <p>
 * 主要特性：
 * 1. 提供基本的 Seata 配置支持
 * 2. 避免复杂的配置中心依赖
 * 3. 支持基于 Tipray 配置的定制化
 * 4. 内存缓存配置项
 *
 * <AUTHOR>
 * @version 2.0
 * @since 2025-01-27
 */
@Slf4j
public class TipraySeataConfigurationFactory {

    private static final Object LOCK = new Object();
    private static volatile TipraySeataConfiguration instance;

    /**
     * 获取配置实例
     */
    public static Configuration getInstance(TiprayTransactionClientProperties properties) {
        if (instance == null) {
            synchronized (LOCK) {
                if (instance == null) {
                    instance = new TipraySeataConfiguration(properties);
                }
            }
        }
        return instance;
    }

    /**
     * Tipray Seata 配置实现
     */
    public static class TipraySeataConfiguration implements Configuration {

        private final TiprayTransactionClientProperties properties;
        private final Map<String, String> configCache = new ConcurrentHashMap<>();

        public TipraySeataConfiguration(TiprayTransactionClientProperties properties) {
            this.properties = properties;
            initializeDefaultConfigs();
        }

        /**
         * 初始化默认配置
         */
        private void initializeDefaultConfigs() {
            // 核心配置类型 - 这是最重要的
            configCache.put("config.type", "file");
            configCache.put("registry.type", "file");

            // 配置文件名
            configCache.put("config.file.name", "file.conf");
            configCache.put("registry.file.name", "registry.conf");

            // 应用配置
            configCache.put("seata.application.id", properties.getApplicationName());
            configCache.put("seata.tx.service.group", properties.getApplicationName() + "_tx_group");

            // UndoLog 相关配置
            configCache.put("client.undo.logTable", properties.getUndoLog().getTableName());
            configCache.put("client.undo.compress.enable", String.valueOf(properties.getUndoLog().isCompressionEnabled()));
            configCache.put("client.undo.compress.type", "gzip");
            configCache.put("client.undo.compress.threshold", formatByteSize(properties.getUndoLog().getCompressionThreshold()));

            // 序列化器配置
            configCache.put("client.undo.logSerialization", "jackson");

            // 数据源配置
            configCache.put("client.rm.datasource.autoCommit", "true");
            configCache.put("client.rm.datasource.maxWait", "5000");

            // 锁配置
            configCache.put("client.rm.lock.retryInterval", "10");
            configCache.put("client.rm.lock.retryTimes", "30");
            configCache.put("client.rm.lock.retryPolicyBranchRollbackOnConflict", "true");

            // 报告配置
            configCache.put("client.rm.reportSuccessEnable", "false");
            configCache.put("client.rm.reportRetryCount", "5");

            // 表元数据配置
            configCache.put("client.rm.tableMetaCheckEnable", "false");
            configCache.put("client.rm.tableMetaCheckerInterval", "60000");

            // 异步提交配置
            configCache.put("client.rm.asyncCommitBufferLimit", "10000");
            configCache.put("client.rm.reportRetryCount", "5");

            log.debug("初始化默认Seata配置完成，配置项数量: {}", configCache.size());
        }

        /**
         * 将字节数格式化为 Seata 期望的单字符单位格式
         */
        private String formatByteSize(int bytes) {
            if (bytes >= 1024 * 1024 * 1024) {
                return (bytes / (1024 * 1024 * 1024)) + "g";
            } else if (bytes >= 1024 * 1024) {
                return (bytes / (1024 * 1024)) + "m";
            } else if (bytes >= 1024) {
                return (bytes / 1024) + "k";
            } else {
                // 对于小于1024的值，直接使用字节数加上'k'单位，避免无单位的情况
                return "1k";
            }
        }

        @Override
        public String getConfig(String dataId) {
            return getConfig(dataId, null, 0L);
        }

        @Override
        public String getConfig(String dataId, String defaultValue) {
            return getConfig(dataId, defaultValue, 0L);
        }

        @Override
        public String getConfig(String dataId, long timeoutMills) {
            return "";
        }

        @Override
        public short getShort(String dataId, short defaultValue, long timeoutMills) {
            return 0;
        }

        @Override
        public short getShort(String dataId, short defaultValue) {
            return 0;
        }

        @Override
        public short getShort(String dataId) {
            return 0;
        }

        @Override
        public int getInt(String dataId, int defaultValue, long timeoutMills) {
            return 0;
        }

        @Override
        public int getInt(String dataId, int defaultValue) {
            return 0;
        }

        @Override
        public int getInt(String dataId) {
            return 0;
        }

        @Override
        public long getLong(String dataId, long defaultValue, long timeoutMills) {
            return 0;
        }

        @Override
        public long getLong(String dataId, long defaultValue) {
            return 0;
        }

        @Override
        public long getLong(String dataId) {
            return 0;
        }

        @Override
        public Duration getDuration(String dataId) {
            return null;
        }

        @Override
        public Duration getDuration(String dataId, Duration defaultValue) {
            return null;
        }

        @Override
        public Duration getDuration(String dataId, Duration defaultValue, long timeoutMills) {
            return null;
        }

        @Override
        public boolean getBoolean(String dataId, boolean defaultValue, long timeoutMills) {
            return false;
        }

        @Override
        public boolean getBoolean(String dataId, boolean defaultValue) {
            return false;
        }

        @Override
        public boolean getBoolean(String dataId) {
            return false;
        }

        @Override
        public String getConfig(String dataId, String defaultValue, long timeoutMills) {
            String value = configCache.get(dataId);
            if (value != null) {
                log.debug("获取配置: {}={}", dataId, value);
                return value;
            }

            // 尝试从系统属性获取
            value = System.getProperty(dataId);
            if (value != null) {
                log.debug("从系统属性获取配置: {}={}", dataId, value);
                return value;
            }

            // 返回默认值
            log.debug("使用默认配置: {}={}", dataId, defaultValue);
            return defaultValue;
        }

        @Override
        public boolean putConfig(String dataId, String content) {
            return putConfig(dataId, content, 0L);
        }

        @Override
        public boolean putConfig(String dataId, String content, long timeoutMills) {
            configCache.put(dataId, content);
            log.debug("设置配置: {}={}", dataId, content);
            return true;
        }

        @Override
        public String getLatestConfig(String dataId, String defaultValue, long timeoutMills) {
            return "";
        }

        @Override
        public boolean putConfigIfAbsent(String dataId, String content) {
            return putConfigIfAbsent(dataId, content, 0L);
        }

        @Override
        public boolean putConfigIfAbsent(String dataId, String content, long timeoutMills) {
            String existing = configCache.putIfAbsent(dataId, content);
            boolean success = existing == null;
            if (success) {
                log.debug("设置配置(如果不存在): {}={}", dataId, content);
            }
            return success;
        }

        @Override
        public boolean removeConfig(String dataId) {
            return removeConfig(dataId, 0L);
        }

        @Override
        public boolean removeConfig(String dataId, long timeoutMills) {
            String removed = configCache.remove(dataId);
            boolean success = removed != null;
            if (success) {
                log.debug("删除配置: {}", dataId);
            }
            return success;
        }

        @Override
        public void addConfigListener(String dataId, io.seata.config.ConfigurationChangeListener listener) {
            // 简化实现，不支持配置监听
            log.debug("添加配置监听器: {} (简化实现，不支持)", dataId);
        }

        @Override
        public void removeConfigListener(String dataId, io.seata.config.ConfigurationChangeListener listener) {
            // 简化实现，不支持配置监听
            log.debug("删除配置监听器: {} (简化实现，不支持)", dataId);
        }

        @Override
        public Set<ConfigurationChangeListener> getConfigListeners(String dataId) {
            return new HashSet<>();
        }

        /**
         * 获取所有配置
         */
        public Map<String, String> getAllConfigs() {
            return new ConcurrentHashMap<>(configCache);
        }

        /**
         * 获取配置数量
         */
        public int getConfigCount() {
            return configCache.size();
        }

        /**
         * 清空配置
         */
        public void clearConfigs() {
            configCache.clear();
            log.debug("清空所有配置");
        }

        @Override
        public String toString() {
            return "TipraySeataConfiguration{" +
                    "configCount=" + configCache.size() +
                    ", undoLogTable='" + properties.getUndoLog().getTableName() + '\'' +
                    '}';
        }
    }
}
