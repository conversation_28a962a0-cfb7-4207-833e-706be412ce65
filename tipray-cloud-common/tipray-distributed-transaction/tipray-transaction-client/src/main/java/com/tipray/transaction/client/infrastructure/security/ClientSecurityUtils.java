package com.tipray.transaction.client.infrastructure.security;


import com.tipray.transaction.core.util.TransactionSecurityUtils;
import lombok.extern.slf4j.Slf4j;

import javax.servlet.http.HttpServletRequest;

/**
 * 客户端安全工具类
 * <p>
 * 提供客户端特有的安全验证功能
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-01-27
 */
@Slf4j
public class ClientSecurityUtils {

    /**
     * 从HTTP请求中提取安全验证信息
     *
     * @param request HTTP请求
     * @return 安全验证信息
     */
    public static SecurityInfo extractSecurityInfo(HttpServletRequest request) {
        SecurityInfo info = new SecurityInfo();

        info.setTransactionId(request.getHeader(TransactionSecurityUtils.HEADER_TRANSACTION_ID));
        info.setBranchId(request.getHeader(TransactionSecurityUtils.HEADER_BRANCH_ID));
        info.setOperation(request.getHeader(TransactionSecurityUtils.HEADER_OPERATION));
        info.setTimestamp(request.getHeader(TransactionSecurityUtils.HEADER_TIMESTAMP));
        info.setSignature(request.getHeader(TransactionSecurityUtils.HEADER_SIGNATURE));
        info.setNonce(request.getHeader(TransactionSecurityUtils.HEADER_NONCE));

        return info;
    }

    /**
     * 验证安全信息的完整性
     *
     * @param info 安全信息
     * @return 验证结果
     */
    public static ValidationResult validateSecurityInfo(SecurityInfo info) {
        ValidationResult result = new ValidationResult();

        // 检查必要字段
        if (info.getTransactionId() == null || info.getTransactionId().trim().isEmpty()) {
            result.setValid(false);
            result.setErrorMessage("缺少事务ID");
            return result;
        }

        if (info.getBranchId() == null || info.getBranchId().trim().isEmpty()) {
            result.setValid(false);
            result.setErrorMessage("缺少分支ID");
            return result;
        }

        if (info.getOperation() == null || info.getOperation().trim().isEmpty()) {
            result.setValid(false);
            result.setErrorMessage("缺少操作类型");
            return result;
        }

        if (info.getTimestamp() == null || info.getTimestamp().trim().isEmpty()) {
            result.setValid(false);
            result.setErrorMessage("缺少时间戳");
            return result;
        }

        if (info.getSignature() == null || info.getSignature().trim().isEmpty()) {
            result.setValid(false);
            result.setErrorMessage("缺少签名");
            return result;
        }

        if (info.getNonce() == null || info.getNonce().trim().isEmpty()) {
            result.setValid(false);
            result.setErrorMessage("缺少随机数");
            return result;
        }

        // 验证数字格式
        try {
            Long.parseLong(info.getBranchId());
        } catch (NumberFormatException e) {
            result.setValid(false);
            result.setErrorMessage("分支ID格式无效");
            return result;
        }

        try {
            Long.parseLong(info.getTimestamp());
        } catch (NumberFormatException e) {
            result.setValid(false);
            result.setErrorMessage("时间戳格式无效");
            return result;
        }

        // 验证操作类型
        if (!TransactionSecurityUtils.isValidOperation(info.getOperation())) {
            result.setValid(false);
            result.setErrorMessage("操作类型无效: " + info.getOperation());
            return result;
        }

        result.setValid(true);
        return result;
    }

    /**
     * 记录安全验证日志
     *
     * @param info        安全信息
     * @param result      验证结果
     * @param requestPath 请求路径
     */
    public static void logSecurityValidation(SecurityInfo info, ValidationResult result, String requestPath) {
        if (result.isValid()) {
            log.debug("安全验证成功: path={}, transactionId={}, branchId={}, operation={}",
                    requestPath, info.getTransactionId(), info.getBranchId(), info.getOperation());
        } else {
            log.warn("安全验证失败: path={}, transactionId={}, branchId={}, operation={}, error={}",
                    requestPath, info.getTransactionId(), info.getBranchId(), info.getOperation(), result.getErrorMessage());
        }
    }

    /**
     * 安全验证信息
     */
    public static class SecurityInfo {
        private String transactionId;
        private String branchId;
        private String operation;
        private String timestamp;
        private String signature;
        private String nonce;

        // Getters and Setters
        public String getTransactionId() {
            return transactionId;
        }

        public void setTransactionId(String transactionId) {
            this.transactionId = transactionId;
        }

        public String getBranchId() {
            return branchId;
        }

        public void setBranchId(String branchId) {
            this.branchId = branchId;
        }

        public String getOperation() {
            return operation;
        }

        public void setOperation(String operation) {
            this.operation = operation;
        }

        public String getTimestamp() {
            return timestamp;
        }

        public void setTimestamp(String timestamp) {
            this.timestamp = timestamp;
        }

        public String getSignature() {
            return signature;
        }

        public void setSignature(String signature) {
            this.signature = signature;
        }

        public String getNonce() {
            return nonce;
        }

        public void setNonce(String nonce) {
            this.nonce = nonce;
        }
    }

    /**
     * 验证结果
     */
    public static class ValidationResult {
        private boolean valid;
        private String errorMessage;

        public boolean isValid() {
            return valid;
        }

        public void setValid(boolean valid) {
            this.valid = valid;
        }

        public String getErrorMessage() {
            return errorMessage;
        }

        public void setErrorMessage(String errorMessage) {
            this.errorMessage = errorMessage;
        }
    }
}
