package com.tipray.transaction.client.domain.model;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.time.LocalDateTime;
import java.util.Objects;

/**
 * 分支事务聚合根
 * <p>
 * 代表云服务端的一个分支事务，包含事务的完整生命周期管理
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-01-27
 */
@Slf4j
@Data
public class BranchTransaction {

    /**
     * 全局事务ID
     */
    private String globalTransactionId;

    /**
     * 分支事务ID
     */
    private Long branchId;

    /**
     * 资源ID
     */
    private String resourceId;

    /**
     * 分支事务状态
     */
    private BranchTransactionStatus status;

    /**
     * 事务类型
     */
    private BranchTransactionType type;

    /**
     * 应用数据
     */
    private String applicationData;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 客户端ID
     */
    private String clientId;

    /**
     * 构造函数
     */
    public BranchTransaction() {
        this.createTime = LocalDateTime.now();
        this.updateTime = LocalDateTime.now();
        this.status = BranchTransactionStatus.REGISTERED;
    }

    /**
     * 构造函数
     *
     * @param globalTransactionId 全局事务ID
     * @param branchId            分支事务ID
     * @param resourceId          资源ID
     * @param type                事务类型
     */
    public BranchTransaction(String globalTransactionId, Long branchId, String resourceId, BranchTransactionType type) {
        this();
        this.globalTransactionId = globalTransactionId;
        this.branchId = branchId;
        this.resourceId = resourceId;
        this.type = type;
    }

    /**
     * 注册分支事务
     *
     * @param applicationData 应用数据
     */
    public void register(String applicationData) {
        if (this.status != BranchTransactionStatus.REGISTERED) {
            throw new IllegalStateException("分支事务状态不正确，无法注册: " + this.status);
        }

        this.applicationData = applicationData;
        this.updateTime = LocalDateTime.now();

        log.info("[{}] 分支事务注册成功 - 分支ID: {}, 资源ID: {}",
                globalTransactionId, branchId, resourceId);
    }

    /**
     * 提交分支事务
     */
    public void commit() {
        if (this.status != BranchTransactionStatus.REGISTERED) {
            throw new IllegalStateException("分支事务状态不正确，无法提交: " + this.status);
        }

        this.status = BranchTransactionStatus.COMMITTED;
        this.updateTime = LocalDateTime.now();

        log.info("[{}] 分支事务提交成功 - 分支ID: {}", globalTransactionId, branchId);
    }

    /**
     * 回滚分支事务
     */
    public void rollback() {
        if (this.status != BranchTransactionStatus.REGISTERED) {
            throw new IllegalStateException("分支事务状态不正确，无法回滚: " + this.status);
        }

        this.status = BranchTransactionStatus.ROLLBACKED;
        this.updateTime = LocalDateTime.now();

        log.info("[{}] 分支事务回滚成功 - 分支ID: {}", globalTransactionId, branchId);
    }

    /**
     * 检查是否可以提交
     *
     * @return 是否可以提交
     */
    public boolean canCommit() {
        return this.status == BranchTransactionStatus.REGISTERED;
    }

    /**
     * 检查是否可以回滚
     *
     * @return 是否可以回滚
     */
    public boolean canRollback() {
        return this.status == BranchTransactionStatus.REGISTERED;
    }

    /**
     * 获取事务标识
     *
     * @return 事务标识
     */
    public String getTransactionKey() {
        return globalTransactionId + ":" + branchId;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        BranchTransaction that = (BranchTransaction) o;
        return Objects.equals(globalTransactionId, that.globalTransactionId) &&
                Objects.equals(branchId, that.branchId);
    }

    @Override
    public int hashCode() {
        return Objects.hash(globalTransactionId, branchId);
    }

    @Override
    public String toString() {
        return "BranchTransaction{" +
                "globalTransactionId='" + globalTransactionId + '\'' +
                ", branchId=" + branchId +
                ", resourceId='" + resourceId + '\'' +
                ", status=" + status +
                ", type=" + type +
                '}';
    }
}
