# 分布式事务日志配置示例

# 生产环境配置
---
spring:
  profiles: production

tipray:
  transaction:
    logging:
      verbose: false                    # 关闭详细日志
      performance-enabled: true        # 启用性能监控
      sql-enabled: false               # 关闭SQL日志
      http-enabled: false              # 关闭HTTP日志
      slow-operation-threshold: 1000   # 慢操作阈值1秒
      timeout-warning-threshold: 30000 # 超时警告30秒

logging:
  level:
    com.tipray.transaction: INFO       # 只显示INFO及以上级别
    root: WARN

---
# 开发环境配置
spring:
  profiles: development

tipray:
  transaction:
    logging:
      verbose: true                     # 启用详细日志
      performance-enabled: true        # 启用性能监控
      sql-enabled: true                # 启用SQL日志
      http-enabled: true               # 启用HTTP日志
      slow-operation-threshold: 500    # 慢操作阈值500ms
      timeout-warning-threshold: 10000 # 超时警告10秒

logging:
  level:
    com.tipray.transaction: DEBUG      # 显示DEBUG级别日志
    root: INFO

---
# 调试环境配置
spring:
  profiles: debug

tipray:
  transaction:
    logging:
      verbose: true                     # 启用详细日志
      performance-enabled: true        # 启用性能监控
      sql-enabled: true                # 启用SQL日志
      http-enabled: true               # 启用HTTP日志
      slow-operation-threshold: 100    # 慢操作阈值100ms
      timeout-warning-threshold: 5000  # 超时警告5秒

logging:
  level:
    com.tipray.transaction: TRACE      # 显示所有级别日志
    com.tipray.transaction.core.infrastructure.logging: DEBUG
    root: DEBUG

  # 日志格式配置
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"
