package com.tipray.transaction.core.application.propagation;

import com.tipray.transaction.core.context.TransactionContextHolder;
import com.tipray.transaction.core.domain.propagation.PropagationResult;
import com.tipray.transaction.core.domain.propagation.TransactionPropagationContext;
import com.tipray.transaction.core.domain.transaction.TransactionContext;
import com.tipray.transaction.core.enums.TransactionPropagation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.transaction.IllegalTransactionStateException;

import java.util.Stack;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 事务传播管理器
 * 处理事务传播行为和嵌套事务管理
 *
 * <AUTHOR>
 * @version 2.0
 * @since 2025-01-23
 */
public class TransactionPropagationManager {

    private static final Logger log = LoggerFactory.getLogger(TransactionPropagationManager.class);

    // 挂起的事务栈，支持嵌套挂起
    private final ThreadLocal<Stack<TransactionContext>> suspendedTransactions =
            ThreadLocal.withInitial(Stack::new);

    // 传播处理器映射
    private final ConcurrentHashMap<TransactionPropagation, PropagationHandler> handlers =
            new ConcurrentHashMap<>();

    public TransactionPropagationManager() {
        initializePropagationHandlers();
    }

    /**
     * 处理事务传播
     */
    public PropagationResult handlePropagation(TransactionPropagationContext propagationContext) {
        TransactionPropagation propagation = propagationContext.getPropagation();
        TransactionContext existingContext = TransactionContextHolder.getCurrentContext();

        log.debug("处理事务传播: {} (当前事务: {})",
                propagation, existingContext != null ? existingContext.getTransactionId() : "无");

        PropagationHandler handler = handlers.get(propagation);
        if (handler == null) {
            throw new IllegalArgumentException("不支持的传播行为: " + propagation);
        }

        return handler.handle(propagationContext, existingContext);
    }

    /**
     * 清理传播上下文
     */
    public void cleanupPropagation(PropagationResult propagationResult) {
        if (propagationResult == null) {
            return;
        }

        try {
            // 如果有挂起的事务，恢复它
            if (propagationResult.getSuspendedContext() != null) {
                resumeTransaction(propagationResult.getSuspendedContext());
            }

            // 如果是新事务，清理上下文
            if (propagationResult.isNewTransactionRequired()) {
                TransactionContextHolder.clearCurrentContext();
            }

        } catch (Exception e) {
            log.error("清理传播上下文失败", e);
        }
    }

    /**
     * 挂起当前事务
     */
    public TransactionContext suspendCurrentTransaction(String reason) {
        TransactionContext currentContext = TransactionContextHolder.getCurrentContext();
        if (currentContext == null) {
            return null;
        }

        // 挂起事务
        currentContext.suspend(reason);

        // 添加到挂起栈
        suspendedTransactions.get().push(currentContext);

        // 清理当前上下文
        TransactionContextHolder.clearCurrentContext();

        log.debug("挂起事务: {} (原因: {})", currentContext.getTransactionId(), reason);

        return currentContext;
    }

    /**
     * 恢复挂起的事务
     */
    public void resumeTransaction(TransactionContext suspendedContext) {
        if (suspendedContext == null) {
            return;
        }

        // 恢复事务
        suspendedContext.resume();

        // 从挂起栈中移除
        Stack<TransactionContext> stack = suspendedTransactions.get();
        if (!stack.isEmpty() && stack.peek().equals(suspendedContext)) {
            stack.pop();
        }

        // 设置为当前上下文
        TransactionContextHolder.setCurrentContext(suspendedContext);

        log.debug("恢复事务: {}", suspendedContext.getTransactionId());
    }

    /**
     * 获取挂起的事务数量
     */
    public int getSuspendedTransactionCount() {
        return suspendedTransactions.get().size();
    }

    /**
     * 清理线程本地变量
     */
    public void cleanup() {
        suspendedTransactions.remove();
    }

    /**
     * 初始化传播处理器
     */
    private void initializePropagationHandlers() {
        // REQUIRED处理器
        handlers.put(TransactionPropagation.REQUIRED, (context, existing) -> {
            if (existing != null) {
                // 加入现有事务
                return PropagationResult.builder()
                        .newTransactionRequired(false)
                        .existingContext(existing)
                        .build();
            } else {
                // 创建新事务
                return PropagationResult.builder()
                        .newTransactionRequired(true)
                        .build();
            }
        });

        // SUPPORTS处理器
        handlers.put(TransactionPropagation.SUPPORTS, (context, existing) -> {
            if (existing != null) {
                // 加入现有事务
                return PropagationResult.builder()
                        .newTransactionRequired(false)
                        .existingContext(existing)
                        .build();
            } else {
                // 非事务执行
                return PropagationResult.builder()
                        .newTransactionRequired(false)
                        .build();
            }
        });

        // MANDATORY处理器
        handlers.put(TransactionPropagation.MANDATORY, (context, existing) -> {
            if (existing != null) {
                // 加入现有事务
                return PropagationResult.builder()
                        .newTransactionRequired(false)
                        .existingContext(existing)
                        .build();
            } else {
                // 抛出异常
                throw new IllegalTransactionStateException("MANDATORY传播行为要求必须存在事务");
            }
        });

        // REQUIRES_NEW处理器
        handlers.put(TransactionPropagation.REQUIRES_NEW, (context, existing) -> {
            TransactionContext suspended = null;
            if (existing != null) {
                // 挂起现有事务
                suspended = suspendCurrentTransaction("REQUIRES_NEW传播行为");
            }

            // 创建新事务
            return PropagationResult.builder()
                    .newTransactionRequired(true)
                    .suspendedContext(suspended)
                    .build();
        });

        // NOT_SUPPORTED处理器
        handlers.put(TransactionPropagation.NOT_SUPPORTED, (context, existing) -> {
            TransactionContext suspended = null;
            if (existing != null) {
                // 挂起现有事务
                suspended = suspendCurrentTransaction("NOT_SUPPORTED传播行为");
            }

            // 非事务执行
            return PropagationResult.builder()
                    .newTransactionRequired(false)
                    .suspendedContext(suspended)
                    .build();
        });

        // NEVER处理器
        handlers.put(TransactionPropagation.NEVER, (context, existing) -> {
            if (existing != null) {
                // 抛出异常
                throw new IllegalTransactionStateException("NEVER传播行为禁止存在事务");
            } else {
                // 非事务执行
                return PropagationResult.builder()
                        .newTransactionRequired(false)
                        .build();
            }
        });

        // NESTED处理器
        handlers.put(TransactionPropagation.NESTED, (context, existing) -> {
            if (existing != null) {
                // 创建嵌套事务
                return PropagationResult.builder()
                        .newTransactionRequired(true)
                        .existingContext(existing)
                        .nested(true)
                        .build();
            } else {
                // 创建新事务
                return PropagationResult.builder()
                        .newTransactionRequired(true)
                        .build();
            }
        });
    }

    /**
     * 传播处理器接口
     */
    @FunctionalInterface
    private interface PropagationHandler {
        PropagationResult handle(TransactionPropagationContext context, TransactionContext existing);
    }
}
