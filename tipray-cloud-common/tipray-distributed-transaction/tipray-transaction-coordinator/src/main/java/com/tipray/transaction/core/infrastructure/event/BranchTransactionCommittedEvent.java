package com.tipray.transaction.core.infrastructure.event;

import com.tipray.transaction.core.domain.coordinator.BranchTransactionInfo;
import org.springframework.context.ApplicationEvent;

/**
 * 分支事务提交事件
 *
 * <AUTHOR>
 * @version 2.0
 * @since 2025-01-23
 */
public class BranchTransactionCommittedEvent extends ApplicationEvent {

    private final BranchTransactionInfo branchInfo;

    public BranchTransactionCommittedEvent(BranchTransactionInfo branchInfo) {
        super(branchInfo);
        this.branchInfo = branchInfo;
    }

    public BranchTransactionInfo getBranchInfo() {
        return branchInfo;
    }

    public String getTransactionId() {
        return branchInfo.getTransactionId();
    }

    public String getBranchId() {
        return branchInfo.getBranchId();
    }

    @Override
    public String toString() {
        return String.format("BranchTransactionCommittedEvent{txId='%s', branchId='%s', endpoint='%s'}",
                branchInfo.getTransactionId(), branchInfo.getBranchId(), branchInfo.getServiceEndpoint());
    }
}
