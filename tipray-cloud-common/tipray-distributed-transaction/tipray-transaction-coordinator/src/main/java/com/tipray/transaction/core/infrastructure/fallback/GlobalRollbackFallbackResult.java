package com.tipray.transaction.core.infrastructure.fallback;

import java.time.LocalDateTime;

/**
 * 全局回滚降级处理结果
 *
 * <AUTHOR>
 * @version 2.0
 * @since 2025-01-23
 */
public class GlobalRollbackFallbackResult {

    private final FallbackResultStatus status;
    private final String message;
    private final LocalDateTime processTime;
    private final String details;

    private GlobalRollbackFallbackResult(FallbackResultStatus status, String message, String details) {
        this.status = status;
        this.message = message;
        this.details = details;
        this.processTime = LocalDateTime.now();
    }

    public static GlobalRollbackFallbackResult success(String message) {
        return new GlobalRollbackFallbackResult(FallbackResultStatus.SUCCESS, message, null);
    }

    public static GlobalRollbackFallbackResult failed(String message) {
        return new GlobalRollbackFallbackResult(FallbackResultStatus.FAILED, message, null);
    }

    public static GlobalRollbackFallbackResult retryLater(String message) {
        return new GlobalRollbackFallbackResult(FallbackResultStatus.RETRY_LATER, message, null);
    }

    public static GlobalRollbackFallbackResult scheduled(String message) {
        return new GlobalRollbackFallbackResult(FallbackResultStatus.SCHEDULED, message, null);
    }

    public static GlobalRollbackFallbackResult manualIntervention(String message) {
        return new GlobalRollbackFallbackResult(FallbackResultStatus.MANUAL_INTERVENTION, message, null);
    }

    public static GlobalRollbackFallbackResult compensationRequired(String message) {
        return new GlobalRollbackFallbackResult(FallbackResultStatus.COMPENSATION_REQUIRED, message, null);
    }

    public static GlobalRollbackFallbackResult withDetails(FallbackResultStatus status, String message, String details) {
        return new GlobalRollbackFallbackResult(status, message, details);
    }

    // Getters
    public FallbackResultStatus getStatus() {
        return status;
    }

    public String getMessage() {
        return message;
    }

    public LocalDateTime getProcessTime() {
        return processTime;
    }

    public String getDetails() {
        return details;
    }

    public boolean isSuccess() {
        return status == FallbackResultStatus.SUCCESS;
    }

    public boolean requiresRetry() {
        return status == FallbackResultStatus.RETRY_LATER || status == FallbackResultStatus.SCHEDULED;
    }

    public boolean requiresManualIntervention() {
        return status == FallbackResultStatus.MANUAL_INTERVENTION;
    }

    @Override
    public String toString() {
        return String.format("GlobalRollbackFallbackResult{status=%s, message='%s', processTime=%s}",
                status, message, processTime);
    }

    /**
     * 降级处理结果状态
     */
    public enum FallbackResultStatus {
        SUCCESS("成功"),
        FAILED("失败"),
        RETRY_LATER("稍后重试"),
        SCHEDULED("已调度"),
        MANUAL_INTERVENTION("需要人工干预"),
        COMPENSATION_REQUIRED("需要补偿");

        private final String description;

        FallbackResultStatus(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }
}
