package com.tipray.transaction.core.application.listener;

import com.tipray.transaction.core.domain.statemachine.branch.BranchStateTransitionContext;
import com.tipray.transaction.core.domain.statemachine.branch.BranchStateTransitionListener;
import com.tipray.transaction.core.domain.transaction.BranchTransactionDO;
import com.tipray.transaction.core.enums.BranchTransactionStatus;
import com.tipray.transaction.core.infrastructure.persistence.AsyncPersistenceManager;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * 分支事务持久化监听器
 * 监听分支事务状态转换，触发持久化操作
 *
 * <AUTHOR>
 * @version 2.0
 * @since 2025-01-29
 */
public class BranchTransactionPersistenceListener implements BranchStateTransitionListener {

    private static final Logger log = LoggerFactory.getLogger(BranchTransactionPersistenceListener.class);

    private final AsyncPersistenceManager persistenceManager;

    public BranchTransactionPersistenceListener(AsyncPersistenceManager persistenceManager) {
        this.persistenceManager = persistenceManager;
    }

    @Override
    public void afterTransition(BranchStateTransitionContext context) {
        Long branchId = context.getBranchTransactionId();
        BranchTransactionStatus toStatus = context.getToStatus();
        String transactionId = context.getTransactionId();

        try {
            switch (toStatus) {
                case REGISTERED:
                    // 分支注册 - 保存分支事务
                    handleBranchRegistered(context);
                    break;

                case EXECUTING:
                case EXECUTED:
                case COMMITTED:
                case ROLLBACKED:
                    // 分支状态变更 - 更新状态
                    handleStatusUpdate(context);
                    break;

                case COMMIT_FAILED:
                case ROLLBACK_FAILED:
                    // 分支失败 - 保存失败记录
                    handleBranchFailed(context);
                    break;

                default:
                    // 其他状态 - 默认更新状态
                    handleStatusUpdate(context);
                    break;
            }

            log.debug("分支事务持久化处理完成: {} -> {} (事务: {}, 分支: {})",
                    context.getFromStatus(), toStatus, transactionId, branchId);

        } catch (Exception e) {
            log.error("分支事务持久化处理失败: {} -> {} (事务: {}, 分支: {})",
                    context.getFromStatus(), toStatus, transactionId, branchId, e);
            // 持久化失败不影响业务流程
        }
    }

    /**
     * 处理分支注册
     */
    private void handleBranchRegistered(BranchStateTransitionContext context) {
        BranchTransactionDO branch = context.getBranchTransaction();
        if (branch != null) {
            persistenceManager.asyncSaveBranchTransaction(branch);
        } else {
            log.warn("分支注册时未提供分支事务数据: {} - {}",
                    context.getTransactionId(), context.getBranchTransactionId());
        }
    }

    /**
     * 处理状态更新
     */
    private void handleStatusUpdate(BranchStateTransitionContext context) {
        persistenceManager.asyncUpdateBranchTransactionStatus(
                context.getBranchTransactionId(), context.getToStatus());
    }

    /**
     * 处理分支失败
     */
    private void handleBranchFailed(BranchStateTransitionContext context) {
        // 更新失败状态
        handleStatusUpdate(context);

        // 保存失败记录
        if (context.isFailure()) {
            Map<String, Object> failureData = buildBranchFailureData(context);

            switch (context.getToStatus()) {
                case COMMIT_FAILED:
                    log.error("分支提交失败: {} - {} (原因: {})",
                            context.getTransactionId(), context.getBranchTransactionId(),
                            context.getFailureMessage());
                    break;

                case ROLLBACK_FAILED:
                    log.error("分支回滚失败: {} - {} (原因: {})",
                            context.getTransactionId(), context.getBranchTransactionId(),
                            context.getFailureMessage());
                    break;
            }
        }
    }

    /**
     * 处理分支超时
     */
    private void handleBranchTimeout(BranchStateTransitionContext context) {
        handleStatusUpdate(context);

        log.warn("分支事务超时: {} - {} (超时信息: {})",
                context.getTransactionId(), context.getBranchTransactionId(),
                context.getFailureMessage());

        // 可以保存超时记录
        // Map<String, Object> timeoutData = buildBranchTimeoutData(context);
        // persistenceManager.asyncSaveBranchTimeoutRecord(timeoutData);
    }

    /**
     * 构建分支失败数据
     */
    private Map<String, Object> buildBranchFailureData(BranchStateTransitionContext context) {
        Map<String, Object> failureData = new HashMap<>();
        failureData.put("transactionId", context.getTransactionId());
        failureData.put("branchTransactionId", context.getBranchTransactionId());
        failureData.put("fromStatus", context.getFromStatus());
        failureData.put("toStatus", context.getToStatus());
        failureData.put("event", context.getEvent());
        failureData.put("timestamp", context.getTimestamp());

        if (context.getFailureCause() != null) {
            failureData.put("exceptionClass", context.getFailureCause().getClass().getSimpleName());
            failureData.put("exceptionMessage", context.getFailureCause().getMessage());
        }

        if (context.getFailureMessage() != null) {
            failureData.put("failureMessage", context.getFailureMessage());
        }

        // 添加分支特有信息
        BranchTransactionDO branch = context.getBranchTransaction();
        if (branch != null) {
            failureData.put("targetService", branch.getTargetService());
            failureData.put("targetMethod", branch.getTargetMethod());
            failureData.put("stepName", branch.getStepName());
            failureData.put("retryCount", branch.getCurrentRetryCount());
            failureData.put("timeoutSeconds", branch.getTimeoutSeconds());
        }

        failureData.putAll(context.getContextData());

        return failureData;
    }

    @Override
    public int getPriority() {
        return 90; // 分支持久化监听器优先级稍低于事务持久化监听器
    }

    @Override
    public String getListenerName() {
        return "BranchTransactionPersistenceListener";
    }

    @Override
    public boolean supports(BranchStateTransitionContext context) {
        // 支持所有分支事务状态转换
        return true;
    }
}
