package com.tipray.transaction.core.infrastructure.barrier.persistence;

/**
 * 屏障存储接口
 * 定义事务屏障数据的存储操作
 *
 * <AUTHOR>
 * @version 2.0
 * @since 2025-01-23
 */
public interface BarrierStorage {

    /**
     * 插入屏障记录
     *
     * @param transactionId 事务ID
     * @param branchId      分支ID
     * @param action        动作类型
     * @return true表示插入成功，false表示已存在
     */
    boolean insertBarrier(String transactionId, Long branchId, String action);

    /**
     * 检查屏障是否存在
     *
     * @param transactionId 事务ID
     * @param branchId      分支ID
     * @param action        动作类型
     * @return true表示存在
     */
    boolean hasBarrier(String transactionId, Long branchId, String action);

    /**
     * 删除屏障记录
     *
     * @param transactionId 事务ID
     */
    void deleteBarrier(String transactionId);

    /**
     * 获取存储统计信息
     *
     * @return 统计信息
     */
    Object getStatistics();
}
