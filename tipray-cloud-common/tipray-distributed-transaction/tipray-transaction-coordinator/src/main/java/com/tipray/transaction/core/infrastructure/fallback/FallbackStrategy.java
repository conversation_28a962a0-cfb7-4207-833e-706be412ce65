package com.tipray.transaction.core.infrastructure.fallback;

/**
 * 降级策略枚举
 * 定义事务回滚失败时的处理策略
 *
 * <AUTHOR>
 * @version 2.0
 * @since 2025-01-23
 */
public enum FallbackStrategy {

    /**
     * 立即重试
     * 适用于临时性异常，如网络抖动、服务暂时不可用等
     */
    RETRY("RETRY", "立即重试"),

    /**
     * 延迟重试
     * 适用于需要等待一段时间后重试的场景，如服务过载、限流等
     */
    DELAYED_RETRY("DELAYED_RETRY", "延迟重试"),

    /**
     * 需要人工干预
     * 适用于系统无法自动处理的异常，需要人工介入解决
     */
    MANUAL_INTERVENTION("MANUAL_INTERVENTION", "需要人工干预"),

    /**
     * 需要补偿
     * 适用于Saga模式下的补偿逻辑执行
     */
    COMPENSATION_REQUIRED("COMPENSATION_REQUIRED", "需要补偿"),

    /**
     * 忽略错误
     * 适用于非关键操作的回滚失败，可以忽略继续执行
     */
    IGNORE_ERROR("IGNORE_ERROR", "忽略错误"),

    /**
     * 降级处理
     * 执行备用的降级逻辑
     */
    DEGRADED_HANDLING("DEGRADED_HANDLING", "降级处理");

    private final String code;
    private final String description;

    FallbackStrategy(String code, String description) {
        this.code = code;
        this.description = description;
    }

    public String getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    @Override
    public String toString() {
        return String.format("%s(%s)", code, description);
    }
}
