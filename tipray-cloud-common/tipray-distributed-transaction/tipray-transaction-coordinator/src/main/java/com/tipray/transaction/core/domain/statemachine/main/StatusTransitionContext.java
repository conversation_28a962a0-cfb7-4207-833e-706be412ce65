package com.tipray.transaction.core.domain.statemachine.main;

import com.tipray.transaction.core.enums.TransactionEvent;
import com.tipray.transaction.core.enums.TransactionStatus;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.HashMap;
import java.util.Map;

/**
 * 状态转换上下文
 * 包含状态转换过程中的所有相关信息
 *
 * <AUTHOR>
 * @version 2.0
 * @since 2025-01-23
 */
public class StatusTransitionContext {

    private final String transactionId;
    private final TransactionStatus fromStatus;
    private final TransactionStatus toStatus;
    private final TransactionEvent event;
    private final String reason;
    private final Exception exception;
    private final long timestamp;
    private final String threadName;
    private final Map<String, Object> attributes;

    private StatusTransitionContext(Builder builder) {
        this.transactionId = builder.transactionId;
        this.fromStatus = builder.fromStatus;
        this.toStatus = builder.toStatus;
        this.event = builder.event;
        this.reason = builder.reason;
        this.exception = builder.exception;
        this.timestamp = builder.timestamp;
        this.threadName = Thread.currentThread().getName();
        this.attributes = new HashMap<>(builder.attributes);
    }

    public static Builder builder() {
        return new Builder();
    }

    /**
     * 获取转换持续时间（毫秒）
     */
    public long getDuration() {
        return System.currentTimeMillis() - timestamp;
    }

    /**
     * 获取转换时间
     */
    public LocalDateTime getTransitionTime() {
        return LocalDateTime.ofInstant(Instant.ofEpochMilli(timestamp), ZoneId.systemDefault());
    }

    /**
     * 判断是否为成功转换
     */
    public boolean isSuccessTransition() {
        return toStatus.isSuccess() ||
                (event != null && event.isSuccessEvent());
    }

    /**
     * 判断是否为失败转换
     */
    public boolean isFailureTransition() {
        return toStatus.isFailure() ||
                (event != null && event.isFailureEvent());
    }

    /**
     * 判断是否为重试转换
     */
    public boolean isRetryTransition() {
        return event != null && event.isRetryEvent();
    }

    /**
     * 判断是否为终结转换
     */
    public boolean isTerminalTransition() {
        return toStatus.isTerminal();
    }

    /**
     * 获取异常信息摘要
     */
    public String getExceptionSummary() {
        if (exception == null) {
            return null;
        }

        StringBuilder summary = new StringBuilder();
        summary.append(exception.getClass().getSimpleName());

        if (exception.getMessage() != null) {
            summary.append(": ").append(exception.getMessage());
        }

        return summary.toString();
    }

    /**
     * 添加属性
     */
    public void setAttribute(String key, Object value) {
        attributes.put(key, value);
    }

    /**
     * 获取属性
     */
    @SuppressWarnings("unchecked")
    public <T> T getAttribute(String key) {
        return (T) attributes.get(key);
    }

    /**
     * 转换为日志格式
     */
    public String toLogString() {
        StringBuilder sb = new StringBuilder();
        sb.append("StatusTransition{");
        sb.append("txId=").append(transactionId);
        sb.append(", from=").append(fromStatus);
        sb.append(", to=").append(toStatus);
        sb.append(", event=").append(event);
        sb.append(", time=").append(getTransitionTime());
        sb.append(", thread=").append(threadName);

        if (reason != null) {
            sb.append(", reason='").append(reason).append("'");
        }

        if (exception != null) {
            sb.append(", exception=").append(getExceptionSummary());
        }

        sb.append("}");
        return sb.toString();
    }

    /**
     * 转换为监控指标标签
     */
    public Map<String, String> toMetricTags() {
        Map<String, String> tags = new HashMap<>();
        tags.put("from_status", fromStatus.name());
        tags.put("to_status", toStatus.name());
        tags.put("event", event != null ? event.name() : "UNKNOWN");
        tags.put("success", String.valueOf(isSuccessTransition()));
        tags.put("terminal", String.valueOf(isTerminalTransition()));

        if (exception != null) {
            tags.put("exception_type", exception.getClass().getSimpleName());
        }

        return tags;
    }

    // getters
    public String getTransactionId() {
        return transactionId;
    }

    public TransactionStatus getFromStatus() {
        return fromStatus;
    }

    public TransactionStatus getToStatus() {
        return toStatus;
    }

    public TransactionEvent getEvent() {
        return event;
    }

    public String getReason() {
        return reason;
    }

    public Exception getException() {
        return exception;
    }

    public long getTimestamp() {
        return timestamp;
    }

    public String getThreadName() {
        return threadName;
    }

    public Map<String, Object> getAttributes() {
        return new HashMap<>(attributes);
    }

    @Override
    public String toString() {
        return toLogString();
    }

    public static class Builder {
        private String transactionId;
        private TransactionStatus fromStatus;
        private TransactionStatus toStatus;
        private TransactionEvent event;
        private String reason;
        private Exception exception;
        private long timestamp = System.currentTimeMillis();
        private Map<String, Object> attributes = new HashMap<>();

        public Builder transactionId(String transactionId) {
            this.transactionId = transactionId;
            return this;
        }

        public Builder fromStatus(TransactionStatus fromStatus) {
            this.fromStatus = fromStatus;
            return this;
        }

        public Builder toStatus(TransactionStatus toStatus) {
            this.toStatus = toStatus;
            return this;
        }

        public Builder event(TransactionEvent event) {
            this.event = event;
            return this;
        }

        public Builder reason(String reason) {
            this.reason = reason;
            return this;
        }

        public Builder exception(Exception exception) {
            this.exception = exception;
            return this;
        }

        public Builder timestamp(long timestamp) {
            this.timestamp = timestamp;
            return this;
        }

        public Builder attribute(String key, Object value) {
            this.attributes.put(key, value);
            return this;
        }

        public StatusTransitionContext build() {
            if (transactionId == null) {
                throw new IllegalArgumentException("事务ID不能为空");
            }
            if (fromStatus == null) {
                throw new IllegalArgumentException("源状态不能为空");
            }
            if (toStatus == null) {
                throw new IllegalArgumentException("目标状态不能为空");
            }

            return new StatusTransitionContext(this);
        }
    }
}
