package com.tipray.transaction.core.infrastructure.fallback;

import com.tipray.transaction.core.enums.TransactionMode;
import com.tipray.transaction.core.enums.TransactionStatus;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 全局回滚失败记录
 * 记录事务整体回滚失败的完整信息
 *
 * <AUTHOR>
 * @version 2.0
 * @since 2025-01-23
 */
public class GlobalRollbackFailureRecord {

    private final String transactionId;
    private final TransactionMode transactionMode;
    private final TransactionStatus transactionStatus;
    private final String applicationName;
    private final String methodSignature;
    private final ExceptionInfo originalException;
    private final ExceptionInfo rollbackException;
    private final LocalDateTime failureTime;
    private final Map<String, Object> contextSnapshot;
    private final int maxRetryCount;
    private final List<ExceptionInfo> retryExceptions;
    // 可变字段
    private int retryCount;
    private boolean requiresManualIntervention;
    private boolean requiresCompensation;
    private LocalDateTime lastRetryTime;
    private String notes;

    private GlobalRollbackFailureRecord(Builder builder) {
        this.transactionId = builder.transactionId;
        this.transactionMode = builder.transactionMode;
        this.transactionStatus = builder.transactionStatus;
        this.applicationName = builder.applicationName;
        this.methodSignature = builder.methodSignature;
        this.originalException = builder.originalException;
        this.rollbackException = builder.rollbackException;
        this.failureTime = builder.failureTime;
        this.contextSnapshot = builder.contextSnapshot;
        this.retryCount = builder.retryCount;
        this.maxRetryCount = builder.maxRetryCount;
        this.retryExceptions = new ArrayList<>();
        this.requiresManualIntervention = false;
        this.requiresCompensation = false;
    }

    public static Builder builder() {
        return new Builder();
    }

    // Getters
    public String getTransactionId() {
        return transactionId;
    }

    public TransactionMode getTransactionMode() {
        return transactionMode;
    }

    public TransactionStatus getTransactionStatus() {
        return transactionStatus;
    }

    public String getApplicationName() {
        return applicationName;
    }

    public String getMethodSignature() {
        return methodSignature;
    }

    public ExceptionInfo getOriginalException() {
        return originalException;
    }

    public ExceptionInfo getRollbackException() {
        return rollbackException;
    }

    public LocalDateTime getFailureTime() {
        return failureTime;
    }

    public Map<String, Object> getContextSnapshot() {
        return contextSnapshot;
    }

    public int getRetryCount() {
        return retryCount;
    }

    public int getMaxRetryCount() {
        return maxRetryCount;
    }

    public List<ExceptionInfo> getRetryExceptions() {
        return new ArrayList<>(retryExceptions);
    }

    public boolean isRequiresManualIntervention() {
        return requiresManualIntervention;
    }

    public void setRequiresManualIntervention(boolean requiresManualIntervention) {
        this.requiresManualIntervention = requiresManualIntervention;
    }

    public boolean isRequiresCompensation() {
        return requiresCompensation;
    }

    public void setRequiresCompensation(boolean requiresCompensation) {
        this.requiresCompensation = requiresCompensation;
    }

    public LocalDateTime getLastRetryTime() {
        return lastRetryTime;
    }

    public String getNotes() {
        return notes;
    }

    public void setNotes(String notes) {
        this.notes = notes;
    }

    // 操作方法
    public void incrementRetryCount() {
        this.retryCount++;
        this.lastRetryTime = LocalDateTime.now();
    }

    public void addRetryException(ExceptionInfo exceptionInfo) {
        this.retryExceptions.add(exceptionInfo);
    }

    /**
     * 判断是否已达到最大重试次数
     */
    public boolean isMaxRetryReached() {
        return retryCount >= maxRetryCount;
    }

    /**
     * 获取失败持续时间（分钟）
     */
    public long getFailureDurationMinutes() {
        return java.time.Duration.between(failureTime, LocalDateTime.now()).toMinutes();
    }

    /**
     * 获取摘要信息
     */
    public String getSummary() {
        return String.format("全局回滚失败: %s [%s] - 原因: %s, 回滚异常: %s, 重试: %d/%d",
                transactionId, transactionMode,
                originalException != null ? originalException.getSimpleInfo() : "未知",
                rollbackException != null ? rollbackException.getSimpleInfo() : "未知",
                retryCount, maxRetryCount);
    }

    @Override
    public String toString() {
        return String.format("GlobalRollbackFailureRecord{transactionId='%s', mode=%s, " +
                        "failureTime=%s, retryCount=%d, requiresManualIntervention=%s}",
                transactionId, transactionMode, failureTime, retryCount, requiresManualIntervention);
    }

    public static class Builder {
        private String transactionId;
        private TransactionMode transactionMode;
        private TransactionStatus transactionStatus;
        private String applicationName;
        private String methodSignature;
        private ExceptionInfo originalException;
        private ExceptionInfo rollbackException;
        private LocalDateTime failureTime;
        private Map<String, Object> contextSnapshot;
        private int retryCount = 0;
        private int maxRetryCount = 3;

        public Builder transactionId(String transactionId) {
            this.transactionId = transactionId;
            return this;
        }

        public Builder transactionMode(TransactionMode transactionMode) {
            this.transactionMode = transactionMode;
            return this;
        }

        public Builder transactionStatus(TransactionStatus transactionStatus) {
            this.transactionStatus = transactionStatus;
            return this;
        }

        public Builder applicationName(String applicationName) {
            this.applicationName = applicationName;
            return this;
        }

        public Builder methodSignature(String methodSignature) {
            this.methodSignature = methodSignature;
            return this;
        }

        public Builder originalException(ExceptionInfo originalException) {
            this.originalException = originalException;
            return this;
        }

        public Builder rollbackException(ExceptionInfo rollbackException) {
            this.rollbackException = rollbackException;
            return this;
        }

        public Builder failureTime(LocalDateTime failureTime) {
            this.failureTime = failureTime;
            return this;
        }

        public Builder contextSnapshot(Map<String, Object> contextSnapshot) {
            this.contextSnapshot = contextSnapshot;
            return this;
        }

        public Builder retryCount(int retryCount) {
            this.retryCount = retryCount;
            return this;
        }

        public Builder maxRetryCount(int maxRetryCount) {
            this.maxRetryCount = maxRetryCount;
            return this;
        }

        public GlobalRollbackFailureRecord build() {
            return new GlobalRollbackFailureRecord(this);
        }
    }
}
