package com.tipray.transaction.core.domain.statemachine.branch;

import com.tipray.transaction.core.context.TransactionContextHolder;
import com.tipray.transaction.core.domain.transaction.BranchTransactionDO;
import com.tipray.transaction.core.enums.BranchTransactionEvent;
import com.tipray.transaction.core.enums.BranchTransactionStatus;
import com.tipray.transaction.core.infrastructure.metrics.TransactionMetricsCollector;
import com.tipray.transaction.core.util.LoggingUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.locks.ReentrantReadWriteLock;

/**
 * 分支事务状态机
 * 管理分支事务的状态转换，支持监听器机制实现持久化解耦
 *
 * <AUTHOR>
 * @version 2.0
 * @since 2025-01-29
 */
@Slf4j
public class BranchTransactionStateMachine {

    private final TransactionMetricsCollector metricsCollector;

    // 分支事务状态缓存 (branchId -> status)
    private final Map<Long, BranchTransactionStatus> statusCache = new ConcurrentHashMap<>();

    // 读写锁，保证状态转换的线程安全
    private final ReentrantReadWriteLock lock = new ReentrantReadWriteLock();

    // 分支状态转换监听器列表（按优先级排序）
    private final List<BranchStateTransitionListener> listeners = new ArrayList<>();

    public BranchTransactionStateMachine(TransactionMetricsCollector metricsCollector,
                                         BranchStateTransitionListener listener) {
        this.metricsCollector = metricsCollector;
        registerListener(listener);
    }

    /**
     * 注册分支状态转换监听器
     *
     * @param listener 监听器
     */
    public void registerListener(BranchStateTransitionListener listener) {
        if (listener == null) {
            log.warn("[{}|{}] [WARN] - 尝试注册空的分支状态转换监听器",
                    LoggingUtils.getTxId(), LoggingUtils.getBranchId());
            return;
        }

        synchronized (listeners) {
            listeners.add(listener);
            // 按优先级排序，优先级高的先执行
            listeners.sort(Comparator.comparingInt(BranchStateTransitionListener::getPriority).reversed());
        }

        log.debug("[{}|{}] [DEBUG] - 注册分支状态转换监听器 {}",
                LoggingUtils.getTxId(), LoggingUtils.getBranchId(),
                LoggingUtils.formatContext("name", listener.getListenerName(), "priority", listener.getPriority()));
    }

    /**
     * 移除分支状态转换监听器
     *
     * @param listener 监听器
     */
    public void removeListener(BranchStateTransitionListener listener) {
        if (listener == null) {
            return;
        }

        synchronized (listeners) {
            boolean removed = listeners.remove(listener);
            if (removed) {
                log.debug("[{}|{}] [DEBUG] - 移除分支状态转换监听器 {}",
                        LoggingUtils.getTxId(), LoggingUtils.getBranchId(),
                        LoggingUtils.formatContext("name", listener.getListenerName()));
            }
        }
    }

    /**
     * 分支状态转换（成功场景）
     *
     * @param branchId 分支事务ID
     * @param event    触发事件
     * @param branch   分支事务对象
     * @return 转换是否成功
     */
    public boolean transition(Long branchId, BranchTransactionEvent event, BranchTransactionDO branch) {
        if (branchId == null || event == null) {
            log.warn("[{}|{}] [WARN] - 分支状态转换参数不能为空 {}",
                    LoggingUtils.getTxId(branch != null ? branch.getTransactionId() : null),
                    LoggingUtils.getBranchId(branchId),
                    LoggingUtils.formatContext("event", event));
            return false;
        }

        BranchTransactionStatus fromStatus = getCurrentStatus(branchId);

        // 自动处理null状态（兜底机制）
        if (fromStatus == null) {
            fromStatus = inferInitialBranchStatus(event);
            if (fromStatus != null) {
                // 自动初始化状态到缓存
                statusCache.put(branchId, fromStatus);
                log.warn("[{}|{}] [WARN] - 分支状态未初始化，自动推断初始状态 {}",
                        LoggingUtils.getTxId(branch.getTransactionId()),
                        LoggingUtils.getBranchId(branchId),
                        LoggingUtils.formatContext("推断状态", fromStatus, "事件", event));
            } else {
                log.error("[{}|{}] [ERROR] - 无法推断分支初始状态 {}",
                        LoggingUtils.getTxId(branch.getTransactionId()),
                        LoggingUtils.getBranchId(branchId),
                        LoggingUtils.formatContext("事件", event));
                return false;
            }
        }

        BranchTransactionStatus toStatus = calculateTargetStatus(fromStatus, event);

        if (toStatus == null) {
            log.warn("[{}|{}] [WARN] - 无法根据事件计算分支目标状态 {}",
                    LoggingUtils.getTxId(), LoggingUtils.getBranchId(),
                    LoggingUtils.formatContext("从状态", fromStatus, "事件", event));
            return false;
        }

        BranchStateTransitionContext context = BranchStateTransitionContext.success(
                branch != null ? branch.getTransactionId() : null,
                branchId, fromStatus, toStatus, event, branch);

        boolean success = executeTransition(context);

        // 状态机负责更新分支事务对象的内存状态
        if (success && branch != null) {
            updateBranchTransactionStatus(branch, toStatus);
        }

        return success;
    }

    /**
     * 分支状态转换（失败场景）
     *
     * @param branchId 分支事务ID
     * @param event    触发事件
     * @param cause    失败原因
     * @param message  失败消息
     * @return 转换是否成功
     */
    public boolean transitionWithFailure(Long branchId, BranchTransactionEvent event, BranchTransactionDO branch, Exception cause, String message) {
        if (branchId == null || event == null) {
            log.warn("[{}|{}] [WARN] - 分支状态转换参数不能为空 {}",
                    LoggingUtils.getTxId(), LoggingUtils.getBranchId(),
                    LoggingUtils.formatContext("event", event));
            return false;
        }

        BranchTransactionStatus fromStatus = getCurrentStatus(branchId);

        // 自动处理null状态（兜底机制）
        if (fromStatus == null) {
            fromStatus = inferInitialBranchStatus(event);
            if (fromStatus != null) {
                // 自动初始化状态到缓存
                statusCache.put(branchId, fromStatus);
                log.warn("[{}|{}] [WARN] - 分支状态未初始化，自动推断初始状态 {}",
                        LoggingUtils.getTxId(), LoggingUtils.getBranchId(),
                        LoggingUtils.formatContext("推断状态", fromStatus, "失败事件", event));
            } else {
                log.error("[{}|{}] [ERROR] - 无法推断分支初始状态 {}",
                        LoggingUtils.getTxId(), LoggingUtils.getBranchId(),
                        LoggingUtils.formatContext("失败事件", event));
                return false;
            }
        }

        BranchTransactionStatus toStatus = calculateTargetStatus(fromStatus, event);

        if (toStatus == null) {
            log.warn("[{}|{}] [WARN] - 无法根据事件计算分支目标状态 {}",
                    LoggingUtils.getTxId(), LoggingUtils.getBranchId(),
                    LoggingUtils.formatContext("从状态", fromStatus, "失败事件", event));
            return false;
        }

        BranchStateTransitionContext context = BranchStateTransitionContext.failure(
                null, branchId, fromStatus, toStatus, event, cause, message);

        boolean success = executeTransition(context);

        // 状态机负责更新分支事务对象的内存状态
        if (success && branch != null) {
            updateBranchTransactionStatus(branch, toStatus);
        }

        return success;
    }

    /**
     * 执行分支状态转换（核心方法）
     *
     * @param context 分支状态转换上下文
     * @return 转换是否成功
     */
    public boolean executeTransition(BranchStateTransitionContext context) {
        Long branchId = context.getBranchTransactionId();
        BranchTransactionStatus fromStatus = context.getFromStatus();
        BranchTransactionStatus toStatus = context.getToStatus();
        BranchTransactionEvent event = context.getEvent();

        lock.writeLock().lock();
        try {
            // 1. 验证状态转换是否合法
            if (fromStatus != null && !isValidTransition(fromStatus, toStatus)) {
                log.warn("[{}|{}] [WARN] - 非法分支状态转换 {}",
                        LoggingUtils.getTxId(), LoggingUtils.getBranchId(),
                        LoggingUtils.formatContext("从状态", fromStatus, "到状态", toStatus, "事件", event));
                return false;
            }

            // 2. 执行转换前监听器
            executeBeforeTransitionListeners(context);

            // 3. 执行实际状态转换（同步更新内存状态）
            boolean success = doTransition(branchId, fromStatus, toStatus, event);

            if (success) {
                // 4. 执行转换后监听器（异步持久化）
                executeAfterTransitionListeners(context);
            }

            return success;

        } catch (Exception e) {
            log.error("[{}|{}] [ERROR] - 分支状态转换执行失败 {}",
                    LoggingUtils.getTxId(), LoggingUtils.getBranchId(),
                    LoggingUtils.formatException(e), e);

            // 5. 执行失败监听器
            executeFailureListeners(context, e);

            return false;
        } finally {
            lock.writeLock().unlock();
        }
    }

    /**
     * 初始化分支状态
     * 显式设置分支事务的初始状态到缓存中
     *
     * @param branchId      分支事务ID
     * @param initialStatus 初始状态
     */
    public void initializeBranchStatus(Long branchId, BranchTransactionStatus initialStatus) {
        if (branchId == null || initialStatus == null) {
            log.warn("[{}|{}] [WARN] - 初始化分支状态参数不能为空 {}",
                    LoggingUtils.getTxId(), LoggingUtils.getBranchId(),
                    LoggingUtils.formatContext("initialStatus", initialStatus));
            return;
        }

        statusCache.put(branchId, initialStatus);
        log.debug("[{}|{}] [DEBUG] - 初始化分支状态 {}",
                LoggingUtils.getTxId(), LoggingUtils.getBranchId(),
                LoggingUtils.formatContext("status", initialStatus));
    }

    /**
     * 获取当前状态
     */
    public BranchTransactionStatus getCurrentStatus(Long branchId) {
        if (branchId == null) {
            return null;
        }
        return statusCache.get(branchId);
    }

    /**
     * 清理分支状态
     */
    public void cleanupBranchStatus(Long branchId) {
        if (branchId != null) {
            statusCache.remove(branchId);
            log.debug("[{}|{}] [DEBUG] - 清理分支状态",
                    LoggingUtils.getTxId(), LoggingUtils.getBranchId());
        }
    }

    /**
     * 更新分支事务对象状态
     * 状态机负责同步更新内存中的分支事务对象状态
     *
     * @param branch    分支事务对象
     * @param newStatus 新状态
     */
    private void updateBranchTransactionStatus(BranchTransactionDO branch, BranchTransactionStatus newStatus) {
        try {
            if (branch != null) {
                branch.setStatus(newStatus);
                log.debug("[{}|{}] [DEBUG] - 状态机更新分支事务状态 {}",
                        LoggingUtils.getTxId(), LoggingUtils.getBranchId(),
                        LoggingUtils.formatContext("status", newStatus));
            }
        } catch (Exception e) {
            log.error("[{}|{}] [ERROR] - 状态机更新分支事务状态失败 {}",
                    LoggingUtils.getTxId(), LoggingUtils.getBranchId(),
                    LoggingUtils.formatException(e), e);
            // 更新失败不影响状态机本身的状态转换
        }
    }

    /**
     * 推断分支初始状态（兜底机制）
     * 根据事件推断可能的初始状态
     *
     * @param event 触发事件
     * @return 推断的初始状态，如果无法推断则返回null
     */
    private BranchTransactionStatus inferInitialBranchStatus(BranchTransactionEvent event) {
        switch (event) {
            case REGISTER:
                // 分支注册事件，这是分支的第一个事件，初始状态应该是UNKNOWN
                return BranchTransactionStatus.UNKNOWN;

            case START_EXECUTE:
                // 开始执行事件，说明分支已经注册，初始状态应该是REGISTERED
                return BranchTransactionStatus.REGISTERED;

            case EXECUTE_SUCCESS:
            case EXECUTE_FAILURE:
                // 执行结果事件，说明分支正在执行，初始状态应该是EXECUTING
                return BranchTransactionStatus.EXECUTING;

            case START_COMMIT:
            case COMMIT_SUCCESS:
            case COMMIT_FAILURE:
                // 提交相关事件，说明分支已执行完成，初始状态应该是EXECUTED
                return BranchTransactionStatus.EXECUTED;

            case START_ROLLBACK:
            case ROLLBACK_SUCCESS:
            case ROLLBACK_FAILURE:
                // 回滚相关事件，可能从多个状态触发，默认为EXECUTED
                return BranchTransactionStatus.EXECUTED;

            case TIMEOUT:
                // 超时事件，通常发生在执行阶段
                return BranchTransactionStatus.EXECUTING;

            case RETRY:
                // 重试事件，通常是从失败状态重试，无法确定具体的初始状态
                return null;

            case MANUAL_INTERVENTION:
                // 人工干预事件，通常发生在失败后，无法确定具体的初始状态
                return null;

            default:
                // 其他事件无法推断初始状态
                log.warn("[{}|{}] [WARN] - 无法推断分支事件的初始状态 {}",
                        LoggingUtils.getTxId(), LoggingUtils.getBranchId(),
                        LoggingUtils.formatContext("事件", event));
                return null;
        }
    }

    // ==================== 私有方法 ====================

    /**
     * 执行实际状态转换
     */
    private boolean doTransition(Long branchId, BranchTransactionStatus fromStatus,
                                 BranchTransactionStatus toStatus, BranchTransactionEvent event) {
        long startTime = System.currentTimeMillis();

        try {
            // 1. 更新内存缓存（同步操作）
            statusCache.put(branchId, toStatus);

            // 2. 记录指标
            long duration = System.currentTimeMillis() - startTime;
            metricsCollector.recordBranchStatusTransition(fromStatus, toStatus, duration);

            log.debug("[{}|{}] [STATE] - {} -> {} {}",
                    LoggingUtils.getTxId(), LoggingUtils.getBranchId(),
                    fromStatus, toStatus,
                    LoggingUtils.formatContext("event", event.toString(), "cost", duration + "ms"));

            return true;

        } catch (Exception e) {
            long duration = System.currentTimeMillis() - startTime;
            metricsCollector.recordBranchStatusTransitionFailure(fromStatus, toStatus, duration);

            log.error("[{}|{}] [ERROR] - 分支状态转换失败 {}",
                    LoggingUtils.getTxId(), LoggingUtils.getBranchId(),
                    LoggingUtils.formatException(e), e);

            return false;
        }
    }

    /**
     * 计算目标状态
     */
    private BranchTransactionStatus calculateTargetStatus(BranchTransactionStatus fromStatus, BranchTransactionEvent event) {
        // 根据事件计算目标状态的逻辑
        switch (event) {
            case REGISTER:
                return BranchTransactionStatus.REGISTERED;

            case REGISTER_FAILURE:
                return BranchTransactionStatus.FAILED;

            case START_EXECUTE:
                return BranchTransactionStatus.EXECUTING;

            case EXECUTE_SUCCESS:
                return BranchTransactionStatus.EXECUTED;

            case EXECUTE_FAILURE:
                return BranchTransactionStatus.FAILED; // 执行失败状态

            case START_COMMIT:
                return BranchTransactionStatus.COMMITTING; // 开始提交 -> 提交中

            case COMMIT_SUCCESS:
                return BranchTransactionStatus.COMMITTED; // 提交成功 -> 已提交

            case COMMIT_FAILURE:
                return BranchTransactionStatus.COMMIT_FAILED; // 提交失败

            case START_ROLLBACK:
                return BranchTransactionStatus.ROLLBACKING; // 开始回滚 -> 回滚中

            case ROLLBACK_SUCCESS:
                return BranchTransactionStatus.ROLLBACKED; // 回滚成功 -> 已回滚

            case ROLLBACK_FAILURE:
                return BranchTransactionStatus.ROLLBACK_FAILED; // 回滚失败

            case TIMEOUT:
                // 超时可能发生在任何阶段，通常导致失败状态
                if (fromStatus == BranchTransactionStatus.EXECUTING) {
                    return BranchTransactionStatus.FAILED; // 执行超时
                } else if (fromStatus == BranchTransactionStatus.COMMITTING) {
                    return BranchTransactionStatus.COMMIT_FAILED; // 提交超时
                } else if (fromStatus == BranchTransactionStatus.ROLLBACKING) {
                    return BranchTransactionStatus.ROLLBACK_FAILED; // 回滚超时
                } else {
                    return BranchTransactionStatus.FAILED; // 默认失败
                }

            case RETRY:
                // 重试事件根据当前状态决定目标状态
                if (fromStatus == BranchTransactionStatus.COMMIT_FAILED) {
                    return BranchTransactionStatus.COMMITTING; // 重试提交
                } else if (fromStatus == BranchTransactionStatus.ROLLBACK_FAILED) {
                    return BranchTransactionStatus.ROLLBACKING; // 重试回滚
                } else if (fromStatus == BranchTransactionStatus.FAILED) {
                    return BranchTransactionStatus.EXECUTING; // 重试执行
                } else {
                    return fromStatus; // 其他情况保持当前状态
                }

            case MANUAL_INTERVENTION:
                // 需要人工干预，保持当前状态等待处理
                return fromStatus;

            default:
                log.warn("[{}|{}] [WARN] - 未知的分支事务事件 {}",
                        LoggingUtils.getTxId(), LoggingUtils.getBranchId(),
                        LoggingUtils.formatContext("事件", event));
                return null;
        }
    }

    /**
     * 验证状态转换是否合法
     */
    private boolean isValidTransition(BranchTransactionStatus fromStatus, BranchTransactionStatus toStatus) {
        // 简单的状态转换验证逻辑
        if (fromStatus == toStatus) {
            return true; // 允许相同状态转换
        }

        // 可以根据业务需要添加更复杂的验证逻辑
        return true;
    }

    /**
     * 执行转换前监听器
     */
    private void executeBeforeTransitionListeners(BranchStateTransitionContext context) {
        List<BranchStateTransitionListener> currentListeners;
        synchronized (listeners) {
            currentListeners = new ArrayList<>(listeners);
        }

        for (BranchStateTransitionListener listener : currentListeners) {
            try {
                if (listener.supports(context)) {
                    listener.beforeTransition(context);
                }
            } catch (Exception e) {
                log.error("[{}|{}] [ERROR] - 执行分支转换前监听器失败 {}",
                        LoggingUtils.getTxId(), LoggingUtils.getBranchId(),
                        LoggingUtils.formatException(e), e);
            }
        }
    }

    /**
     * 执行转换后监听器
     */
    private void executeAfterTransitionListeners(BranchStateTransitionContext context) {
        List<BranchStateTransitionListener> currentListeners;
        synchronized (listeners) {
            currentListeners = new ArrayList<>(listeners);
        }

        for (BranchStateTransitionListener listener : currentListeners) {
            try {
                if (listener.supports(context)) {
                    listener.afterTransition(context);
                }
            } catch (Exception e) {
                log.error("[{}|{}] [ERROR] - 执行分支转换后监听器失败 {}",
                        LoggingUtils.getTxId(), LoggingUtils.getBranchId(),
                        LoggingUtils.formatException(e), e);
                // 持久化失败不影响业务流程，只记录日志
            }
        }
    }

    /**
     * 执行失败监听器
     */
    private void executeFailureListeners(BranchStateTransitionContext context, Exception cause) {
        List<BranchStateTransitionListener> currentListeners;
        synchronized (listeners) {
            currentListeners = new ArrayList<>(listeners);
        }

        for (BranchStateTransitionListener listener : currentListeners) {
            try {
                if (listener.supports(context)) {
                    listener.onTransitionFailure(context, cause);
                }
            } catch (Exception e) {
                log.error("[{}|{}] [ERROR] - 执行分支失败监听器失败 {}",
                        LoggingUtils.getTxId(), LoggingUtils.getBranchId(),
                        LoggingUtils.formatException(e), e);
            }
        }
    }
}
