package com.tipray.transaction.core.domain.coordinator;

import com.tipray.transaction.core.enums.BranchTransactionStatus;

/**
 * 分支事务信息
 * 记录分支事务的详细信息
 *
 * <AUTHOR>
 * @version 2.0
 * @since 2025-01-23
 */
public class BranchTransactionInfo {

    private final String transactionId;
    private final String branchId;
    private final String serviceEndpoint;
    private final String serviceMethod;
    private final Object requestData;
    private final long registrationTime;

    private volatile BranchTransactionStatus status;
    private volatile long executionTime;
    private volatile long commitTime;
    private volatile long rollbackTime;
    private volatile Object result;
    private volatile Exception exception;

    private BranchTransactionInfo(Builder builder) {
        this.transactionId = builder.transactionId;
        this.branchId = builder.branchId;
        this.serviceEndpoint = builder.serviceEndpoint;
        this.serviceMethod = builder.serviceMethod;
        this.requestData = builder.requestData;
        this.registrationTime = builder.registrationTime;
        this.status = builder.status;
    }

    public static Builder builder() {
        return new Builder();
    }

    /**
     * 获取分支事务执行时长（毫秒）
     */
    public long getExecutionDuration() {
        if (executionTime > 0 && registrationTime > 0) {
            return executionTime - registrationTime;
        }
        return 0;
    }

    /**
     * 获取分支事务总时长（毫秒）
     */
    public long getTotalDuration() {
        long endTime = Math.max(commitTime, rollbackTime);
        if (endTime > 0 && registrationTime > 0) {
            return endTime - registrationTime;
        }
        return System.currentTimeMillis() - registrationTime;
    }

    /**
     * 判断是否执行成功
     */
    public boolean isExecutionSuccessful() {
        return status == BranchTransactionStatus.EXECUTED ||
                status == BranchTransactionStatus.COMMITTED;
    }

    /**
     * 判断是否最终成功
     */
    public boolean isFinallySuccessful() {
        return status == BranchTransactionStatus.COMMITTED;
    }

    /**
     * 判断是否失败
     */
    public boolean isFailed() {
        return status == BranchTransactionStatus.FAILED ||
                status == BranchTransactionStatus.COMMIT_FAILED ||
                status == BranchTransactionStatus.ROLLBACK_FAILED;
    }

    /**
     * 判断是否已完成
     */
    public boolean isCompleted() {
        return status == BranchTransactionStatus.COMMITTED ||
                status == BranchTransactionStatus.ROLLBACKED ||
                isFailed();
    }

    /**
     * 获取异常摘要
     */
    public String getExceptionSummary() {
        if (exception == null) {
            return null;
        }

        StringBuilder summary = new StringBuilder();
        summary.append(exception.getClass().getSimpleName());

        if (exception.getMessage() != null) {
            summary.append(": ").append(exception.getMessage());
        }

        return summary.toString();
    }

    /**
     * 转换为摘要信息
     */
    public BranchTransactionSummary toSummary() {
        return BranchTransactionSummary.builder()
                .transactionId(transactionId)
                .branchId(branchId)
                .serviceEndpoint(serviceEndpoint)
                .status(status)
                .registrationTime(registrationTime)
                .executionDuration(getExecutionDuration())
                .totalDuration(getTotalDuration())
                .successful(isFinallySuccessful())
                .failed(isFailed())
                .build();
    }

    // getters and setters
    public String getTransactionId() {
        return transactionId;
    }

    public String getBranchId() {
        return branchId;
    }

    public String getServiceEndpoint() {
        return serviceEndpoint;
    }

    public String getServiceMethod() {
        return serviceMethod;
    }

    public Object getRequestData() {
        return requestData;
    }

    public long getRegistrationTime() {
        return registrationTime;
    }

    public BranchTransactionStatus getStatus() {
        return status;
    }

    public void setStatus(BranchTransactionStatus status) {
        this.status = status;
    }

    public long getExecutionTime() {
        return executionTime;
    }

    public void setExecutionTime(long executionTime) {
        this.executionTime = executionTime;
    }

    public long getCommitTime() {
        return commitTime;
    }

    public void setCommitTime(long commitTime) {
        this.commitTime = commitTime;
    }

    public long getRollbackTime() {
        return rollbackTime;
    }

    public void setRollbackTime(long rollbackTime) {
        this.rollbackTime = rollbackTime;
    }

    public Object getResult() {
        return result;
    }

    public void setResult(Object result) {
        this.result = result;
    }

    public Exception getException() {
        return exception;
    }

    public void setException(Exception exception) {
        this.exception = exception;
    }

    @Override
    public String toString() {
        return String.format("BranchTransactionInfo{txId='%s', branchId='%s', endpoint='%s', status=%s}",
                transactionId, branchId, serviceEndpoint, status);
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        BranchTransactionInfo that = (BranchTransactionInfo) o;
        return java.util.Objects.equals(transactionId, that.transactionId) &&
                java.util.Objects.equals(branchId, that.branchId);
    }

    @Override
    public int hashCode() {
        return java.util.Objects.hash(transactionId, branchId);
    }

    public static class Builder {
        private String transactionId;
        private String branchId;
        private String serviceEndpoint;
        private String serviceMethod;
        private Object requestData;
        private long registrationTime = System.currentTimeMillis();
        private BranchTransactionStatus status = BranchTransactionStatus.REGISTERED;

        public Builder transactionId(String transactionId) {
            this.transactionId = transactionId;
            return this;
        }

        public Builder branchId(String branchId) {
            this.branchId = branchId;
            return this;
        }

        public Builder serviceEndpoint(String serviceEndpoint) {
            this.serviceEndpoint = serviceEndpoint;
            return this;
        }

        public Builder serviceMethod(String serviceMethod) {
            this.serviceMethod = serviceMethod;
            return this;
        }

        public Builder requestData(Object requestData) {
            this.requestData = requestData;
            return this;
        }

        public Builder registrationTime(long registrationTime) {
            this.registrationTime = registrationTime;
            return this;
        }

        public Builder status(BranchTransactionStatus status) {
            this.status = status;
            return this;
        }

        public BranchTransactionInfo build() {
            if (transactionId == null || transactionId.trim().isEmpty()) {
                throw new IllegalArgumentException("事务ID不能为空");
            }
            if (branchId == null || branchId.trim().isEmpty()) {
                throw new IllegalArgumentException("分支ID不能为空");
            }
            if (serviceEndpoint == null || serviceEndpoint.trim().isEmpty()) {
                throw new IllegalArgumentException("服务端点不能为空");
            }

            return new BranchTransactionInfo(this);
        }
    }

    /**
     * 分支事务摘要信息
     */
    public static class BranchTransactionSummary {
        private final String transactionId;
        private final String branchId;
        private final String serviceEndpoint;
        private final BranchTransactionStatus status;
        private final long registrationTime;
        private final long executionDuration;
        private final long totalDuration;
        private final boolean successful;
        private final boolean failed;

        private BranchTransactionSummary(Builder builder) {
            this.transactionId = builder.transactionId;
            this.branchId = builder.branchId;
            this.serviceEndpoint = builder.serviceEndpoint;
            this.status = builder.status;
            this.registrationTime = builder.registrationTime;
            this.executionDuration = builder.executionDuration;
            this.totalDuration = builder.totalDuration;
            this.successful = builder.successful;
            this.failed = builder.failed;
        }

        public static Builder builder() {
            return new Builder();
        }

        // getters
        public String getTransactionId() {
            return transactionId;
        }

        public String getBranchId() {
            return branchId;
        }

        public String getServiceEndpoint() {
            return serviceEndpoint;
        }

        public BranchTransactionStatus getStatus() {
            return status;
        }

        public long getRegistrationTime() {
            return registrationTime;
        }

        public long getExecutionDuration() {
            return executionDuration;
        }

        public long getTotalDuration() {
            return totalDuration;
        }

        public boolean isSuccessful() {
            return successful;
        }

        public boolean isFailed() {
            return failed;
        }

        @Override
        public String toString() {
            return String.format("BranchTransactionSummary{txId='%s', branchId='%s', status=%s, successful=%s}",
                    transactionId, branchId, status, successful);
        }

        public static class Builder {
            private String transactionId;
            private String branchId;
            private String serviceEndpoint;
            private BranchTransactionStatus status;
            private long registrationTime;
            private long executionDuration;
            private long totalDuration;
            private boolean successful;
            private boolean failed;

            public Builder transactionId(String transactionId) {
                this.transactionId = transactionId;
                return this;
            }

            public Builder branchId(String branchId) {
                this.branchId = branchId;
                return this;
            }

            public Builder serviceEndpoint(String serviceEndpoint) {
                this.serviceEndpoint = serviceEndpoint;
                return this;
            }

            public Builder status(BranchTransactionStatus status) {
                this.status = status;
                return this;
            }

            public Builder registrationTime(long registrationTime) {
                this.registrationTime = registrationTime;
                return this;
            }

            public Builder executionDuration(long executionDuration) {
                this.executionDuration = executionDuration;
                return this;
            }

            public Builder totalDuration(long totalDuration) {
                this.totalDuration = totalDuration;
                return this;
            }

            public Builder successful(boolean successful) {
                this.successful = successful;
                return this;
            }

            public Builder failed(boolean failed) {
                this.failed = failed;
                return this;
            }

            public BranchTransactionSummary build() {
                return new BranchTransactionSummary(this);
            }
        }
    }
}
