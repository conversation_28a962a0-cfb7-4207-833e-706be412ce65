package com.tipray.transaction.core.application.extension.main;

import com.tipray.transaction.core.domain.transaction.TransactionContext;
import lombok.Builder;
import lombok.Data;

import java.util.Map;

/**
 * 事务扩展上下文
 */
@Data
@Builder
public class TransactionExtensionContext {
    private final TransactionContext transactionContext;
    private final String extensionPoint;
    private final long timestamp;
    private final Map<String, Object> attributes;
    private final Exception exception;
    private final boolean committed;
}
