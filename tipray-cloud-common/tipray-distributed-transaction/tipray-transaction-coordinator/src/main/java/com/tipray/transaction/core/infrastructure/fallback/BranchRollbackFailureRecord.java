package com.tipray.transaction.core.infrastructure.fallback;

import com.tipray.transaction.core.enums.BranchTransactionStatus;
import com.tipray.transaction.core.enums.TransactionMode;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 分支回滚失败记录
 * 记录分支事务回滚失败的完整信息
 *
 * <AUTHOR>
 * @version 2.0
 * @since 2025-01-23
 */
public class BranchRollbackFailureRecord {

    private final String transactionId;
    private final Long branchId;
    private final TransactionMode transactionMode;
    private final BranchTransactionStatus branchStatus;
    private final String targetService;
    private final String targetMethod;
    private final String rollbackUrl;
    private final ExceptionInfo rollbackException;
    private final LocalDateTime failureTime;
    private final Map<String, Object> branchSnapshot;
    private final int maxRetryCount;
    private final List<ExceptionInfo> retryExceptions;
    // 可变字段
    private int retryCount;
    private boolean requiresManualIntervention;
    private LocalDateTime lastRetryTime;
    private String notes;

    private BranchRollbackFailureRecord(Builder builder) {
        this.transactionId = builder.transactionId;
        this.branchId = builder.branchId;
        this.transactionMode = builder.transactionMode;
        this.branchStatus = builder.branchStatus;
        this.targetService = builder.targetService;
        this.targetMethod = builder.targetMethod;
        this.rollbackUrl = builder.rollbackUrl;
        this.rollbackException = builder.rollbackException;
        this.failureTime = builder.failureTime;
        this.branchSnapshot = builder.branchSnapshot;
        this.retryCount = builder.retryCount;
        this.maxRetryCount = builder.maxRetryCount;
        this.retryExceptions = new ArrayList<>();
        this.requiresManualIntervention = false;
    }

    public static Builder builder() {
        return new Builder();
    }

    // Getters
    public String getTransactionId() {
        return transactionId;
    }

    public Long getBranchId() {
        return branchId;
    }

    public TransactionMode getTransactionMode() {
        return transactionMode;
    }

    public BranchTransactionStatus getBranchStatus() {
        return branchStatus;
    }

    public String getTargetService() {
        return targetService;
    }

    public String getTargetMethod() {
        return targetMethod;
    }

    public String getRollbackUrl() {
        return rollbackUrl;
    }

    public ExceptionInfo getRollbackException() {
        return rollbackException;
    }

    public LocalDateTime getFailureTime() {
        return failureTime;
    }

    public Map<String, Object> getBranchSnapshot() {
        return branchSnapshot;
    }

    public int getRetryCount() {
        return retryCount;
    }

    public int getMaxRetryCount() {
        return maxRetryCount;
    }

    public List<ExceptionInfo> getRetryExceptions() {
        return new ArrayList<>(retryExceptions);
    }

    public boolean isRequiresManualIntervention() {
        return requiresManualIntervention;
    }

    public void setRequiresManualIntervention(boolean requiresManualIntervention) {
        this.requiresManualIntervention = requiresManualIntervention;
    }

    public LocalDateTime getLastRetryTime() {
        return lastRetryTime;
    }

    public String getNotes() {
        return notes;
    }

    public void setNotes(String notes) {
        this.notes = notes;
    }

    // 操作方法
    public void incrementRetryCount() {
        this.retryCount++;
        this.lastRetryTime = LocalDateTime.now();
    }

    public void addRetryException(ExceptionInfo exceptionInfo) {
        this.retryExceptions.add(exceptionInfo);
    }

    /**
     * 判断是否已达到最大重试次数
     */
    public boolean isMaxRetryReached() {
        return retryCount >= maxRetryCount;
    }

    /**
     * 获取失败持续时间（分钟）
     */
    public long getFailureDurationMinutes() {
        return java.time.Duration.between(failureTime, LocalDateTime.now()).toMinutes();
    }

    /**
     * 获取摘要信息
     */
    public String getSummary() {
        return String.format("分支回滚失败: %s[%d] -> %s.%s - 异常: %s, 重试: %d/%d",
                transactionId, branchId, targetService, targetMethod,
                rollbackException != null ? rollbackException.getSimpleInfo() : "未知",
                retryCount, maxRetryCount);
    }

    @Override
    public String toString() {
        return String.format("BranchRollbackFailureRecord{transactionId='%s', branchId=%d, " +
                        "targetService='%s', failureTime=%s, retryCount=%d, requiresManualIntervention=%s}",
                transactionId, branchId, targetService, failureTime, retryCount, requiresManualIntervention);
    }

    public static class Builder {
        private String transactionId;
        private Long branchId;
        private TransactionMode transactionMode;
        private BranchTransactionStatus branchStatus;
        private String targetService;
        private String targetMethod;
        private String rollbackUrl;
        private ExceptionInfo rollbackException;
        private LocalDateTime failureTime;
        private Map<String, Object> branchSnapshot;
        private int retryCount = 0;
        private int maxRetryCount = 3;

        public Builder transactionId(String transactionId) {
            this.transactionId = transactionId;
            return this;
        }

        public Builder branchId(Long branchId) {
            this.branchId = branchId;
            return this;
        }

        public Builder transactionMode(TransactionMode transactionMode) {
            this.transactionMode = transactionMode;
            return this;
        }

        public Builder branchStatus(BranchTransactionStatus branchStatus) {
            this.branchStatus = branchStatus;
            return this;
        }

        public Builder targetService(String targetService) {
            this.targetService = targetService;
            return this;
        }

        public Builder targetMethod(String targetMethod) {
            this.targetMethod = targetMethod;
            return this;
        }

        public Builder rollbackUrl(String rollbackUrl) {
            this.rollbackUrl = rollbackUrl;
            return this;
        }

        public Builder rollbackException(ExceptionInfo rollbackException) {
            this.rollbackException = rollbackException;
            return this;
        }

        public Builder failureTime(LocalDateTime failureTime) {
            this.failureTime = failureTime;
            return this;
        }

        public Builder branchSnapshot(Map<String, Object> branchSnapshot) {
            this.branchSnapshot = branchSnapshot;
            return this;
        }

        public Builder retryCount(int retryCount) {
            this.retryCount = retryCount;
            return this;
        }

        public Builder maxRetryCount(int maxRetryCount) {
            this.maxRetryCount = maxRetryCount;
            return this;
        }

        public BranchRollbackFailureRecord build() {
            return new BranchRollbackFailureRecord(this);
        }
    }
}
