package com.tipray.transaction.core.infrastructure.fallback;

import java.time.LocalDateTime;

/**
 * 分支回滚降级处理结果
 *
 * <AUTHOR>
 * @version 2.0
 * @since 2025-01-23
 */
public class BranchRollbackFallbackResult {

    private final FallbackResultStatus status;
    private final String message;
    private final LocalDateTime processTime;
    private final String details;

    private BranchRollbackFallbackResult(FallbackResultStatus status, String message, String details) {
        this.status = status;
        this.message = message;
        this.details = details;
        this.processTime = LocalDateTime.now();
    }

    public static BranchRollbackFallbackResult success(String message) {
        return new BranchRollbackFallbackResult(FallbackResultStatus.SUCCESS, message, null);
    }

    public static BranchRollbackFallbackResult failed(String message) {
        return new BranchRollbackFallbackResult(FallbackResultStatus.FAILED, message, null);
    }

    public static BranchRollbackFallbackResult retryLater(String message) {
        return new BranchRollbackFallbackResult(FallbackResultStatus.RETRY_LATER, message, null);
    }

    public static BranchRollbackFallbackResult scheduled(String message) {
        return new BranchRollbackFallbackResult(FallbackResultStatus.SCHEDULED, message, null);
    }

    public static BranchRollbackFallbackResult manualIntervention(String message) {
        return new BranchRollbackFallbackResult(FallbackResultStatus.MANUAL_INTERVENTION, message, null);
    }

    public static BranchRollbackFallbackResult withDetails(FallbackResultStatus status, String message, String details) {
        return new BranchRollbackFallbackResult(status, message, details);
    }

    // Getters
    public FallbackResultStatus getStatus() {
        return status;
    }

    public String getMessage() {
        return message;
    }

    public LocalDateTime getProcessTime() {
        return processTime;
    }

    public String getDetails() {
        return details;
    }

    public boolean isSuccess() {
        return status == FallbackResultStatus.SUCCESS;
    }

    public boolean requiresRetry() {
        return status == FallbackResultStatus.RETRY_LATER || status == FallbackResultStatus.SCHEDULED;
    }

    public boolean requiresManualIntervention() {
        return status == FallbackResultStatus.MANUAL_INTERVENTION;
    }

    @Override
    public String toString() {
        return String.format("BranchRollbackFallbackResult{status=%s, message='%s', processTime=%s}",
                status, message, processTime);
    }

    /**
     * 降级处理结果状态
     */
    public enum FallbackResultStatus {
        SUCCESS("成功"),
        FAILED("失败"),
        RETRY_LATER("稍后重试"),
        SCHEDULED("已调度"),
        MANUAL_INTERVENTION("需要人工干预");

        private final String description;

        FallbackResultStatus(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }
}
