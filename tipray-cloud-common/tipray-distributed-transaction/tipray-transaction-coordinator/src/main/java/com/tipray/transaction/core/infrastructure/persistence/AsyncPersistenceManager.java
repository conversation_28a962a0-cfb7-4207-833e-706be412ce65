package com.tipray.transaction.core.infrastructure.persistence;

import com.tipray.transaction.core.domain.transaction.BranchTransactionDO;
import com.tipray.transaction.core.domain.transaction.TransactionDO;
import com.tipray.transaction.core.enums.BranchTransactionStatus;
import com.tipray.transaction.core.enums.TransactionStatus;
import com.tipray.transaction.core.infrastructure.pool.TransactionConnectionPoolManager;
import com.tipray.transaction.core.persistence.BranchTransactionStorage;
import com.tipray.transaction.core.persistence.TransactionStorage;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 异步持久化管理器
 * 通过 TransactionConnectionPoolManager 统一管理线程池，不再硬编码线程池配置
 *
 * <AUTHOR>
 * @version 3.0
 * @since 2025-01-23
 */
public class AsyncPersistenceManager {

    private static final Logger log = LoggerFactory.getLogger(AsyncPersistenceManager.class);

    @Autowired
    private TransactionConnectionPoolManager poolManager;

    // 持久化接口
    private final TransactionStorage transactionStorage;
    private final BranchTransactionStorage branchTransactionStorage;

    // 异步执行器（从连接池管理器获取）
    private ExecutorService asyncExecutor;
    // 批量处理器（从连接池管理器获取）
    private ScheduledExecutorService batchProcessor;

    // 任务队列
    private BlockingQueue<PersistenceTask> taskQueue;

    // 统计信息
    private final AtomicLong totalTasks = new AtomicLong(0);
    private final AtomicLong successTasks = new AtomicLong(0);
    private final AtomicLong failedTasks = new AtomicLong(0);
    private final AtomicLong queueOverflowCount = new AtomicLong(0);

    public AsyncPersistenceManager(TransactionStorage transactionStorage, BranchTransactionStorage branchTransactionStorage) {
        // 持久化接口
        this.transactionStorage = transactionStorage;
        this.branchTransactionStorage = branchTransactionStorage;
    }

    @PostConstruct
    public void init() {
        log.info("初始化异步持久化管理器...");

        // 从连接池管理器获取线程池，不再硬编码配置
        this.asyncExecutor = poolManager.getAsyncTaskExecutor();
        this.batchProcessor = poolManager.getScheduledExecutor();

        // 初始化任务队列（队列容量从配置管理器获取）
        int queueCapacity = poolManager.getConfigManager().getAsyncPersistenceQueueCapacity();
        this.taskQueue = new LinkedBlockingQueue<>(queueCapacity);

        // 启动批量处理
        startBatchProcessing();

        log.info("异步持久化管理器初始化完成 - 队列容量: {}", queueCapacity);
    }

    /**
     * 异步保存事务
     */
    public void asyncSaveTransaction(TransactionDO transaction) {
        PersistenceTask task = new PersistenceTask(PersistenceTaskType.SAVE_TRANSACTION, transaction);
        submitTask(task);
    }

    /**
     * 异步更新事务状态
     */
    public void asyncUpdateTransactionStatus(String transactionId, TransactionStatus status) {
        TransactionStatusUpdate update = new TransactionStatusUpdate(transactionId, status);
        PersistenceTask task = new PersistenceTask(PersistenceTaskType.UPDATE_STATUS, update);
        submitTask(task);
    }

    /**
     * 异步保存回滚失败信息
     */
    public void asyncSaveRollbackFailure(String transactionId, Exception exception) {
        RollbackFailureRecord record = new RollbackFailureRecord(transactionId, exception);
        PersistenceTask task = new PersistenceTask(PersistenceTaskType.SAVE_ROLLBACK_FAILURE, record);
        submitTask(task);
    }

    /**
     * 异步删除事务
     */
    public void asyncDeleteTransaction(String transactionId) {
        PersistenceTask task = new PersistenceTask(PersistenceTaskType.DELETE_TRANSACTION, transactionId);
        submitTask(task);
    }

    /**
     * 同步保存事务（队列满时的降级处理）
     */
    public void syncSaveTransaction(TransactionDO transaction) {
        try {
            transactionStorage.saveTransaction(transaction);
            successTasks.incrementAndGet();
            log.debug("同步保存事务成功: {}", transaction.getTransactionId());
        } catch (Exception e) {
            failedTasks.incrementAndGet();
            log.error("同步保存事务失败: {}", transaction.getTransactionId(), e);
        }
    }

    /**
     * 获取持久化统计信息
     */
    public PersistenceStatistics getStatistics() {
        return PersistenceStatistics.builder()
                .totalTasks(totalTasks.get())
                .successTasks(successTasks.get())
                .failedTasks(failedTasks.get())
                .queueOverflowCount(queueOverflowCount.get())
                .queueSize(taskQueue.size())
                .activeThreads(((ThreadPoolExecutor) asyncExecutor).getActiveCount())
                .build();
    }

    /**
     * 关闭异步持久化管理器
     * 注意：线程池的实际关闭由 TransactionConnectionPoolManager 统一管理
     */
    @PreDestroy
    public void shutdown() {
        log.info("关闭异步持久化管理器...");

        // 处理剩余任务
        processRemainingTasks();

        log.info("异步持久化管理器已关闭 - 线程池由 TransactionConnectionPoolManager 统一管理");
    }

    // ==================== 私有方法 ====================

    /**
     * 提交任务
     */
    private void submitTask(PersistenceTask task) {
        totalTasks.incrementAndGet();

        if (!taskQueue.offer(task)) {
            // 队列满时同步处理，确保数据不丢失
            queueOverflowCount.incrementAndGet();
            log.warn("持久化队列已满，同步处理任务: {}", task.getType());

            processTaskSync(task);
        }
    }

    /**
     * 启动批量处理
     */
    private void startBatchProcessing() {
        // 从配置管理器获取批量处理间隔
        long batchInterval = poolManager.getConfigManager().getAsyncPersistenceBatchInterval();

        batchProcessor.scheduleWithFixedDelay(
                this::processBatch,
                batchInterval,
                batchInterval,
                TimeUnit.MILLISECONDS
        );

        log.debug("启动批量处理 - 间隔: {}ms", batchInterval);
    }

    /**
     * 批量处理任务
     */
    private void processBatch() {
        if (taskQueue.isEmpty()) {
            return;
        }

        try {
            // 从配置管理器获取批量大小
            int batchSize = poolManager.getConfigManager().getAsyncPersistenceBatchSize();

            // 批量获取任务
            java.util.List<PersistenceTask> batch = new java.util.ArrayList<>(batchSize);
            taskQueue.drainTo(batch, batchSize);

            if (!batch.isEmpty()) {
                // 异步处理批量任务
                asyncExecutor.submit(() -> processBatchTasks(batch));
            }

        } catch (Exception e) {
            log.error("批量处理任务失败", e);
        }
    }

    /**
     * 处理批量任务
     */
    private void processBatchTasks(java.util.List<PersistenceTask> tasks) {
        for (PersistenceTask task : tasks) {
            try {
                processTask(task);
                successTasks.incrementAndGet();
            } catch (Exception e) {
                failedTasks.incrementAndGet();
                log.error("处理持久化任务失败: {}", task.getType(), e);
            }
        }
    }

    /**
     * 处理单个任务
     */
    private void processTask(PersistenceTask task) {
        switch (task.getType()) {
            // 事务相关持久化
            case SAVE_TRANSACTION:
                TransactionDO transaction = (TransactionDO) task.getData();
                transactionStorage.saveTransaction(transaction);
                break;

            case UPDATE_STATUS:
                TransactionStatusUpdate update = (TransactionStatusUpdate) task.getData();
                transactionStorage.updateTransactionStatus(update.getTransactionId(), update.getStatus());
                break;

            case SAVE_ROLLBACK_FAILURE:
                RollbackFailureRecord record = (RollbackFailureRecord) task.getData();
                transactionStorage.saveRollbackFailure(record);
                break;

            case DELETE_TRANSACTION:
                String transactionId = (String) task.getData();
                transactionStorage.deleteTransaction(transactionId);
                break;

            // 分支事务相关持久化
            case SAVE_BRANCH_TRANSACTION:
                BranchTransactionDO branchTransaction = (BranchTransactionDO) task.getData();
                branchTransactionStorage.saveBranchTransaction(branchTransaction);
                break;
            case UPDATE_BRANCH_STATUS:
                BranchTransactionStatusUpdate branchUpdate = (BranchTransactionStatusUpdate) task.getData();
                branchTransactionStorage.updateBranchTransactionStatus(branchUpdate.getBranchTransactionId(), branchUpdate.getStatus());
                break;
            default:
                log.warn("未知的持久化任务类型: {}", task.getType());
        }
    }

    /**
     * 同步处理任务
     */
    private void processTaskSync(PersistenceTask task) {
        try {
            processTask(task);
            successTasks.incrementAndGet();
        } catch (Exception e) {
            failedTasks.incrementAndGet();
            log.error("同步处理持久化任务失败: {}", task.getType(), e);
        }
    }

    /**
     * 处理剩余任务
     */
    private void processRemainingTasks() {
        log.info("处理剩余持久化任务，队列大小: {}", taskQueue.size());

        PersistenceTask task;
        while ((task = taskQueue.poll()) != null) {
            processTaskSync(task);
        }
    }

    public void asyncSaveBranchTransaction(BranchTransactionDO branchTransactionDO) {
        PersistenceTask task = new PersistenceTask(PersistenceTaskType.SAVE_BRANCH_TRANSACTION, branchTransactionDO);
        submitTask(task);
    }

    /**
     * 异步更新分支事务状态
     *
     * @param branchTransactionId     分支事务id
     * @param branchTransactionStatus 分支事务当前状态
     */
    public void asyncUpdateBranchTransactionStatus(Long branchTransactionId, BranchTransactionStatus branchTransactionStatus) {
        BranchTransactionStatusUpdate update = new BranchTransactionStatusUpdate(branchTransactionId, branchTransactionStatus);
        PersistenceTask task = new PersistenceTask(PersistenceTaskType.UPDATE_BRANCH_STATUS, update);
        submitTask(task);
    }

    /**
     * 持久化任务类型
     */
    private enum PersistenceTaskType {
        SAVE_TRANSACTION,
        UPDATE_STATUS,
        SAVE_ROLLBACK_FAILURE,
        DELETE_TRANSACTION,
        // 分支相关
        SAVE_BRANCH_TRANSACTION,
        UPDATE_BRANCH_STATUS
    }

    /**
     * 持久化任务
     */
    private static class PersistenceTask {
        private final PersistenceTaskType type;
        private final Object data;
        private final long createTime;

        public PersistenceTask(PersistenceTaskType type, Object data) {
            this.type = type;
            this.data = data;
            this.createTime = System.currentTimeMillis();
        }

        public PersistenceTaskType getType() {
            return type;
        }

        public Object getData() {
            return data;
        }

        public long getCreateTime() {
            return createTime;
        }
    }

    /**
     * 事务状态更新
     */
    private static class TransactionStatusUpdate {
        private final String transactionId;
        private final TransactionStatus status;

        public TransactionStatusUpdate(String transactionId, TransactionStatus status) {
            this.transactionId = transactionId;
            this.status = status;
        }

        public String getTransactionId() {
            return transactionId;
        }

        public TransactionStatus getStatus() {
            return status;
        }
    }

    /**
     * 分支事务状态更新
     */
    private static class BranchTransactionStatusUpdate {
        private final Long branchTransactionId;
        private final BranchTransactionStatus status;

        public BranchTransactionStatusUpdate(Long branchTransactionId, BranchTransactionStatus status) {
            this.branchTransactionId = branchTransactionId;
            this.status = status;
        }

        public Long getBranchTransactionId() {
            return branchTransactionId;
        }

        public BranchTransactionStatus getStatus() {
            return status;
        }
    }

    /**
     * 回滚失败记录
     */
    private static class RollbackFailureRecord {
        private final String transactionId;
        private final Exception exception;
        private final long timestamp;

        public RollbackFailureRecord(String transactionId, Exception exception) {
            this.transactionId = transactionId;
            this.exception = exception;
            this.timestamp = System.currentTimeMillis();
        }

        public String getTransactionId() {
            return transactionId;
        }

        public Exception getException() {
            return exception;
        }

        public long getTimestamp() {
            return timestamp;
        }
    }

    /**
     * 持久化统计信息
     */
    public static class PersistenceStatistics {
        private final long totalTasks;
        private final long successTasks;
        private final long failedTasks;
        private final long queueOverflowCount;
        private final int queueSize;
        private final int activeThreads;

        private PersistenceStatistics(Builder builder) {
            this.totalTasks = builder.totalTasks;
            this.successTasks = builder.successTasks;
            this.failedTasks = builder.failedTasks;
            this.queueOverflowCount = builder.queueOverflowCount;
            this.queueSize = builder.queueSize;
            this.activeThreads = builder.activeThreads;
        }

        public static Builder builder() {
            return new Builder();
        }

        // getters
        public long getTotalTasks() {
            return totalTasks;
        }

        public long getSuccessTasks() {
            return successTasks;
        }

        public long getFailedTasks() {
            return failedTasks;
        }

        public long getQueueOverflowCount() {
            return queueOverflowCount;
        }

        public int getQueueSize() {
            return queueSize;
        }

        public int getActiveThreads() {
            return activeThreads;
        }

        public double getSuccessRate() {
            return totalTasks > 0 ? (double) successTasks / totalTasks * 100 : 0.0;
        }

        @Override
        public String toString() {
            return String.format("PersistenceStatistics{total=%d, success=%d, failed=%d, successRate=%.2f%%}",
                    totalTasks, successTasks, failedTasks, getSuccessRate());
        }

        public static class Builder {
            private long totalTasks;
            private long successTasks;
            private long failedTasks;
            private long queueOverflowCount;
            private int queueSize;
            private int activeThreads;

            public Builder totalTasks(long totalTasks) {
                this.totalTasks = totalTasks;
                return this;
            }

            public Builder successTasks(long successTasks) {
                this.successTasks = successTasks;
                return this;
            }

            public Builder failedTasks(long failedTasks) {
                this.failedTasks = failedTasks;
                return this;
            }

            public Builder queueOverflowCount(long queueOverflowCount) {
                this.queueOverflowCount = queueOverflowCount;
                return this;
            }

            public Builder queueSize(int queueSize) {
                this.queueSize = queueSize;
                return this;
            }

            public Builder activeThreads(int activeThreads) {
                this.activeThreads = activeThreads;
                return this;
            }

            public PersistenceStatistics build() {
                return new PersistenceStatistics(this);
            }
        }
    }
}
