package com.tipray.transaction.core.infrastructure.persistence.impl;

import cn.hutool.cache.Cache;
import cn.hutool.cache.CacheUtil;
import com.tipray.transaction.core.domain.transaction.TransactionDO;
import com.tipray.transaction.core.enums.TransactionStatus;
import com.tipray.transaction.core.persistence.TransactionStorage;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

/**
 * 默认事务存储实现
 * 基于Hutool缓存的实现，支持过期时间防止内存无限膨胀
 *
 * <AUTHOR>
 * @version 2.0
 * @since 2025-01-23
 */
public class DefaultTransactionStorage implements TransactionStorage {

    private static final Logger log = LoggerFactory.getLogger(DefaultTransactionStorage.class);

    // 使用Hutool的LRU缓存，支持过期时间
    // 默认容量1000，过期时间30分钟
    private final Cache<String, TransactionDO> transactionCache = CacheUtil.newTimedCache(30 * 60 * 1000L);

    @Override
    public void saveTransaction(TransactionDO transaction) {
        if (transaction == null || transaction.getTransactionId() == null) {
            log.warn("保存事务失败：事务对象或事务ID为空");
            return;
        }

        transactionCache.put(transaction.getTransactionId(), transaction);
        log.debug("保存事务成功: {}", transaction.getTransactionId());
    }

    @Override
    public void updateTransactionStatus(String transactionId, TransactionStatus status) {
        if (transactionId == null || status == null) {
            log.warn("更新事务状态失败：事务ID或状态为空");
            return;
        }

        TransactionDO transaction = transactionCache.get(transactionId);
        if (transaction != null) {
            transaction.setStatus(status);
            // 重新放入缓存以更新过期时间
            transactionCache.put(transactionId, transaction);
            log.debug("更新事务状态成功: {} -> {}", transactionId, status);
        } else {
            log.warn("更新事务状态失败：事务不存在: {}", transactionId);
        }
    }

    @Override
    public void saveRollbackFailure(Object record) {
        log.debug("保存回滚失败记录: {}", record);
        // TODO: 实现回滚失败记录的持久化
    }

    @Override
    public void deleteTransaction(String transactionId) {
        if (transactionId == null) {
            log.warn("删除事务失败：事务ID为空");
            return;
        }

        TransactionDO removed = transactionCache.get(transactionId);
        transactionCache.remove(transactionId);

        if (removed != null) {
            log.debug("删除事务成功: {}", transactionId);
        } else {
            log.warn("删除事务失败：事务不存在: {}", transactionId);
        }
    }

    @Override
    public TransactionDO findTransactionById(String transactionId) {
        if (transactionId == null) {
            log.warn("查询事务失败：事务ID为空");
            return null;
        }

        TransactionDO transaction = transactionCache.get(transactionId);
        log.debug("查询事务: {} -> {}", transactionId, transaction != null ? "找到" : "未找到");
        return transaction;
    }

    @Override
    public Object getStatistics() {
        // 启动缓存的定时清理
//        transactionCache.schedulePrune();

        int totalTransactions = transactionCache.size();
        log.debug("获取存储统计信息: 总事务数={}", totalTransactions);

        return new StorageStatistics(totalTransactions);
    }

    /**
     * 存储统计信息
     */
    public static class StorageStatistics {
        private final int totalTransactions;

        public StorageStatistics(int totalTransactions) {
            this.totalTransactions = totalTransactions;
        }

        public int getTotalTransactions() {
            return totalTransactions;
        }

        @Override
        public String toString() {
            return "StorageStatistics{totalTransactions=" + totalTransactions + '}';
        }
    }
}
