//package com.tipray.transaction.core.infrastructure.persistence;
//
//import com.tipray.transaction.core.domain.transaction.DistributedTransactionDO;
//import com.tipray.transaction.core.domain.transaction.StepExecutionHistory;
//import com.tipray.transaction.core.domain.transaction.TransactionExecutionHistory;
//import com.tipray.transaction.core.domain.transaction.TransactionStep;
//import com.tipray.transaction.core.infrastructure.config.TransactionConfigurationManager;
//import com.tipray.transaction.core.infrastructure.context.TransactionContext;
//import com.tipray.transaction.core.infrastructure.context.TransactionContextCleaner;
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//import org.springframework.beans.factory.annotation.Autowired;
//
//import javax.annotation.PostConstruct;
//import javax.annotation.PreDestroy;
//import java.util.List;
//import java.util.Map;
//import java.util.concurrent.*;
//
/// **
// * @deprecated 此类已过时，请使用新的 {@link com.tipray.transaction.core.infrastructure.persistence.AsyncPersistenceManager}
// * 新的管理器采用更高效的批量处理和队列管理机制
// *
// * 异步事务持久化管理器
// * 提供异步的事务和步骤持久化功能，避免阻塞主业务流程
// *
// * <AUTHOR>
// * @version 1.0
// * @since 2025-01-20
// * @see com.tipray.transaction.core.infrastructure.persistence.AsyncPersistenceManager
// */
//@Deprecated
//public class AsyncTransactionPersistenceManager {
//
//    private static final Logger logger = LoggerFactory.getLogger(AsyncTransactionPersistenceManager.class);
//
//    @Autowired
//    private TransactionRepository transactionRepository;
//
//    @Autowired
//    private TransactionStepRepository stepRepository;
//
//    @Autowired(required = false)
//    private StepExecutionHistoryRepository stepExecutionHistoryRepository;
//
//    @Autowired(required = false)
//    private TransactionExecutionHistoryRepository transactionExecutionHistoryRepository;
//
//    @Autowired
//    private TransactionConfigurationManager configManager;
//
//    /**
//     * 异步执行器
//     */
//    private ThreadPoolExecutor asyncExecutor;
//
//    /**
//     * 重试调度器
//     */
//    private ScheduledExecutorService retryScheduler;
//
//    /**
//     * 批量处理队列
//     */
//    private BlockingQueue<PersistenceTask> taskQueue;
//
//    /**
//     * 批量处理线程
//     */
//    private Thread batchProcessThread;
//
//    /**
//     * 是否正在运行
//     */
//    private volatile boolean running = false;
//
//    @PostConstruct
//    public void init() {
//        // 从统一配置管理器获取配置
//        int corePoolSize = configManager.getAsyncPersistenceCorePoolSize();
//        int maximumPoolSize = configManager.getAsyncPersistenceMaximumPoolSize();
//        long keepAliveTime = configManager.getAsyncPersistenceKeepAliveTime();
//        int queueCapacity = configManager.getAsyncPersistenceQueueCapacity();
//        String threadNamePrefix = "tipray-async-persistence-";
//
//        this.asyncExecutor = new ThreadPoolExecutor(
//                corePoolSize,
//                maximumPoolSize,
//                keepAliveTime,
//                TimeUnit.SECONDS,
//                new LinkedBlockingQueue<>(queueCapacity),
//                new ThreadFactory() {
//                    private int counter = 0;
//                    @Override
//                    public Thread newThread(Runnable r) {
//                        Thread thread = new Thread(r, threadNamePrefix + (++counter));
//                        thread.setDaemon(true);
//                        return thread;
//                    }
//                },
//                new ThreadPoolExecutor.CallerRunsPolicy()
//        );
//
//        // 初始化重试调度器
//        this.retryScheduler = Executors.newScheduledThreadPool(2, r -> {
//            Thread t = new Thread(r, "persistence-retry-scheduler");
//            t.setDaemon(true);
//            return t;
//        });
//
//        // 初始化批量处理队列
//        this.taskQueue = new LinkedBlockingQueue<>(5000);
//
//        // 启动批量处理线程
//        this.running = true;
//        this.batchProcessThread = new Thread(this::batchProcessTasks, "batch-persistence-processor");
//        this.batchProcessThread.setDaemon(true);
//        this.batchProcessThread.start();
//
//        logger.debug("分布式事务-异步事务持久化管理器初始化完成 - 核心线程数: {}, 最大线程数: {}", corePoolSize, maximumPoolSize);
//    }
//
//    @PreDestroy
//    public void destroy() {
//        this.running = false;
//
//        // 关闭批量处理线程
//        if (this.batchProcessThread != null) {
//            this.batchProcessThread.interrupt();
//        }
//
//        // 关闭重试调度器
//        if (this.retryScheduler != null) {
//            this.retryScheduler.shutdown();
//            try {
//                if (!this.retryScheduler.awaitTermination(10, TimeUnit.SECONDS)) {
//                    this.retryScheduler.shutdownNow();
//                }
//            } catch (InterruptedException e) {
//                this.retryScheduler.shutdownNow();
//                Thread.currentThread().interrupt();
//            }
//        }
//
//        // 关闭异步执行器
//        if (this.asyncExecutor != null) {
//            this.asyncExecutor.shutdown();
//            try {
//                if (!this.asyncExecutor.awaitTermination(30, TimeUnit.SECONDS)) {
//                    this.asyncExecutor.shutdownNow();
//                }
//            } catch (InterruptedException e) {
//                this.asyncExecutor.shutdownNow();
//                Thread.currentThread().interrupt();
//            }
//        }
//
//        logger.debug("异步事务持久化管理器已关闭");
//    }
//
//    /**
//     * 异步保存事务
//     *
//     * @param transaction 分布式事务
//     */
//    public void asyncSaveTransaction(DistributedTransactionDO transaction) {
//        if (transaction == null) {
//            return;
//        }
//
//        PersistenceTask task = new PersistenceTask(TaskType.SAVE_TRANSACTION, transaction, null);
//        submitTask(task);
//    }
//
//    /**
//     * 异步更新事务
//     *
//     * @param transaction 分布式事务
//     */
//    public void asyncUpdateTransaction(DistributedTransactionDO transaction) {
//        if (transaction == null) {
//            return;
//        }
//
//        PersistenceTask task = new PersistenceTask(TaskType.UPDATE_TRANSACTION, transaction, null);
//        submitTask(task);
//    }
//
//    /**
//     * 异步保存事务步骤
//     *
//     * @param step 事务步骤
//     */
//    public void asyncSaveStep(TransactionStep step) {
//        if (step == null) {
//            return;
//        }
//
//        PersistenceTask task = new PersistenceTask(TaskType.SAVE_STEP, null, step);
//        submitTask(task);
//    }
//
//    /**
//     * 异步更新事务步骤
//     *
//     * @param step 事务步骤
//     */
//    public void asyncUpdateStep(TransactionStep step) {
//        if (step == null) {
//            return;
//        }
//
//        PersistenceTask task = new PersistenceTask(TaskType.UPDATE_STEP, null, step);
//        submitTask(task);
//    }
//
//    /**
//     * 异步批量更新事务步骤
//     *
//     * @param steps 事务步骤列表
//     */
//    public void asyncBatchUpdateSteps(List<TransactionStep> steps) {
//        if (steps == null || steps.isEmpty()) {
//            return;
//        }
//
//        for (TransactionStep step : steps) {
//            asyncUpdateStep(step);
//        }
//    }
//
//    /**
//     * 异步保存步骤执行历史
//     *
//     * @param history 执行历史
//     */
//    public void saveStepExecutionHistoryAsync(StepExecutionHistory history) {
//        if (history == null) {
//            return;
//        }
//
//        PersistenceTask task = new PersistenceTask(TaskType.SAVE_STEP_HISTORY, null, null, history);
//        submitTask(task);
//    }
//
//    /**
//     * 异步更新步骤
//     */
//    public void saveStepAsync(TransactionStep step) {
//        asyncSaveStep(step);
//    }
//
//    /**
//     * 异步更新步骤
//     */
//    public void updateStepAsync(TransactionStep step) {
//        asyncUpdateStep(step);
//    }
//
//    /**
//     * 同步保存事务（阻塞方法）
//     *
//     * @param transaction 分布式事务
//     */
//    public void syncSaveTransaction(DistributedTransactionDO transaction) {
//        if (transaction == null) {
//            return;
//        }
//
//        try {
//            transactionRepository.save(transaction);
//            logger.debug("同步保存事务成功: {}", transaction.getTransactionId());
//        } catch (Exception e) {
//            logger.error("同步保存事务失败: {}", transaction.getTransactionId(), e);
//            throw e;
//        }
//    }
//
//    /**
//     * 同步更新事务（阻塞方法）
//     *
//     * @param transaction 分布式事务
//     */
//    public void syncUpdateTransaction(DistributedTransactionDO transaction) {
//        if (transaction == null) {
//            return;
//        }
//
//        try {
//            transactionRepository.update(transaction);
//            logger.debug("同步更新事务成功: {}", transaction.getTransactionId());
//        } catch (Exception e) {
//            logger.error("同步更新事务失败: {}", transaction.getTransactionId(), e);
//            throw e;
//        }
//    }
//
//    /**
//     * 异步持久化异常信息
//     *
//     * @param transactionId 事务ID
//     * @param exceptionInfo 异常信息
//     */
//    public void persistExceptionAsync(String transactionId, Map<String, Object> exceptionInfo) {
//        if (transactionId == null || exceptionInfo == null) {
//            return;
//        }
//
//        // 创建异常持久化任务
//        PersistenceTask task = new PersistenceTask(TaskType.PERSIST_EXCEPTION, null, null, transactionId, exceptionInfo);
//        submitTask(task);
//    }
//
//    /**
//     * 同步持久化异常信息（阻塞方法）
//     *
//     * @param transactionId 事务ID
//     * @param exceptionInfo 异常信息
//     */
//    public void persistExceptionSync(String transactionId, Map<String, Object> exceptionInfo) {
//        if (transactionId == null || exceptionInfo == null) {
//            return;
//        }
//
//        try {
//            // 这里可以将异常信息保存到数据库或日志文件
//            // 为了简化，我们先记录到日志中
//            logger.error("事务异常信息持久化 - 事务ID: {}, 异常信息: {}", transactionId, exceptionInfo);
//
//            // 如果有专门的异常信息表，可以在这里保存
//            // exceptionRepository.save(createExceptionRecord(transactionId, exceptionInfo));
//
//        } catch (Exception e) {
//            logger.error("持久化异常信息失败 - 事务ID: {}", transactionId, e);
//        }
//    }
//
//    /**
//     * 提交持久化任务
//     *
//     * @param task 持久化任务
//     */
//    private void submitTask(PersistenceTask task) {
//        try {
//            if (!taskQueue.offer(task, 100, TimeUnit.MILLISECONDS)) {
//                logger.warn("持久化任务队列已满，使用线程池直接执行: {}", task.getType());
//
//                // 关键修复：不在当前线程执行，而是提交给线程池
//                try {
//                    asyncExecutor.submit(() -> executeTask(task));
//                } catch (RejectedExecutionException ree) {
//                    // 线程池也满了，这是严重问题
//                    logger.error("线程池已满，持久化任务被拒绝执行，可能导致数据不一致！任务类型: {}", task.getType());
//                    handlePersistenceFailureAlert(task, ree);
//                }
//            }
//        } catch (InterruptedException e) {
//            logger.warn("提交持久化任务被中断，使用线程池执行: {}", task.getType());
//            try {
//                asyncExecutor.submit(() -> executeTask(task));
//            } catch (RejectedExecutionException ree) {
//                logger.error("线程池已满，持久化任务被拒绝执行: {}", task.getType());
//                handlePersistenceFailureAlert(task, ree);
//            }
//            Thread.currentThread().interrupt();
//        }
//    }
//
//    /**
//     * 批量处理任务
//     */
//    private void batchProcessTasks() {
//        while (running) {
//            try {
//                PersistenceTask task = taskQueue.poll(1, TimeUnit.SECONDS);
//                if (task != null) {
//                    asyncExecutor.submit(() -> executeTask(task));
//                }
//            } catch (InterruptedException e) {
//                logger.info("批量处理线程被中断");
//                break;
//            } catch (Exception e) {
//                logger.error("批量处理任务异常", e);
//            }
//        }
//    }
//
//    /**
//     * 执行持久化任务
//     *
//     * @param task 持久化任务
//     */
//    private void executeTask(PersistenceTask task) {
//        // 恢复事务上下文
//        TransactionContext originalContext = TransactionContext.current();
//        if (task.transactionContext != null) {
//            TransactionContextCleaner.restoreSnapshot(task.transactionContext);
//        }
//
//        boolean completed = false;
//        boolean rejected = false;
//
//        try {
//            switch (task.getType()) {
//                case SAVE_TRANSACTION:
//                    transactionRepository.save(task.getTransaction());
//                    break;
//                case UPDATE_TRANSACTION:
//                    transactionRepository.update(task.getTransaction());
//                    break;
//                case SAVE_STEP:
//                    stepRepository.save(task.getStep());
//                    break;
//                case UPDATE_STEP:
//                    stepRepository.update(task.getStep());
//                    break;
//                case SAVE_STEP_HISTORY:
//                    if (stepExecutionHistoryRepository != null && task.getStepHistory() != null) {
//                        stepExecutionHistoryRepository.save(task.getStepHistory());
//                    }
//                    break;
//                case PERSIST_EXCEPTION:
//                    if (task.getTransactionId() != null && task.getExceptionInfo() != null) {
//                        persistExceptionSync(task.getTransactionId(), task.getExceptionInfo());
//                    }
//                    break;
//                default:
//                    logger.warn("未知的持久化任务类型: {}", task.getType());
//            }
//            completed = true;
//        } catch (Exception e) {
//            logger.error("执行持久化任务失败 - 类型: {}", task.getType(), e);
//
//            // 关键修复：持久化失败时的补偿机制
//            handlePersistenceFailure(task, e);
//
//        } finally {
//            // 恢复原始事务上下文
//            TransactionContext.setCurrent(originalContext);
//        }
//    }
//
//    /**
//     * 处理持久化失败的补偿机制
//     */
//    private void handlePersistenceFailure(PersistenceTask task, Exception e) {
//        try {
//            // 增加重试计数
//            task.incrementRetryCount();
//
//            // 如果还可以重试
//            if (task.canRetry()) {
//                logger.warn("持久化任务失败，将进行第{}次重试: {}", task.getRetryCount(), task.getType());
//
//                // 延迟重试（指数退避）
//                long delay = calculateRetryDelay(task.getRetryCount());
//
//                // 延迟重试实现
//                scheduleRetryTask(task, delay);
//
//            } else {
//                // 重试次数耗尽，记录为严重错误
//                logger.error("持久化任务重试次数耗尽，数据可能不一致！任务类型: {}, 事务ID: {}",
//                        task.getType(),
//                        task.getTransaction() != null ? task.getTransaction().getTransactionId() : "unknown");
//
//                // 可以考虑发送告警或者写入死信队列
//                handlePersistenceFailureAlert(task, e);
//            }
//
//        } catch (Exception retryException) {
//            logger.error("处理持久化失败补偿机制时发生异常", retryException);
//        }
//    }
//
//    /**
//     * 延迟重试任务调度
//     */
//    private void scheduleRetryTask(PersistenceTask task, long delay) {
//        if (retryScheduler != null && !retryScheduler.isShutdown()) {
//            retryScheduler.schedule(() -> {
//                logger.debug("开始重试持久化任务: {}", task.getType());
//                executeTask(task);
//            }, delay, TimeUnit.MILLISECONDS);
//        } else {
//            logger.warn("重试调度器不可用，跳过重试: {}", task.getType());
//        }
//    }
//
//    /**
//     * 计算重试延迟（指数退避）
//     */
//    private long calculateRetryDelay(int retryCount) {
//        // 基础延迟1秒，指数退避，最大30秒
//        long baseDelay = 1000L;
//        long delay = (long) (baseDelay * Math.pow(2, retryCount - 1));
//        return Math.min(delay, 30000L);
//    }
//
//    /**
//     * 处理持久化失败告警
//     */
//    private void handlePersistenceFailureAlert(PersistenceTask task, Exception e) {
//        // 这里可以实现告警机制，比如：
//        // 1. 发送邮件/短信告警
//        // 2. 写入死信队列
//        // 3. 触发人工干预流程
//
//        logger.error("=== 严重告警 === 持久化任务彻底失败，可能导致数据不一致！");
//        logger.error("任务类型: {}", task.getType());
//        if (task.getTransaction() != null) {
//            logger.error("事务ID: {}", task.getTransaction().getTransactionId());
//            logger.error("事务状态: {}", task.getTransaction().getStatus());
//        }
//        logger.error("失败原因: {}", e.getMessage(), e);
//    }
//
//    /**
//     * 持久化任务类型
//     */
//    private enum TaskType {
//        SAVE_TRANSACTION,
//        UPDATE_TRANSACTION,
//        SAVE_STEP,
//        UPDATE_STEP,
//        SAVE_STEP_HISTORY,
//        PERSIST_EXCEPTION
//    }
//
//    /**
//     * 持久化任务
//     */
//    private static class PersistenceTask {
//        private final TaskType type;
//        private final DistributedTransactionDO transaction;
//        private final TransactionStep step;
//        private final StepExecutionHistory stepHistory;
//        private final String transactionId;
//        private final Map<String, Object> exceptionInfo;
//        private final TransactionContext transactionContext;
//
//        // 重试机制相关字段
//        private volatile int retryCount = 0;
//        private static final int MAX_RETRY_COUNT = 3;
//
//        public PersistenceTask(TaskType type, DistributedTransactionDO transaction, TransactionStep step) {
//            this.type = type;
//            this.transaction = transaction;
//            this.step = step;
//            this.stepHistory = null;
//            this.transactionId = null;
//            this.exceptionInfo = null;
//            this.transactionContext = TransactionContext.current() != null ? TransactionContext.current().copy() : null;
//        }
//
//        public PersistenceTask(TaskType type, DistributedTransactionDO transaction, TransactionStep step, StepExecutionHistory stepHistory) {
//            this.type = type;
//            this.transaction = transaction;
//            this.step = step;
//            this.stepHistory = stepHistory;
//            this.transactionId = null;
//            this.exceptionInfo = null;
//            this.transactionContext = TransactionContext.current() != null ? TransactionContext.current().copy() : null;
//        }
//
//        public PersistenceTask(TaskType type, DistributedTransactionDO transaction, TransactionStep step,
//                             String transactionId, Map<String, Object> exceptionInfo) {
//            this.type = type;
//            this.transaction = transaction;
//            this.step = step;
//            this.stepHistory = null;
//            this.transactionId = transactionId;
//            this.exceptionInfo = exceptionInfo;
//            this.transactionContext = TransactionContext.current() != null ? TransactionContext.current().copy() : null;
//        }
//
//        public TaskType getType() {
//            return type;
//        }
//
//        public DistributedTransactionDO getTransaction() {
//            return transaction;
//        }
//
//        public TransactionStep getStep() {
//            return step;
//        }
//
//        public String getTransactionId() {
//            return transactionId;
//        }
//
//        public Map<String, Object> getExceptionInfo() {
//            return exceptionInfo;
//        }
//
//        public StepExecutionHistory getStepHistory() {
//            return stepHistory;
//        }
//
//        /**
//         * 增加重试次数
//         */
//        public void incrementRetryCount() {
//            this.retryCount++;
//        }
//
//        /**
//         * 获取重试次数
//         */
//        public int getRetryCount() {
//            return retryCount;
//        }
//
//        /**
//         * 判断是否可以重试
//         */
//        public boolean canRetry() {
//            return retryCount < MAX_RETRY_COUNT;
//        }
//    }
//
//    // ==================== 事务执行历史相关方法 ====================
//
//    /**
//     * 异步保存事务执行历史
//     *
//     * @param history 事务执行历史
//     */
//    public void saveTransactionExecutionHistoryAsync(TransactionExecutionHistory history) {
//        if (history == null) {
//            return;
//        }
//
//        if (transactionExecutionHistoryRepository == null) {
//            logger.warn("TransactionExecutionHistoryRepository未配置，跳过事务执行历史保存");
//            return;
//        }
//
//        asyncExecutor.execute(() -> {
//            try {
//                transactionExecutionHistoryRepository.save(history);
//                logger.debug("异步保存事务执行历史成功: transactionId={}, attemptNumber={}",
//                    history.getTransactionId(), history.getAttemptNumber());
//            } catch (Exception e) {
//                logger.error("异步保存事务执行历史失败: transactionId={}, attemptNumber={}",
//                    history.getTransactionId(), history.getAttemptNumber(), e);
//            }
//        });
//    }
//
//    /**
//     * 批量异步保存事务执行历史
//     *
//     * @param histories 事务执行历史列表
//     */
//    public void saveTransactionExecutionHistoriesAsync(List<TransactionExecutionHistory> histories) {
//        if (histories == null || histories.isEmpty()) {
//            return;
//        }
//
//        if (transactionExecutionHistoryRepository == null) {
//            logger.warn("TransactionExecutionHistoryRepository未配置，跳过事务执行历史批量保存");
//            return;
//        }
//
//        asyncExecutor.execute(() -> {
//            try {
//                transactionExecutionHistoryRepository.saveAll(histories);
//                logger.debug("异步批量保存事务执行历史成功，数量: {}", histories.size());
//            } catch (Exception e) {
//                logger.error("异步批量保存事务执行历史失败，数量: {}", histories.size(), e);
//            }
//        });
//    }
//
//    // ==================== 异常信息持久化方法 ====================
//
//    /**
//     * 批量异步持久化异常信息
//     *
//     * @param exceptionInfoList 异常信息列表
//     */
//    public void persistExceptionsAsync(List<Map<String, Object>> exceptionInfoList) {
//        if (exceptionInfoList == null || exceptionInfoList.isEmpty()) {
//            return;
//        }
//
//        asyncExecutor.execute(() -> {
//            try {
//                logger.debug("异步批量持久化异常信息，数量: {}", exceptionInfoList.size());
//
//                // TODO: 实际实现中需要根据具体的存储方式来批量持久化异常信息
//                // 例如：exceptionRepository.saveAll(exceptionInfoList);
//
//            } catch (Exception e) {
//                logger.error("异步批量持久化异常信息失败，数量: {}", exceptionInfoList.size(), e);
//            }
//        });
//    }
//}
