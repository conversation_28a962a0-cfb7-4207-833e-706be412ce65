package com.tipray.transaction.core.application.listener;

import com.tipray.transaction.core.domain.statemachine.main.StateTransitionContext;
import com.tipray.transaction.core.domain.statemachine.main.StateTransitionListener;
import com.tipray.transaction.core.domain.transaction.TransactionDO;
import com.tipray.transaction.core.enums.TransactionStatus;
import com.tipray.transaction.core.infrastructure.persistence.AsyncPersistenceManager;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * 事务持久化监听器
 * 监听事务状态转换，触发持久化操作
 *
 * <AUTHOR>
 * @version 2.0
 * @since 2025-01-29
 */
public class TransactionPersistenceListener implements StateTransitionListener {

    private static final Logger log = LoggerFactory.getLogger(TransactionPersistenceListener.class);

    private final AsyncPersistenceManager persistenceManager;

    public TransactionPersistenceListener(AsyncPersistenceManager persistenceManager) {
        this.persistenceManager = persistenceManager;
    }

    @Override
    public void afterTransition(StateTransitionContext context) {
        String transactionId = context.getTransactionId();
        TransactionStatus toStatus = context.getToStatus();

        try {
            switch (toStatus) {
                case BEGIN:
                    // 事务开始 - 保存事务
                    handleTransactionBegin(context);
                    break;

                case EXECUTING:
                case COMMITTING:
                case ROLLBACKING:
                case COMMIT_RETRYING:
                case ROLLBACK_RETRYING:
                case ASYNC_COMMITTING:
                case TIMEOUT_ROLLBACKING:
                case TIMEOUT_ROLLBACK_RETRYING:
                    // 状态变更 - 更新状态
                    handleStatusUpdate(context);
                    break;

                case COMMITTED:
                case ROLLBACKED:
                case TIMEOUT_ROLLBACKED:
                    // 事务完成 - 更新状态，可选择归档
                    handleTransactionCompleted(context);
                    break;

                case COMMIT_FAILED:
                case ROLLBACK_FAILED:
                case TIMEOUT_ROLLBACK_FAILED:
                    // 失败状态 - 保存失败记录
                    handleTransactionFailed(context);
                    break;

                case MANUAL_INTERVENTION_REQUIRED:
                    // 需要人工干预 - 保存干预记录
                    handleManualInterventionRequired(context);
                    break;

                case FINISHED:
                    // 事务完成 - 清理或归档
                    handleTransactionFinished(context);
                    break;

                default:
                    // 其他状态 - 默认更新状态
                    handleStatusUpdate(context);
                    break;
            }

            log.debug("事务持久化处理完成: {} -> {} (事务: {})",
                    context.getFromStatus(), toStatus, transactionId);

        } catch (Exception e) {
            log.error("事务持久化处理失败: {} -> {} (事务: {})",
                    context.getFromStatus(), toStatus, transactionId, e);
            // 持久化失败不影响业务流程
        }
    }

    /**
     * 处理事务开始
     */
    private void handleTransactionBegin(StateTransitionContext context) {
        TransactionDO transaction = (TransactionDO) context.getData();
        if (transaction != null) {
            persistenceManager.asyncSaveTransaction(transaction);
        } else {
            log.warn("事务开始时未提供事务数据: {}", context.getTransactionId());
        }
    }

    /**
     * 处理状态更新
     */
    private void handleStatusUpdate(StateTransitionContext context) {
        persistenceManager.asyncUpdateTransactionStatus(
                context.getTransactionId(), context.getToStatus());
    }

    /**
     * 处理事务完成
     */
    private void handleTransactionCompleted(StateTransitionContext context) {
        // 更新最终状态
        handleStatusUpdate(context);

        // 可以在这里添加归档逻辑
        // persistenceManager.asyncArchiveTransaction(context.getTransactionId());
    }

    /**
     * 处理事务失败
     */
    private void handleTransactionFailed(StateTransitionContext context) {
        // 更新失败状态
        handleStatusUpdate(context);

        // 保存失败记录
        if (context.isFailure()) {
            Map<String, Object> failureData = buildFailureData(context);

            switch (context.getToStatus()) {
                case ROLLBACK_FAILED:
                case TIMEOUT_ROLLBACK_FAILED:
                    persistenceManager.asyncSaveRollbackFailure(
                            context.getTransactionId(), context.getFailureCause());
                    break;

                case COMMIT_FAILED:
                    // 可以添加提交失败记录
                    log.warn("事务提交失败: {} - {}",
                            context.getTransactionId(), context.getFailureMessage());
                    break;
            }
        }
    }

    /**
     * 处理需要人工干预
     */
    private void handleManualInterventionRequired(StateTransitionContext context) {
        handleStatusUpdate(context);

        // 发送告警或通知
        log.error("事务需要人工干预: {} - {}",
                context.getTransactionId(), context.getFailureMessage());

        // 可以在这里添加告警逻辑
        // alertService.sendManualInterventionAlert(context);
    }

    /**
     * 处理事务完成
     */
    private void handleTransactionFinished(StateTransitionContext context) {
        // 清理事务数据
        persistenceManager.asyncDeleteTransaction(context.getTransactionId());
    }

    /**
     * 构建失败数据
     */
    private Map<String, Object> buildFailureData(StateTransitionContext context) {
        Map<String, Object> failureData = new HashMap<>();
        failureData.put("transactionId", context.getTransactionId());
        failureData.put("fromStatus", context.getFromStatus());
        failureData.put("toStatus", context.getToStatus());
        failureData.put("event", context.getEvent());
        failureData.put("timestamp", context.getTimestamp());

        if (context.getFailureCause() != null) {
            failureData.put("exceptionClass", context.getFailureCause().getClass().getSimpleName());
            failureData.put("exceptionMessage", context.getFailureCause().getMessage());
        }

        if (context.getFailureMessage() != null) {
            failureData.put("failureMessage", context.getFailureMessage());
        }

        failureData.putAll(context.getContextData());

        return failureData;
    }

    @Override
    public int getPriority() {
        return 100; // 持久化监听器优先级较高
    }

    @Override
    public String getListenerName() {
        return "TransactionPersistenceListener";
    }

    @Override
    public boolean supports(StateTransitionContext context) {
        // 支持所有事务状态转换
        return true;
    }
}
