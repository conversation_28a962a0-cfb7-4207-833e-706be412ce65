package com.tipray.transaction.core.config;

import com.tipray.transaction.core.api.template.DistributedTransactionTemplate;
import com.tipray.transaction.core.application.coordinator.BranchTransactionCoordinator;
import com.tipray.transaction.core.application.engine.DistributedTransactionEngine;
import com.tipray.transaction.core.application.exception.TransactionExceptionHandler;
import com.tipray.transaction.core.application.execution.ExecutionHistoryManager;
import com.tipray.transaction.core.application.extension.branch.BranchTransactionExtension;
import com.tipray.transaction.core.application.extension.branch.BranchTransactionExtensionManager;
import com.tipray.transaction.core.application.extension.main.TransactionExtension;
import com.tipray.transaction.core.application.extension.main.TransactionExtensionManager;
import com.tipray.transaction.core.application.handler.mode.TransationModeHandlerFactory;
import com.tipray.transaction.core.application.listener.BranchTransactionPersistenceListener;
import com.tipray.transaction.core.application.listener.TransactionPersistenceListener;
import com.tipray.transaction.core.application.manual.ManualOperationService;
import com.tipray.transaction.core.application.propagation.TransactionPropagationManager;
import com.tipray.transaction.core.domain.statemachine.branch.BranchStateTransitionListener;
import com.tipray.transaction.core.domain.statemachine.branch.BranchTransactionStateMachine;
import com.tipray.transaction.core.domain.statemachine.main.StateTransitionListener;
import com.tipray.transaction.core.domain.statemachine.main.TransactionStateMachine;
import com.tipray.transaction.core.infrastructure.async.AsyncTransactionExecutor;
import com.tipray.transaction.core.infrastructure.barrier.TransactionBarrier;
import com.tipray.transaction.core.infrastructure.barrier.persistence.BarrierStorage;
import com.tipray.transaction.core.infrastructure.barrier.persistence.impl.DefaultBarrierStorage;
import com.tipray.transaction.core.infrastructure.event.TransactionEventPublisherImpl;
import com.tipray.transaction.core.infrastructure.metrics.TransactionMetricsCollector;
import com.tipray.transaction.core.infrastructure.monitor.TransactionTimeoutMonitor;
import com.tipray.transaction.core.infrastructure.persistence.AsyncPersistenceManager;
import com.tipray.transaction.core.infrastructure.pool.TransactionConnectionPoolManager;
import com.tipray.transaction.core.infrastructure.retry.RetryManager;
import com.tipray.transaction.core.persistence.BranchTransactionStorage;
import com.tipray.transaction.core.persistence.TransactionStorage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.transaction.PlatformTransactionManager;

import java.util.List;

/**
 * 事务核心基础设施配置类
 * 负责基础设施相关Bean的配置
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-05-20
 */
@Slf4j
@Configuration
@ConditionalOnProperty(
        prefix = "tipray.transaction",
        name = "enabled",
        havingValue = "true",
        matchIfMissing = true
)
public class TransactionCoreInfrastructureConfiguration {

    // 连接池相关
    @Bean
    @ConditionalOnMissingBean
    public TransactionConnectionPoolManager transactionConnectionPoolManager() {
        return new TransactionConnectionPoolManager();
    }

    // 存储相关 默认内存实现 ===============================

    // TODO 根据配置
    @Bean
    @ConditionalOnMissingBean
    public TransactionStorage defaultTransactionStorage() {
        return new com.tipray.transaction.core.infrastructure.persistence.impl.DefaultTransactionStorage();
    }

    // TODO 根据配置
    @Bean
    @ConditionalOnMissingBean
    public BranchTransactionStorage defaultBranchTransactionStorage() {
        return new com.tipray.transaction.core.infrastructure.persistence.impl.DefaultBranchTransactionStorage();
    }

    // TODO 根据配置
    @Bean
    @ConditionalOnMissingBean
    public BarrierStorage defaultBarrierStorage() {
        return new DefaultBarrierStorage();
    }

    // 存储相关 默认内存实现 ===============================


    @Bean
    @ConditionalOnMissingBean
    public RetryManager retryManager() {
        return new RetryManager();
    }

    @Bean
    @ConditionalOnMissingBean
    public TransactionEventPublisherImpl transactionEventPublisher(ApplicationEventPublisher applicationEventPublisher) {
        return new TransactionEventPublisherImpl(applicationEventPublisher);
    }

    @Bean
    @ConditionalOnMissingBean
    public TransactionMetricsCollector transactionMetricsCollector() {
        return new TransactionMetricsCollector();
    }

    @Bean
    @ConditionalOnMissingBean
    public AsyncPersistenceManager asyncPersistenceManager(TransactionStorage transactionStorage,
                                                           BranchTransactionStorage branchTransactionStorage) {
        return new AsyncPersistenceManager(transactionStorage, branchTransactionStorage);
    }

    @Bean
    @ConditionalOnMissingBean
    public StateTransitionListener transactionPersistenceListener(AsyncPersistenceManager persistenceManager) {
        return new TransactionPersistenceListener(persistenceManager);
    }

    @Bean
    @ConditionalOnMissingBean
    public BranchStateTransitionListener branchStateTransitionListener(AsyncPersistenceManager persistenceManager) {
        return new BranchTransactionPersistenceListener(persistenceManager);
    }

    @Bean
    @ConditionalOnMissingBean
    public TransactionStateMachine transactionStateMachine(TransactionMetricsCollector metricsCollector, StateTransitionListener listener) {
        return new TransactionStateMachine(metricsCollector, listener);
    }

    @Bean
    @ConditionalOnMissingBean
    public BranchTransactionStateMachine branchTransactionStateMachine(TransactionMetricsCollector metricsCollector, BranchStateTransitionListener listener) {
        return new BranchTransactionStateMachine(metricsCollector, listener);
    }

    @Bean
    @ConditionalOnMissingBean
    public TransationModeHandlerFactory transationModeHandlerFactory() {
        return new TransationModeHandlerFactory();
    }

    @Bean
    @ConditionalOnMissingBean
    public TransactionBarrier transactionBarrier(BarrierStorage barrierStorage) {
        return new TransactionBarrier(barrierStorage);
    }

    @Bean
    @ConditionalOnMissingBean
    public DistributedTransactionEngine distributedTransactionEngine(TransactionStateMachine stateMachine,
                                                                     TransactionPropagationManager propagationManager,
                                                                     TransactionExtensionManager extensionManager,
                                                                     TransactionExceptionHandler exceptionHandler,
                                                                     BranchTransactionCoordinator branchCoordinator,
                                                                     TransactionBarrier transactionBarrier) {
        return new DistributedTransactionEngine(stateMachine, propagationManager, extensionManager, exceptionHandler,
                branchCoordinator, transactionBarrier);
    }

    @Bean
    @ConditionalOnMissingBean
    public TransactionExceptionHandler transactionExceptionHandler() {
        return new TransactionExceptionHandler();
    }

    @Bean
    @ConditionalOnMissingBean
    public TransactionExtensionManager transactionExtensionManager(
            @org.springframework.beans.factory.annotation.Autowired(required = false) List<TransactionExtension> extensions) {
        return new TransactionExtensionManager(extensions);
    }

    @Bean
    @ConditionalOnMissingBean(BranchTransactionExtensionManager.class)
    public BranchTransactionExtensionManager branchTransactionExtensionManager(
            @org.springframework.beans.factory.annotation.Autowired(required = false) List<BranchTransactionExtension> extensions) {
        return new BranchTransactionExtensionManager(extensions);
    }

    @Bean
    @ConditionalOnMissingBean
    public TransactionPropagationManager transactionPropagationManager() {
        return new TransactionPropagationManager();
    }

    @Bean
    @ConditionalOnMissingBean
    public AsyncTransactionExecutor asyncTransactionExecutor() {
        return new AsyncTransactionExecutor();
    }

    @Bean
    @ConditionalOnMissingBean
    public BranchTransactionCoordinator branchTransactionCoordinator(TransactionEventPublisherImpl eventPublisher,
                                                                     TransactionMetricsCollector metricsCollector,
                                                                     TransationModeHandlerFactory transationModeHandlerFactory,
                                                                     AsyncTransactionExecutor asyncExecutor,
                                                                     BranchTransactionExtensionManager extensionManager,
                                                                     PlatformTransactionManager platformTransactionManager,
                                                                     BranchTransactionStateMachine branchStateMachine) {
        return new BranchTransactionCoordinator(eventPublisher, metricsCollector,
                transationModeHandlerFactory, asyncExecutor, extensionManager, platformTransactionManager, branchStateMachine);
    }

    @Bean
    @ConditionalOnMissingBean
    public DistributedTransactionTemplate distributedTransactionTemplate(DistributedTransactionEngine transactionEngine) {
        return new DistributedTransactionTemplate(transactionEngine);
    }

    /**
     * 配置手动操作服务
     */
    @Bean
    @ConditionalOnMissingBean(ManualOperationService.class)
    public ManualOperationService manualOperationService(
            ExecutionHistoryManager executionHistoryManager,
            TransactionStorage transactionStorage,
            BranchTransactionStorage branchTransactionStorage) {
        return new ManualOperationService(executionHistoryManager, transactionStorage, branchTransactionStorage);
    }

    @Bean
    @ConditionalOnMissingBean
    public TransactionTimeoutMonitor transactionTimeoutMonitor(TransactionConnectionPoolManager poolManager) {
        return new TransactionTimeoutMonitor(poolManager);
    }
}
