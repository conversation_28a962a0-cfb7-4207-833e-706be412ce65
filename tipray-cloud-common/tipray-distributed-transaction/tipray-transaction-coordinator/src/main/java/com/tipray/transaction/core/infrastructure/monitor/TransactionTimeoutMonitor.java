package com.tipray.transaction.core.infrastructure.monitor;

import com.alibaba.ttl.threadpool.TtlExecutors;
import com.tipray.transaction.core.exception.DistributedTransactionSystemException;
import com.tipray.transaction.core.exception.DistributedTransactionTimeoutException;
import com.tipray.transaction.core.infrastructure.pool.TransactionConnectionPoolManager;
import lombok.extern.slf4j.Slf4j;

import java.util.concurrent.*;
import java.util.function.Supplier;

/**
 * 简洁的超时监控器
 *
 * 功能特点：
 * 1. 支持 lambda 方法的超时监控
 * 2. 简单易用，只需传入方法和超时时间
 * 3. 支持同步和异步超时检查
 * 4. 自动处理超时异常
 *
 * <AUTHOR>
 * @version 2.0
 * @since 2025-01-23
 */
@Slf4j
public class TransactionTimeoutMonitor {

    // 线程池用于执行超时监控任务
    private final ExecutorService executorService;

    public TransactionTimeoutMonitor(TransactionConnectionPoolManager poolManager) {
        // 使用ttl包装线程池
        this.executorService = poolManager.getTransactionExecutor();
    }

    // ==================== 核心监控方法 ====================

    /**
     * 执行带超时的操作（推荐使用）
     * 为每个任务创建独立的线程池，确保超时后资源被正确释放
     *
     * @param operation 要执行的操作
     * @param timeoutMillis 超时时间（毫秒）
     * @param operationName 操作名称（用于日志）
     * @return 操作结果
     */
    public <T> T executeWithTimeoutSafe(Supplier<T> operation, long timeoutMillis, String operationName) {
        // 为每个任务创建独立的线程池，避免资源泄漏
        ExecutorService taskExecutor = TtlExecutors.getTtlExecutorService(Executors.newSingleThreadExecutor());

        try {
            CompletableFuture<T> future = CompletableFuture.supplyAsync(operation, taskExecutor);

            try {
                return future.get(timeoutMillis, TimeUnit.MILLISECONDS);

            } catch (TimeoutException e) {
                // 取消Future
                boolean cancelled = future.cancel(true);
                log.warn("操作执行超时: {} - 超时时间: {}ms, 取消状态: {}", operationName, timeoutMillis, cancelled);
                // 抛出Tipray框架内部超时异常，区别于业务HTTP超时异常
                throw new DistributedTransactionTimeoutException(
                        "操作执行超时: " + operationName,
                        timeoutMillis,
                        System.currentTimeMillis(),
                        DistributedTransactionTimeoutException.TimeoutType.RESOURCE_TIMEOUT,
                        operationName
                );

            } catch (InterruptedException e) {
                // 取消Future
                future.cancel(true);
                // 恢复中断状态
                Thread.currentThread().interrupt();
                log.warn("操作被中断: {}", operationName);
                throw new DistributedTransactionTimeoutException(
                        "操作被中断: " + operationName,
                        e,
                        timeoutMillis,
                        System.currentTimeMillis(),
                        DistributedTransactionTimeoutException.TimeoutType.UNKNOWN,
                        operationName
                );

            } catch (ExecutionException e) {
                Throwable cause = e.getCause();
                if (cause instanceof RuntimeException) {
                    throw (RuntimeException) cause;
                } else {
                    throw new DistributedTransactionSystemException(
                            "操作执行异常: " + operationName,
                            cause,
                            DistributedTransactionSystemException.SystemExceptionType.UNKNOWN,
                            DistributedTransactionSystemException.SeverityLevel.MEDIUM,
                            operationName
                    );
                }
            }

        } finally {
            // 确保线程池被关闭
            shutdownExecutor(taskExecutor, operationName);
        }
    }


    /**
     * 监控方法执行，如果超时则抛出异常
     *
     * @param operation 要执行的操作
     * @param timeoutMillis 超时时间（毫秒）
     * @param operationName 操作名称（用于日志）
     * @return 操作结果
     * @throws DistributedTransactionTimeoutException 如果操作超时
     */
    public <T> T executeWithTimeout(Supplier<T> operation, long timeoutMillis, String operationName) {
        CompletableFuture<T> future = CompletableFuture.supplyAsync(operation, executorService);

        try {
            T result = future.get(timeoutMillis, TimeUnit.MILLISECONDS);
            log.debug("操作执行成功: {}", operationName);
            return result;

        } catch (TimeoutException e) {
            future.cancel(true);
            log.warn("操作执行超时: {} - 超时时间: {}ms", operationName, timeoutMillis);
            throw new DistributedTransactionTimeoutException(
                    "操作执行超时: " + operationName,
                    timeoutMillis,
                    System.currentTimeMillis(),
                    DistributedTransactionTimeoutException.TimeoutType.RESOURCE_TIMEOUT,
                    operationName
            );

        } catch (InterruptedException e) {
            future.cancel(true);
            Thread.currentThread().interrupt();
            throw new DistributedTransactionTimeoutException(
                    "操作被中断: " + operationName,
                    e,
                    timeoutMillis,
                    System.currentTimeMillis(),
                    DistributedTransactionTimeoutException.TimeoutType.UNKNOWN,
                    operationName
            );

        } catch (ExecutionException e) {
            Throwable cause = e.getCause();
            if (cause instanceof RuntimeException) {
                throw (RuntimeException) cause;
            } else {
                throw new DistributedTransactionSystemException(
                        "操作执行异常: " + operationName,
                        cause,
                        DistributedTransactionSystemException.SystemExceptionType.UNKNOWN,
                        DistributedTransactionSystemException.SeverityLevel.MEDIUM,
                        operationName
                );
            }
        }
    }

    /**
     * 监控 Runnable 方法执行，如果超时则抛出异常
     *
     * @param operation 要执行的操作
     * @param timeoutMillis 超时时间（毫秒）
     * @param operationName 操作名称（用于日志）
     * @throws TimeoutException 如果操作超时
     */
    public void executeWithTimeout(Runnable operation, long timeoutMillis, String operationName) throws TimeoutException {
        executeWithTimeout(() -> {
            operation.run();
            return null;
        }, timeoutMillis, operationName);
    }

    /**
     * 简单的超时检查（不执行操作，只检查是否超时）
     *
     * @param startTime 开始时间（毫秒）
     * @param timeoutMillis 超时时间（毫秒）
     * @param operationName 操作名称（用于日志）
     * @return 是否超时
     */
    public boolean isTimeout(long startTime, long timeoutMillis, String operationName) {
        long elapsed = System.currentTimeMillis() - startTime;
        boolean timeout = elapsed > timeoutMillis;

        if (timeout) {
            log.warn("检测到超时: {} - 执行时间: {}ms, 超时限制: {}ms", operationName, elapsed, timeoutMillis);
        }

        return timeout;
    }

    /**
     * 安全关闭线程池
     *
     * @param executor 要关闭的线程池
     * @param operationName 操作名称（用于日志）
     */
    private void shutdownExecutor(ExecutorService executor, String operationName) {
        if (executor == null || executor.isShutdown()) {
            return;
        }

        try {
            // 优雅关闭
            executor.shutdown();

            // 等待任务完成，最多等待1秒
            if (!executor.awaitTermination(1, TimeUnit.SECONDS)) {
                log.warn("线程池未在1秒内优雅关闭，强制关闭: {}", operationName);
                // 强制关闭
                executor.shutdownNow();

                // 再次等待，最多等待500毫秒
                if (!executor.awaitTermination(500, TimeUnit.MILLISECONDS)) {
                    log.error("线程池强制关闭失败: {}", operationName);
                }
            }
        } catch (InterruptedException e) {
            log.warn("关闭线程池时被中断: {}", operationName);
            // 强制关闭
            executor.shutdownNow();
            // 恢复中断状态
            Thread.currentThread().interrupt();
        }
    }
}
