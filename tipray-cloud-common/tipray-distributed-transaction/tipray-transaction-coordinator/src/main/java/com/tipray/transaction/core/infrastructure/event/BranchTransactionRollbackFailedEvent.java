package com.tipray.transaction.core.infrastructure.event;

import com.tipray.transaction.core.domain.coordinator.BranchTransactionInfo;
import org.springframework.context.ApplicationEvent;

/**
 * 分支事务回滚失败事件
 *
 * <AUTHOR>
 * @version 2.0
 * @since 2025-01-23
 */
public class BranchTransactionRollbackFailedEvent extends ApplicationEvent {

    private final BranchTransactionInfo branchInfo;
    private final Exception cause;

    public BranchTransactionRollbackFailedEvent(BranchTransactionInfo branchInfo, Exception cause) {
        super(branchInfo);
        this.branchInfo = branchInfo;
        this.cause = cause;
    }

    public BranchTransactionInfo getBranchInfo() {
        return branchInfo;
    }

    public Exception getCause() {
        return cause;
    }

    public String getTransactionId() {
        return branchInfo.getTransactionId();
    }

    public String getBranchId() {
        return branchInfo.getBranchId();
    }

    @Override
    public String toString() {
        return String.format("BranchTransactionRollbackFailedEvent{txId='%s', branchId='%s', endpoint='%s', cause='%s'}",
                branchInfo.getTransactionId(), branchInfo.getBranchId(),
                branchInfo.getServiceEndpoint(), cause != null ? cause.getMessage() : "null");
    }
}
