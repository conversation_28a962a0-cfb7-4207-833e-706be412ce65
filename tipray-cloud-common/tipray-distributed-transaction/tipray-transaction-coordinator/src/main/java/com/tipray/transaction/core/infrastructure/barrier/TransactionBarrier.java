package com.tipray.transaction.core.infrastructure.barrier;

import com.tipray.transaction.core.domain.barrier.BarrierCheckResult;
import com.tipray.transaction.core.domain.barrier.BarrierInfo;
import com.tipray.transaction.core.enums.TransactionMode;
import com.tipray.transaction.core.infrastructure.barrier.persistence.BarrierStorage;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

/**
 * 事务屏障（统一实现）
 * 防止空回滚、悬挂和幂等问题的核心组件
 * 支持AT和Saga模式的屏障检查
 *
 * <AUTHOR>
 * @version 2.0
 * @since 2025-01-23
 */
public class TransactionBarrier {

    private static final Logger log = LoggerFactory.getLogger(TransactionBarrier.class);

    private final BarrierStorage barrierStorage;

    public TransactionBarrier(BarrierStorage barrierStorage) {
        this.barrierStorage = barrierStorage;
    }

    /**
     * 统一的屏障检查接口
     * 支持AT和Saga模式的屏障检查
     *
     * @param barrierInfo 屏障信息
     * @return 屏障检查结果
     */
    public BarrierCheckResult checkBarrier(BarrierInfo barrierInfo) {
        if (barrierInfo == null) {
            log.warn("屏障检查失败：屏障信息不能为空");
            return BarrierCheckResult.error("屏障信息不能为空");
        }

        String gid = barrierInfo.getGid();
        Long branchId = barrierInfo.getBranchId();
        String operation = barrierInfo.getOperation();
        TransactionMode mode = barrierInfo.getMode();

        try {
            // 根据事务模式进行不同的屏障处理
            if (mode != null && mode.isSaga()) {
                return handleSagaBarrier(barrierInfo);
            } else {
                // AT模式或其他模式的屏障检查
                return handleAtBarrier(barrierInfo);
            }

        } catch (Exception e) {
            log.error("屏障检查异常: gid={}, branchId={}, operation={}", gid, branchId, operation, e);
            // 异常情况下，为了安全起见，允许执行
            return BarrierCheckResult.pass();
        }
    }

    /**
     * AT模式屏障处理
     * 处理try/confirm/cancel操作的屏障逻辑
     * 实现DTM标准的"两个insert判断"算法
     */
    private BarrierCheckResult handleAtBarrier(BarrierInfo barrierInfo) {
        String gid = barrierInfo.getGid();
        Long branchId = barrierInfo.getBranchId();
        String operation = barrierInfo.getOperation();

        if ("cancel".equals(operation)) {
            // cancel操作的处理逻辑
            return handleAtCancelOperation(gid, branchId);
        } else if ("try".equals(operation)) {
            // try操作的处理逻辑
            return handleAtTryOperation(gid, branchId);
        } else {
            // confirm等其他操作：只做幂等控制
            boolean currentInserted = barrierStorage.insertBarrier(gid, branchId, operation);
            if (!currentInserted) {
                log.debug("屏障幂等控制: gid={}, branchId={}, operation={}", gid, branchId, operation);
                return BarrierCheckResult.idempotent("操作已执行，幂等控制");
            }
            log.debug("屏障检查通过: gid={}, branchId={}, operation={} (AT模式)", gid, branchId, operation);
            return BarrierCheckResult.pass();
        }
    }

    /**
     * 处理AT模式的cancel操作
     */
    private BarrierCheckResult handleAtCancelOperation(String gid, Long branchId) {
        // 1. 插入cancel记录
        boolean cancelInserted = barrierStorage.insertBarrier(gid, branchId, "cancel");
        if (!cancelInserted) {
            // cancel操作已执行过，幂等控制
            log.debug("屏障幂等控制: gid={}, branchId={}, operation=cancel", gid, branchId);
            return BarrierCheckResult.idempotent("cancel操作已执行，幂等控制");
        }

        // 2. 尝试插入try记录，如果成功说明try未执行（空回滚）
        boolean tryInserted = barrierStorage.insertBarrier(gid, branchId, "try");
        if (tryInserted) {
            log.warn("屏障检测到AT空回滚: gid={}, branchId={}", gid, branchId);
            return BarrierCheckResult.emptyRollback("AT空回滚：try操作未执行");
        }

        // 3. try记录已存在，说明try已执行，正常回滚
        log.debug("屏障检查通过: gid={}, branchId={}, operation=cancel", gid, branchId);
        return BarrierCheckResult.pass();
    }

    /**
     * 处理AT模式的try操作
     */
    private BarrierCheckResult handleAtTryOperation(String gid, Long branchId) {
        // 1. 检查cancel是否已执行
        boolean cancelExists = barrierStorage.hasBarrier(gid, branchId, "cancel");
        if (cancelExists) {
            // 悬挂：cancel已执行，try操作不应执行
            log.warn("屏障检测到AT悬挂: gid={}, branchId={}", gid, branchId);
            return BarrierCheckResult.hanging("AT悬挂：cancel已执行");
        }

        // 2. 插入try记录
        boolean tryInserted = barrierStorage.insertBarrier(gid, branchId, "try");
        if (!tryInserted) {
            // try操作已执行过，幂等控制
            log.debug("屏障幂等控制: gid={}, branchId={}, operation=try", gid, branchId);
            return BarrierCheckResult.idempotent("try操作已执行，幂等控制");
        }

        // 3. try记录插入成功，正常执行
        log.debug("屏障检查通过: gid={}, branchId={}, operation=try", gid, branchId);
        return BarrierCheckResult.pass();
    }

    /**
     * Saga模式屏障处理
     * 处理forward/compensate操作的屏障逻辑
     * 实现DTM标准的"两个insert判断"算法
     */
    private BarrierCheckResult handleSagaBarrier(BarrierInfo barrierInfo) {
        String gid = barrierInfo.getGid();
        Long branchId = barrierInfo.getBranchId();
        String operation = barrierInfo.getOperation();

        if ("compensate".equals(operation)) {
            // compensate操作的处理逻辑
            return handleSagaCompensateOperation(gid, branchId);
        } else if ("forward".equals(operation)) {
            // forward操作的处理逻辑
            return handleSagaForwardOperation(gid, branchId);
        } else {
            // 其他操作：只做幂等控制
            boolean currentInserted = barrierStorage.insertBarrier(gid, branchId, operation);
            if (!currentInserted) {
                log.debug("屏障幂等控制: gid={}, branchId={}, operation={}", gid, branchId, operation);
                return BarrierCheckResult.idempotent("操作已执行，幂等控制");
            }
            log.debug("屏障检查通过: gid={}, branchId={}, operation={} (Saga模式)", gid, branchId, operation);
            return BarrierCheckResult.pass();
        }
    }

    /**
     * 处理Saga模式的compensate操作
     */
    private BarrierCheckResult handleSagaCompensateOperation(String gid, Long branchId) {
        // 1. 插入compensate记录
        boolean compensateInserted = barrierStorage.insertBarrier(gid, branchId, "compensate");
        if (!compensateInserted) {
            // compensate操作已执行过，幂等控制
            log.debug("屏障幂等控制: gid={}, branchId={}, operation=compensate", gid, branchId);
            return BarrierCheckResult.idempotent("compensate操作已执行，幂等控制");
        }

        // 2. 尝试插入forward记录，如果成功说明forward未执行（空补偿）
        boolean forwardInserted = barrierStorage.insertBarrier(gid, branchId, "forward");
        if (forwardInserted) {
            log.warn("屏障检测到Saga空补偿: gid={}, branchId={}", gid, branchId);
            return BarrierCheckResult.emptyRollback("Saga空补偿：正向操作未执行");
        }

        // 3. forward记录已存在，说明forward已执行，正常补偿
        log.debug("屏障检查通过: gid={}, branchId={}, operation=compensate", gid, branchId);
        return BarrierCheckResult.pass();
    }

    /**
     * 处理Saga模式的forward操作
     */
    private BarrierCheckResult handleSagaForwardOperation(String gid, Long branchId) {
        // 1. 检查compensate是否已执行
        boolean compensateExists = barrierStorage.hasBarrier(gid, branchId, "compensate");
        if (compensateExists) {
            // 悬挂：compensate已执行，forward操作不应执行
            log.warn("屏障检测到Saga悬挂: gid={}, branchId={}", gid, branchId);
            return BarrierCheckResult.hanging("Saga悬挂：补偿已执行");
        }

        // 2. 插入forward记录
        boolean forwardInserted = barrierStorage.insertBarrier(gid, branchId, "forward");
        if (!forwardInserted) {
            // forward操作已执行过，幂等控制
            log.debug("屏障幂等控制: gid={}, branchId={}, operation=forward", gid, branchId);
            return BarrierCheckResult.idempotent("forward操作已执行，幂等控制");
        }

        // 3. forward记录插入成功，正常执行
        log.debug("屏障检查通过: gid={}, branchId={}, operation=forward", gid, branchId);
        return BarrierCheckResult.pass();
    }

    /**
     * 清理事务屏障记录
     *
     * @param gid 全局事务ID
     */
    public void cleanupBarrier(String gid) {
        try {
            barrierStorage.deleteBarrier(gid);
            log.debug("屏障清理完成: gid={}", gid);
        } catch (Exception e) {
            log.error("屏障清理失败: gid={}", gid, e);
        }
    }

    /**
     * 构建屏障信息
     * 便于从现有参数构建BarrierInfo对象
     */
    public BarrierInfo buildBarrierInfo(String transactionId, Long branchId, String operation, TransactionMode mode) {
        return new BarrierInfo(transactionId, branchId, operation, mode);
    }
}
