package com.tipray.transaction.core.application.exception;

import java.util.*;

/**
 * 异常回滚规则
 * 定义哪些异常需要回滚，哪些异常不需要回滚
 *
 * <AUTHOR>
 * @version 2.0
 * @since 2025-01-23
 */
public class ExceptionRollbackRule {

    private final String ruleName;
    private final String description;
    private final Set<Class<? extends Throwable>> rollbackFor;
    private final Set<Class<? extends Throwable>> noRollbackFor;
    private final int priority;
    private final boolean enabled;

    private ExceptionRollbackRule(Builder builder) {
        this.ruleName = builder.ruleName;
        this.description = builder.description;
        this.rollbackFor = Collections.unmodifiableSet(new HashSet<>(builder.rollbackFor));
        this.noRollbackFor = Collections.unmodifiableSet(new HashSet<>(builder.noRollbackFor));
        this.priority = builder.priority;
        this.enabled = builder.enabled;

        validateRule();
    }

    public static Builder builder() {
        return new Builder();
    }

    /**
     * 判断是否需要为指定异常回滚
     *
     * @param throwable 异常
     * @return true需要回滚，false不需要回滚，null规则不匹配
     */
    public Boolean shouldRollback(Throwable throwable) {
        if (!enabled || throwable == null) {
            return null;
        }

        Class<?> throwableClass = throwable.getClass();

        // 1. 检查noRollbackFor规则（优先级更高）
        for (Class<? extends Throwable> noRollbackClass : noRollbackFor) {
            if (isAssignableFrom(noRollbackClass, throwableClass)) {
                return false;
            }
        }

        // 2. 检查rollbackFor规则
        for (Class<? extends Throwable> rollbackClass : rollbackFor) {
            if (isAssignableFrom(rollbackClass, throwableClass)) {
                return true;
            }
        }

        // 3. 规则不匹配
        return null;
    }

    /**
     * 判断规则是否匹配指定异常
     */
    public boolean matches(Throwable throwable) {
        return shouldRollback(throwable) != null;
    }

    /**
     * 获取规则匹配的异常类型
     */
    public Set<Class<? extends Throwable>> getMatchingExceptionTypes() {
        Set<Class<? extends Throwable>> matchingTypes = new HashSet<>();
        matchingTypes.addAll(rollbackFor);
        matchingTypes.addAll(noRollbackFor);
        return Collections.unmodifiableSet(matchingTypes);
    }

    /**
     * 合并规则
     */
    public ExceptionRollbackRule merge(ExceptionRollbackRule other) {
        if (other == null) {
            return this;
        }

        Builder builder = builder()
                .ruleName(this.ruleName + "+" + other.ruleName)
                .description("合并规则: " + this.description + " + " + other.description)
                .priority(Math.max(this.priority, other.priority))
                .enabled(this.enabled && other.enabled);

        // 合并rollbackFor
        Set<Class<? extends Throwable>> mergedRollbackFor = new HashSet<>(this.rollbackFor);
        mergedRollbackFor.addAll(other.rollbackFor);
        builder.rollbackFor(mergedRollbackFor.toArray(new Class[0]));

        // 合并noRollbackFor
        Set<Class<? extends Throwable>> mergedNoRollbackFor = new HashSet<>(this.noRollbackFor);
        mergedNoRollbackFor.addAll(other.noRollbackFor);
        builder.noRollbackFor(mergedNoRollbackFor.toArray(new Class[0]));

        return builder.build();
    }

    /**
     * 创建副本
     */
    public ExceptionRollbackRule copy() {
        return builder()
                .ruleName(ruleName)
                .description(description)
                .rollbackFor(rollbackFor.toArray(new Class[0]))
                .noRollbackFor(noRollbackFor.toArray(new Class[0]))
                .priority(priority)
                .enabled(enabled)
                .build();
    }

    /**
     * 转换为详细描述
     */
    public String toDetailedDescription() {
        StringBuilder sb = new StringBuilder();
        sb.append("规则名称: ").append(ruleName).append("\n");
        sb.append("描述: ").append(description).append("\n");
        sb.append("优先级: ").append(priority).append("\n");
        sb.append("启用状态: ").append(enabled ? "启用" : "禁用").append("\n");

        if (!rollbackFor.isEmpty()) {
            sb.append("回滚异常: ");
            rollbackFor.forEach(clazz -> sb.append(clazz.getSimpleName()).append(" "));
            sb.append("\n");
        }

        if (!noRollbackFor.isEmpty()) {
            sb.append("不回滚异常: ");
            noRollbackFor.forEach(clazz -> sb.append(clazz.getSimpleName()).append(" "));
            sb.append("\n");
        }

        return sb.toString();
    }

    /**
     * 验证规则的有效性
     */
    private void validateRule() {
        // 检查是否有冲突的规则
        Set<Class<? extends Throwable>> conflicts = new HashSet<>(rollbackFor);
        conflicts.retainAll(noRollbackFor);

        if (!conflicts.isEmpty()) {
            throw new IllegalArgumentException(
                    String.format("回滚规则冲突，以下异常同时出现在rollbackFor和noRollbackFor中: %s",
                            conflicts));
        }

        // 检查是否有继承关系冲突
        for (Class<? extends Throwable> rollbackClass : rollbackFor) {
            for (Class<? extends Throwable> noRollbackClass : noRollbackFor) {
                if (isAssignableFrom(rollbackClass, noRollbackClass) ||
                        isAssignableFrom(noRollbackClass, rollbackClass)) {
                    throw new IllegalArgumentException(
                            String.format("回滚规则继承冲突: %s 和 %s 存在继承关系",
                                    rollbackClass.getSimpleName(), noRollbackClass.getSimpleName()));
                }
            }
        }
    }

    // ==================== 私有方法 ====================

    /**
     * 判断类型是否匹配
     */
    private boolean isAssignableFrom(Class<? extends Throwable> ruleClass, Class<?> throwableClass) {
        return ruleClass.isAssignableFrom(throwableClass);
    }

    // getters
    public String getRuleName() {
        return ruleName;
    }

    public String getDescription() {
        return description;
    }

    public Set<Class<? extends Throwable>> getRollbackFor() {
        return rollbackFor;
    }

    public Set<Class<? extends Throwable>> getNoRollbackFor() {
        return noRollbackFor;
    }

    public int getPriority() {
        return priority;
    }

    public boolean isEnabled() {
        return enabled;
    }

    @Override
    public String toString() {
        return String.format("ExceptionRollbackRule{name='%s', priority=%d, enabled=%s}",
                ruleName, priority, enabled);
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        ExceptionRollbackRule that = (ExceptionRollbackRule) o;
        return Objects.equals(ruleName, that.ruleName);
    }

    @Override
    public int hashCode() {
        return Objects.hash(ruleName);
    }

    public static class Builder {
        private String ruleName;
        private String description = "";
        private Set<Class<? extends Throwable>> rollbackFor = new HashSet<>();
        private Set<Class<? extends Throwable>> noRollbackFor = new HashSet<>();
        private int priority = 0;
        private boolean enabled = true;

        public Builder ruleName(String ruleName) {
            this.ruleName = ruleName;
            return this;
        }

        public Builder description(String description) {
            this.description = description;
            return this;
        }

        @SafeVarargs
        public final Builder rollbackFor(Class<? extends Throwable>... rollbackFor) {
            this.rollbackFor.addAll(Arrays.asList(rollbackFor));
            return this;
        }

        @SafeVarargs
        public final Builder noRollbackFor(Class<? extends Throwable>... noRollbackFor) {
            this.noRollbackFor.addAll(Arrays.asList(noRollbackFor));
            return this;
        }

        public Builder priority(int priority) {
            this.priority = priority;
            return this;
        }

        public Builder enabled(boolean enabled) {
            this.enabled = enabled;
            return this;
        }

        public ExceptionRollbackRule build() {
            if (ruleName == null || ruleName.trim().isEmpty()) {
                throw new IllegalArgumentException("规则名称不能为空");
            }

            return new ExceptionRollbackRule(this);
        }
    }
}
