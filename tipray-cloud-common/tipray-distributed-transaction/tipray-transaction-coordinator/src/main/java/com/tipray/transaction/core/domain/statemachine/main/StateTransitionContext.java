package com.tipray.transaction.core.domain.statemachine.main;

import com.tipray.transaction.core.enums.TransactionEvent;
import com.tipray.transaction.core.enums.TransactionStatus;

import java.util.HashMap;
import java.util.Map;

/**
 * 状态转换上下文
 * 携带状态转换的所有相关信息，特别是失败场景的详细信息
 *
 * <AUTHOR>
 * @version 2.0
 * @since 2025-01-29
 */
public class StateTransitionContext {

    private final String transactionId;
    private final TransactionStatus fromStatus;
    private final TransactionStatus toStatus;
    private final TransactionEvent event;
    private final long timestamp;

    // 扩展数据字段
    private final Object data;                          // 通用数据载体
    private final Exception failureCause;               // 失败原因（失败状态必填）
    private final String failureMessage;                // 失败消息
    private final Map<String, Object> contextData;      // 上下文数据

    /**
     * 完整构造函数
     */
    public StateTransitionContext(String transactionId,
                                  TransactionStatus fromStatus,
                                  TransactionStatus toStatus,
                                  TransactionEvent event,
                                  Object data,
                                  Exception failureCause,
                                  String failureMessage,
                                  Map<String, Object> contextData) {
        this.transactionId = transactionId;
        this.fromStatus = fromStatus;
        this.toStatus = toStatus;
        this.event = event;
        this.timestamp = System.currentTimeMillis();
        this.data = data;
        this.failureCause = failureCause;
        this.failureMessage = failureMessage;
        this.contextData = contextData != null ? new HashMap<>(contextData) : new HashMap<>();
    }

    /**
     * 成功场景构造方法
     */
    public static StateTransitionContext success(String transactionId,
                                                 TransactionStatus fromStatus,
                                                 TransactionStatus toStatus,
                                                 TransactionEvent event,
                                                 Object data) {
        return new StateTransitionContext(transactionId, fromStatus, toStatus,
                event, data, null, null, null);
    }

    /**
     * 失败场景构造方法
     */
    public static StateTransitionContext failure(String transactionId,
                                                 TransactionStatus fromStatus,
                                                 TransactionStatus toStatus,
                                                 TransactionEvent event,
                                                 Exception cause,
                                                 String message) {
        return new StateTransitionContext(transactionId, fromStatus, toStatus,
                event, null, cause, message, null);
    }

    /**
     * 失败场景构造方法（带上下文数据）
     */
    public static StateTransitionContext failure(String transactionId,
                                                 TransactionStatus fromStatus,
                                                 TransactionStatus toStatus,
                                                 TransactionEvent event,
                                                 Exception cause,
                                                 String message,
                                                 Map<String, Object> contextData) {
        return new StateTransitionContext(transactionId, fromStatus, toStatus,
                event, null, cause, message, contextData);
    }

    // ==================== 判断方法 ====================

    /**
     * 判断是否为失败场景
     */
    public boolean isFailure() {
        return failureCause != null;
    }

    /**
     * 判断是否为成功场景
     */
    public boolean isSuccess() {
        return failureCause == null;
    }

    /**
     * 判断是否为终态转换
     */
    public boolean isTerminalTransition() {
        return toStatus != null && toStatus.isTerminal();
    }

    /**
     * 判断是否为提交相关转换
     */
    public boolean isCommitRelated() {
        return event != null && event.isCommitRelated();
    }

    /**
     * 判断是否为回滚相关转换
     */
    public boolean isRollbackRelated() {
        return event != null && event.isRollbackRelated();
    }

    // ==================== 上下文数据操作 ====================

    /**
     * 添加上下文数据
     */
    public void addContextData(String key, Object value) {
        this.contextData.put(key, value);
    }

    /**
     * 获取上下文数据
     */
    @SuppressWarnings("unchecked")
    public <T> T getContextData(String key) {
        return (T) this.contextData.get(key);
    }

    /**
     * 获取上下文数据（带默认值）
     */
    @SuppressWarnings("unchecked")
    public <T> T getContextData(String key, T defaultValue) {
        return (T) this.contextData.getOrDefault(key, defaultValue);
    }

    // ==================== Getter方法 ====================

    public String getTransactionId() {
        return transactionId;
    }

    public TransactionStatus getFromStatus() {
        return fromStatus;
    }

    public TransactionStatus getToStatus() {
        return toStatus;
    }

    public TransactionEvent getEvent() {
        return event;
    }

    public long getTimestamp() {
        return timestamp;
    }

    public Object getData() {
        return data;
    }

    public Exception getFailureCause() {
        return failureCause;
    }

    public String getFailureMessage() {
        return failureMessage;
    }

    public Map<String, Object> getContextData() {
        return new HashMap<>(contextData);
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("StateTransitionContext{");
        sb.append("transactionId='").append(transactionId).append('\'');
        sb.append(", transition=").append(fromStatus).append("->").append(toStatus);
        sb.append(", event=").append(event);
        sb.append(", timestamp=").append(timestamp);

        if (isFailure()) {
            sb.append(", failure=true");
            sb.append(", cause=").append(failureCause.getClass().getSimpleName());
            sb.append(", message='").append(failureMessage).append('\'');
        }

        if (!contextData.isEmpty()) {
            sb.append(", contextData=").append(contextData.keySet());
        }

        sb.append('}');
        return sb.toString();
    }
}
