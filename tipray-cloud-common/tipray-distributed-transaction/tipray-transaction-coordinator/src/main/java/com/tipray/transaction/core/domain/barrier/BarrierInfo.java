package com.tipray.transaction.core.domain.barrier;

import com.tipray.transaction.core.enums.TransactionMode;

/**
 * 屏障信息
 * 封装屏障检查所需的所有信息
 *
 * <AUTHOR>
 * @version 2.0
 * @since 2025-01-23
 */
public class BarrierInfo {

    /**
     * 全局事务ID
     */
    private String gid;

    /**
     * 分支事务ID
     */
    private Long branchId;

    /**
     * 操作类型
     * AT模式：try/confirm/cancel
     * Saga模式：forward/compensate
     * TCC模式：try/confirm/cancel
     */
    private String operation;

    /**
     * 事务模式
     */
    private TransactionMode mode;

    /**
     * 请求时间戳
     */
    private long timestamp;

    /**
     * 步骤名称（Saga模式使用）
     */
    private String stepName;

    /**
     * 默认构造函数
     */
    public BarrierInfo() {
    }

    /**
     * 构造函数
     */
    public BarrierInfo(String gid, Long branchId, String operation, TransactionMode mode) {
        this.gid = gid;
        this.branchId = branchId;
        this.operation = operation;
        this.mode = mode;
        this.timestamp = System.currentTimeMillis();
    }

    /**
     * 构造函数（包含步骤名称）
     */
    public BarrierInfo(String gid, Long branchId, String operation, TransactionMode mode, String stepName) {
        this.gid = gid;
        this.branchId = branchId;
        this.operation = operation;
        this.mode = mode;
        this.stepName = stepName;
        this.timestamp = System.currentTimeMillis();
    }

    /**
     * 创建Builder
     */
    public static Builder builder() {
        return new Builder();
    }

    /**
     * 构建屏障键
     * 格式：gid:branchId:operation
     */
    public String buildBarrierKey() {
        return gid + ":" + branchId + ":" + operation;
    }

    /**
     * 构建屏障键（包含步骤名称）
     * 格式：gid:branchId:stepName:operation
     */
    public String buildBarrierKeyWithStep() {
        if (stepName != null && !stepName.trim().isEmpty()) {
            return gid + ":" + branchId + ":" + stepName + ":" + operation;
        }
        return buildBarrierKey();
    }

    /**
     * 判断是否为AT模式
     */
    public boolean isAtMode() {
        return mode != null && mode.isAt();
    }

    /**
     * 判断是否为Saga模式
     */
    public boolean isSagaMode() {
        return mode != null && mode.isSaga();
    }

    /**
     * 判断是否为TCC模式
     */
    public boolean isTccMode() {
        return mode != null && mode.isTcc();
    }

    /**
     * 判断是否为try操作
     */
    public boolean isTryOperation() {
        return "try".equalsIgnoreCase(operation);
    }

    /**
     * 判断是否为confirm操作
     */
    public boolean isConfirmOperation() {
        return "confirm".equalsIgnoreCase(operation);
    }

    /**
     * 判断是否为cancel操作
     */
    public boolean isCancelOperation() {
        return "cancel".equalsIgnoreCase(operation);
    }

    /**
     * 判断是否为forward操作（Saga模式）
     */
    public boolean isForwardOperation() {
        return "forward".equalsIgnoreCase(operation);
    }

    /**
     * 判断是否为compensate操作（Saga模式）
     */
    public boolean isCompensateOperation() {
        return "compensate".equalsIgnoreCase(operation);
    }

    /**
     * 获取对应的try操作屏障信息
     */
    public BarrierInfo getTryBarrierInfo() {
        return new BarrierInfo(gid, branchId, "try", mode, stepName);
    }

    /**
     * 获取对应的forward操作屏障信息（Saga模式）
     */
    public BarrierInfo getForwardBarrierInfo() {
        return new BarrierInfo(gid, branchId, "forward", mode, stepName);
    }

    // Getters and Setters
    public String getGid() {
        return gid;
    }

    public void setGid(String gid) {
        this.gid = gid;
    }

    public Long getBranchId() {
        return branchId;
    }

    public void setBranchId(Long branchId) {
        this.branchId = branchId;
    }

    public String getOperation() {
        return operation;
    }

    public void setOperation(String operation) {
        this.operation = operation;
    }

    public TransactionMode getMode() {
        return mode;
    }

    public void setMode(TransactionMode mode) {
        this.mode = mode;
    }

    public long getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(long timestamp) {
        this.timestamp = timestamp;
    }

    public String getStepName() {
        return stepName;
    }

    public void setStepName(String stepName) {
        this.stepName = stepName;
    }

    @Override
    public String toString() {
        return String.format("BarrierInfo{gid='%s', branchId='%s', operation='%s', mode=%s, stepName='%s', timestamp=%d}",
                gid, branchId, operation, mode, stepName, timestamp);
    }

    /**
     * Builder类
     */
    public static class Builder {
        private String gid;
        private Long branchId;
        private String operation;
        private TransactionMode mode;
        private String stepName;
        private long timestamp = System.currentTimeMillis();

        public Builder gid(String gid) {
            this.gid = gid;
            return this;
        }

        public Builder branchId(Long branchId) {
            this.branchId = branchId;
            return this;
        }

        public Builder operation(String operation) {
            this.operation = operation;
            return this;
        }

        public Builder mode(TransactionMode mode) {
            this.mode = mode;
            return this;
        }

        public Builder stepName(String stepName) {
            this.stepName = stepName;
            return this;
        }

        public Builder timestamp(long timestamp) {
            this.timestamp = timestamp;
            return this;
        }

        public BarrierInfo build() {
            BarrierInfo barrierInfo = new BarrierInfo();
            barrierInfo.gid = this.gid;
            barrierInfo.branchId = this.branchId;
            barrierInfo.operation = this.operation;
            barrierInfo.mode = this.mode;
            barrierInfo.stepName = this.stepName;
            barrierInfo.timestamp = this.timestamp;
            return barrierInfo;
        }
    }
}
