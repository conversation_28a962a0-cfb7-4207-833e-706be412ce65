package com.tipray.transaction.core.infrastructure.persistence.impl;

import cn.hutool.cache.Cache;
import cn.hutool.cache.CacheUtil;
import com.tipray.transaction.core.domain.execution.BranchTransactionExecutionRecordDO;
import com.tipray.transaction.core.persistence.BranchTransactionExecutionHistoryStorage;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;
import java.util.stream.Collectors;

/**
 * 默认分支事务执行历史存储实现
 * 基于Hutool缓存的内存实现，支持过期时间防止内存无限膨胀
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-01-25
 */
public class DefaultBranchTransactionExecutionHistoryStorage implements BranchTransactionExecutionHistoryStorage {

    private static final Logger log = LoggerFactory.getLogger(DefaultBranchTransactionExecutionHistoryStorage.class);

    // 使用Hutool的LRU缓存，支持过期时间
    // 默认容量5000，过期时间2小时
    private final Cache<Long, BranchTransactionExecutionRecordDO> recordCache = CacheUtil.newTimedCache(2 * 60 * 60 * 1000L);

    // 分支事务ID索引 - 分支事务ID -> 执行记录ID列表
    private final ConcurrentHashMap<Long, List<Long>> branchIdIndex = new ConcurrentHashMap<>();

    // 全局事务ID索引 - 全局事务ID -> 执行记录ID列表
    private final ConcurrentHashMap<String, List<Long>> transactionIdIndex = new ConcurrentHashMap<>();

    // 步骤名称索引 - 步骤名称 -> 执行记录ID列表
    private final ConcurrentHashMap<String, List<Long>> stepNameIndex = new ConcurrentHashMap<>();

    // 执行状态索引 - 执行状态 -> 执行记录ID列表
    private final ConcurrentHashMap<String, List<Long>> statusIndex = new ConcurrentHashMap<>();

    // 目标服务索引 - 目标服务 -> 执行记录ID列表
    private final ConcurrentHashMap<String, List<Long>> targetServiceIndex = new ConcurrentHashMap<>();

    // ID生成器
    private final AtomicLong idGenerator = new AtomicLong(1);

    @Override
    public BranchTransactionExecutionRecordDO saveExecutionRecord(BranchTransactionExecutionRecordDO record) {
        if (record == null) {
            log.warn("保存分支事务执行记录失败：记录对象为空");
            return null;
        }

        try {
            // 生成ID
            if (record.getRecordId() == null) {
                record.setRecordId(idGenerator.getAndIncrement());
            }

            // 设置创建时间
            if (record.getCreateTime() == null) {
                record.setCreateTime(LocalDateTime.now());
            }
            record.setUpdateTime(LocalDateTime.now());

            // 保存到主存储
            recordCache.put(record.getRecordId(), record);

            // 更新索引
            updateIndexes(record);

            log.debug("保存分支事务执行记录成功: 事务[{}] 分支[{}] 记录ID[{}]",
                    record.getTransactionId(), record.getBranchTransactionId(), record.getRecordId());
            return record;

        } catch (Exception e) {
            log.error("保存分支事务执行记录异常: 事务[{}] 分支[{}] - {}",
                    record.getTransactionId(), record.getBranchTransactionId(), e.getMessage(), e);
            return null;
        }
    }

    @Override
    public List<BranchTransactionExecutionRecordDO> saveExecutionRecords(List<BranchTransactionExecutionRecordDO> records) {
        if (records == null || records.isEmpty()) {
            log.warn("批量保存分支事务执行记录失败：记录列表为空");
            return Collections.emptyList();
        }

        List<BranchTransactionExecutionRecordDO> savedRecords = new ArrayList<>();
        for (BranchTransactionExecutionRecordDO record : records) {
            BranchTransactionExecutionRecordDO savedRecord = saveExecutionRecord(record);
            if (savedRecord != null) {
                savedRecords.add(savedRecord);
            }
        }

        log.debug("批量保存分支事务执行记录完成: 总数[{}] 成功[{}]", records.size(), savedRecords.size());
        return savedRecords;
    }

    @Override
    public BranchTransactionExecutionRecordDO updateExecutionRecord(BranchTransactionExecutionRecordDO record) {
        if (record == null || record.getRecordId() == null) {
            log.warn("更新分支事务执行记录失败：记录对象或记录ID为空");
            return null;
        }

        try {
            BranchTransactionExecutionRecordDO existingRecord = recordCache.get(record.getRecordId());
            if (existingRecord == null) {
                log.warn("更新分支事务执行记录失败：记录不存在: {}", record.getRecordId());
                return null;
            }

            // 更新时间
            record.setUpdateTime(LocalDateTime.now());

            // 更新缓存
            recordCache.put(record.getRecordId(), record);

            // 如果索引字段发生变化，需要更新索引
            if (!Objects.equals(existingRecord.getBranchTransactionId(), record.getBranchTransactionId()) ||
                    !Objects.equals(existingRecord.getTransactionId(), record.getTransactionId()) ||
                    !Objects.equals(existingRecord.getStepName(), record.getStepName()) ||
                    !Objects.equals(existingRecord.getStatus(), record.getStatus()) ||
                    !Objects.equals(existingRecord.getTargetService(), record.getTargetService())) {

                removeFromIndexes(existingRecord);
                updateIndexes(record);
            }

            log.debug("更新分支事务执行记录成功: 记录ID[{}]", record.getRecordId());
            return record;

        } catch (Exception e) {
            log.error("更新分支事务执行记录异常: 记录ID[{}] - {}", record.getRecordId(), e.getMessage(), e);
            return null;
        }
    }

    @Override
    public BranchTransactionExecutionRecordDO findExecutionRecordById(Long recordId) {
        if (recordId == null) {
            log.warn("查询分支事务执行记录失败：记录ID为空");
            return null;
        }

        try {
            BranchTransactionExecutionRecordDO record = recordCache.get(recordId);
            if (record == null) {
                log.debug("分支事务执行记录不存在: {}", recordId);
            }
            return record;

        } catch (Exception e) {
            log.error("查询分支事务执行记录异常: 记录ID[{}] - {}", recordId, e.getMessage(), e);
            return null;
        }
    }

    @Override
    public List<BranchTransactionExecutionRecordDO> findExecutionRecordsByBranchId(Long branchTransactionId) {
        if (branchTransactionId == null) {
            log.warn("查询分支事务执行记录失败：分支事务ID为空");
            return Collections.emptyList();
        }

        try {
            List<Long> recordIds = branchIdIndex.get(branchTransactionId);
            if (recordIds == null || recordIds.isEmpty()) {
                log.debug("分支事务[{}] 没有找到执行记录", branchTransactionId);
                return Collections.emptyList();
            }

            return recordIds.stream()
                    .map(recordCache::get)
                    .filter(Objects::nonNull)
                    .sorted(Comparator.comparing(BranchTransactionExecutionRecordDO::getAttemptNumber,
                            Comparator.nullsLast(Comparator.naturalOrder())))
                    .collect(Collectors.toList());

        } catch (Exception e) {
            log.error("查询分支事务[{}] 执行记录异常: {}", branchTransactionId, e.getMessage(), e);
            return Collections.emptyList();
        }
    }

    @Override
    public BranchTransactionExecutionRecordDO findExecutionRecordByBranchIdAndAttempt(Long branchTransactionId, Integer attemptNumber) {
        if (branchTransactionId == null || attemptNumber == null) {
            log.warn("查询分支事务执行记录失败：参数为空");
            return null;
        }

        try {
            List<BranchTransactionExecutionRecordDO> records = findExecutionRecordsByBranchId(branchTransactionId);
            return records.stream()
                    .filter(r -> Objects.equals(r.getAttemptNumber(), attemptNumber))
                    .findFirst()
                    .orElse(null);

        } catch (Exception e) {
            log.error("查询分支事务[{}] 第[{}]次执行记录异常: {}", branchTransactionId, attemptNumber, e.getMessage(), e);
            return null;
        }
    }

    @Override
    public List<BranchTransactionExecutionRecordDO> findExecutionRecordsByTransactionId(String transactionId) {
        if (transactionId == null || transactionId.trim().isEmpty()) {
            log.warn("查询分支事务执行记录失败：全局事务ID为空");
            return Collections.emptyList();
        }

        try {
            List<Long> recordIds = transactionIdIndex.get(transactionId);
            if (recordIds == null || recordIds.isEmpty()) {
                log.debug("全局事务[{}] 没有找到分支执行记录", transactionId);
                return Collections.emptyList();
            }

            return recordIds.stream()
                    .map(recordCache::get)
                    .filter(Objects::nonNull)
                    .sorted(Comparator.comparing(BranchTransactionExecutionRecordDO::getBranchTransactionId)
                            .thenComparing(BranchTransactionExecutionRecordDO::getAttemptNumber,
                                    Comparator.nullsLast(Comparator.naturalOrder())))
                    .collect(Collectors.toList());

        } catch (Exception e) {
            log.error("查询全局事务[{}] 分支执行记录异常: {}", transactionId, e.getMessage(), e);
            return Collections.emptyList();
        }
    }

    @Override
    public List<BranchTransactionExecutionRecordDO> findExecutionRecordsByStepName(String stepName) {
        if (stepName == null || stepName.trim().isEmpty()) {
            log.warn("查询分支事务执行记录失败：步骤名称为空");
            return Collections.emptyList();
        }

        try {
            List<Long> recordIds = stepNameIndex.get(stepName);
            if (recordIds == null || recordIds.isEmpty()) {
                log.debug("步骤[{}] 没有找到执行记录", stepName);
                return Collections.emptyList();
            }

            return recordIds.stream()
                    .map(recordCache::get)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());

        } catch (Exception e) {
            log.error("查询步骤[{}] 执行记录异常: {}", stepName, e.getMessage(), e);
            return Collections.emptyList();
        }
    }

    @Override
    public List<BranchTransactionExecutionRecordDO> findExecutionRecordsByExecutionType(String executionType) {
        if (executionType == null || executionType.trim().isEmpty()) {
            log.warn("查询分支事务执行记录失败：执行类型为空");
            return Collections.emptyList();
        }

        try {
            return getAllRecords().stream()
                    .filter(record -> Objects.equals(record.getExecutionType(), executionType))
                    .collect(Collectors.toList());

        } catch (Exception e) {
            log.error("查询执行类型[{}] 执行记录异常: {}", executionType, e.getMessage(), e);
            return Collections.emptyList();
        }
    }

    @Override
    public List<BranchTransactionExecutionRecordDO> findExecutionRecordsByStatus(String status) {
        if (status == null || status.trim().isEmpty()) {
            log.warn("查询分支事务执行记录失败：执行状态为空");
            return Collections.emptyList();
        }

        try {
            List<Long> recordIds = statusIndex.get(status);
            if (recordIds == null || recordIds.isEmpty()) {
                log.debug("执行状态[{}] 没有找到执行记录", status);
                return Collections.emptyList();
            }

            return recordIds.stream()
                    .map(recordCache::get)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());

        } catch (Exception e) {
            log.error("查询执行状态[{}] 执行记录异常: {}", status, e.getMessage(), e);
            return Collections.emptyList();
        }
    }

    @Override
    public List<BranchTransactionExecutionRecordDO> findExecutionRecordsByTimeRange(LocalDateTime startTime, LocalDateTime endTime) {
        if (startTime == null || endTime == null) {
            log.warn("查询分支事务执行记录失败：时间范围参数为空");
            return Collections.emptyList();
        }

        try {
            return getAllRecords().stream()
                    .filter(record -> {
                        LocalDateTime createTime = record.getCreateTime();
                        return createTime != null &&
                                !createTime.isBefore(startTime) &&
                                !createTime.isAfter(endTime);
                    })
                    .collect(Collectors.toList());

        } catch (Exception e) {
            log.error("查询时间范围[{} - {}] 执行记录异常: {}", startTime, endTime, e.getMessage(), e);
            return Collections.emptyList();
        }
    }

    @Override
    public List<BranchTransactionExecutionRecordDO> findExecutionRecordsByTargetService(String targetService) {
        if (targetService == null || targetService.trim().isEmpty()) {
            log.warn("查询分支事务执行记录失败：目标服务为空");
            return Collections.emptyList();
        }

        try {
            List<Long> recordIds = targetServiceIndex.get(targetService);
            if (recordIds == null || recordIds.isEmpty()) {
                log.debug("目标服务[{}] 没有找到执行记录", targetService);
                return Collections.emptyList();
            }

            return recordIds.stream()
                    .map(recordCache::get)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());

        } catch (Exception e) {
            log.error("查询目标服务[{}] 执行记录异常: {}", targetService, e.getMessage(), e);
            return Collections.emptyList();
        }
    }

    @Override
    public List<BranchTransactionExecutionRecordDO> findFailedExecutionRecords() {
        return findExecutionRecordsByStatus("FAILED");
    }

    @Override
    public List<BranchTransactionExecutionRecordDO> findTimeoutExecutionRecords() {
        try {
            return getAllRecords().stream()
                    .filter(record -> Boolean.TRUE.equals(record.getTimeout()) || "TIMEOUT".equals(record.getStatus()))
                    .collect(Collectors.toList());

        } catch (Exception e) {
            log.error("查询超时执行记录异常: {}", e.getMessage(), e);
            return Collections.emptyList();
        }
    }

    @Override
    public List<BranchTransactionExecutionRecordDO> findRetryableExecutionRecords() {
        try {
            return getAllRecords().stream()
                    .filter(record -> "FAILED".equals(record.getStatus()) || "TIMEOUT".equals(record.getStatus()))
                    .collect(Collectors.toList());

        } catch (Exception e) {
            log.error("查询可重试执行记录异常: {}", e.getMessage(), e);
            return Collections.emptyList();
        }
    }

    @Override
    public BranchTransactionExecutionRecordDO findLatestExecutionRecordByBranchId(Long branchTransactionId) {
        if (branchTransactionId == null) {
            log.warn("查询最新分支事务执行记录失败：分支事务ID为空");
            return null;
        }

        try {
            List<BranchTransactionExecutionRecordDO> records = findExecutionRecordsByBranchId(branchTransactionId);
            return records.stream()
                    .max(Comparator.comparing(BranchTransactionExecutionRecordDO::getCreateTime,
                            Comparator.nullsLast(Comparator.naturalOrder())))
                    .orElse(null);

        } catch (Exception e) {
            log.error("查询分支事务[{}] 最新执行记录异常: {}", branchTransactionId, e.getMessage(), e);
            return null;
        }
    }

    /**
     * 获取所有记录（解决Hutool Cache没有values()方法的问题）
     */
    private List<BranchTransactionExecutionRecordDO> getAllRecords() {
        List<BranchTransactionExecutionRecordDO> allRecords = new ArrayList<>();
        for (List<Long> recordIds : branchIdIndex.values()) {
            for (Long recordId : recordIds) {
                BranchTransactionExecutionRecordDO record = recordCache.get(recordId);
                if (record != null) {
                    allRecords.add(record);
                }
            }
        }
        // 如果分支ID索引为空，尝试从事务ID索引获取
        if (allRecords.isEmpty()) {
            for (List<Long> recordIds : transactionIdIndex.values()) {
                for (Long recordId : recordIds) {
                    BranchTransactionExecutionRecordDO record = recordCache.get(recordId);
                    if (record != null) {
                        allRecords.add(record);
                    }
                }
            }
        }
        return allRecords;
    }

    /**
     * 更新索引
     */
    private void updateIndexes(BranchTransactionExecutionRecordDO record) {
        Long recordId = record.getRecordId();

        // 更新分支事务ID索引
        if (record.getBranchTransactionId() != null) {
            branchIdIndex.computeIfAbsent(record.getBranchTransactionId(), k -> new ArrayList<>()).add(recordId);
        }

        // 更新全局事务ID索引
        if (record.getTransactionId() != null) {
            transactionIdIndex.computeIfAbsent(record.getTransactionId(), k -> new ArrayList<>()).add(recordId);
        }

        // 更新步骤名称索引
        if (record.getStepName() != null) {
            stepNameIndex.computeIfAbsent(record.getStepName(), k -> new ArrayList<>()).add(recordId);
        }

        // 更新执行状态索引
        if (record.getStatus() != null) {
            statusIndex.computeIfAbsent(record.getStatus(), k -> new ArrayList<>()).add(recordId);
        }

        // 更新目标服务索引
        if (record.getTargetService() != null) {
            targetServiceIndex.computeIfAbsent(record.getTargetService(), k -> new ArrayList<>()).add(recordId);
        }
    }

    /**
     * 从索引中移除记录
     */
    private void removeFromIndexes(BranchTransactionExecutionRecordDO record) {
        Long recordId = record.getRecordId();

        // 从分支事务ID索引中移除
        if (record.getBranchTransactionId() != null) {
            List<Long> recordIds = branchIdIndex.get(record.getBranchTransactionId());
            if (recordIds != null) {
                recordIds.remove(recordId);
                if (recordIds.isEmpty()) {
                    branchIdIndex.remove(record.getBranchTransactionId());
                }
            }
        }

        // 从全局事务ID索引中移除
        if (record.getTransactionId() != null) {
            List<Long> recordIds = transactionIdIndex.get(record.getTransactionId());
            if (recordIds != null) {
                recordIds.remove(recordId);
                if (recordIds.isEmpty()) {
                    transactionIdIndex.remove(record.getTransactionId());
                }
            }
        }

        // 从步骤名称索引中移除
        if (record.getStepName() != null) {
            List<Long> recordIds = stepNameIndex.get(record.getStepName());
            if (recordIds != null) {
                recordIds.remove(recordId);
                if (recordIds.isEmpty()) {
                    stepNameIndex.remove(record.getStepName());
                }
            }
        }

        // 从执行状态索引中移除
        if (record.getStatus() != null) {
            List<Long> recordIds = statusIndex.get(record.getStatus());
            if (recordIds != null) {
                recordIds.remove(recordId);
                if (recordIds.isEmpty()) {
                    statusIndex.remove(record.getStatus());
                }
            }
        }

        // 从目标服务索引中移除
        if (record.getTargetService() != null) {
            List<Long> recordIds = targetServiceIndex.get(record.getTargetService());
            if (recordIds != null) {
                recordIds.remove(recordId);
                if (recordIds.isEmpty()) {
                    targetServiceIndex.remove(record.getTargetService());
                }
            }
        }
    }

    @Override
    public int countExecutionRecordsByBranchId(Long branchTransactionId) {
        if (branchTransactionId == null) {
            log.warn("统计分支事务执行次数失败：分支事务ID为空");
            return 0;
        }

        try {
            List<Long> recordIds = branchIdIndex.get(branchTransactionId);
            return recordIds != null ? recordIds.size() : 0;

        } catch (Exception e) {
            log.error("统计分支事务[{}] 执行次数异常: {}", branchTransactionId, e.getMessage(), e);
            return 0;
        }
    }

    @Override
    public int countRetryExecutionRecordsByBranchId(Long branchTransactionId) {
        if (branchTransactionId == null) {
            log.warn("统计分支事务重试次数失败：分支事务ID为空");
            return 0;
        }

        try {
            List<BranchTransactionExecutionRecordDO> records = findExecutionRecordsByBranchId(branchTransactionId);
            return (int) records.stream()
                    .filter(record -> "RETRY".equals(record.getExecutionType()))
                    .count();

        } catch (Exception e) {
            log.error("统计分支事务[{}] 重试次数异常: {}", branchTransactionId, e.getMessage(), e);
            return 0;
        }
    }

    @Override
    public int countExecutionRecordsByTransactionId(String transactionId) {
        if (transactionId == null || transactionId.trim().isEmpty()) {
            log.warn("统计全局事务分支执行记录数失败：全局事务ID为空");
            return 0;
        }

        try {
            List<Long> recordIds = transactionIdIndex.get(transactionId);
            return recordIds != null ? recordIds.size() : 0;

        } catch (Exception e) {
            log.error("统计全局事务[{}] 分支执行记录数异常: {}", transactionId, e.getMessage(), e);
            return 0;
        }
    }

    @Override
    public int countExecutionRecordsByStatus(String status) {
        if (status == null || status.trim().isEmpty()) {
            log.warn("统计执行记录数失败：执行状态为空");
            return 0;
        }

        try {
            List<Long> recordIds = statusIndex.get(status);
            return recordIds != null ? recordIds.size() : 0;

        } catch (Exception e) {
            log.error("统计执行状态[{}] 记录数异常: {}", status, e.getMessage(), e);
            return 0;
        }
    }

    @Override
    public int countExecutionRecordsByTimeRange(LocalDateTime startTime, LocalDateTime endTime) {
        if (startTime == null || endTime == null) {
            log.warn("统计执行记录数失败：时间范围参数为空");
            return 0;
        }

        try {
            return (int) getAllRecords().stream()
                    .filter(record -> {
                        LocalDateTime createTime = record.getCreateTime();
                        return createTime != null &&
                                !createTime.isBefore(startTime) &&
                                !createTime.isAfter(endTime);
                    })
                    .count();

        } catch (Exception e) {
            log.error("统计时间范围[{} - {}] 执行记录数异常: {}", startTime, endTime, e.getMessage(), e);
            return 0;
        }
    }

    @Override
    public int deleteExecutionRecordsByBranchId(Long branchTransactionId) {
        if (branchTransactionId == null) {
            log.warn("删除分支事务执行记录失败：分支事务ID为空");
            return 0;
        }

        try {
            List<Long> recordIds = branchIdIndex.remove(branchTransactionId);
            if (recordIds == null || recordIds.isEmpty()) {
                log.debug("分支事务[{}] 没有找到执行记录", branchTransactionId);
                return 0;
            }

            int deleteCount = 0;
            for (Long recordId : recordIds) {
                BranchTransactionExecutionRecordDO record = recordCache.get(recordId);
                if (record != null) {
                    recordCache.remove(recordId);
                    removeFromIndexes(record);
                    deleteCount++;
                }
            }

            log.info("删除分支事务[{}] 执行记录完成，删除记录数: {}", branchTransactionId, deleteCount);
            return deleteCount;

        } catch (Exception e) {
            log.error("删除分支事务[{}] 执行记录异常: {}", branchTransactionId, e.getMessage(), e);
            return 0;
        }
    }

    @Override
    public int deleteExecutionRecordsByTransactionId(String transactionId) {
        if (transactionId == null || transactionId.trim().isEmpty()) {
            log.warn("删除全局事务分支执行记录失败：全局事务ID为空");
            return 0;
        }

        try {
            List<Long> recordIds = transactionIdIndex.remove(transactionId);
            if (recordIds == null || recordIds.isEmpty()) {
                log.debug("全局事务[{}] 没有找到分支执行记录", transactionId);
                return 0;
            }

            int deleteCount = 0;
            for (Long recordId : recordIds) {
                BranchTransactionExecutionRecordDO record = recordCache.get(recordId);
                if (record != null) {
                    recordCache.remove(recordId);
                    removeFromIndexes(record);
                    deleteCount++;
                }
            }

            log.info("删除全局事务[{}] 分支执行记录完成，删除记录数: {}", transactionId, deleteCount);
            return deleteCount;

        } catch (Exception e) {
            log.error("删除全局事务[{}] 分支执行记录异常: {}", transactionId, e.getMessage(), e);
            return 0;
        }
    }

    @Override
    public int deleteExecutionRecordsBefore(LocalDateTime beforeTime) {
        if (beforeTime == null) {
            log.warn("删除执行记录失败：时间参数为空");
            return 0;
        }

        try {
            List<BranchTransactionExecutionRecordDO> recordsToDelete = getAllRecords().stream()
                    .filter(record -> {
                        LocalDateTime createTime = record.getCreateTime();
                        return createTime != null && createTime.isBefore(beforeTime);
                    })
                    .collect(Collectors.toList());

            int deleteCount = 0;
            for (BranchTransactionExecutionRecordDO record : recordsToDelete) {
                recordCache.remove(record.getRecordId());
                removeFromIndexes(record);
                deleteCount++;
            }

            log.info("删除时间[{}]之前的执行记录完成，删除记录数: {}", beforeTime, deleteCount);
            return deleteCount;

        } catch (Exception e) {
            log.error("删除时间[{}]之前的执行记录异常: {}", beforeTime, e.getMessage(), e);
            return 0;
        }
    }

    @Override
    public List<BranchTransactionExecutionRecordDO> findExecutionRecordsWithPagination(int offset, int limit) {
        if (offset < 0 || limit <= 0) {
            log.warn("分页查询分支事务执行记录失败：分页参数无效");
            return Collections.emptyList();
        }

        try {
            return getAllRecords().stream()
                    .sorted(Comparator.comparing(BranchTransactionExecutionRecordDO::getCreateTime,
                            Comparator.nullsLast(Comparator.reverseOrder())))
                    .skip(offset)
                    .limit(limit)
                    .collect(Collectors.toList());

        } catch (Exception e) {
            log.error("分页查询分支事务执行记录异常: offset={}, limit={} - {}", offset, limit, e.getMessage(), e);
            return Collections.emptyList();
        }
    }

    @Override
    public List<BranchTransactionExecutionRecordDO> findExecutionRecordsByBranchIdWithPagination(Long branchTransactionId, int offset, int limit) {
        if (branchTransactionId == null || offset < 0 || limit <= 0) {
            log.warn("分页查询分支事务执行记录失败：参数无效");
            return Collections.emptyList();
        }

        try {
            List<BranchTransactionExecutionRecordDO> records = findExecutionRecordsByBranchId(branchTransactionId);
            return records.stream()
                    .sorted(Comparator.comparing(BranchTransactionExecutionRecordDO::getCreateTime,
                            Comparator.nullsLast(Comparator.reverseOrder())))
                    .skip(offset)
                    .limit(limit)
                    .collect(Collectors.toList());

        } catch (Exception e) {
            log.error("分页查询分支事务[{}] 执行记录异常: offset={}, limit={} - {}", branchTransactionId, offset, limit, e.getMessage(), e);
            return Collections.emptyList();
        }
    }

    @Override
    public List<BranchTransactionExecutionRecordDO> findExecutionRecordsByTransactionIdWithPagination(String transactionId, int offset, int limit) {
        if (transactionId == null || transactionId.trim().isEmpty() || offset < 0 || limit <= 0) {
            log.warn("分页查询全局事务分支执行记录失败：参数无效");
            return Collections.emptyList();
        }

        try {
            List<BranchTransactionExecutionRecordDO> records = findExecutionRecordsByTransactionId(transactionId);
            return records.stream()
                    .sorted(Comparator.comparing(BranchTransactionExecutionRecordDO::getCreateTime,
                            Comparator.nullsLast(Comparator.reverseOrder())))
                    .skip(offset)
                    .limit(limit)
                    .collect(Collectors.toList());

        } catch (Exception e) {
            log.error("分页查询全局事务[{}] 分支执行记录异常: offset={}, limit={} - {}", transactionId, offset, limit, e.getMessage(), e);
            return Collections.emptyList();
        }
    }

    @Override
    public Object getStatistics() {
        try {
            int totalRecords = recordCache.size();
            int totalBranchTransactions = branchIdIndex.size();
            int totalGlobalTransactions = transactionIdIndex.size();
            int totalSteps = stepNameIndex.size();
            int totalTargetServices = targetServiceIndex.size();

            // 统计各状态的记录数
            Map<String, Integer> statusCounts = new HashMap<>();
            for (Map.Entry<String, List<Long>> entry : statusIndex.entrySet()) {
                statusCounts.put(entry.getKey(), entry.getValue().size());
            }

            log.debug("获取分支事务执行历史存储统计信息: 总记录数={}, 分支事务数={}, 全局事务数={}, 步骤数={}, 目标服务数={}",
                    totalRecords, totalBranchTransactions, totalGlobalTransactions, totalSteps, totalTargetServices);

            return new BranchExecutionHistoryStorageStatistics(totalRecords, totalBranchTransactions,
                    totalGlobalTransactions, totalSteps, totalTargetServices, statusCounts);

        } catch (Exception e) {
            log.error("获取分支事务执行历史存储统计信息异常: {}", e.getMessage(), e);
            return new BranchExecutionHistoryStorageStatistics(0, 0, 0, 0, 0, Collections.emptyMap());
        }
    }

    /**
     * 清空所有数据（用于测试）
     */
    public void clear() {
        recordCache.clear();
        branchIdIndex.clear();
        transactionIdIndex.clear();
        stepNameIndex.clear();
        statusIndex.clear();
        targetServiceIndex.clear();
        idGenerator.set(1);
        log.info("清空所有分支事务执行历史数据");
    }

    /**
     * 获取当前存储的记录数量（用于测试）
     */
    public int size() {
        return recordCache.size();
    }

    /**
     * 分支事务执行历史存储统计信息
     */
    public static class BranchExecutionHistoryStorageStatistics {
        private final int totalRecords;
        private final int totalBranchTransactions;
        private final int totalGlobalTransactions;
        private final int totalSteps;
        private final int totalTargetServices;
        private final Map<String, Integer> statusCounts;

        public BranchExecutionHistoryStorageStatistics(int totalRecords, int totalBranchTransactions,
                                                       int totalGlobalTransactions, int totalSteps,
                                                       int totalTargetServices, Map<String, Integer> statusCounts) {
            this.totalRecords = totalRecords;
            this.totalBranchTransactions = totalBranchTransactions;
            this.totalGlobalTransactions = totalGlobalTransactions;
            this.totalSteps = totalSteps;
            this.totalTargetServices = totalTargetServices;
            this.statusCounts = statusCounts != null ? statusCounts : Collections.emptyMap();
        }

        public int getTotalRecords() {
            return totalRecords;
        }

        public int getTotalBranchTransactions() {
            return totalBranchTransactions;
        }

        public int getTotalGlobalTransactions() {
            return totalGlobalTransactions;
        }

        public int getTotalSteps() {
            return totalSteps;
        }

        public int getTotalTargetServices() {
            return totalTargetServices;
        }

        public Map<String, Integer> getStatusCounts() {
            return statusCounts;
        }

        @Override
        public String toString() {
            return "BranchExecutionHistoryStorageStatistics{" +
                    "totalRecords=" + totalRecords +
                    ", totalBranchTransactions=" + totalBranchTransactions +
                    ", totalGlobalTransactions=" + totalGlobalTransactions +
                    ", totalSteps=" + totalSteps +
                    ", totalTargetServices=" + totalTargetServices +
                    ", statusCounts=" + statusCounts +
                    '}';
        }
    }
}
