package com.tipray.transaction.core.domain.statemachine.main;

import com.tipray.transaction.core.enums.TransactionStatus;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * 事务状态统计信息
 *
 * <AUTHOR>
 * @version 2.0
 * @since 2025-01-23
 */
public class TransactionStatusStatistics {

    private final int totalTransactions;
    private final Map<TransactionStatus, Integer> statusDistribution;
    private final LocalDateTime statisticsTime;

    private TransactionStatusStatistics(Builder builder) {
        this.totalTransactions = builder.totalTransactions;
        this.statusDistribution = new HashMap<>(builder.statusDistribution);
        this.statisticsTime = LocalDateTime.now();
    }

    public static Builder builder() {
        return new Builder();
    }

    /**
     * 获取指定状态的事务数量
     */
    public int getCountByStatus(TransactionStatus status) {
        return statusDistribution.getOrDefault(status, 0);
    }

    /**
     * 获取成功事务数量
     */
    public int getSuccessCount() {
        return statusDistribution.entrySet().stream()
                .filter(entry -> entry.getKey().isSuccess())
                .mapToInt(Map.Entry::getValue)
                .sum();
    }

    /**
     * 获取失败事务数量
     */
    public int getFailureCount() {
        return statusDistribution.entrySet().stream()
                .filter(entry -> entry.getKey().isFailure())
                .mapToInt(Map.Entry::getValue)
                .sum();
    }

    /**
     * 获取进行中事务数量
     */
    public int getActiveCount() {
        return statusDistribution.entrySet().stream()
                .filter(entry -> !entry.getKey().isTerminal())
                .mapToInt(Map.Entry::getValue)
                .sum();
    }

    /**
     * 获取需要人工干预的事务数量
     */
    public int getInterventionRequiredCount() {
        return statusDistribution.entrySet().stream()
                .filter(entry -> entry.getKey().needsIntervention())
                .mapToInt(Map.Entry::getValue)
                .sum();
    }

    /**
     * 计算成功率
     */
    public double getSuccessRate() {
        if (totalTransactions == 0) {
            return 0.0;
        }
        return (double) getSuccessCount() / totalTransactions * 100;
    }

    /**
     * 计算失败率
     */
    public double getFailureRate() {
        if (totalTransactions == 0) {
            return 0.0;
        }
        return (double) getFailureCount() / totalTransactions * 100;
    }

    /**
     * 转换为监控指标
     */
    public Map<String, Object> toMetrics() {
        Map<String, Object> metrics = new HashMap<>();

        metrics.put("total_transactions", totalTransactions);
        metrics.put("success_count", getSuccessCount());
        metrics.put("failure_count", getFailureCount());
        metrics.put("active_count", getActiveCount());
        metrics.put("intervention_required_count", getInterventionRequiredCount());
        metrics.put("success_rate", getSuccessRate());
        metrics.put("failure_rate", getFailureRate());

        // 各状态的详细统计
        for (Map.Entry<TransactionStatus, Integer> entry : statusDistribution.entrySet()) {
            metrics.put("status_" + entry.getKey().name().toLowerCase(), entry.getValue());
        }

        return metrics;
    }

    /**
     * 转换为可读的统计报告
     */
    public String toReport() {
        StringBuilder report = new StringBuilder();
        report.append("=== 事务状态统计报告 ===\n");
        report.append("统计时间: ").append(statisticsTime).append("\n");
        report.append("总事务数: ").append(totalTransactions).append("\n");
        report.append("成功事务: ").append(getSuccessCount()).append(" (").append(String.format("%.2f", getSuccessRate())).append("%)\n");
        report.append("失败事务: ").append(getFailureCount()).append(" (").append(String.format("%.2f", getFailureRate())).append("%)\n");
        report.append("进行中事务: ").append(getActiveCount()).append("\n");
        report.append("需要干预事务: ").append(getInterventionRequiredCount()).append("\n");

        report.append("\n=== 状态分布详情 ===\n");
        statusDistribution.entrySet().stream()
                .sorted(Map.Entry.<TransactionStatus, Integer>comparingByValue().reversed())
                .forEach(entry -> {
                    TransactionStatus status = entry.getKey();
                    int count = entry.getValue();
                    double percentage = totalTransactions > 0 ? (double) count / totalTransactions * 100 : 0;

                    report.append(String.format("%-25s: %6d (%5.2f%%)\n",
                            status.getDescription(), count, percentage));
                });

        return report.toString();
    }

    // getters
    public int getTotalTransactions() {
        return totalTransactions;
    }

    public Map<TransactionStatus, Integer> getStatusDistribution() {
        return new HashMap<>(statusDistribution);
    }

    public LocalDateTime getStatisticsTime() {
        return statisticsTime;
    }

    @Override
    public String toString() {
        return toReport();
    }

    public static class Builder {
        private int totalTransactions;
        private Map<TransactionStatus, Integer> statusDistribution = new HashMap<>();

        public Builder totalTransactions(int totalTransactions) {
            this.totalTransactions = totalTransactions;
            return this;
        }

        public Builder statusDistribution(Map<TransactionStatus, Integer> statusDistribution) {
            this.statusDistribution = new HashMap<>(statusDistribution);
            return this;
        }

        public Builder addStatusCount(TransactionStatus status, int count) {
            this.statusDistribution.put(status, count);
            return this;
        }

        public TransactionStatusStatistics build() {
            return new TransactionStatusStatistics(this);
        }
    }
}
