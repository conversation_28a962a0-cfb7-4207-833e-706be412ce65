package com.tipray.transaction.core.api.monitor;

import com.tipray.transaction.core.application.coordinator.BranchTransactionCoordinator;
import com.tipray.transaction.core.application.extension.main.TransactionExtensionManager;
import com.tipray.transaction.core.context.TransactionContextHolder;
import com.tipray.transaction.core.domain.statemachine.main.TransactionStateMachine;
import com.tipray.transaction.core.exception.DistributedTransactionSystemException;
import com.tipray.transaction.core.infrastructure.barrier.TransactionBarrier;
import com.tipray.transaction.core.infrastructure.persistence.AsyncPersistenceManager;

import java.util.HashMap;
import java.util.Map;

/**
 * 分布式事务监控API
 * 提供事务监控和统计功能
 *
 * <AUTHOR>
 * @version 2.0
 * @since 2025-01-23
 */
public class DistributedTransactionMonitor {

    private final TransactionStateMachine stateMachine;
    private final BranchTransactionCoordinator branchCoordinator;
    private final TransactionBarrier transactionBarrier;
    private final AsyncPersistenceManager persistenceManager;
    private final TransactionExtensionManager extensionManager;

    public DistributedTransactionMonitor(TransactionStateMachine stateMachine,
                                         BranchTransactionCoordinator branchCoordinator,
                                         TransactionBarrier transactionBarrier,
                                         AsyncPersistenceManager persistenceManager,
                                         TransactionExtensionManager extensionManager) {
        this.stateMachine = stateMachine;
        this.branchCoordinator = branchCoordinator;
        this.transactionBarrier = transactionBarrier;
        this.persistenceManager = persistenceManager;
        this.extensionManager = extensionManager;
    }

    /**
     * 获取完整的监控报告
     */
    public TransactionMonitorReport getMonitorReport() {
        return TransactionMonitorReport.builder()
                .statusStatistics(stateMachine.getStatistics())
                .branchTransactionStatistics(branchCoordinator.getStatistics())
                .persistenceStatistics(persistenceManager.getStatistics())
                .extensionStatistics(extensionManager.getExtensionStatistics())
                .contextStatistics(TransactionContextHolder.getContextStatistics())
                .build();
    }

    /**
     * 获取状态机统计信息
     */
    public Object getStatusStatistics() {
        return stateMachine.getStatistics();
    }

    /**
     * 获取分支事务统计信息
     */
    public Object getBranchTransactionStatistics() {
        return branchCoordinator.getStatistics();
    }


    /**
     * 获取持久化统计信息
     */
    public Object getPersistenceStatistics() {
        return persistenceManager.getStatistics();
    }

    /**
     * 获取扩展统计信息
     */
    public Object getExtensionStatistics() {
        return extensionManager.getExtensionStatistics();
    }

    /**
     * 获取上下文统计信息
     */
    public Object getContextStatistics() {
        return TransactionContextHolder.getContextStatistics();
    }

    /**
     * 获取系统健康状态
     */
    public HealthStatus getHealthStatus() {
        try {
            // 检查各组件状态
            boolean statusMachineHealthy = checkStatusMachineHealth();
            boolean persistenceHealthy = checkPersistenceHealth();
            boolean coordinatorHealthy = checkCoordinatorHealth();

            boolean overall = statusMachineHealthy && persistenceHealthy && coordinatorHealthy;

            return HealthStatus.builder()
                    .overall(overall ? "HEALTHY" : "UNHEALTHY")
                    .statusMachine(statusMachineHealthy ? "HEALTHY" : "UNHEALTHY")
                    .persistence(persistenceHealthy ? "HEALTHY" : "UNHEALTHY")
                    .coordinator(coordinatorHealthy ? "HEALTHY" : "UNHEALTHY")
                    .timestamp(System.currentTimeMillis())
                    .build();

        } catch (Exception e) {
            return HealthStatus.builder()
                    .overall("ERROR")
                    .error(e.getMessage())
                    .timestamp(System.currentTimeMillis())
                    .build();
        }
    }

    /**
     * 获取性能指标
     */
    public Map<String, Object> getPerformanceMetrics() {
        Map<String, Object> metrics = new HashMap<>();

        // 状态机指标
        Object statusStats = stateMachine.getStatistics();
        metrics.put("status_machine", statusStats);

        // 持久化指标
        Object persistenceStats = persistenceManager.getStatistics();
        metrics.put("persistence", persistenceStats);

        // 协调器指标
        metrics.put("branch_coordinator", branchCoordinator.getStatistics());

        // 屏障指标
//        metrics.put("barrier", transactionBarrier());

        // 扩展指标
        metrics.put("extension", extensionManager.getExtensionStatistics());

        // 上下文指标
        metrics.put("context", TransactionContextHolder.getContextStatistics());

        return metrics;
    }

    /**
     * 执行系统清理
     */
    public void performCleanup() {
        try {
            // 清理状态机缓存
            stateMachine.cleanupFinishedTransactions();

            // 清理屏障缓存
//            transactionBarrier.cleanupBarrier();

            // 清理上下文
            TransactionContextHolder.clearAllContexts();

        } catch (Exception e) {
            throw new DistributedTransactionSystemException(
                    "系统清理失败: " + e.getMessage(),
                    e,
                    DistributedTransactionSystemException.SystemExceptionType.UNKNOWN,
                    DistributedTransactionSystemException.SeverityLevel.HIGH,
                    "SystemCleanup"
            );
        }
    }

    // ==================== 私有方法 ====================

    /**
     * 检查状态机健康状态
     */
    private boolean checkStatusMachineHealth() {
        try {
            Object stats = stateMachine.getStatistics();
            return stats != null;
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 检查持久化健康状态
     */
    private boolean checkPersistenceHealth() {
        try {
            Object stats = persistenceManager.getStatistics();
            return stats != null;
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 检查协调器健康状态
     */
    private boolean checkCoordinatorHealth() {
        try {
            Object branchStats = branchCoordinator.getStatistics();
            return branchStats != null;
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 监控报告
     */
    public static class TransactionMonitorReport {
        private final Object statusStatistics;
        private final Object localTransactionStatistics;
        private final Object branchTransactionStatistics;
        private final Object barrierStatistics;
        private final Object persistenceStatistics;
        private final Object extensionStatistics;
        private final Object contextStatistics;
        private final long timestamp;

        private TransactionMonitorReport(Builder builder) {
            this.statusStatistics = builder.statusStatistics;
            this.localTransactionStatistics = builder.localTransactionStatistics;
            this.branchTransactionStatistics = builder.branchTransactionStatistics;
            this.barrierStatistics = builder.barrierStatistics;
            this.persistenceStatistics = builder.persistenceStatistics;
            this.extensionStatistics = builder.extensionStatistics;
            this.contextStatistics = builder.contextStatistics;
            this.timestamp = System.currentTimeMillis();
        }

        public static Builder builder() {
            return new Builder();
        }

        // getters
        public Object getStatusStatistics() {
            return statusStatistics;
        }

        public Object getLocalTransactionStatistics() {
            return localTransactionStatistics;
        }

        public Object getBranchTransactionStatistics() {
            return branchTransactionStatistics;
        }

        public Object getBarrierStatistics() {
            return barrierStatistics;
        }

        public Object getPersistenceStatistics() {
            return persistenceStatistics;
        }

        public Object getExtensionStatistics() {
            return extensionStatistics;
        }

        public Object getContextStatistics() {
            return contextStatistics;
        }

        public long getTimestamp() {
            return timestamp;
        }

        public static class Builder {
            private Object statusStatistics;
            private Object localTransactionStatistics;
            private Object branchTransactionStatistics;
            private Object barrierStatistics;
            private Object persistenceStatistics;
            private Object extensionStatistics;
            private Object contextStatistics;

            public Builder statusStatistics(Object statusStatistics) {
                this.statusStatistics = statusStatistics;
                return this;
            }

            public Builder localTransactionStatistics(Object localTransactionStatistics) {
                this.localTransactionStatistics = localTransactionStatistics;
                return this;
            }

            public Builder branchTransactionStatistics(Object branchTransactionStatistics) {
                this.branchTransactionStatistics = branchTransactionStatistics;
                return this;
            }

            public Builder barrierStatistics(Object barrierStatistics) {
                this.barrierStatistics = barrierStatistics;
                return this;
            }

            public Builder persistenceStatistics(Object persistenceStatistics) {
                this.persistenceStatistics = persistenceStatistics;
                return this;
            }

            public Builder extensionStatistics(Object extensionStatistics) {
                this.extensionStatistics = extensionStatistics;
                return this;
            }

            public Builder contextStatistics(Object contextStatistics) {
                this.contextStatistics = contextStatistics;
                return this;
            }

            public TransactionMonitorReport build() {
                return new TransactionMonitorReport(this);
            }
        }
    }

    /**
     * 健康状态
     */
    public static class HealthStatus {
        private final String overall;
        private final String statusMachine;
        private final String persistence;
        private final String coordinator;
        private final String error;
        private final long timestamp;

        private HealthStatus(Builder builder) {
            this.overall = builder.overall;
            this.statusMachine = builder.statusMachine;
            this.persistence = builder.persistence;
            this.coordinator = builder.coordinator;
            this.error = builder.error;
            this.timestamp = builder.timestamp;
        }

        public static Builder builder() {
            return new Builder();
        }

        // getters
        public String getOverall() {
            return overall;
        }

        public String getStatusMachine() {
            return statusMachine;
        }

        public String getPersistence() {
            return persistence;
        }

        public String getCoordinator() {
            return coordinator;
        }

        public String getError() {
            return error;
        }

        public long getTimestamp() {
            return timestamp;
        }

        @Override
        public String toString() {
            return String.format("HealthStatus{overall='%s', timestamp=%d}", overall, timestamp);
        }

        public static class Builder {
            private String overall;
            private String statusMachine;
            private String persistence;
            private String coordinator;
            private String error;
            private long timestamp;

            public Builder overall(String overall) {
                this.overall = overall;
                return this;
            }

            public Builder statusMachine(String statusMachine) {
                this.statusMachine = statusMachine;
                return this;
            }

            public Builder persistence(String persistence) {
                this.persistence = persistence;
                return this;
            }

            public Builder coordinator(String coordinator) {
                this.coordinator = coordinator;
                return this;
            }

            public Builder error(String error) {
                this.error = error;
                return this;
            }

            public Builder timestamp(long timestamp) {
                this.timestamp = timestamp;
                return this;
            }

            public HealthStatus build() {
                return new HealthStatus(this);
            }
        }
    }
}
