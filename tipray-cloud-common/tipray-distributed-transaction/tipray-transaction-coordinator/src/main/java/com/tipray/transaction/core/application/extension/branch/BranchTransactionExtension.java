package com.tipray.transaction.core.application.extension.branch;

/**
 * 分支事务扩展接口
 * 定义分支事务生命周期中的扩展点
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-01-25
 */
public interface BranchTransactionExtension {

    /**
     * 获取扩展点
     */
    BranchTransactionExtensionPoint getExtensionPoint();

    /**
     * 获取执行顺序
     * 数值越小，优先级越高
     */
    default int getOrder() {
        return 1000;
    }

    /**
     * 是否支持当前上下文
     */
    boolean supports(BranchTransactionExtensionContext context);

    /**
     * 执行扩展逻辑
     */
    void execute(BranchTransactionExtensionPoint extensionPoint, BranchTransactionExtensionContext context);

    /**
     * 扩展名称（用于日志和调试）
     */
    default String getName() {
        return this.getClass().getSimpleName();
    }

    /**
     * 扩展描述
     */
    default String getDescription() {
        return "分支事务扩展: " + getName();
    }
}
