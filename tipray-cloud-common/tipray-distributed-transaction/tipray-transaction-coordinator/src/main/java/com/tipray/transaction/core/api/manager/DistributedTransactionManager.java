package com.tipray.transaction.core.api.manager;

import com.tipray.transaction.core.application.engine.DistributedTransactionEngine;
import com.tipray.transaction.core.domain.statemachine.main.TransactionStateMachine;
import com.tipray.transaction.core.domain.statemachine.main.TransactionStatusStatistics;
import com.tipray.transaction.core.domain.transaction.TransactionContext;
import com.tipray.transaction.core.enums.TransactionStatus;

/**
 * 分布式事务管理API
 * 提供事务管理和监控功能
 *
 * <AUTHOR>
 * @version 2.0
 * @since 2025-01-23
 */
public class DistributedTransactionManager {

    private final DistributedTransactionEngine transactionEngine;
    private final TransactionStateMachine stateMachine;

    public DistributedTransactionManager(DistributedTransactionEngine transactionEngine,
                                         TransactionStateMachine stateMachine) {
        this.transactionEngine = transactionEngine;
        this.stateMachine = stateMachine;
    }

    /**
     * 获取当前事务上下文
     */
    public TransactionContext getCurrentTransaction() {
        return transactionEngine.getCurrentContext();
    }

    /**
     * 获取事务状态
     */
    public TransactionStatus getTransactionStatus(String transactionId) {
        return transactionEngine.getTransactionStatus(transactionId);
    }

    /**
     * 暂停事务
     */
    public void pauseTransaction(String transactionId, String reason) {
        transactionEngine.pauseTransaction(transactionId, reason);
    }

    /**
     * 恢复事务
     */
    public void resumeTransaction(String transactionId) {
        transactionEngine.resumeTransaction(transactionId);
    }

    /**
     * 强制回滚事务
     */
    public void forceRollback(String transactionId, String reason, String operator) {
        transactionEngine.forceRollback(transactionId, reason, operator);
    }

    /**
     * 强制设置事务状态（人工干预）
     */
    public boolean forceSetStatus(String transactionId, TransactionStatus targetStatus,
                                  String reason, String operator) {
        return stateMachine.forceSetStatus(transactionId, targetStatus, reason, operator);
    }

    /**
     * 获取事务统计信息
     */
    public TransactionStatusStatistics getTransactionStatistics() {
        return stateMachine.getStatistics();
    }

    /**
     * 清理已完成的事务
     */
    public void cleanupFinishedTransactions() {
        stateMachine.cleanupFinishedTransactions();
    }

    /**
     * 判断当前是否存在事务
     */
    public boolean hasCurrentTransaction() {
        return getCurrentTransaction() != null;
    }

    /**
     * 获取当前事务ID
     */
    public String getCurrentTransactionId() {
        TransactionContext context = getCurrentTransaction();
        return context != null ? context.getTransactionId() : null;
    }

    /**
     * 判断当前事务是否为根事务
     */
    public boolean isRootTransaction() {
        TransactionContext context = getCurrentTransaction();
        return context != null && context.isRoot();
    }

    /**
     * 判断当前事务是否为嵌套事务
     */
    public boolean isNestedTransaction() {
        TransactionContext context = getCurrentTransaction();
        return context != null && !context.isRoot();
    }

    /**
     * 获取当前事务的嵌套层级
     */
    public int getCurrentNestingLevel() {
        TransactionContext context = getCurrentTransaction();
        return context != null ? context.getNestingLevel() : 0;
    }

    /**
     * 获取当前事务的层级路径
     */
    public String getCurrentHierarchyPath() {
        TransactionContext context = getCurrentTransaction();
        return context != null ? context.getHierarchyPath() : "";
    }

    /**
     * 判断事务是否超时
     */
    public boolean isTransactionTimeout(String transactionId) {
        TransactionContext context = getCurrentTransaction();
        if (context != null && context.getTransactionId().equals(transactionId)) {
            return context.isTimeout();
        }
        return false;
    }

    /**
     * 获取事务剩余超时时间
     */
    public long getRemainingTimeout(String transactionId) {
        TransactionContext context = getCurrentTransaction();
        if (context != null && context.getTransactionId().equals(transactionId)) {
            return context.getRemainingTimeoutMillis();
        }
        return 0;
    }

    /**
     * 判断事务是否活跃
     */
    public boolean isTransactionActive(String transactionId) {
        TransactionContext context = getCurrentTransaction();
        if (context != null && context.getTransactionId().equals(transactionId)) {
            return context.isActive();
        }
        return false;
    }
}
