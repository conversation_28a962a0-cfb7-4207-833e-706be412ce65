package com.tipray.transaction.core.infrastructure.event;

import com.tipray.transaction.core.domain.coordinator.BranchTransactionInfo;
import org.springframework.context.ApplicationEvent;

/**
 * 分支事务执行事件
 *
 * <AUTHOR>
 * @version 2.0
 * @since 2025-01-23
 */
public class BranchTransactionExecutedEvent extends ApplicationEvent {

    private final BranchTransactionInfo branchInfo;

    public BranchTransactionExecutedEvent(BranchTransactionInfo branchInfo) {
        super(branchInfo);
        this.branchInfo = branchInfo;
    }

    public BranchTransactionInfo getBranchInfo() {
        return branchInfo;
    }

    public String getTransactionId() {
        return branchInfo.getTransactionId();
    }

    public String getBranchId() {
        return branchInfo.getBranchId();
    }

    @Override
    public String toString() {
        return String.format("BranchTransactionExecutedEvent{txId='%s', branchId='%s', endpoint='%s', duration=%dms}",
                branchInfo.getTransactionId(), branchInfo.getBranchId(),
                branchInfo.getServiceEndpoint(), branchInfo.getExecutionDuration());
    }
}
