package com.tipray.transaction.core.domain.statemachine.branch;

import com.tipray.transaction.core.domain.transaction.BranchTransactionDO;
import com.tipray.transaction.core.enums.BranchTransactionEvent;
import com.tipray.transaction.core.enums.BranchTransactionStatus;

import java.util.HashMap;
import java.util.Map;

/**
 * 分支事务状态转换上下文
 * 携带分支事务状态转换的所有相关信息
 *
 * <AUTHOR>
 * @version 2.0
 * @since 2025-01-29
 */
public class BranchStateTransitionContext {

    private final String transactionId;
    private final Long branchTransactionId;
    private final BranchTransactionStatus fromStatus;
    private final BranchTransactionStatus toStatus;
    private final BranchTransactionEvent event;
    private final long timestamp;

    // 扩展数据字段
    private final BranchTransactionDO branchTransaction;     // 分支事务对象
    private final Object data;                               // 通用数据载体
    private final Exception failureCause;                    // 失败原因
    private final String failureMessage;                     // 失败消息
    private final Map<String, Object> contextData;           // 上下文数据

    /**
     * 完整构造函数
     */
    public BranchStateTransitionContext(String transactionId,
                                       Long branchTransactionId,
                                       BranchTransactionStatus fromStatus,
                                       BranchTransactionStatus toStatus,
                                       BranchTransactionEvent event,
                                       BranchTransactionDO branchTransaction,
                                       Object data,
                                       Exception failureCause,
                                       String failureMessage,
                                       Map<String, Object> contextData) {
        this.transactionId = transactionId;
        this.branchTransactionId = branchTransactionId;
        this.fromStatus = fromStatus;
        this.toStatus = toStatus;
        this.event = event;
        this.timestamp = System.currentTimeMillis();
        this.branchTransaction = branchTransaction;
        this.data = data;
        this.failureCause = failureCause;
        this.failureMessage = failureMessage;
        this.contextData = contextData != null ? new HashMap<>(contextData) : new HashMap<>();
    }

    /**
     * 成功场景构造方法
     */
    public static BranchStateTransitionContext success(String transactionId,
                                                      Long branchTransactionId,
                                                      BranchTransactionStatus fromStatus,
                                                      BranchTransactionStatus toStatus,
                                                      BranchTransactionEvent event,
                                                      BranchTransactionDO branchTransaction) {
        return new BranchStateTransitionContext(transactionId, branchTransactionId,
                fromStatus, toStatus, event, branchTransaction, null, null, null, null);
    }

    /**
     * 失败场景构造方法
     */
    public static BranchStateTransitionContext failure(String transactionId,
                                                      Long branchTransactionId,
                                                      BranchTransactionStatus fromStatus,
                                                      BranchTransactionStatus toStatus,
                                                      BranchTransactionEvent event,
                                                      Exception cause,
                                                      String message) {
        return new BranchStateTransitionContext(transactionId, branchTransactionId,
                fromStatus, toStatus, event, null, null, cause, message, null);
    }

    /**
     * 失败场景构造方法（带上下文数据）
     */
    public static BranchStateTransitionContext failure(String transactionId,
                                                      Long branchTransactionId,
                                                      BranchTransactionStatus fromStatus,
                                                      BranchTransactionStatus toStatus,
                                                      BranchTransactionEvent event,
                                                      Exception cause,
                                                      String message,
                                                      Map<String, Object> contextData) {
        return new BranchStateTransitionContext(transactionId, branchTransactionId,
                fromStatus, toStatus, event, null, null, cause, message, contextData);
    }

    // ==================== 判断方法 ====================

    /**
     * 判断是否为失败场景
     */
    public boolean isFailure() {
        return failureCause != null;
    }

    /**
     * 判断是否为成功场景
     */
    public boolean isSuccess() {
        return failureCause == null;
    }

    /**
     * 判断是否为终态转换
     */
    public boolean isTerminalTransition() {
        return toStatus != null && toStatus.isTerminal();
    }

    // ==================== 上下文数据操作 ====================

    /**
     * 添加上下文数据
     */
    public void addContextData(String key, Object value) {
        this.contextData.put(key, value);
    }

    /**
     * 获取上下文数据
     */
    @SuppressWarnings("unchecked")
    public <T> T getContextData(String key) {
        return (T) this.contextData.get(key);
    }

    /**
     * 获取上下文数据（带默认值）
     */
    @SuppressWarnings("unchecked")
    public <T> T getContextData(String key, T defaultValue) {
        return (T) this.contextData.getOrDefault(key, defaultValue);
    }

    // ==================== Getter方法 ====================

    public String getTransactionId() {
        return transactionId;
    }

    public Long getBranchTransactionId() {
        return branchTransactionId;
    }

    public BranchTransactionStatus getFromStatus() {
        return fromStatus;
    }

    public BranchTransactionStatus getToStatus() {
        return toStatus;
    }

    public BranchTransactionEvent getEvent() {
        return event;
    }

    public long getTimestamp() {
        return timestamp;
    }

    public BranchTransactionDO getBranchTransaction() {
        return branchTransaction;
    }

    public Object getData() {
        return data;
    }

    public Exception getFailureCause() {
        return failureCause;
    }

    public String getFailureMessage() {
        return failureMessage;
    }

    public Map<String, Object> getContextData() {
        return new HashMap<>(contextData);
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("BranchStateTransitionContext{");
        sb.append("transactionId='").append(transactionId).append('\'');
        sb.append(", branchId=").append(branchTransactionId);
        sb.append(", transition=").append(fromStatus).append("->").append(toStatus);
        sb.append(", event=").append(event);
        sb.append(", timestamp=").append(timestamp);

        if (isFailure()) {
            sb.append(", failure=true");
            sb.append(", cause=").append(failureCause.getClass().getSimpleName());
            sb.append(", message='").append(failureMessage).append('\'');
        }

        if (!contextData.isEmpty()) {
            sb.append(", contextData=").append(contextData.keySet());
        }

        sb.append('}');
        return sb.toString();
    }
}
