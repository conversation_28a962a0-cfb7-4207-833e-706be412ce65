package com.tipray.transaction.core.infrastructure.barrier.persistence.impl;

import cn.hutool.cache.Cache;
import cn.hutool.cache.CacheUtil;
import com.tipray.transaction.core.infrastructure.barrier.persistence.BarrierStorage;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;

/**
 * 默认屏障存储实现
 * 基于Hutool缓存的实现，支持过期时间防止内存无限膨胀
 *
 * <AUTHOR>
 * @version 2.0
 * @since 2025-01-23
 */
public class DefaultBarrierStorage implements BarrierStorage {

    private static final Logger log = LoggerFactory.getLogger(DefaultBarrierStorage.class);

    // 使用Hutool的TimedCache，支持过期时间
    // 屏障记录过期时间：2小时（考虑重试和网络延迟）
    // 容量：10000（支持更多并发事务）
    private final Cache<String, BarrierRecord> barrierCache = CacheUtil.newLRUCache(10000, 2 * 60 * 60 * 1000L);

    @Override
    public boolean insertBarrier(String transactionId, Long branchId, String action) {
        if (transactionId == null || branchId == null || action == null) {
            log.warn("插入屏障记录失败：参数不能为空 - transactionId={}, branchId={}, action={}",
                    transactionId, branchId, action);
            return false;
        }

        String barrierKey = buildBarrierKey(transactionId, branchId, action);
        BarrierRecord record = new BarrierRecord(transactionId, branchId, action);

        // 使用putIfAbsent确保原子性操作
        // 如果key已存在，返回已存在的值；如果不存在，插入新值并返回null
        boolean contains = barrierCache.containsKey(barrierKey);
        if (!contains) {
            barrierCache.put(barrierKey, record);
            log.debug("插入屏障记录成功: transactionId={}, branchId={}, action={}",
                    transactionId, branchId, action);
            return true;
        }
        log.debug("屏障记录已存在，插入失败: transactionId={}, branchId={}, action={}",
                transactionId, branchId, action);

        return false;
    }

    @Override
    public boolean hasBarrier(String transactionId, Long branchId, String action) {
        if (transactionId == null || branchId == null || action == null) {
            log.warn("检查屏障记录失败：参数不能为空 - transactionId={}, branchId={}, action={}",
                    transactionId, branchId, action);
            return false;
        }

        String barrierKey = buildBarrierKey(transactionId, branchId, action);
        boolean exists = barrierCache.containsKey(barrierKey);

        log.debug("检查屏障记录: transactionId={}, branchId={}, action={} -> {}",
                transactionId, branchId, action, exists ? "存在" : "不存在");

        return exists;
    }

    @Override
    public void deleteBarrier(String transactionId) {
        if (transactionId == null) {
            log.warn("删除屏障记录失败：事务ID不能为空");
            return;
        }

        // 删除指定事务ID的所有屏障记录
        int deletedCount = 0;
        // 由于Hutool缓存没有直接的keySet方法，我们需要遍历所有可能的键
        // 这里简化处理，实际生产环境可能需要更复杂的索引机制
//        barrierCache.schedulePrune(); // 清理过期缓存

        // 注意：这里的实现有限制，无法高效删除所有相关记录
        // 在生产环境中建议使用数据库或Redis等支持模式匹配删除的存储
        log.warn("内存缓存实现无法高效删除事务相关的所有屏障记录，建议使用数据库存储: transactionId={}", transactionId);

        log.debug("删除屏障记录完成: transactionId={}, 删除数量={}", transactionId, deletedCount);
    }

    @Override
    public Object getStatistics() {
        // 启动缓存的定时清理
//        barrierCache.schedulePrune();

        int totalBarriers = barrierCache.size();
        log.debug("获取屏障存储统计信息: 总屏障记录数={}", totalBarriers);

        return new BarrierStorageStatistics(totalBarriers);
    }

    /**
     * 构建屏障键
     * 格式：transactionId:branchId:action
     */
    private String buildBarrierKey(String transactionId, Long branchId, String action) {
        return transactionId + ":" + branchId + ":" + action;
    }

    /**
     * 构建屏障键（字符串版本）
     * 格式：transactionId:branchId:action
     */
    private String buildBarrierKey(String transactionId, String branchId, String action) {
        return transactionId + ":" + branchId + ":" + action;
    }

    /**
     * 屏障记录
     */
    private static class BarrierRecord {
        private final String transactionId;
        private final Long branchId;
        private final String action;
        private final LocalDateTime createTime;

        public BarrierRecord(String transactionId, Long branchId, String action) {
            this.transactionId = transactionId;
            this.branchId = branchId;
            this.action = action;
            this.createTime = LocalDateTime.now();
        }

        public String getTransactionId() {
            return transactionId;
        }

        public Long getBranchId() {
            return branchId;
        }

        public String getAction() {
            return action;
        }

        public LocalDateTime getCreateTime() {
            return createTime;
        }

        @Override
        public String toString() {
            return "BarrierRecord{" +
                    "transactionId='" + transactionId + '\'' +
                    ", branchId=" + branchId +
                    ", action='" + action + '\'' +
                    ", createTime=" + createTime +
                    '}';
        }
    }

    /**
     * 屏障存储统计信息
     */
    public static class BarrierStorageStatistics {
        private final int totalBarriers;

        public BarrierStorageStatistics(int totalBarriers) {
            this.totalBarriers = totalBarriers;
        }

        public int getTotalBarriers() {
            return totalBarriers;
        }

        @Override
        public String toString() {
            return "BarrierStorageStatistics{totalBarriers=" + totalBarriers + '}';
        }
    }
}
