package com.tipray.transaction.core.api.aspect;

import com.tipray.transaction.core.annotation.DistributedTransaction;
import com.tipray.transaction.core.application.engine.DistributedTransactionEngine;
import com.tipray.transaction.core.domain.funcation.TransactionCallback;
import com.tipray.transaction.core.domain.transaction.TransactionDefinition;
import com.tipray.transaction.core.exception.DistributedTransactionSystemException;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.core.annotation.Order;

import java.lang.reflect.Method;
import java.util.HashMap;
import java.util.Map;

/**
 * 分布式事务AOP切面
 * 处理@DistributedTransaction注解的方法调用
 *
 * <AUTHOR>
 * @version 2.0
 * @since 2025-01-23
 */
@Aspect
@Order(100) // 确保在其他切面之前执行
public class DistributedTransactionAspect {

    private final DistributedTransactionEngine transactionEngine;

    public DistributedTransactionAspect(DistributedTransactionEngine transactionEngine) {
        this.transactionEngine = transactionEngine;
    }

    /**
     * 环绕通知：处理分布式事务
     */
    @Around("@annotation(distributedTransaction)")
    public Object handleDistributedTransaction(ProceedingJoinPoint joinPoint,
                                               DistributedTransaction distributedTransaction) {

        // 1. 构建事务定义
        TransactionDefinition definition = buildTransactionDefinition(joinPoint, distributedTransaction);

        // 2. 创建事务回调
        TransactionCallback<Object> callback = context -> {
            try {
                return joinPoint.proceed();
            } catch (Throwable throwable) {
                if (throwable instanceof RuntimeException) {
                    throw (RuntimeException) throwable;
                } else if (throwable instanceof Error) {
                    throw (Error) throwable;
                } else {
                    throw new DistributedTransactionSystemException(
                            "事务执行异常: " + throwable.getMessage(),
                            throwable,
                            DistributedTransactionSystemException.SystemExceptionType.UNKNOWN,
                            DistributedTransactionSystemException.SeverityLevel.MEDIUM,
                            definition.getMethodSignature()
                    );
                }
            }
        };

        // 3. 执行分布式事务（屏障检查在具体的事务处理器中进行）
        return transactionEngine.execute(definition, callback);
    }

    /**
     * 类级别的分布式事务处理
     */
    @Around("@within(distributedTransaction) && execution(public * *(..))")
    public Object handleClassLevelTransaction(ProceedingJoinPoint joinPoint,
                                              DistributedTransaction distributedTransaction) throws Throwable {

        // 检查方法级别是否有注解，如果有则跳过类级别处理
        Method method = ((MethodSignature) joinPoint.getSignature()).getMethod();
        if (method.isAnnotationPresent(DistributedTransaction.class)) {
            return joinPoint.proceed();
        }

        return handleDistributedTransaction(joinPoint, distributedTransaction);
    }

    /**
     * 构建事务定义
     */
    private TransactionDefinition buildTransactionDefinition(ProceedingJoinPoint joinPoint,
                                                             DistributedTransaction annotation) {

        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Method method = signature.getMethod();
        Class<?> targetClass = joinPoint.getTarget().getClass();

        // 构建方法签名
        String methodSignature = buildMethodSignature(targetClass, method);

        // 构建事务名称
        String transactionName = buildTransactionName(annotation, targetClass, method);

        // 构建事务组ID
        String groupId = buildGroupId(annotation, targetClass, method);

        // 解析自定义属性
        Map<String, Object> attributes = parseAttributes(annotation.attributes());

        return TransactionDefinition.builder()
                .name(transactionName)
                .groupId(groupId)
                .mode(annotation.mode())
                .propagation(annotation.propagation())
                .timeout(annotation.timeout())
                .readOnly(annotation.readOnly())
                .applicationName(getApplicationName())
                .methodSignature(methodSignature)
                .description(annotation.description())
                .retryCount(annotation.retryCount())
                .retryInterval(annotation.retryInterval())
                .asyncPersistence(annotation.asyncPersistence())
                .enableBarrier(annotation.enableBarrier())
                .asyncCommitOrRollback(annotation.asyncCommitOrRollback())
                .rollbackFor(annotation.rollbackFor())
                .noRollbackFor(annotation.noRollbackFor())
                .attributes(attributes)
                .build();
    }

    /**
     * 构建方法签名
     */
    private String buildMethodSignature(Class<?> targetClass, Method method) {
        StringBuilder signature = new StringBuilder();
        signature.append(targetClass.getName()).append(".");
        signature.append(method.getName()).append("(");

        Class<?>[] parameterTypes = method.getParameterTypes();
        for (int i = 0; i < parameterTypes.length; i++) {
            if (i > 0) {
                signature.append(",");
            }
            signature.append(parameterTypes[i].getSimpleName());
        }

        signature.append(")");
        return signature.toString();
    }

    /**
     * 构建事务名称
     */
    private String buildTransactionName(DistributedTransaction annotation, Class<?> targetClass, Method method) {
        if (!annotation.name().isEmpty()) {
            return annotation.name();
        }

        return targetClass.getSimpleName() + "." + method.getName();
    }

    /**
     * 构建事务组ID
     */
    private String buildGroupId(DistributedTransaction annotation, Class<?> targetClass, Method method) {
        if (!annotation.groupId().isEmpty()) {
            return annotation.groupId();
        }

        return targetClass.getSimpleName() + "_" + method.getName() + "_Group";
    }

    /**
     * 解析自定义属性
     */
    private Map<String, Object> parseAttributes(String[] attributeArray) {
        Map<String, Object> attributes = new HashMap<>();

        for (String attribute : attributeArray) {
            if (attribute == null || attribute.trim().isEmpty()) {
                continue;
            }

            String[] parts = attribute.split("=", 2);
            if (parts.length == 2) {
                String key = parts[0].trim();
                String value = parts[1].trim();
                attributes.put(key, value);
            } else {
                // 如果没有等号，将整个字符串作为key，值为true
                attributes.put(attribute.trim(), true);
            }
        }

        return attributes;
    }

    /**
     * 获取应用名称
     */
    private String getApplicationName() {
        // 可以从Spring环境或配置中获取
        String appName = System.getProperty("spring.application.name");
        if (appName != null && !appName.isEmpty()) {
            return appName;
        }

        // 从环境变量获取
        appName = System.getenv("APPLICATION_NAME");
        if (appName != null && !appName.isEmpty()) {
            return appName;
        }

        // 默认值
        return "tipray-application";
    }
}
