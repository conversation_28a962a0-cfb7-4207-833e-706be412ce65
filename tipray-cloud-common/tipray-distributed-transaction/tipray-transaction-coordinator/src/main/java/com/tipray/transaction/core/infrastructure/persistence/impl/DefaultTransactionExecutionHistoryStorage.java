package com.tipray.transaction.core.infrastructure.persistence.impl;

import cn.hutool.cache.Cache;
import cn.hutool.cache.CacheUtil;
import com.tipray.transaction.core.domain.execution.TransactionExecutionRecordDO;
import com.tipray.transaction.core.persistence.TransactionExecutionHistoryStorage;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;
import java.util.stream.Collectors;

/**
 * 默认全局事务执行历史存储实现
 * 基于Hutool缓存的内存实现，支持过期时间防止内存无限膨胀
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-01-25
 */
public class DefaultTransactionExecutionHistoryStorage implements TransactionExecutionHistoryStorage {

    private static final Logger log = LoggerFactory.getLogger(DefaultTransactionExecutionHistoryStorage.class);

    // 使用Hutool的LRU缓存，支持过期时间
    // 默认容量3000，过期时间2小时
    private final Cache<Long, TransactionExecutionRecordDO> recordCache = CacheUtil.newTimedCache(2 * 60 * 60 * 1000L);

    // 事务ID索引 - 事务ID -> 执行记录ID列表
    private final ConcurrentHashMap<String, List<Long>> transactionIdIndex = new ConcurrentHashMap<>();

    // 事务组ID索引 - 事务组ID -> 执行记录ID列表
    private final ConcurrentHashMap<String, List<Long>> groupIdIndex = new ConcurrentHashMap<>();

    // 事务名称索引 - 事务名称 -> 执行记录ID列表
    private final ConcurrentHashMap<String, List<Long>> transactionNameIndex = new ConcurrentHashMap<>();

    // 执行状态索引 - 执行状态 -> 执行记录ID列表
    private final ConcurrentHashMap<String, List<Long>> statusIndex = new ConcurrentHashMap<>();

    // 触发用户索引 - 触发用户 -> 执行记录ID列表
    private final ConcurrentHashMap<String, List<Long>> triggeredByIndex = new ConcurrentHashMap<>();

    // 事务模式索引 - 事务模式 -> 执行记录ID列表
    private final ConcurrentHashMap<String, List<Long>> transactionModeIndex = new ConcurrentHashMap<>();

    // 业务标识索引 - 业务标识 -> 执行记录ID列表
    private final ConcurrentHashMap<String, List<Long>> businessKeyIndex = new ConcurrentHashMap<>();

    // 业务类名索引 - 业务类名 -> 执行记录ID列表
    private final ConcurrentHashMap<String, List<Long>> businessClassNameIndex = new ConcurrentHashMap<>();

    // ID生成器
    private final AtomicLong idGenerator = new AtomicLong(1);

    @Override
    public TransactionExecutionRecordDO saveExecutionRecord(TransactionExecutionRecordDO record) {
        if (record == null) {
            log.warn("保存全局事务执行记录失败：记录对象为空");
            return null;
        }

        try {
            // 生成ID
            if (record.getRecordId() == null) {
                record.setRecordId(idGenerator.getAndIncrement());
            }

            // 设置创建时间
            if (record.getCreateTime() == null) {
                record.setCreateTime(LocalDateTime.now());
            }
            record.setUpdateTime(LocalDateTime.now());

            // 保存到主存储
            recordCache.put(record.getRecordId(), record);

            // 更新索引
            updateIndexes(record);

            log.debug("保存全局事务执行记录成功: 事务[{}] 记录ID[{}]",
                    record.getTransactionId(), record.getRecordId());
            return record;

        } catch (Exception e) {
            log.error("保存全局事务执行记录异常: 事务[{}] - {}",
                    record.getTransactionId(), e.getMessage(), e);
            return null;
        }
    }

    @Override
    public List<TransactionExecutionRecordDO> saveExecutionRecords(List<TransactionExecutionRecordDO> records) {
        if (records == null || records.isEmpty()) {
            log.warn("批量保存全局事务执行记录失败：记录列表为空");
            return Collections.emptyList();
        }

        List<TransactionExecutionRecordDO> savedRecords = new ArrayList<>();
        for (TransactionExecutionRecordDO record : records) {
            TransactionExecutionRecordDO savedRecord = saveExecutionRecord(record);
            if (savedRecord != null) {
                savedRecords.add(savedRecord);
            }
        }

        log.debug("批量保存全局事务执行记录完成: 总数[{}] 成功[{}]", records.size(), savedRecords.size());
        return savedRecords;
    }

    @Override
    public TransactionExecutionRecordDO updateExecutionRecord(TransactionExecutionRecordDO record) {
        if (record == null || record.getRecordId() == null) {
            log.warn("更新全局事务执行记录失败：记录对象或记录ID为空");
            return null;
        }

        try {
            TransactionExecutionRecordDO existingRecord = recordCache.get(record.getRecordId());
            if (existingRecord == null) {
                log.warn("更新全局事务执行记录失败：记录不存在: {}", record.getRecordId());
                return null;
            }

            // 更新时间
            record.setUpdateTime(LocalDateTime.now());

            // 更新缓存
            recordCache.put(record.getRecordId(), record);

            // 如果索引字段发生变化，需要更新索引
            if (!Objects.equals(existingRecord.getTransactionId(), record.getTransactionId()) ||
                    !Objects.equals(existingRecord.getGroupId(), record.getGroupId()) ||
                    !Objects.equals(existingRecord.getTransactionName(), record.getTransactionName()) ||
                    !Objects.equals(existingRecord.getStatus(), record.getStatus()) ||
                    !Objects.equals(existingRecord.getTriggeredBy(), record.getTriggeredBy()) ||
                    !Objects.equals(existingRecord.getTransactionMode(), record.getTransactionMode()) ||
                    !Objects.equals(existingRecord.getBusinessKey(), record.getBusinessKey()) ||
                    !Objects.equals(existingRecord.getBusinessClassName(), record.getBusinessClassName())) {

                removeFromIndexes(existingRecord);
                updateIndexes(record);
            }

            log.debug("更新全局事务执行记录成功: 记录ID[{}]", record.getRecordId());
            return record;

        } catch (Exception e) {
            log.error("更新全局事务执行记录异常: 记录ID[{}] - {}", record.getRecordId(), e.getMessage(), e);
            return null;
        }
    }

    @Override
    public TransactionExecutionRecordDO findExecutionRecordById(Long recordId) {
        if (recordId == null) {
            log.warn("查询全局事务执行记录失败：记录ID为空");
            return null;
        }

        try {
            TransactionExecutionRecordDO record = recordCache.get(recordId);
            if (record == null) {
                log.debug("全局事务执行记录不存在: {}", recordId);
            }
            return record;

        } catch (Exception e) {
            log.error("查询全局事务执行记录异常: 记录ID[{}] - {}", recordId, e.getMessage(), e);
            return null;
        }
    }

    @Override
    public List<TransactionExecutionRecordDO> findExecutionRecordsByTransactionId(String transactionId) {
        if (transactionId == null || transactionId.trim().isEmpty()) {
            log.warn("查询全局事务执行记录失败：事务ID为空");
            return Collections.emptyList();
        }

        try {
            List<Long> recordIds = transactionIdIndex.get(transactionId);
            if (recordIds == null || recordIds.isEmpty()) {
                log.debug("事务[{}] 没有找到执行记录", transactionId);
                return Collections.emptyList();
            }

            return recordIds.stream()
                    .map(recordCache::get)
                    .filter(Objects::nonNull)
                    .sorted(Comparator.comparing(TransactionExecutionRecordDO::getAttemptNumber,
                            Comparator.nullsLast(Comparator.naturalOrder())))
                    .collect(Collectors.toList());

        } catch (Exception e) {
            log.error("查询事务[{}] 执行记录异常: {}", transactionId, e.getMessage(), e);
            return Collections.emptyList();
        }
    }

    @Override
    public TransactionExecutionRecordDO findExecutionRecordByTransactionIdAndAttempt(String transactionId, Integer attemptNumber) {
        if (transactionId == null || transactionId.trim().isEmpty() || attemptNumber == null) {
            log.warn("查询全局事务执行记录失败：参数为空");
            return null;
        }

        try {
            List<TransactionExecutionRecordDO> records = findExecutionRecordsByTransactionId(transactionId);
            return records.stream()
                    .filter(r -> Objects.equals(r.getAttemptNumber(), attemptNumber))
                    .findFirst()
                    .orElse(null);

        } catch (Exception e) {
            log.error("查询事务[{}] 第[{}]次执行记录异常: {}", transactionId, attemptNumber, e.getMessage(), e);
            return null;
        }
    }

    @Override
    public List<TransactionExecutionRecordDO> findExecutionRecordsByGroupId(String groupId) {
        if (groupId == null || groupId.trim().isEmpty()) {
            log.warn("查询全局事务执行记录失败：事务组ID为空");
            return Collections.emptyList();
        }

        try {
            List<Long> recordIds = groupIdIndex.get(groupId);
            if (recordIds == null || recordIds.isEmpty()) {
                log.debug("事务组[{}] 没有找到执行记录", groupId);
                return Collections.emptyList();
            }

            return recordIds.stream()
                    .map(recordCache::get)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());

        } catch (Exception e) {
            log.error("查询事务组[{}] 执行记录异常: {}", groupId, e.getMessage(), e);
            return Collections.emptyList();
        }
    }

    @Override
    public List<TransactionExecutionRecordDO> findExecutionRecordsByTransactionName(String transactionName) {
        if (transactionName == null || transactionName.trim().isEmpty()) {
            log.warn("查询全局事务执行记录失败：事务名称为空");
            return Collections.emptyList();
        }

        try {
            List<Long> recordIds = transactionNameIndex.get(transactionName);
            if (recordIds == null || recordIds.isEmpty()) {
                log.debug("事务名称[{}] 没有找到执行记录", transactionName);
                return Collections.emptyList();
            }

            return recordIds.stream()
                    .map(recordCache::get)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());

        } catch (Exception e) {
            log.error("查询事务名称[{}] 执行记录异常: {}", transactionName, e.getMessage(), e);
            return Collections.emptyList();
        }
    }

    @Override
    public List<TransactionExecutionRecordDO> findExecutionRecordsByExecutionType(String executionType) {
        if (executionType == null || executionType.trim().isEmpty()) {
            log.warn("查询全局事务执行记录失败：执行类型为空");
            return Collections.emptyList();
        }

        try {
            return getAllRecords().stream()
                    .filter(record -> Objects.equals(record.getExecutionType(), executionType))
                    .collect(Collectors.toList());

        } catch (Exception e) {
            log.error("查询执行类型[{}] 执行记录异常: {}", executionType, e.getMessage(), e);
            return Collections.emptyList();
        }
    }

    @Override
    public List<TransactionExecutionRecordDO> findExecutionRecordsByStatus(String status) {
        if (status == null || status.trim().isEmpty()) {
            log.warn("查询全局事务执行记录失败：执行状态为空");
            return Collections.emptyList();
        }

        try {
            List<Long> recordIds = statusIndex.get(status);
            if (recordIds == null || recordIds.isEmpty()) {
                log.debug("执行状态[{}] 没有找到执行记录", status);
                return Collections.emptyList();
            }

            return recordIds.stream()
                    .map(recordCache::get)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());

        } catch (Exception e) {
            log.error("查询执行状态[{}] 执行记录异常: {}", status, e.getMessage(), e);
            return Collections.emptyList();
        }
    }

    @Override
    public List<TransactionExecutionRecordDO> findExecutionRecordsByTriggeredBy(String triggeredBy) {
        if (triggeredBy == null || triggeredBy.trim().isEmpty()) {
            log.warn("查询全局事务执行记录失败：触发用户为空");
            return Collections.emptyList();
        }

        try {
            List<Long> recordIds = triggeredByIndex.get(triggeredBy);
            if (recordIds == null || recordIds.isEmpty()) {
                log.debug("触发用户[{}] 没有找到执行记录", triggeredBy);
                return Collections.emptyList();
            }

            return recordIds.stream()
                    .map(recordCache::get)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());

        } catch (Exception e) {
            log.error("查询触发用户[{}] 执行记录异常: {}", triggeredBy, e.getMessage(), e);
            return Collections.emptyList();
        }
    }

    @Override
    public List<TransactionExecutionRecordDO> findExecutionRecordsByTransactionMode(String transactionMode) {
        if (transactionMode == null || transactionMode.trim().isEmpty()) {
            log.warn("查询全局事务执行记录失败：事务模式为空");
            return Collections.emptyList();
        }

        try {
            List<Long> recordIds = transactionModeIndex.get(transactionMode);
            if (recordIds == null || recordIds.isEmpty()) {
                log.debug("事务模式[{}] 没有找到执行记录", transactionMode);
                return Collections.emptyList();
            }

            return recordIds.stream()
                    .map(recordCache::get)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());

        } catch (Exception e) {
            log.error("查询事务模式[{}] 执行记录异常: {}", transactionMode, e.getMessage(), e);
            return Collections.emptyList();
        }
    }

    @Override
    public List<TransactionExecutionRecordDO> findExecutionRecordsByBusinessKey(String businessKey) {
        if (businessKey == null || businessKey.trim().isEmpty()) {
            log.warn("查询全局事务执行记录失败：业务标识为空");
            return Collections.emptyList();
        }

        try {
            List<Long> recordIds = businessKeyIndex.get(businessKey);
            if (recordIds == null || recordIds.isEmpty()) {
                log.debug("业务标识[{}] 没有找到执行记录", businessKey);
                return Collections.emptyList();
            }

            return recordIds.stream()
                    .map(recordCache::get)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());

        } catch (Exception e) {
            log.error("查询业务标识[{}] 执行记录异常: {}", businessKey, e.getMessage(), e);
            return Collections.emptyList();
        }
    }

    @Override
    public List<TransactionExecutionRecordDO> findExecutionRecordsByTimeRange(LocalDateTime startTime, LocalDateTime endTime) {
        if (startTime == null || endTime == null) {
            log.warn("查询全局事务执行记录失败：时间范围参数为空");
            return Collections.emptyList();
        }

        try {
            return getAllRecords().stream()
                    .filter(record -> {
                        LocalDateTime createTime = record.getCreateTime();
                        return createTime != null &&
                                !createTime.isBefore(startTime) &&
                                !createTime.isAfter(endTime);
                    })
                    .collect(Collectors.toList());

        } catch (Exception e) {
            log.error("查询时间范围[{} - {}] 执行记录异常: {}", startTime, endTime, e.getMessage(), e);
            return Collections.emptyList();
        }
    }

    @Override
    public List<TransactionExecutionRecordDO> findExecutionRecordsByBusinessClassName(String businessClassName) {
        if (businessClassName == null || businessClassName.trim().isEmpty()) {
            log.warn("查询全局事务执行记录失败：业务类名为空");
            return Collections.emptyList();
        }

        try {
            List<Long> recordIds = businessClassNameIndex.get(businessClassName);
            if (recordIds == null || recordIds.isEmpty()) {
                log.debug("业务类名[{}] 没有找到执行记录", businessClassName);
                return Collections.emptyList();
            }

            return recordIds.stream()
                    .map(recordCache::get)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());

        } catch (Exception e) {
            log.error("查询业务类名[{}] 执行记录异常: {}", businessClassName, e.getMessage(), e);
            return Collections.emptyList();
        }
    }

    @Override
    public List<TransactionExecutionRecordDO> findExecutionRecordsByBusinessMethodName(String businessMethodName) {
        if (businessMethodName == null || businessMethodName.trim().isEmpty()) {
            log.warn("查询全局事务执行记录失败：业务方法名为空");
            return Collections.emptyList();
        }

        try {
            return getAllRecords().stream()
                    .filter(record -> Objects.equals(record.getBusinessMethodName(), businessMethodName))
                    .collect(Collectors.toList());

        } catch (Exception e) {
            log.error("查询业务方法名[{}] 执行记录异常: {}", businessMethodName, e.getMessage(), e);
            return Collections.emptyList();
        }
    }

    @Override
    public List<TransactionExecutionRecordDO> findFailedExecutionRecords() {
        return findExecutionRecordsByStatus("FAILED");
    }

    @Override
    public List<TransactionExecutionRecordDO> findTimeoutExecutionRecords() {
        try {
            return getAllRecords().stream()
                    .filter(record -> Boolean.TRUE.equals(record.getTimeout()) || "TIMEOUT".equals(record.getStatus()))
                    .collect(Collectors.toList());

        } catch (Exception e) {
            log.error("查询超时执行记录异常: {}", e.getMessage(), e);
            return Collections.emptyList();
        }
    }

    @Override
    public List<TransactionExecutionRecordDO> findRetryableExecutionRecords() {
        try {
            return getAllRecords().stream()
                    .filter(record -> "FAILED".equals(record.getStatus()) || "TIMEOUT".equals(record.getStatus()))
                    .collect(Collectors.toList());

        } catch (Exception e) {
            log.error("查询可重试执行记录异常: {}", e.getMessage(), e);
            return Collections.emptyList();
        }
    }

    @Override
    public List<TransactionExecutionRecordDO> findManualExecutionRecords() {
        try {
            return getAllRecords().stream()
                    .filter(record -> "MANUAL_RETRY".equals(record.getExecutionType()) ||
                            "MANUAL_ROLLBACK".equals(record.getExecutionType()) ||
                            record.getTriggeredBy() != null)
                    .collect(Collectors.toList());

        } catch (Exception e) {
            log.error("查询手动操作执行记录异常: {}", e.getMessage(), e);
            return Collections.emptyList();
        }
    }

    @Override
    public TransactionExecutionRecordDO findLatestExecutionRecordByTransactionId(String transactionId) {
        if (transactionId == null || transactionId.trim().isEmpty()) {
            log.warn("查询最新全局事务执行记录失败：事务ID为空");
            return null;
        }

        try {
            List<TransactionExecutionRecordDO> records = findExecutionRecordsByTransactionId(transactionId);
            return records.stream()
                    .max(Comparator.comparing(TransactionExecutionRecordDO::getCreateTime,
                            Comparator.nullsLast(Comparator.naturalOrder())))
                    .orElse(null);

        } catch (Exception e) {
            log.error("查询事务[{}] 最新执行记录异常: {}", transactionId, e.getMessage(), e);
            return null;
        }
    }

    @Override
    public int countExecutionRecordsByTransactionId(String transactionId) {
        if (transactionId == null || transactionId.trim().isEmpty()) {
            log.warn("统计全局事务执行次数失败：事务ID为空");
            return 0;
        }

        try {
            List<Long> recordIds = transactionIdIndex.get(transactionId);
            return recordIds != null ? recordIds.size() : 0;

        } catch (Exception e) {
            log.error("统计事务[{}] 执行次数异常: {}", transactionId, e.getMessage(), e);
            return 0;
        }
    }

    @Override
    public int countRetryExecutionRecordsByTransactionId(String transactionId) {
        if (transactionId == null || transactionId.trim().isEmpty()) {
            log.warn("统计全局事务重试次数失败：事务ID为空");
            return 0;
        }

        try {
            List<TransactionExecutionRecordDO> records = findExecutionRecordsByTransactionId(transactionId);
            return (int) records.stream()
                    .filter(record -> "RETRY".equals(record.getExecutionType()))
                    .count();

        } catch (Exception e) {
            log.error("统计事务[{}] 重试次数异常: {}", transactionId, e.getMessage(), e);
            return 0;
        }
    }

    @Override
    public int countManualRetryExecutionRecordsByTransactionId(String transactionId) {
        if (transactionId == null || transactionId.trim().isEmpty()) {
            log.warn("统计全局事务手动重试次数失败：事务ID为空");
            return 0;
        }

        try {
            List<TransactionExecutionRecordDO> records = findExecutionRecordsByTransactionId(transactionId);
            return (int) records.stream()
                    .filter(record -> "MANUAL_RETRY".equals(record.getExecutionType()))
                    .count();

        } catch (Exception e) {
            log.error("统计事务[{}] 手动重试次数异常: {}", transactionId, e.getMessage(), e);
            return 0;
        }
    }

    @Override
    public int countExecutionRecordsByStatus(String status) {
        if (status == null || status.trim().isEmpty()) {
            log.warn("统计执行记录数失败：执行状态为空");
            return 0;
        }

        try {
            List<Long> recordIds = statusIndex.get(status);
            return recordIds != null ? recordIds.size() : 0;

        } catch (Exception e) {
            log.error("统计执行状态[{}] 记录数异常: {}", status, e.getMessage(), e);
            return 0;
        }
    }

    @Override
    public int countExecutionRecordsByTimeRange(LocalDateTime startTime, LocalDateTime endTime) {
        if (startTime == null || endTime == null) {
            log.warn("统计执行记录数失败：时间范围参数为空");
            return 0;
        }

        try {
            return (int) getAllRecords().stream()
                    .filter(record -> {
                        LocalDateTime createTime = record.getCreateTime();
                        return createTime != null &&
                                !createTime.isBefore(startTime) &&
                                !createTime.isAfter(endTime);
                    })
                    .count();

        } catch (Exception e) {
            log.error("统计时间范围[{} - {}] 执行记录数异常: {}", startTime, endTime, e.getMessage(), e);
            return 0;
        }
    }

    @Override
    public int countExecutionRecordsByTriggeredBy(String triggeredBy) {
        if (triggeredBy == null || triggeredBy.trim().isEmpty()) {
            log.warn("统计触发用户执行记录数失败：触发用户为空");
            return 0;
        }

        try {
            List<Long> recordIds = triggeredByIndex.get(triggeredBy);
            return recordIds != null ? recordIds.size() : 0;

        } catch (Exception e) {
            log.error("统计触发用户[{}] 执行记录数异常: {}", triggeredBy, e.getMessage(), e);
            return 0;
        }
    }

    @Override
    public int deleteExecutionRecordsByTransactionId(String transactionId) {
        if (transactionId == null || transactionId.trim().isEmpty()) {
            log.warn("删除全局事务执行记录失败：事务ID为空");
            return 0;
        }

        try {
            List<Long> recordIds = transactionIdIndex.remove(transactionId);
            if (recordIds == null || recordIds.isEmpty()) {
                log.debug("事务[{}] 没有找到执行记录", transactionId);
                return 0;
            }

            int deleteCount = 0;
            for (Long recordId : recordIds) {
                TransactionExecutionRecordDO record = recordCache.get(recordId);
                if (record != null) {
                    recordCache.remove(recordId);
                    removeFromIndexes(record);
                    deleteCount++;
                }
            }

            log.info("删除事务[{}] 执行记录完成，删除记录数: {}", transactionId, deleteCount);
            return deleteCount;

        } catch (Exception e) {
            log.error("删除事务[{}] 执行记录异常: {}", transactionId, e.getMessage(), e);
            return 0;
        }
    }

    @Override
    public int deleteExecutionRecordsBefore(LocalDateTime beforeTime) {
        if (beforeTime == null) {
            log.warn("删除执行记录失败：时间参数为空");
            return 0;
        }

        try {
            List<TransactionExecutionRecordDO> recordsToDelete = getAllRecords().stream()
                    .filter(record -> {
                        LocalDateTime createTime = record.getCreateTime();
                        return createTime != null && createTime.isBefore(beforeTime);
                    })
                    .collect(Collectors.toList());

            int deleteCount = 0;
            for (TransactionExecutionRecordDO record : recordsToDelete) {
                recordCache.remove(record.getRecordId());
                removeFromIndexes(record);
                deleteCount++;
            }

            log.info("删除时间[{}]之前的执行记录完成，删除记录数: {}", beforeTime, deleteCount);
            return deleteCount;

        } catch (Exception e) {
            log.error("删除时间[{}]之前的执行记录异常: {}", beforeTime, e.getMessage(), e);
            return 0;
        }
    }

    @Override
    public List<TransactionExecutionRecordDO> findExecutionRecordsWithPagination(int offset, int limit) {
        if (offset < 0 || limit <= 0) {
            log.warn("分页查询全局事务执行记录失败：分页参数无效");
            return Collections.emptyList();
        }

        try {
            return getAllRecords().stream()
                    .sorted(Comparator.comparing(TransactionExecutionRecordDO::getCreateTime,
                            Comparator.nullsLast(Comparator.reverseOrder())))
                    .skip(offset)
                    .limit(limit)
                    .collect(Collectors.toList());

        } catch (Exception e) {
            log.error("分页查询全局事务执行记录异常: offset={}, limit={} - {}", offset, limit, e.getMessage(), e);
            return Collections.emptyList();
        }
    }

    @Override
    public List<TransactionExecutionRecordDO> findExecutionRecordsByTransactionIdWithPagination(String transactionId, int offset, int limit) {
        if (transactionId == null || transactionId.trim().isEmpty() || offset < 0 || limit <= 0) {
            log.warn("分页查询事务执行记录失败：参数无效");
            return Collections.emptyList();
        }

        try {
            List<TransactionExecutionRecordDO> records = findExecutionRecordsByTransactionId(transactionId);
            return records.stream()
                    .sorted(Comparator.comparing(TransactionExecutionRecordDO::getCreateTime,
                            Comparator.nullsLast(Comparator.reverseOrder())))
                    .skip(offset)
                    .limit(limit)
                    .collect(Collectors.toList());

        } catch (Exception e) {
            log.error("分页查询事务[{}] 执行记录异常: offset={}, limit={} - {}", transactionId, offset, limit, e.getMessage(), e);
            return Collections.emptyList();
        }
    }

    @Override
    public List<TransactionExecutionRecordDO> findExecutionRecordsByGroupIdWithPagination(String groupId, int offset, int limit) {
        if (groupId == null || groupId.trim().isEmpty() || offset < 0 || limit <= 0) {
            log.warn("分页查询事务组执行记录失败：参数无效");
            return Collections.emptyList();
        }

        try {
            List<TransactionExecutionRecordDO> records = findExecutionRecordsByGroupId(groupId);
            return records.stream()
                    .sorted(Comparator.comparing(TransactionExecutionRecordDO::getCreateTime,
                            Comparator.nullsLast(Comparator.reverseOrder())))
                    .skip(offset)
                    .limit(limit)
                    .collect(Collectors.toList());

        } catch (Exception e) {
            log.error("分页查询事务组[{}] 执行记录异常: offset={}, limit={} - {}", groupId, offset, limit, e.getMessage(), e);
            return Collections.emptyList();
        }
    }

    @Override
    public Object getStatistics() {
        try {
            int totalRecords = recordCache.size();
            int totalTransactions = transactionIdIndex.size();
            int totalGroups = groupIdIndex.size();
            int totalTransactionNames = transactionNameIndex.size();
            int totalTriggeredUsers = triggeredByIndex.size();

            // 统计各状态的记录数
            Map<String, Integer> statusCounts = new HashMap<>();
            for (Map.Entry<String, List<Long>> entry : statusIndex.entrySet()) {
                statusCounts.put(entry.getKey(), entry.getValue().size());
            }

            log.debug("获取全局事务执行历史存储统计信息: 总记录数={}, 事务数={}, 事务组数={}, 事务名称数={}, 触发用户数={}",
                    totalRecords, totalTransactions, totalGroups, totalTransactionNames, totalTriggeredUsers);

            return new TransactionExecutionHistoryStorageStatistics(totalRecords, totalTransactions,
                    totalGroups, totalTransactionNames, totalTriggeredUsers, statusCounts);

        } catch (Exception e) {
            log.error("获取全局事务执行历史存储统计信息异常: {}", e.getMessage(), e);
            return new TransactionExecutionHistoryStorageStatistics(0, 0, 0, 0, 0, Collections.emptyMap());
        }
    }

    /**
     * 获取所有记录（解决Hutool Cache没有values()方法的问题）
     */
    private List<TransactionExecutionRecordDO> getAllRecords() {
        List<TransactionExecutionRecordDO> allRecords = new ArrayList<>();
        for (List<Long> recordIds : transactionIdIndex.values()) {
            for (Long recordId : recordIds) {
                TransactionExecutionRecordDO record = recordCache.get(recordId);
                if (record != null) {
                    allRecords.add(record);
                }
            }
        }
        return allRecords;
    }

    /**
     * 更新索引
     */
    private void updateIndexes(TransactionExecutionRecordDO record) {
        Long recordId = record.getRecordId();

        // 更新事务ID索引
        if (record.getTransactionId() != null) {
            transactionIdIndex.computeIfAbsent(record.getTransactionId(), k -> new ArrayList<>()).add(recordId);
        }

        // 更新事务组ID索引
        if (record.getGroupId() != null) {
            groupIdIndex.computeIfAbsent(record.getGroupId(), k -> new ArrayList<>()).add(recordId);
        }

        // 更新事务名称索引
        if (record.getTransactionName() != null) {
            transactionNameIndex.computeIfAbsent(record.getTransactionName(), k -> new ArrayList<>()).add(recordId);
        }

        // 更新执行状态索引
        if (record.getStatus() != null) {
            statusIndex.computeIfAbsent(record.getStatus(), k -> new ArrayList<>()).add(recordId);
        }

        // 更新触发用户索引
        if (record.getTriggeredBy() != null) {
            triggeredByIndex.computeIfAbsent(record.getTriggeredBy(), k -> new ArrayList<>()).add(recordId);
        }

        // 更新事务模式索引
        if (record.getTransactionMode() != null) {
            transactionModeIndex.computeIfAbsent(record.getTransactionMode(), k -> new ArrayList<>()).add(recordId);
        }

        // 更新业务标识索引
        if (record.getBusinessKey() != null) {
            businessKeyIndex.computeIfAbsent(record.getBusinessKey(), k -> new ArrayList<>()).add(recordId);
        }

        // 更新业务类名索引
        if (record.getBusinessClassName() != null) {
            businessClassNameIndex.computeIfAbsent(record.getBusinessClassName(), k -> new ArrayList<>()).add(recordId);
        }
    }

    /**
     * 从索引中移除记录
     */
    private void removeFromIndexes(TransactionExecutionRecordDO record) {
        Long recordId = record.getRecordId();

        // 从事务ID索引中移除
        if (record.getTransactionId() != null) {
            List<Long> recordIds = transactionIdIndex.get(record.getTransactionId());
            if (recordIds != null) {
                recordIds.remove(recordId);
                if (recordIds.isEmpty()) {
                    transactionIdIndex.remove(record.getTransactionId());
                }
            }
        }

        // 从事务组ID索引中移除
        if (record.getGroupId() != null) {
            List<Long> recordIds = groupIdIndex.get(record.getGroupId());
            if (recordIds != null) {
                recordIds.remove(recordId);
                if (recordIds.isEmpty()) {
                    groupIdIndex.remove(record.getGroupId());
                }
            }
        }

        // 从事务名称索引中移除
        if (record.getTransactionName() != null) {
            List<Long> recordIds = transactionNameIndex.get(record.getTransactionName());
            if (recordIds != null) {
                recordIds.remove(recordId);
                if (recordIds.isEmpty()) {
                    transactionNameIndex.remove(record.getTransactionName());
                }
            }
        }

        // 从执行状态索引中移除
        if (record.getStatus() != null) {
            List<Long> recordIds = statusIndex.get(record.getStatus());
            if (recordIds != null) {
                recordIds.remove(recordId);
                if (recordIds.isEmpty()) {
                    statusIndex.remove(record.getStatus());
                }
            }
        }

        // 从触发用户索引中移除
        if (record.getTriggeredBy() != null) {
            List<Long> recordIds = triggeredByIndex.get(record.getTriggeredBy());
            if (recordIds != null) {
                recordIds.remove(recordId);
                if (recordIds.isEmpty()) {
                    triggeredByIndex.remove(record.getTriggeredBy());
                }
            }
        }

        // 从事务模式索引中移除
        if (record.getTransactionMode() != null) {
            List<Long> recordIds = transactionModeIndex.get(record.getTransactionMode());
            if (recordIds != null) {
                recordIds.remove(recordId);
                if (recordIds.isEmpty()) {
                    transactionModeIndex.remove(record.getTransactionMode());
                }
            }
        }

        // 从业务标识索引中移除
        if (record.getBusinessKey() != null) {
            List<Long> recordIds = businessKeyIndex.get(record.getBusinessKey());
            if (recordIds != null) {
                recordIds.remove(recordId);
                if (recordIds.isEmpty()) {
                    businessKeyIndex.remove(record.getBusinessKey());
                }
            }
        }

        // 从业务类名索引中移除
        if (record.getBusinessClassName() != null) {
            List<Long> recordIds = businessClassNameIndex.get(record.getBusinessClassName());
            if (recordIds != null) {
                recordIds.remove(recordId);
                if (recordIds.isEmpty()) {
                    businessClassNameIndex.remove(record.getBusinessClassName());
                }
            }
        }
    }

    /**
     * 清空所有数据（用于测试）
     */
    public void clear() {
        recordCache.clear();
        transactionIdIndex.clear();
        groupIdIndex.clear();
        transactionNameIndex.clear();
        statusIndex.clear();
        triggeredByIndex.clear();
        transactionModeIndex.clear();
        businessKeyIndex.clear();
        businessClassNameIndex.clear();
        idGenerator.set(1);
        log.info("清空所有全局事务执行历史数据");
    }

    /**
     * 获取当前存储的记录数量（用于测试）
     */
    public int size() {
        return recordCache.size();
    }

    /**
     * 全局事务执行历史存储统计信息
     */
    public static class TransactionExecutionHistoryStorageStatistics {
        private final int totalRecords;
        private final int totalTransactions;
        private final int totalGroups;
        private final int totalTransactionNames;
        private final int totalTriggeredUsers;
        private final Map<String, Integer> statusCounts;

        public TransactionExecutionHistoryStorageStatistics(int totalRecords, int totalTransactions,
                                                            int totalGroups, int totalTransactionNames,
                                                            int totalTriggeredUsers, Map<String, Integer> statusCounts) {
            this.totalRecords = totalRecords;
            this.totalTransactions = totalTransactions;
            this.totalGroups = totalGroups;
            this.totalTransactionNames = totalTransactionNames;
            this.totalTriggeredUsers = totalTriggeredUsers;
            this.statusCounts = statusCounts != null ? statusCounts : Collections.emptyMap();
        }

        public int getTotalRecords() {
            return totalRecords;
        }

        public int getTotalTransactions() {
            return totalTransactions;
        }

        public int getTotalGroups() {
            return totalGroups;
        }

        public int getTotalTransactionNames() {
            return totalTransactionNames;
        }

        public int getTotalTriggeredUsers() {
            return totalTriggeredUsers;
        }

        public Map<String, Integer> getStatusCounts() {
            return statusCounts;
        }

        @Override
        public String toString() {
            return "TransactionExecutionHistoryStorageStatistics{" +
                    "totalRecords=" + totalRecords +
                    ", totalTransactions=" + totalTransactions +
                    ", totalGroups=" + totalGroups +
                    ", totalTransactionNames=" + totalTransactionNames +
                    ", totalTriggeredUsers=" + totalTriggeredUsers +
                    ", statusCounts=" + statusCounts +
                    '}';
        }
    }
}
