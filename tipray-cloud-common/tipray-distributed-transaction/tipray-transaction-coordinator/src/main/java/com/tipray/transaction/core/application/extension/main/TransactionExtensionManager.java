package com.tipray.transaction.core.application.extension.main;

import com.tipray.transaction.core.domain.transaction.TransactionContext;
import lombok.Builder;
import lombok.Data;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CopyOnWriteArrayList;

/**
 * 事务扩展管理器
 * 管理事务生命周期中的扩展点和插件
 *
 * <AUTHOR>
 * @version 2.0
 * @since 2025-01-23
 */
public class TransactionExtensionManager {

    private static final Logger log = LoggerFactory.getLogger(TransactionExtensionManager.class);

    // 扩展点映射
    private final Map<TransactionExtensionPoint, List<TransactionExtension>> extensionMap =
            new ConcurrentHashMap<>();

    // 扩展实例缓存
    private final Map<String, TransactionExtension> extensionCache = new ConcurrentHashMap<>();

    // 自动注册拓展点
    public TransactionExtensionManager(List<TransactionExtension> extensions) {
        if (extensions != null) {
            for (TransactionExtension extension : extensions) {
                registerExtension(extension);
            }
        }
    }

    /**
     * 注册扩展
     */
    public void registerExtension(TransactionExtension extension) {
        if (extension == null) {
            throw new IllegalArgumentException("扩展不能为空");
        }

        String extensionId = extension.getExtensionId();
        TransactionExtensionPoint extensionPoint = extension.getExtensionPoint();

        // 缓存扩展实例
        extensionCache.put(extensionId, extension);

        // 添加到扩展点映射
        extensionMap.computeIfAbsent(extensionPoint, k -> new CopyOnWriteArrayList<>())
                .add(extension);

        // 按优先级排序
        sortExtensionsByPriority(extensionPoint);

        log.info("注册事务扩展: {} (扩展点: {}, 优先级: {})",
                extensionId, extensionPoint, extension.getPriority());
    }

    /**
     * 注销扩展
     */
    public void unregisterExtension(String extensionId) {
        TransactionExtension extension = extensionCache.remove(extensionId);
        if (extension != null) {
            TransactionExtensionPoint extensionPoint = extension.getExtensionPoint();
            List<TransactionExtension> extensions = extensionMap.get(extensionPoint);
            if (extensions != null) {
                extensions.remove(extension);
            }

            log.info("注销事务扩展: {}", extensionId);
        }
    }

    /**
     * 执行事务开始前扩展
     */
    public void executeBeforeTransaction(TransactionContext context) {
        executeExtensions(TransactionExtensionPoint.BEFORE_TRANSACTION, context);
    }

    /**
     * 执行事务开始后扩展
     */
    public void executeAfterTransactionBegin(TransactionContext context) {
        executeExtensions(TransactionExtensionPoint.AFTER_TRANSACTION_BEGIN, context);
    }

    /**
     * 执行事务提交前扩展
     */
    public void executeBeforeCommit(TransactionContext context) {
        executeExtensions(TransactionExtensionPoint.BEFORE_COMMIT, context);
    }

    /**
     * 执行事务提交后扩展
     */
    public void executeAfterCommit(TransactionContext context) {
        executeExtensions(TransactionExtensionPoint.AFTER_COMMIT, context);
    }

    /**
     * 执行事务回滚前扩展
     */
    public void executeBeforeRollback(TransactionContext context, Exception cause) {
        TransactionExtensionContext extensionContext = TransactionExtensionContext.builder()
                .transactionContext(context)
                .exception(cause)
                .build();

        executeExtensions(TransactionExtensionPoint.BEFORE_ROLLBACK, extensionContext);
    }

    /**
     * 执行事务回滚后扩展
     */
    public void executeAfterRollback(TransactionContext context, Exception cause) {
        TransactionExtensionContext extensionContext = TransactionExtensionContext.builder()
                .transactionContext(context)
                .exception(cause)
                .build();

        executeExtensions(TransactionExtensionPoint.AFTER_ROLLBACK, extensionContext);
    }

    /**
     * 执行事务完成扩展
     */
    public void executeAfterCompletion(TransactionContext context, boolean committed) {
        TransactionExtensionContext extensionContext = TransactionExtensionContext.builder()
                .transactionContext(context)
                .committed(committed)
                .build();

        executeExtensions(TransactionExtensionPoint.AFTER_COMPLETION, extensionContext);
    }

    /**
     * 获取扩展统计信息
     */
    public ExtensionStatistics getExtensionStatistics() {
        Map<TransactionExtensionPoint, Integer> pointCounts = new HashMap<>();
        for (Map.Entry<TransactionExtensionPoint, List<TransactionExtension>> entry : extensionMap.entrySet()) {
            pointCounts.put(entry.getKey(), entry.getValue().size());
        }

        return ExtensionStatistics.builder()
                .totalExtensions(extensionCache.size())
                .extensionPointCounts(pointCounts)
                .extensionDetails(new HashMap<>(extensionCache))
                .build();
    }

    /**
     * 获取指定扩展点的扩展列表
     */
    public List<TransactionExtension> getExtensions(TransactionExtensionPoint extensionPoint) {
        List<TransactionExtension> extensions = extensionMap.get(extensionPoint);
        return extensions != null ? new ArrayList<>(extensions) : Collections.emptyList();
    }

    /**
     * 检查扩展是否存在
     */
    public boolean hasExtension(String extensionId) {
        return extensionCache.containsKey(extensionId);
    }

    /**
     * 获取扩展实例
     */
    public TransactionExtension getExtension(String extensionId) {
        return extensionCache.get(extensionId);
    }

    /**
     * 启用扩展
     */
    public void enableExtension(String extensionId) {
        TransactionExtension extension = extensionCache.get(extensionId);
        if (extension != null) {
            extension.enable();
            log.info("启用事务扩展: {}", extensionId);
        }
    }

    /**
     * 禁用扩展
     */
    public void disableExtension(String extensionId) {
        TransactionExtension extension = extensionCache.get(extensionId);
        if (extension != null) {
            extension.disable();
            log.info("禁用事务扩展: {}", extensionId);
        }
    }

    /**
     * 清理所有扩展
     */
    public void cleanup() {
        extensionCache.clear();
        extensionMap.clear();
        log.info("清理所有事务扩展");
    }

    // ==================== 私有方法 ====================

    /**
     * 执行扩展
     */
    private void executeExtensions(TransactionExtensionPoint extensionPoint, Object context) {
        List<TransactionExtension> extensions = extensionMap.get(extensionPoint);
        if (extensions == null || extensions.isEmpty()) {
            return;
        }

        for (TransactionExtension extension : extensions) {
            if (!extension.isEnabled()) {
                continue;
            }

            try {
                long startTime = System.currentTimeMillis();
                extension.execute(context);
                long duration = System.currentTimeMillis() - startTime;

                log.debug("执行事务扩展: {} (扩展点: {}, 耗时: {}ms)",
                        extension.getExtensionId(), extensionPoint, duration);

            } catch (Exception e) {
                log.error("执行事务扩展失败: {} (扩展点: {})",
                        extension.getExtensionId(), extensionPoint, e);

                // 根据扩展的错误处理策略决定是否继续
                if (!extension.isContinueOnError()) {
                    throw new TransactionExtensionException(
                            "事务扩展执行失败: " + extension.getExtensionId(), e);
                }
            }
        }
    }

    /**
     * 按优先级排序扩展
     */
    private void sortExtensionsByPriority(TransactionExtensionPoint extensionPoint) {
        List<TransactionExtension> extensions = extensionMap.get(extensionPoint);
        if (extensions != null) {
            extensions.sort(Comparator.comparingInt(TransactionExtension::getPriority).reversed());
        }
    }

    /**
     * 扩展统计信息
     */
    @Builder
    @Data
    public static class ExtensionStatistics {
        private final int totalExtensions;
        private final int activeExtensions;
        private final long totalExecutions;
        private final long totalFailures;
        private final double averageExecutionTime;
        private final Map<TransactionExtensionPoint, Integer> extensionPointCounts;
        private final Map<String, TransactionExtension> extensionDetails;


        // getters
        public int getTotalExtensions() {
            return totalExtensions;
        }

        public int getActiveExtensions() {
            return activeExtensions;
        }

        public long getTotalExecutions() {
            return totalExecutions;
        }

        public long getTotalFailures() {
            return totalFailures;
        }

        public double getAverageExecutionTime() {
            return averageExecutionTime;
        }

        public double getFailureRate() {
            return totalExecutions > 0 ? (double) totalFailures / totalExecutions * 100 : 0.0;
        }

        @Override
        public String toString() {
            return String.format("ExtensionStatistics{total=%d, active=%d, executions=%d, failures=%d, failureRate=%.2f%%}",
                    totalExtensions, activeExtensions, totalExecutions, totalFailures, getFailureRate());
        }
    }


    /**
     * 事务扩展异常
     */
    public static class TransactionExtensionException extends RuntimeException {
        private static final long serialVersionUID = 1L;

        public TransactionExtensionException(String message) {
            super(message);
        }

        public TransactionExtensionException(String message, Throwable cause) {
            super(message, cause);
        }
    }
}
