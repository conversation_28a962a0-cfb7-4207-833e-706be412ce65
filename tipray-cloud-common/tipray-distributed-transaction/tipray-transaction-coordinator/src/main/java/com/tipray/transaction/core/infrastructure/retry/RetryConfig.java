package com.tipray.transaction.core.infrastructure.retry;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 重试配置类
 * 定义重试策略的各种参数
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-01-20
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RetryConfig {

    /**
     * 最大重试次数
     */
    private int maxRetries;

    /**
     * 重试间隔（毫秒）
     */
    private long retryInterval;

    /**
     * 退避倍数
     * 每次重试的延迟时间 = retryInterval * (backoffMultiplier ^ (attempt - 1))
     */
    private double backoffMultiplier;

    /**
     * 最大重试间隔（毫秒）
     * 防止退避时间过长
     */
    private long maxRetryInterval;

    /**
     * 是否启用重试
     */
    private boolean enabled;

    /**
     * 重试策略类型
     */
    private RetryStrategy strategy;

    /**
     * 是否启用随机抖动
     */
    private boolean enableJitter;

    /**
     * 抖动范围（0.0 - 1.0）
     */
    private double jitterRange;

    /**
     * 需要重试的异常类型
     * 如果为空，则对所有异常进行重试
     */
    private Class<? extends Exception>[] retryableExceptions;

    /**
     * 不需要重试的异常类型
     * 优先级高于retryableExceptions
     */
    private Class<? extends Exception>[] nonRetryableExceptions;

    /**
     * 重试条件判断器
     * 自定义重试条件
     */
    private RetryCondition retryCondition;

    /**
     * 重试监听器
     * 用于监听重试过程
     */
    private RetryListener retryListener;

    /**
     * 创建默认配置
     */
    public static RetryConfig defaultConfig() {
        return RetryConfig.builder()
                .maxRetries(3)
                .retryInterval(1000L)
                .backoffMultiplier(2.0)
                .maxRetryInterval(60000L)
                .enabled(true)
                .strategy(RetryStrategy.EXPONENTIAL_BACKOFF)
                .enableJitter(true)
                .jitterRange(0.2)
                .build();
    }

    /**
     * 创建不重试配置
     */
    public static RetryConfig noRetry() {
        return RetryConfig.builder()
                .maxRetries(0)
                .retryInterval(0L)
                .backoffMultiplier(1.0)
                .maxRetryInterval(0L)
                .enabled(false)
                .strategy(RetryStrategy.NO_RETRY)
                .enableJitter(false)
                .jitterRange(0.0)
                .build();
    }

    /**
     * 创建禁用重试配置
     */
    public static RetryConfig disabled() {
        return RetryConfig.builder()
                .maxRetries(0)
                .retryInterval(0L)
                .backoffMultiplier(1.0)
                .maxRetryInterval(0L)
                .enabled(false)
                .strategy(RetryStrategy.NO_RETRY)
                .enableJitter(false)
                .jitterRange(0.0)
                .build();
    }

    /**
     * 创建固定间隔重试配置
     */
    public static RetryConfig fixedInterval(int maxRetries, long interval) {
        return RetryConfig.builder()
                .maxRetries(maxRetries)
                .retryInterval(interval)
                .backoffMultiplier(1.0)
                .maxRetryInterval(interval)
                .enabled(true)
                .strategy(RetryStrategy.FIXED_INTERVAL)
                .enableJitter(false)
                .jitterRange(0.0)
                .build();
    }

    /**
     * 创建指数退避重试配置
     */
    public static RetryConfig exponentialBackoff(int maxRetries, long initialInterval, double multiplier) {
        return RetryConfig.builder()
                .maxRetries(maxRetries)
                .retryInterval(initialInterval)
                .backoffMultiplier(multiplier)
                .maxRetryInterval(60000L)
                .enabled(true)
                .strategy(RetryStrategy.EXPONENTIAL_BACKOFF)
                .build();
    }

    /**
     * 创建线性退避重试配置
     */
    public static RetryConfig linearBackoff(int maxRetries, long initialInterval, long increment) {
        return RetryConfig.builder()
                .maxRetries(maxRetries)
                .retryInterval(initialInterval)
                .backoffMultiplier(1.0)
                .maxRetryInterval(initialInterval + increment * maxRetries)
                .enabled(true)
                .strategy(RetryStrategy.LINEAR_BACKOFF)
                .build();
    }

    /**
     * 检查是否应该重试指定的异常
     */
    public boolean shouldRetry(Exception exception) {
        if (!enabled || maxRetries <= 0) {
            return false;
        }

        // 检查不可重试的异常
        if (nonRetryableExceptions != null) {
            for (Class<? extends Exception> nonRetryableType : nonRetryableExceptions) {
                if (nonRetryableType.isAssignableFrom(exception.getClass())) {
                    return false;
                }
            }
        }

        // 检查可重试的异常
        if (retryableExceptions != null && retryableExceptions.length > 0) {
            for (Class<? extends Exception> retryableType : retryableExceptions) {
                if (retryableType.isAssignableFrom(exception.getClass())) {
                    return true;
                }
            }
            return false; // 如果指定了可重试异常类型，但当前异常不在其中
        }

        // 使用自定义重试条件
        if (retryCondition != null) {
            return retryCondition.shouldRetry(exception);
        }

        // 默认对所有异常进行重试
        return true;
    }

    /**
     * 验证配置的有效性
     */
    public void validate() {
        if (maxRetries < 0) {
            throw new IllegalArgumentException("最大重试次数不能为负数");
        }

        if (retryInterval < 0) {
            throw new IllegalArgumentException("重试间隔不能为负数");
        }

        if (backoffMultiplier < 0) {
            throw new IllegalArgumentException("退避倍数不能为负数");
        }

        if (maxRetryInterval < retryInterval) {
            throw new IllegalArgumentException("最大重试间隔不能小于初始重试间隔");
        }
    }

    /**
     * 重试策略枚举
     */
    public enum RetryStrategy {
        /**
         * 不重试
         */
        NO_RETRY,

        /**
         * 固定间隔重试
         */
        FIXED_INTERVAL,

        /**
         * 指数退避重试
         */
        EXPONENTIAL_BACKOFF,

        /**
         * 线性退避重试
         */
        LINEAR_BACKOFF,

        /**
         * 随机间隔重试
         */
        RANDOM_INTERVAL,

        /**
         * 自定义策略
         */
        CUSTOM
    }

    /**
     * 重试条件接口
     */
    @FunctionalInterface
    public interface RetryCondition {
        /**
         * 判断是否应该重试
         *
         * @param exception 异常
         * @return true-应该重试，false-不应该重试
         */
        boolean shouldRetry(Exception exception);
    }

    /**
     * 重试监听器接口
     */
    public interface RetryListener {
        /**
         * 重试开始前调用
         *
         * @param attempt   重试次数（从1开始）
         * @param exception 导致重试的异常
         */
        default void onRetryStart(int attempt, Exception exception) {
        }

        /**
         * 重试成功后调用
         *
         * @param attempt 重试次数
         */
        default void onRetrySuccess(int attempt) {
        }

        /**
         * 重试失败后调用
         *
         * @param attempt   重试次数
         * @param exception 重试失败的异常
         */
        default void onRetryFailure(int attempt, Exception exception) {
        }

        /**
         * 所有重试都失败后调用
         *
         * @param totalAttempts 总重试次数
         * @param lastException 最后一次的异常
         */
        default void onRetryExhausted(int totalAttempts, Exception lastException) {
        }
    }
}
