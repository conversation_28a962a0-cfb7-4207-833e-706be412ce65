package com.tipray.transaction.core.infrastructure.pool;

import com.alibaba.ttl.threadpool.TtlExecutors;
import com.tipray.transaction.core.config.TransactionConfigurationManager;
import com.tipray.transaction.core.config.propertie.TiprayTransactionProperties;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 事务连接池管理器
 * 管理事务处理相关的线程池和连接池
 *
 * <AUTHOR>
 * @version 2.0
 * @since 2025-01-23
 */
public class TransactionConnectionPoolManager {

    private static final Logger log = LoggerFactory.getLogger(TransactionConnectionPoolManager.class);

    @Autowired
    private TransactionConfigurationManager configManager;

    // 事务处理线程池 处理事务内一些异步请求 比如回滚和提交 超时监控等
    private ExecutorService transactionExecutor;

    // 异步任务线程池 用以持久化 和 其它异步任务
    private ExecutorService asyncTaskExecutor;

    // 定时任务调度器 用于持久化 和 其它定时任务
    private ScheduledExecutorService scheduledExecutor;

    @PostConstruct
    public void init() {
        log.info("初始化事务连接池管理器...");

        // 初始化事务处理线程池
        this.transactionExecutor = TtlExecutors.getTtlExecutorService(createTransactionExecutor());

        // 初始化异步任务线程池
        this.asyncTaskExecutor = TtlExecutors.getTtlExecutorService(createAsyncTaskExecutor());

        // 初始化定时任务调度器
        this.scheduledExecutor = TtlExecutors.getTtlScheduledExecutorService(createScheduledExecutor());

        log.info("事务连接池管理器初始化完成 - {}", configManager.getConfigSummary());
    }

    /**
     * 获取事务处理线程池
     */
    public ExecutorService getTransactionExecutor() {
        return transactionExecutor;
    }

    /**
     * 获取异步任务线程池
     */
    public ExecutorService getAsyncTaskExecutor() {
        return asyncTaskExecutor;
    }

    /**
     * 获取定时任务调度器
     */
    public ScheduledExecutorService getScheduledExecutor() {
        return scheduledExecutor;
    }

    /**
     * 提交事务任务
     */
    public <T> Future<T> submitTransactionTask(Callable<T> task) {
        return transactionExecutor.submit(task);
    }

    /**
     * 提交异步任务
     */
    public CompletableFuture<Void> submitAsyncTask(Runnable task) {
        return CompletableFuture.runAsync(task, asyncTaskExecutor);
    }

    /**
     * 调度定时任务
     */
    public ScheduledFuture<?> scheduleTask(Runnable task, long delay, TimeUnit unit) {
        return scheduledExecutor.schedule(task, delay, unit);
    }

    /**
     * 调度周期性任务
     */
    public ScheduledFuture<?> scheduleAtFixedRate(Runnable task, long initialDelay,
                                                  long period, TimeUnit unit) {
        return scheduledExecutor.scheduleAtFixedRate(task, initialDelay, period, unit);
    }

    /**
     * 获取配置管理器
     */
    public TransactionConfigurationManager getConfigManager() {
        return configManager;
    }

    /**
     * 关闭连接池管理器
     */
    @PreDestroy
    public void shutdown() {
        log.info("关闭事务连接池管理器...");

        // 关闭线程池
        shutdownExecutor(transactionExecutor, "TransactionExecutor");
        shutdownExecutor(asyncTaskExecutor, "AsyncTaskExecutor");
        shutdownExecutor(scheduledExecutor, "ScheduledExecutor");

        log.info("事务连接池管理器已关闭");
    }

    // ==================== 私有方法 ====================

    /**
     * 创建事务处理线程池
     * 使用配置管理器获取线程池配置
     */
    private ExecutorService createTransactionExecutor() {
        // 从配置管理器获取线程池配置
        int corePoolSize = configManager.getThreadPoolDefaultCoreSize();
        int maximumPoolSize = configManager.getThreadPoolDefaultMaxSize();
        int queueCapacity = configManager.getThreadPoolDefaultQueueCapacity();
        long keepAliveTime = configManager.getThreadPoolKeepAliveTime();

        log.info("创建事务处理线程池 - 核心线程数: {}, 最大线程数: {}, 队列容量: {}, 保活时间: {}秒",
                corePoolSize, maximumPoolSize, queueCapacity, keepAliveTime / 1000);

        return new ThreadPoolExecutor(
                corePoolSize,
                maximumPoolSize,
                keepAliveTime,
                TimeUnit.MILLISECONDS,
                new LinkedBlockingQueue<>(queueCapacity),
                new TransactionThreadFactory("transaction-executor"),
                new ThreadPoolExecutor.CallerRunsPolicy()
        );
    }

    /**
     * 创建异步任务线程池
     * 使用配置管理器获取异步任务线程池配置
     */
    private ExecutorService createAsyncTaskExecutor() {
        // 从配置管理器获取重试线程池配置（用于异步任务）
        TiprayTransactionProperties.PoolConfig retryConfig = configManager.getRetryThreadPoolConfig();
        long keepAliveTime = configManager.getThreadPoolKeepAliveTime();

        log.info("创建异步任务线程池 - 核心线程数: {}, 最大线程数: {}, 队列容量: {}, 保活时间: {}秒",
                retryConfig.getCoreSize(), retryConfig.getMaxSize(), retryConfig.getQueueCapacity(), keepAliveTime / 1000);

        return new ThreadPoolExecutor(
                retryConfig.getCoreSize(),
                retryConfig.getMaxSize(),
                keepAliveTime,
                TimeUnit.MILLISECONDS,
                new LinkedBlockingQueue<>(retryConfig.getQueueCapacity()),
                new TransactionThreadFactory("async-task-executor"),
                new ThreadPoolExecutor.AbortPolicy()
        );
    }

    /**
     * 创建定时任务调度器
     * 使用配置管理器获取定时任务线程池配置
     */
    private ScheduledExecutorService createScheduledExecutor() {
        // 从配置管理器获取定时任务线程池配置
        TiprayTransactionProperties.PoolConfig scheduledConfig = configManager.getScheduledThreadPoolConfig();

        log.info("创建定时任务调度器 - 核心线程数: {}", scheduledConfig.getCoreSize());

        return new ScheduledThreadPoolExecutor(
                scheduledConfig.getCoreSize(),
                new TransactionThreadFactory("scheduled-executor")
        );
    }

    /**
     * 创建分布式事务重试线程池
     * 专门用于事务提交、回滚等关键操作，使用AbortPolicy拒绝策略
     */
    private ExecutorService createTransactionRetryExecutor() {
        // 从配置管理器获取重试线程池配置
        TiprayTransactionProperties.PoolConfig retryConfig = configManager.getRetryThreadPoolConfig();
        long keepAliveTime = configManager.getThreadPoolKeepAliveTime();

        log.info("创建分布式事务重试线程池 - 核心线程数: {}, 最大线程数: {}, 队列容量: {}, 保活时间: {}秒",
                retryConfig.getCoreSize(), retryConfig.getMaxSize(), retryConfig.getQueueCapacity(), keepAliveTime / 1000);

        return new ThreadPoolExecutor(
                retryConfig.getCoreSize(),
                retryConfig.getMaxSize(),
                keepAliveTime,
                TimeUnit.MILLISECONDS,
                new LinkedBlockingQueue<>(retryConfig.getQueueCapacity()),
                new TransactionThreadFactory("transaction-retry-executor"),
                new ThreadPoolExecutor.AbortPolicy() // 事务重试失败时抛出异常，不能丢弃
        );
    }

    /**
     * 创建业务重试线程池
     * 专门用于业务逻辑重试，使用CallerRunsPolicy确保任务不会丢失
     */
    private ExecutorService createBusinessRetryExecutor() {
        // 从配置管理器获取补偿线程池配置（用于业务重试）
        TiprayTransactionProperties.PoolConfig compensationConfig = configManager.getCompensationThreadPoolConfig();
        long keepAliveTime = configManager.getThreadPoolKeepAliveTime();

        log.info("创建业务重试线程池 - 核心线程数: {}, 最大线程数: {}, 队列容量: {}, 保活时间: {}秒",
                compensationConfig.getCoreSize(), compensationConfig.getMaxSize(),
                compensationConfig.getQueueCapacity(), keepAliveTime / 1000);

        return new ThreadPoolExecutor(
                compensationConfig.getCoreSize(),
                compensationConfig.getMaxSize(),
                keepAliveTime,
                TimeUnit.MILLISECONDS,
                new LinkedBlockingQueue<>(compensationConfig.getQueueCapacity()),
                new TransactionThreadFactory("business-retry-executor"),
                new ThreadPoolExecutor.CallerRunsPolicy() // 业务重试失败时由主线程执行，确保不丢失
        );
    }

    /**
     * 关闭线程池
     */
    private void shutdownExecutor(ExecutorService executor, String name) {
        try {
            executor.shutdown();
            if (!executor.awaitTermination(30, TimeUnit.SECONDS)) {
                executor.shutdownNow();
                if (!executor.awaitTermination(10, TimeUnit.SECONDS)) {
                    log.warn("线程池未能正常关闭: {}", name);
                }
            }
        } catch (InterruptedException e) {
            executor.shutdownNow();
            Thread.currentThread().interrupt();
        }
    }

    /**
     * 事务线程工厂
     */
    private static class TransactionThreadFactory implements ThreadFactory {
        private final AtomicInteger threadNumber = new AtomicInteger(1);
        private final String namePrefix;

        TransactionThreadFactory(String namePrefix) {
            this.namePrefix = "tipray-" + namePrefix + "-";
        }

        @Override
        public Thread newThread(Runnable r) {
            Thread t = new Thread(r, namePrefix + threadNumber.getAndIncrement());
            if (t.isDaemon()) {
                t.setDaemon(false);
            }
            if (t.getPriority() != Thread.NORM_PRIORITY) {
                t.setPriority(Thread.NORM_PRIORITY);
            }
            return t;
        }
    }

    /**
     * 线程池统计信息
     */
    public static class ExecutorStatistics {
        private final int corePoolSize;
        private final int maximumPoolSize;
        private final int activeCount;
        private final int poolSize;
        private final int queueSize;
        private final long completedTaskCount;
        private final long taskCount;

        private ExecutorStatistics(Builder builder) {
            this.corePoolSize = builder.corePoolSize;
            this.maximumPoolSize = builder.maximumPoolSize;
            this.activeCount = builder.activeCount;
            this.poolSize = builder.poolSize;
            this.queueSize = builder.queueSize;
            this.completedTaskCount = builder.completedTaskCount;
            this.taskCount = builder.taskCount;
        }

        public static Builder builder() {
            return new Builder();
        }

        // getters
        public int getCorePoolSize() {
            return corePoolSize;
        }

        public int getMaximumPoolSize() {
            return maximumPoolSize;
        }

        public int getActiveCount() {
            return activeCount;
        }

        public int getPoolSize() {
            return poolSize;
        }

        public int getQueueSize() {
            return queueSize;
        }

        public long getCompletedTaskCount() {
            return completedTaskCount;
        }

        public long getTaskCount() {
            return taskCount;
        }

        @Override
        public String toString() {
            return String.format("ExecutorStats{core=%d, max=%d, active=%d, pool=%d, queue=%d}",
                    corePoolSize, maximumPoolSize, activeCount, poolSize, queueSize);
        }

        public static class Builder {
            private int corePoolSize;
            private int maximumPoolSize;
            private int activeCount;
            private int poolSize;
            private int queueSize;
            private long completedTaskCount;
            private long taskCount;

            public Builder corePoolSize(int corePoolSize) {
                this.corePoolSize = corePoolSize;
                return this;
            }

            public Builder maximumPoolSize(int maximumPoolSize) {
                this.maximumPoolSize = maximumPoolSize;
                return this;
            }

            public Builder activeCount(int activeCount) {
                this.activeCount = activeCount;
                return this;
            }

            public Builder poolSize(int poolSize) {
                this.poolSize = poolSize;
                return this;
            }

            public Builder queueSize(int queueSize) {
                this.queueSize = queueSize;
                return this;
            }

            public Builder completedTaskCount(long completedTaskCount) {
                this.completedTaskCount = completedTaskCount;
                return this;
            }

            public Builder taskCount(long taskCount) {
                this.taskCount = taskCount;
                return this;
            }

            public ExecutorStatistics build() {
                return new ExecutorStatistics(this);
            }
        }
    }

    /**
     * 连接池统计信息
     */
    public static class PoolStatistics {
        private final ExecutorStatistics transactionExecutorStats;
        private final ExecutorStatistics asyncTaskExecutorStats;
        private final ExecutorStatistics scheduledExecutorStats;
        private final ExecutorStatistics transactionRetryExecutorStats;
        private final ExecutorStatistics businessRetryExecutorStats;
        private final Object httpConnectionPoolStats;
        private final Object dbConnectionPoolStats;

        private PoolStatistics(Builder builder) {
            this.transactionExecutorStats = builder.transactionExecutorStats;
            this.asyncTaskExecutorStats = builder.asyncTaskExecutorStats;
            this.scheduledExecutorStats = builder.scheduledExecutorStats;
            this.transactionRetryExecutorStats = builder.transactionRetryExecutorStats;
            this.businessRetryExecutorStats = builder.businessRetryExecutorStats;
            this.httpConnectionPoolStats = builder.httpConnectionPoolStats;
            this.dbConnectionPoolStats = builder.dbConnectionPoolStats;
        }

        public static Builder builder() {
            return new Builder();
        }

        // getters
        public ExecutorStatistics getTransactionExecutorStats() {
            return transactionExecutorStats;
        }

        public ExecutorStatistics getAsyncTaskExecutorStats() {
            return asyncTaskExecutorStats;
        }

        public ExecutorStatistics getScheduledExecutorStats() {
            return scheduledExecutorStats;
        }

        public ExecutorStatistics getTransactionRetryExecutorStats() {
            return transactionRetryExecutorStats;
        }

        public ExecutorStatistics getBusinessRetryExecutorStats() {
            return businessRetryExecutorStats;
        }

        public Object getHttpConnectionPoolStats() {
            return httpConnectionPoolStats;
        }

        public Object getDbConnectionPoolStats() {
            return dbConnectionPoolStats;
        }

        @Override
        public String toString() {
            return String.format("PoolStatistics{txExecutor=%s, asyncExecutor=%s, txRetry=%s, bizRetry=%s}",
                    transactionExecutorStats, asyncTaskExecutorStats,
                    transactionRetryExecutorStats, businessRetryExecutorStats);
        }

        public static class Builder {
            private ExecutorStatistics transactionExecutorStats;
            private ExecutorStatistics asyncTaskExecutorStats;
            private ExecutorStatistics scheduledExecutorStats;
            private ExecutorStatistics transactionRetryExecutorStats;
            private ExecutorStatistics businessRetryExecutorStats;
            private Object httpConnectionPoolStats;
            private Object dbConnectionPoolStats;

            public Builder transactionExecutorStats(ExecutorStatistics transactionExecutorStats) {
                this.transactionExecutorStats = transactionExecutorStats;
                return this;
            }

            public Builder asyncTaskExecutorStats(ExecutorStatistics asyncTaskExecutorStats) {
                this.asyncTaskExecutorStats = asyncTaskExecutorStats;
                return this;
            }

            public Builder scheduledExecutorStats(ExecutorStatistics scheduledExecutorStats) {
                this.scheduledExecutorStats = scheduledExecutorStats;
                return this;
            }

            public Builder transactionRetryExecutorStats(ExecutorStatistics transactionRetryExecutorStats) {
                this.transactionRetryExecutorStats = transactionRetryExecutorStats;
                return this;
            }

            public Builder businessRetryExecutorStats(ExecutorStatistics businessRetryExecutorStats) {
                this.businessRetryExecutorStats = businessRetryExecutorStats;
                return this;
            }

            public Builder httpConnectionPoolStats(Object httpConnectionPoolStats) {
                this.httpConnectionPoolStats = httpConnectionPoolStats;
                return this;
            }

            public Builder dbConnectionPoolStats(Object dbConnectionPoolStats) {
                this.dbConnectionPoolStats = dbConnectionPoolStats;
                return this;
            }

            public PoolStatistics build() {
                return new PoolStatistics(this);
            }
        }
    }
}
