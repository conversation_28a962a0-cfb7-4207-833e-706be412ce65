package com.tipray.transaction.core.application.handler.mode;

import cn.hutool.core.util.StrUtil;
import com.tipray.transaction.core.annotation.DistributedBranchTransaction;
import com.tipray.transaction.core.domain.barrier.BarrierCheckResult;
import com.tipray.transaction.core.domain.barrier.BarrierInfo;
import com.tipray.transaction.core.domain.funcation.StepExecutor;
import com.tipray.transaction.core.domain.transaction.BranchTransactionDO;
import com.tipray.transaction.core.domain.transaction.BranchTransationConfig;
import com.tipray.transaction.core.domain.transaction.TransactionContext;
import com.tipray.transaction.core.enums.TransactionMode;
import com.tipray.transaction.core.infrastructure.barrier.TransactionBarrier;
import com.tipray.transaction.core.infrastructure.retry.RetryConfig;
import com.tipray.transaction.core.infrastructure.retry.RetryManager;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import java.lang.reflect.Method;

/**
 * 抽象事务处理器
 * 提供事务处理的通用逻辑和模板方法
 * AT和Saga处理器都继承此类
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-01-20
 */
public abstract class AbstractTransactionModeHandler implements TransactionModeHandler {

    protected final Logger logger = LoggerFactory.getLogger(getClass());

    @Autowired
    protected RetryManager retryManager;

    @Autowired(required = false)
    protected TransactionBarrier transactionBarrier;

    @Override
    public BranchTransationConfig parseBranchTransationConfig(DistributedBranchTransaction annotation, Method method, TransactionMode mode) {
        BranchTransationConfig config = new BranchTransationConfig();

        config.setStepName(StrUtil.isNotBlank(annotation.value()) ? annotation.value() : method.getName());
        config.setDescription(StrUtil.isNotBlank(annotation.description()) ? annotation.description() : config.getStepName());
        config.setCompensation(annotation.compensation());
        config.setCritical(annotation.critical());
        config.setTimeout(annotation.timeout());
        config.setConnectTimeout(annotation.connectTimeout());
        config.setReadTimeout(annotation.readTimeout());
        config.setRetryCount(annotation.retryCount());
        config.setRetryInterval(annotation.retryInterval());
        config.setTargetService(annotation.targetService());

        return config;
    }

    @Override
    public void clearBranchTransactionResource() {
        // 这边做一些清理资源的操作 目前只有at模式要清理
    }

    @Override
    public Object executeBranch(TransactionContext currentContext, BranchTransactionDO branchTransactionDO, StepExecutor stepExecutor) {
        String transactionId = currentContext.getTransactionId();
        String mode = getSupportedMode().getName();

        try {
            // 屏障检查
            if (!checkBarrierBeforeTry(branchTransactionDO)) {
                // 屏障拦截时返回null,应该返回之前的结果值，先这样吧
                return null;
            }

            // 执行模式特有的前置处理
            beforeBranchTransactionExecution(currentContext, branchTransactionDO);

            // 使用重试机制执行步骤
            Object result = executeBranchWithRetryAndTimeout(currentContext, branchTransactionDO, stepExecutor);

            // 执行模式特有的后置处理
            afterBranchTransactionExecution(currentContext, branchTransactionDO, result);

            return result;

        } catch (Exception e) {
            logger.error("[{}] - [{}] 步骤执行失败: {}", transactionId, mode, e.getMessage(), e);

            // 执行模式特有的异常处理
            onBranchTransactionExecutionFailure(currentContext, branchTransactionDO, e);

            throw e;
        }
    }

    // 执行模式特有的异常处理
    protected void onBranchTransactionExecutionFailure(TransactionContext currentContext, BranchTransactionDO branchTransactionDO, Exception e) {
        // 默认空实现
    }

    /**
     * 执行分支事务 带重试和超时监控的
     */
    private Object executeBranchWithRetryAndTimeout(TransactionContext currentContext, BranchTransactionDO branchTransactionDO, StepExecutor stepExecutor) {
        // 创建重试配置
        RetryConfig retryConfig = getBranchTransactionRetryConfig(branchTransactionDO);

        // 使用RetryManager的业务重试方法，保持原始异常
        return retryManager.executeBusinessWithRetry(stepExecutor::execute, "分支事务执行", retryConfig);
    }

    private RetryConfig getBranchTransactionRetryConfig(BranchTransactionDO branchTransactionDO) {
        // 从步骤配置中获取重试次数，如果没有则使用配置管理器的默认值
        int maxRetries = branchTransactionDO.getRetryCount() != null ? branchTransactionDO.getRetryCount() : 0;

        if (maxRetries <= 0) {
            return RetryConfig.builder()
                    .maxRetries(0)
                    .enabled(false)
                    .build();
        }

        return RetryConfig.exponentialBackoff(maxRetries, 0, 0);
    }

    /**
     * 执行模式特有的后置处理
     */
    protected void afterBranchTransactionExecution(TransactionContext currentContext, BranchTransactionDO branchTransactionDO, Object result) {
        // 默认空实现
    }

    /**
     * 执行模式特有的前置处理
     */
    protected void beforeBranchTransactionExecution(TransactionContext currentContext, BranchTransactionDO branchTransactionDO) {
        // 默认空实现
    }

    @Override
    public void commitBranch(BranchTransactionDO branchTransactionDO) {

        if (branchTransactionDO == null) {
            throw new IllegalArgumentException("分布式事务不能为空");
        }

        String mode = getSupportedMode().getName();
        Long branchTransactionId = branchTransactionDO.getBranchTransactionId();

        logger.info("[{}] - [{}] 开始提交事务 - 处理器: {}",
                branchTransactionId, mode, getHandlerName());

        try {
            // 屏障检查
            if (!checkBarrierBeforeCommit(branchTransactionDO)) {
                return;
            }

            // 提交前置处理
            beforeBranchCommit(branchTransactionDO);

            // 执行具体的提交逻辑
            retryManager.executeWithRetry(() -> {
                doBranchCommit(branchTransactionDO);
                return null;
            }, "分支事务提交", 3);

            // 提交后置处理
            afterBranchCommit(branchTransactionDO);

            logger.info("[{}] - [{}] 事务提交完成", branchTransactionId, mode);

        } catch (Exception e) {
            logger.error("[{}] - [{}] 事务提交异常: {}", branchTransactionId, mode, e.getMessage(), e);

            // 执行提交异常处理
            handleBranchCommitException(branchTransactionDO, e);

            throw e;
        }
    }

    @Override
    public void rollbackBranch(BranchTransactionDO branchTransactionDO, Exception cause) {

        if (branchTransactionDO == null) {
            throw new IllegalArgumentException("分布式事务不能为空");
        }

        Long branchTransactionId = branchTransactionDO.getBranchTransactionId();
        String mode = getSupportedMode().getName();

        try {
            // 屏障检查（不可重写）
            if (!checkBarrierBeforeRollback(branchTransactionDO)) {
                // 屏障拦截，视为成功
                logger.info("[{}] - [{}] 事务回滚被屏障拦截，视为成功", branchTransactionId, mode);
                return;
            }

            // 回滚前置处理
            beforeBranchRollback(branchTransactionDO, cause);

            // 执行回滚
            retryManager.executeWithRetry(() -> {
                doBranchRollback(branchTransactionDO, cause);
                return null;
            }, "分支事务回滚", 3);

            // 回滚后置处理
            afterBranchRollback(branchTransactionDO, cause);

            logger.info("[{}] - [{}] 事务回滚完成", branchTransactionId, mode);

        } catch (Exception e) {
            logger.error("[{}] - [{}] 事务回滚异常: {}", branchTransactionId, mode, e.getMessage(), e);

            // 执行回滚异常处理
            handleBranchRollbackException(branchTransactionDO, e);

            throw e;
        }
    }

    /**
     * 支持回滚
     */
    protected void doBranchRollback(BranchTransactionDO branchTransactionDO, Exception cause) {
        // 回滚失败比较严重，看看是否要抛出异常还是什么其它操作
    }

    /**
     * 处理回滚异常
     */
    protected void handleBranchRollbackException(BranchTransactionDO branchTransactionDO, Exception e) {

    }

    /**
     * 回滚后置处理
     */
    protected void afterBranchRollback(BranchTransactionDO branchTransactionDO, Exception cause) {

    }

    /**
     * 回滚前置处理
     */
    protected void beforeBranchRollback(BranchTransactionDO branchTransactionDO, Exception cause) {

    }

    /**
     * 执行提交
     */
    protected void doBranchCommit(BranchTransactionDO branchTransactionDO) {

    }

    /**
     * 执行提交异常处理
     */
    protected void handleBranchCommitException(BranchTransactionDO branchTransactionDO, Exception e) {

    }

    /**
     * 提交后置处理
     */
    protected void afterBranchCommit(BranchTransactionDO branchTransactionDO) {

    }

    /**
     * 提交前置处理
     */
    protected void beforeBranchCommit(BranchTransactionDO branchTransactionDO) {
    }

    /**
     * 检查是否启用屏障
     */
    protected boolean isBarrierEnabled(BranchTransactionDO branchTransactionDO) {
        return transactionBarrier == null || !branchTransactionDO.isEnableBarrier();
    }

    /**
     * 执行屏障检查
     */
    protected BarrierCheckResult checkBarrier(BranchTransactionDO branchTransactionDO, String operation) {
        if (transactionBarrier == null) {
            return BarrierCheckResult.pass();
        }

        // 构建屏障信息
        BarrierInfo barrierInfo = new BarrierInfo(
                branchTransactionDO.getTransactionId(),
                branchTransactionDO.getBranchTransactionId(),
                operation,
                this.getSupportedMode()
        );

        // 执行屏障检查
        return transactionBarrier.checkBarrier(barrierInfo);
    }

    /**
     * 获取提交操作名称
     */
    protected String getCommitOperation(TransactionMode mode) {
        if (mode != null && mode.isSaga()) {
            // Saga模式没有单独的commit阶段，forward就是最终操作
            // 这里返回confirm是为了屏障记录的一致性，但实际上Saga不会调用commit
            return "confirm";
        } else {
            return "confirm"; // AT模式的确认操作
        }
    }

    /**
     * 获取回滚操作名称
     */
    protected String getRollbackOperation(TransactionMode mode) {
        if (mode != null && mode.isSaga()) {
            return "compensate"; // Saga模式的补偿操作
        } else {
            return "cancel"; // AT模式的取消操作
        }
    }


    /**
     * 提交前屏障检查（final方法，防止子类重写）
     *
     * @param branchTransactionDO 分支事务
     * @return true-通过检查，false-被屏障拦截
     */
    private boolean checkBarrierBeforeCommit(BranchTransactionDO branchTransactionDO) {
        if (isBarrierEnabled(branchTransactionDO)) {
            return true; // 未启用屏障，直接通过
        }

        // Saga模式没有commit阶段，跳过屏障检查
        if (this.getSupportedMode().isSaga()) {
            return true;
        }

        String operation = getCommitOperation(this.getSupportedMode());
        BarrierCheckResult barrierResult = checkBarrier(branchTransactionDO, operation);

        if (barrierResult.isBlocked()) {
            logger.info("[{}] {}操作被屏障拦截 - 分支ID: {}, 状态: {}, 消息: {}",
                    branchTransactionDO.getTransactionId(), operation,
                    branchTransactionDO.getBranchTransactionId(),
                    barrierResult.getStatus(), barrierResult.getMessage());
            return false; // 被屏障拦截
        }

        return true; // 通过屏障检查
    }

    /**
     * 回滚前屏障检查（final方法，防止子类重写）
     *
     * @param branchTransactionDO 分支事务
     * @return true-通过检查，false-被屏障拦截
     */
    private boolean checkBarrierBeforeRollback(BranchTransactionDO branchTransactionDO) {
        if (isBarrierEnabled(branchTransactionDO)) {
            return true; // 未启用屏障，直接通过
        }

        String operation = getRollbackOperation(this.getSupportedMode());
        BarrierCheckResult barrierResult = checkBarrier(branchTransactionDO, operation);

        if (barrierResult.isBlocked()) {
            logger.info("[{}] {}操作被屏障拦截 - 分支ID: {}, 状态: {}, 消息: {}",
                    branchTransactionDO.getTransactionId(), operation,
                    branchTransactionDO.getBranchTransactionId(),
                    barrierResult.getStatus(), barrierResult.getMessage());
            return false; // 被屏障拦截
        }

        return true; // 通过屏障检查
    }

    /**
     * try操作前屏障检查（final方法，防止子类重写）
     *
     * @param branchTransactionDO 分支事务
     * @return true-通过检查，false-被屏障拦截
     */
    private boolean checkBarrierBeforeTry(BranchTransactionDO branchTransactionDO) {
        if (isBarrierEnabled(branchTransactionDO)) {
            return true; // 未启用屏障，直接通过
        }

        String operation = getTryOperation(this.getSupportedMode());
        BarrierCheckResult barrierResult = checkBarrier(branchTransactionDO, operation);

        if (barrierResult.isBlocked()) {
            logger.info("[{}] {}操作被屏障拦截 - 分支ID: {}, 状态: {}, 消息: {}",
                    branchTransactionDO.getTransactionId(), operation,
                    branchTransactionDO.getBranchTransactionId(),
                    barrierResult.getStatus(), barrierResult.getMessage());
            return false; // 被屏障拦截
        }

        return true; // 通过屏障检查
    }

    /**
     * 获取try操作名称
     */
    protected String getTryOperation(TransactionMode mode) {
        if (mode != null && mode.isSaga()) {
            return "forward"; // Saga模式的正向操作就是try操作
        } else {
            return "try"; // AT模式的try操作
        }
    }

}
