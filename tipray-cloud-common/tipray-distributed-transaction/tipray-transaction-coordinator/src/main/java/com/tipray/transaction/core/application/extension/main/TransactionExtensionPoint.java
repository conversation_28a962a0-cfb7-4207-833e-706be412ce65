package com.tipray.transaction.core.application.extension.main;

/**
 * 事务扩展点枚举
 * 定义事务生命周期中的扩展点
 *
 * <AUTHOR>
 * @version 2.0
 * @since 2025-01-23
 */
public enum TransactionExtensionPoint {

    /**
     * 事务开始前
     * 在事务开始之前执行，可以进行预处理、参数验证等
     */
    BEFORE_TRANSACTION("事务开始前", "在事务开始之前执行，可以进行预处理、参数验证等"),

    /**
     * 事务开始后
     * 在事务开始之后执行，可以进行初始化、日志记录等
     */
    AFTER_TRANSACTION_BEGIN("事务开始后", "在事务开始之后执行，可以进行初始化、日志记录等"),

    /**
     * 分支事务注册前
     * 在分支事务注册之前执行，可以进行分支事务预处理
     */
    BEFORE_BRANCH_REGISTER("分支事务注册前", "在分支事务注册之前执行，可以进行分支事务预处理"),

    /**
     * 分支事务注册后
     * 在分支事务注册之后执行，可以进行分支事务后处理
     */
    AFTER_BRANCH_REGISTER("分支事务注册后", "在分支事务注册之后执行，可以进行分支事务后处理"),

    /**
     * 事务提交前
     * 在事务提交之前执行，可以进行提交前检查、数据验证等
     */
    BEFORE_COMMIT("事务提交前", "在事务提交之前执行，可以进行提交前检查、数据验证等"),

    /**
     * 事务提交后
     * 在事务提交之后执行，可以进行后续处理、通知发送等
     */
    AFTER_COMMIT("事务提交后", "在事务提交之后执行，可以进行后续处理、通知发送等"),

    /**
     * 事务回滚前
     * 在事务回滚之前执行，可以进行回滚前处理、数据备份等
     */
    BEFORE_ROLLBACK("事务回滚前", "在事务回滚之前执行，可以进行回滚前处理、数据备份等"),

    /**
     * 事务回滚后
     * 在事务回滚之后执行，可以进行清理、通知发送等
     */
    AFTER_ROLLBACK("事务回滚后", "在事务回滚之后执行，可以进行清理、通知发送等"),

    /**
     * 事务完成后
     * 在事务完成后执行（无论提交还是回滚），可以进行资源清理、统计等
     */
    AFTER_COMPLETION("事务完成后", "在事务完成后执行（无论提交还是回滚），可以进行资源清理、统计等"),

    /**
     * 事务超时处理
     * 在事务超时时执行，可以进行超时处理、告警等
     */
    ON_TIMEOUT("事务超时处理", "在事务超时时执行，可以进行超时处理、告警等"),

    /**
     * 事务异常处理
     * 在事务发生异常时执行，可以进行异常处理、日志记录等
     */
    ON_EXCEPTION("事务异常处理", "在事务发生异常时执行，可以进行异常处理、日志记录等");

    private final String displayName;
    private final String description;

    TransactionExtensionPoint(String displayName, String description) {
        this.displayName = displayName;
        this.description = description;
    }

    /**
     * 判断是否为事务开始相关扩展点
     */
    public boolean isTransactionBeginRelated() {
        return this == BEFORE_TRANSACTION || this == AFTER_TRANSACTION_BEGIN;
    }

    /**
     * 判断是否为分支事务相关扩展点
     */
    public boolean isBranchTransactionRelated() {
        return this == BEFORE_BRANCH_REGISTER || this == AFTER_BRANCH_REGISTER;
    }

    /**
     * 判断是否为提交相关扩展点
     */
    public boolean isCommitRelated() {
        return this == BEFORE_COMMIT || this == AFTER_COMMIT;
    }

    /**
     * 判断是否为回滚相关扩展点
     */
    public boolean isRollbackRelated() {
        return this == BEFORE_ROLLBACK || this == AFTER_ROLLBACK;
    }

    /**
     * 判断是否为完成相关扩展点
     */
    public boolean isCompletionRelated() {
        return this == AFTER_COMPLETION;
    }

    /**
     * 判断是否为异常处理相关扩展点
     */
    public boolean isExceptionRelated() {
        return this == ON_TIMEOUT || this == ON_EXCEPTION;
    }

    /**
     * 判断是否为前置扩展点
     */
    public boolean isBeforeExtensionPoint() {
        return this == BEFORE_TRANSACTION || this == BEFORE_BRANCH_REGISTER ||
                this == BEFORE_COMMIT || this == BEFORE_ROLLBACK;
    }

    /**
     * 判断是否为后置扩展点
     */
    public boolean isAfterExtensionPoint() {
        return this == AFTER_TRANSACTION_BEGIN || this == AFTER_BRANCH_REGISTER ||
                this == AFTER_COMMIT || this == AFTER_ROLLBACK || this == AFTER_COMPLETION;
    }

    /**
     * 获取对应的后置扩展点
     */
    public TransactionExtensionPoint getCorrespondingAfterPoint() {
        switch (this) {
            case BEFORE_TRANSACTION:
                return AFTER_TRANSACTION_BEGIN;
            case BEFORE_BRANCH_REGISTER:
                return AFTER_BRANCH_REGISTER;
            case BEFORE_COMMIT:
                return AFTER_COMMIT;
            case BEFORE_ROLLBACK:
                return AFTER_ROLLBACK;
            default:
                return null;
        }
    }

    /**
     * 获取对应的前置扩展点
     */
    public TransactionExtensionPoint getCorrespondingBeforePoint() {
        switch (this) {
            case AFTER_TRANSACTION_BEGIN:
                return BEFORE_TRANSACTION;
            case AFTER_BRANCH_REGISTER:
                return BEFORE_BRANCH_REGISTER;
            case AFTER_COMMIT:
                return BEFORE_COMMIT;
            case AFTER_ROLLBACK:
                return BEFORE_ROLLBACK;
            default:
                return null;
        }
    }

    // getters
    public String getDisplayName() {
        return displayName;
    }

    public String getDescription() {
        return description;
    }

    @Override
    public String toString() {
        return displayName;
    }
}
