package com.tipray.transaction.core.infrastructure.async;

import org.springframework.transaction.support.TransactionSynchronization;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Map;

/**
 * Spring事务上下文封装类
 * 用于在异步线程间传递Spring事务管理器的上下文信息
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-01-30
 */
@Data
@Slf4j
public class SpringTransactionContext {

    /**
     * 事务资源映射（数据库连接等）
     */
    private final Map<Object, Object> resourceMap;

    /**
     * 事务同步器列表
     */
    private final List<TransactionSynchronization> synchronizations;

    /**
     * 当前事务名称
     */
    private final String transactionName;

    /**
     * 是否只读事务
     */
    private final boolean readOnly;

    /**
     * 事务隔离级别
     */
    private final Integer isolationLevel;

    /**
     * 是否存在实际的事务
     */
    private final boolean actualTransactionActive;

    /**
     * 上下文是否有效
     */
    private final boolean valid;

    /**
     * 完整构造函数
     */
    public SpringTransactionContext(Map<Object, Object> resourceMap,
                                    List<TransactionSynchronization> synchronizations,
                                    String transactionName,
                                    boolean readOnly,
                                    Integer isolationLevel,
                                    boolean actualTransactionActive) {
        this.resourceMap = resourceMap;
        this.synchronizations = synchronizations;
        this.transactionName = transactionName;
        this.readOnly = readOnly;
        this.isolationLevel = isolationLevel;
        this.actualTransactionActive = actualTransactionActive;
        this.valid = true;
    }

    /**
     * 空构造函数（用于异常情况）
     */
    public SpringTransactionContext() {
        this.resourceMap = null;
        this.synchronizations = null;
        this.transactionName = null;
        this.readOnly = false;
        this.isolationLevel = null;
        this.actualTransactionActive = false;
        this.valid = false;
    }

    /**
     * 检查上下文是否有效
     */
    public boolean isValid() {
        return valid;
    }

    /**
     * 检查是否有事务资源
     */
    public boolean hasResources() {
        return resourceMap != null && !resourceMap.isEmpty();
    }

    /**
     * 检查是否有事务同步器
     */
    public boolean hasSynchronizations() {
        return synchronizations != null && !synchronizations.isEmpty();
    }

    /**
     * 获取资源数量
     */
    public int getResourceCount() {
        return resourceMap != null ? resourceMap.size() : 0;
    }

    /**
     * 获取同步器数量
     */
    public int getSynchronizationCount() {
        return synchronizations != null ? synchronizations.size() : 0;
    }

    @Override
    public String toString() {
        if (!valid) {
            return "SpringTransactionContext{invalid}";
        }

        return String.format("SpringTransactionContext{" +
                        "transactionName='%s', " +
                        "readOnly=%s, " +
                        "isolationLevel=%s, " +
                        "actualTransactionActive=%s, " +
                        "resourceCount=%d, " +
                        "synchronizationCount=%d}",
                transactionName,
                readOnly,
                isolationLevel,
                actualTransactionActive,
                getResourceCount(),
                getSynchronizationCount());
    }
}
