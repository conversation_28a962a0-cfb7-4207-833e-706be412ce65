//package com.tipray.transaction.core.infrastructure.fallback;
//
//import com.tipray.transaction.core.enums.BranchTransactionStatus;
//import com.tipray.transaction.core.enums.TransactionMode;
//import com.tipray.transaction.core.enums.TransactionStatus;
//import com.tipray.transaction.core.domain.transaction.BranchTransactionDO;
//import com.tipray.transaction.core.domain.transaction.TransactionContext;
//import com.tipray.transaction.core.infrastructure.event.TransactionEventPublisherImpl;
//import com.tipray.transaction.core.infrastructure.metrics.TransactionMetricsCollector;
//import com.tipray.transaction.core.persistence.TransactionStorage;
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//import org.springframework.stereotype.Component;
//
//import java.time.LocalDateTime;
//import java.util.ArrayList;
//import java.util.HashMap;
//import java.util.List;
//import java.util.Map;
//import java.util.concurrent.ConcurrentHashMap;
//import java.util.concurrent.atomic.AtomicLong;
//
/// **
// * 事务回滚异常降级处理器
// * 处理事务整体回滚异常和分支事务回滚异常的降级逻辑
// * 收集完整的异常信息，提供人工干预和自动重试机制
// *
// * <AUTHOR>
// * @version 2.0
// * @since 2025-01-23
// */
//@Component
//public class TransactionRollbackFallbackHandler {
//
//    private static final Logger log = LoggerFactory.getLogger(TransactionRollbackFallbackHandler.class);
//
//    private final TransactionStorage transactionStorage;
//    private final TransactionEventPublisherImpl eventPublisher;
//    private final TransactionMetricsCollector metricsCollector;
//
//    // 失败记录存储
//    private final Map<String, GlobalRollbackFailureRecord> globalFailures = new ConcurrentHashMap<>();
//    private final Map<String, List<BranchRollbackFailureRecord>> branchFailures = new ConcurrentHashMap<>();
//
//    // 统计计数器
//    private final AtomicLong globalFailureCount = new AtomicLong(0);
//    private final AtomicLong branchFailureCount = new AtomicLong(0);
//    private final AtomicLong recoveredCount = new AtomicLong(0);
//
//    public TransactionRollbackFallbackHandler(TransactionStorage transactionStorage,
//                                              TransactionEventPublisherImpl eventPublisher,
//                                              TransactionMetricsCollector metricsCollector) {
//        this.transactionStorage = transactionStorage;
//        this.eventPublisher = eventPublisher;
//        this.metricsCollector = metricsCollector;
//    }
//
//    /**
//     * 处理事务整体回滚异常
//     *
//     * @param context           事务上下文
//     * @param originalException 原始异常
//     * @param rollbackException 回滚异常
//     * @return 降级处理结果
//     */
//    public GlobalRollbackFallbackResult handleGlobalRollbackFailure(TransactionContext context,
//                                                                    Throwable originalException,
//                                                                    Throwable rollbackException) {
//        String transactionId = context.getTransactionId();
//
//        log.error("事务整体回滚失败，启动降级处理: transactionId={}", transactionId, rollbackException);
//
//        // 创建失败记录
//        GlobalRollbackFailureRecord failureRecord = createGlobalFailureRecord(context, originalException, rollbackException);
//        globalFailures.put(transactionId, failureRecord);
//        globalFailureCount.incrementAndGet();
//
//        // 记录指标
//        metricsCollector.recordGlobalRollbackFailure(context.getMode(), rollbackException);
//
//        // 发布事件
//        eventPublisher.publishGlobalRollbackFailed(transactionId, failureRecord);
//
//        // 执行降级策略
//        FallbackStrategy strategy = determineFallbackStrategy(failureRecord);
//        GlobalRollbackFallbackResult result = executeFallbackStrategy(failureRecord, strategy);
//
//        log.warn("事务整体回滚降级处理完成: transactionId={}, 策略={}, 结果={}",
//                transactionId, strategy, result.getStatus());
//
//        return result;
//    }
//
//    /**
//     * 处理分支事务回滚异常
//     *
//     * @param context           事务上下文
//     * @param branchTransaction 分支事务
//     * @param rollbackException 回滚异常
//     * @return 降级处理结果
//     */
//    public BranchRollbackFallbackResult handleBranchRollbackFailure(TransactionContext context,
//                                                                    BranchTransactionDO branchTransaction,
//                                                                    Throwable rollbackException) {
//        String transactionId = context.getTransactionId();
//        Long branchId = branchTransaction.getBranchTransactionId();
//
//        log.error("分支事务回滚失败，启动降级处理: transactionId={}, branchId={}",
//                transactionId, branchId, rollbackException);
//
//        // 创建失败记录
//        BranchRollbackFailureRecord failureRecord = createBranchFailureRecord(context, branchTransaction, rollbackException);
//
//        // 存储失败记录
//        branchFailures.computeIfAbsent(transactionId, k -> new ArrayList<>()).add(failureRecord);
//        branchFailureCount.incrementAndGet();
//
//        // 记录指标
//        metricsCollector.recordBranchRollbackFailure(context.getMode(), branchTransaction.getTargetService(), rollbackException);
//
//        // 发布事件
//        eventPublisher.publishBranchRollbackFailed(transactionId, branchId, failureRecord);
//
//        // 执行降级策略
//        FallbackStrategy strategy = determineBranchFallbackStrategy(failureRecord);
//        BranchRollbackFallbackResult result = executeBranchFallbackStrategy(failureRecord, strategy);
//
//        log.warn("分支事务回滚降级处理完成: transactionId={}, branchId={}, 策略={}, 结果={}",
//                transactionId, branchId, strategy, result.getStatus());
//
//        return result;
//    }
//
//    /**
//     * 创建全局回滚失败记录
//     */
//    private GlobalRollbackFailureRecord createGlobalFailureRecord(TransactionContext context,
//                                                                  Throwable originalException,
//                                                                  Throwable rollbackException) {
//        return GlobalRollbackFailureRecord.builder()
//                .transactionId(context.getTransactionId())
//                .transactionMode(context.getMode())
//                .transactionStatus(context.getStatus())
//                .applicationName(context.getApplicationName())
//                .methodSignature(context.getMethodSignature())
//                .originalException(ExceptionInfo.from(originalException))
//                .rollbackException(ExceptionInfo.from(rollbackException))
//                .failureTime(LocalDateTime.now())
//                .retryCount(0)
//                .maxRetryCount(getMaxRetryCount(context.getMode()))
//                .contextSnapshot(captureContextSnapshot(context))
//                .build();
//    }
//
//    /**
//     * 创建分支回滚失败记录
//     */
//    private BranchRollbackFailureRecord createBranchFailureRecord(TransactionContext context,
//                                                                  BranchTransactionDO branchTransaction,
//                                                                  Throwable rollbackException) {
//        return BranchRollbackFailureRecord.builder()
//                .transactionId(context.getTransactionId())
//                .branchId(branchTransaction.getBranchTransactionId())
//                .transactionMode(context.getMode())
//                .branchStatus(branchTransaction.getStatus())
//                .targetService(branchTransaction.getTargetService())
//                .targetMethod(branchTransaction.getTargetMethod())
//                .rollbackUrl(branchTransaction.getRollbackUrl())
//                .rollbackException(ExceptionInfo.from(rollbackException))
//                .failureTime(LocalDateTime.now())
//                .retryCount(0)
//                .maxRetryCount(getMaxRetryCount(context.getMode()))
//                .branchSnapshot(captureBranchSnapshot(branchTransaction))
//                .build();
//    }
//
//    /**
//     * 确定降级策略
//     */
//    private FallbackStrategy determineFallbackStrategy(GlobalRollbackFailureRecord record) {
//        // 根据异常类型、重试次数、事务模式等确定策略
//        if (record.getRetryCount() < record.getMaxRetryCount()) {
//            if (isRetryableException(record.getRollbackException())) {
//                return FallbackStrategy.RETRY;
//            }
//        }
//
//        if (record.getTransactionMode() == TransactionMode.AT) {
//            return FallbackStrategy.MANUAL_INTERVENTION;
//        } else {
//            return FallbackStrategy.COMPENSATION_REQUIRED;
//        }
//    }
//
//    /**
//     * 确定分支降级策略
//     */
//    private FallbackStrategy determineBranchFallbackStrategy(BranchRollbackFailureRecord record) {
//        if (record.getRetryCount() < record.getMaxRetryCount()) {
//            if (isRetryableException(record.getRollbackException())) {
//                return FallbackStrategy.RETRY;
//            }
//        }
//
//        if (isNetworkException(record.getRollbackException())) {
//            return FallbackStrategy.DELAYED_RETRY;
//        }
//
//        return FallbackStrategy.MANUAL_INTERVENTION;
//    }
//
//    /**
//     * 执行全局降级策略
//     */
//    private GlobalRollbackFallbackResult executeFallbackStrategy(GlobalRollbackFailureRecord record,
//                                                                 FallbackStrategy strategy) {
//        switch (strategy) {
//            case RETRY:
//                return executeGlobalRetry(record);
//            case DELAYED_RETRY:
//                return scheduleGlobalDelayedRetry(record);
//            case MANUAL_INTERVENTION:
//                return markForManualIntervention(record);
//            case COMPENSATION_REQUIRED:
//                return markForCompensation(record);
//            default:
//                return GlobalRollbackFallbackResult.failed("未知的降级策略: " + strategy);
//        }
//    }
//
//    /**
//     * 执行分支降级策略
//     */
//    private BranchRollbackFallbackResult executeBranchFallbackStrategy(BranchRollbackFailureRecord record,
//                                                                       FallbackStrategy strategy) {
//        switch (strategy) {
//            case RETRY:
//                return executeBranchRetry(record);
//            case DELAYED_RETRY:
//                return scheduleBranchDelayedRetry(record);
//            case MANUAL_INTERVENTION:
//                return markBranchForManualIntervention(record);
//            default:
//                return BranchRollbackFallbackResult.failed("未知的降级策略: " + strategy);
//        }
//    }
//
//    /**
//     * 执行全局重试
//     */
//    private GlobalRollbackFallbackResult executeGlobalRetry(GlobalRollbackFailureRecord record) {
//        record.incrementRetryCount();
//
//        try {
//            // TODO: 实际的重试逻辑，调用事务协调器重新执行回滚
//            log.info("执行全局事务回滚重试: transactionId={}, 重试次数={}",
//                    record.getTransactionId(), record.getRetryCount());
//
//            // 模拟重试成功
//            recoveredCount.incrementAndGet();
//            globalFailures.remove(record.getTransactionId());
//
//            return GlobalRollbackFallbackResult.success("重试成功");
//
//        } catch (Exception e) {
//            log.error("全局事务回滚重试失败: transactionId={}", record.getTransactionId(), e);
//            record.addRetryException(ExceptionInfo.from(e));
//
//            if (record.getRetryCount() >= record.getMaxRetryCount()) {
//                return markForManualIntervention(record);
//            }
//
//            return GlobalRollbackFallbackResult.retryLater("重试失败，稍后再试");
//        }
//    }
//
//    /**
//     * 调度全局延迟重试
//     */
//    private GlobalRollbackFallbackResult scheduleGlobalDelayedRetry(GlobalRollbackFailureRecord record) {
//        // TODO: 实现延迟重试调度逻辑
//        log.info("调度全局事务延迟重试: transactionId={}, 延迟={}秒",
//                record.getTransactionId(), calculateRetryDelay(record.getRetryCount()));
//
//        return GlobalRollbackFallbackResult.scheduled("已调度延迟重试");
//    }
//
//    /**
//     * 标记为需要人工干预
//     */
//    private GlobalRollbackFallbackResult markForManualIntervention(GlobalRollbackFailureRecord record) {
//        record.setRequiresManualIntervention(true);
//
//        // 发送告警
//        sendAlert("全局事务回滚失败需要人工干预", record);
//
//        log.error("全局事务回滚失败，标记为需要人工干预: transactionId={}", record.getTransactionId());
//
//        return GlobalRollbackFallbackResult.manualIntervention("需要人工干预");
//    }
//
//    /**
//     * 标记为需要补偿
//     */
//    private GlobalRollbackFallbackResult markForCompensation(GlobalRollbackFailureRecord record) {
//        record.setRequiresCompensation(true);
//
//        log.warn("Saga事务回滚失败，标记为需要补偿: transactionId={}", record.getTransactionId());
//
//        return GlobalRollbackFallbackResult.compensationRequired("需要执行补偿逻辑");
//    }
//
//    /**
//     * 执行分支重试
//     */
//    private BranchRollbackFallbackResult executeBranchRetry(BranchRollbackFailureRecord record) {
//        record.incrementRetryCount();
//
//        try {
//            // TODO: 实际的分支重试逻辑
//            log.info("执行分支事务回滚重试: transactionId={}, branchId={}, 重试次数={}",
//                    record.getTransactionId(), record.getBranchId(), record.getRetryCount());
//
//            // 模拟重试成功
//            recoveredCount.incrementAndGet();
//            removeBranchFailure(record.getTransactionId(), record.getBranchId());
//
//            return BranchRollbackFallbackResult.success("重试成功");
//
//        } catch (Exception e) {
//            log.error("分支事务回滚重试失败: transactionId={}, branchId={}",
//                    record.getTransactionId(), record.getBranchId(), e);
//            record.addRetryException(ExceptionInfo.from(e));
//
//            if (record.getRetryCount() >= record.getMaxRetryCount()) {
//                return markBranchForManualIntervention(record);
//            }
//
//            return BranchRollbackFallbackResult.retryLater("重试失败，稍后再试");
//        }
//    }
//
//    /**
//     * 调度分支延迟重试
//     */
//    private BranchRollbackFallbackResult scheduleBranchDelayedRetry(BranchRollbackFailureRecord record) {
//        // TODO: 实现延迟重试调度逻辑
//        log.info("调度分支事务延迟重试: transactionId={}, branchId={}, 延迟={}秒",
//                record.getTransactionId(), record.getBranchId(), calculateRetryDelay(record.getRetryCount()));
//
//        return BranchRollbackFallbackResult.scheduled("已调度延迟重试");
//    }
//
//    /**
//     * 标记分支为需要人工干预
//     */
//    private BranchRollbackFallbackResult markBranchForManualIntervention(BranchRollbackFailureRecord record) {
//        record.setRequiresManualIntervention(true);
//
//        // 发送告警
//        sendAlert("分支事务回滚失败需要人工干预", record);
//
//        log.error("分支事务回滚失败，标记为需要人工干预: transactionId={}, branchId={}",
//                record.getTransactionId(), record.getBranchId());
//
//        return BranchRollbackFallbackResult.manualIntervention("需要人工干预");
//    }
//
//    // ==================== 工具方法 ====================
//
//    /**
//     * 判断是否为可重试异常
//     */
//    private boolean isRetryableException(ExceptionInfo exceptionInfo) {
//        String exceptionType = exceptionInfo.getExceptionType();
//        return exceptionType.contains("TimeoutException") ||
//                exceptionType.contains("ConnectException") ||
//                exceptionType.contains("SocketTimeoutException") ||
//                exceptionType.contains("HttpRetryException");
//    }
//
//    /**
//     * 判断是否为网络异常
//     */
//    private boolean isNetworkException(ExceptionInfo exceptionInfo) {
//        String exceptionType = exceptionInfo.getExceptionType();
//        return exceptionType.contains("ConnectException") ||
//                exceptionType.contains("SocketException") ||
//                exceptionType.contains("UnknownHostException") ||
//                exceptionType.contains("NoRouteToHostException");
//    }
//
//    /**
//     * 获取最大重试次数
//     */
//    private int getMaxRetryCount(TransactionMode mode) {
//        switch (mode) {
//            case AT:
//                return 3; // AT模式重试3次
//            case SAGA:
//                return 5; // Saga模式重试5次
//            default:
//                return 2;
//        }
//    }
//
//    /**
//     * 计算重试延迟时间（秒）
//     */
//    private long calculateRetryDelay(int retryCount) {
//        // 指数退避算法：2^retryCount * 基础延迟
//        return Math.min((long) Math.pow(2, retryCount) * 5, 300); // 最大5分钟
//    }
//
//    /**
//     * 捕获事务上下文快照
//     */
//    private Map<String, Object> captureContextSnapshot(TransactionContext context) {
//        Map<String, Object> snapshot = new HashMap<>();
//        snapshot.put("transactionId", context.getTransactionId());
//        snapshot.put("mode", context.getMode());
//        snapshot.put("status", context.getStatus());
//        snapshot.put("timeout", context.getTimeout());
//        snapshot.put("applicationName", context.getApplicationName());
//        snapshot.put("methodSignature", context.getMethodSignature());
//        snapshot.put("startTime", context.getStartTime());
//        snapshot.put("asyncCommitOrRollback", context.isAsyncCommitOrRollback());
//        return snapshot;
//    }
//
//    /**
//     * 捕获分支事务快照
//     */
//    private Map<String, Object> captureBranchSnapshot(BranchTransactionDO branchTransaction) {
//        Map<String, Object> snapshot = new HashMap<>();
//        snapshot.put("branchId", branchTransaction.getBranchTransactionId());
//        snapshot.put("targetService", branchTransaction.getTargetService());
//        snapshot.put("targetMethod", branchTransaction.getTargetMethod());
//        snapshot.put("status", branchTransaction.getStatus());
//        snapshot.put("rollbackUrl", branchTransaction.getRollbackUrl());
//        snapshot.put("requestData", branchTransaction.getRequestData());
//        snapshot.put("result", branchTransaction.getResult());
//        return snapshot;
//    }
//
//    /**
//     * 移除分支失败记录
//     */
//    private void removeBranchFailure(String transactionId, Long branchId) {
//        List<BranchRollbackFailureRecord> failures = branchFailures.get(transactionId);
//        if (failures != null) {
//            failures.removeIf(record -> record.getBranchId().equals(branchId));
//            if (failures.isEmpty()) {
//                branchFailures.remove(transactionId);
//            }
//        }
//    }
//
//    /**
//     * 发送告警
//     */
//    private void sendAlert(String message, Object record) {
//        // TODO: 实现告警发送逻辑（邮件、短信、钉钉等）
//        log.error("发送告警: {} - {}", message, record);
//    }
//
//    // ==================== 查询和管理方法 ====================
//
//    /**
//     * 获取全局失败记录
//     */
//    public GlobalRollbackFailureRecord getGlobalFailureRecord(String transactionId) {
//        return globalFailures.get(transactionId);
//    }
//
//    /**
//     * 获取分支失败记录
//     */
//    public List<BranchRollbackFailureRecord> getBranchFailureRecords(String transactionId) {
//        return branchFailures.getOrDefault(transactionId, new ArrayList<>());
//    }
//
//    /**
//     * 获取所有需要人工干预的记录
//     */
//    public List<GlobalRollbackFailureRecord> getManualInterventionRecords() {
//        return globalFailures.values().stream()
//                .filter(GlobalRollbackFailureRecord::isRequiresManualIntervention)
//                .collect(java.util.stream.Collectors.toList());
//    }
//
//    /**
//     * 获取统计信息
//     */
//    public FallbackStatistics getStatistics() {
//        return FallbackStatistics.builder()
//                .globalFailureCount(globalFailureCount.get())
//                .branchFailureCount(branchFailureCount.get())
//                .recoveredCount(recoveredCount.get())
//                .pendingGlobalFailures(globalFailures.size())
//                .pendingBranchFailures(branchFailures.values().stream().mapToInt(List::size).sum())
//                .manualInterventionCount(getManualInterventionRecords().size())
//                .build();
//    }
//}
