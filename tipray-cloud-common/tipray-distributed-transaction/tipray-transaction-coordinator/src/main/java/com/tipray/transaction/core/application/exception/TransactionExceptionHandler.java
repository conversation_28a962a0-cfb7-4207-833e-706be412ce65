package com.tipray.transaction.core.application.exception;

import com.tipray.transaction.core.domain.transaction.TransactionContext;
import com.tipray.transaction.core.exception.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 事务异常处理器
 * 处理事务执行过程中的异常和回滚规则
 *
 * <AUTHOR>
 * @version 2.0
 * @since 2025-01-23
 */
public class TransactionExceptionHandler {

    private static final Logger log = LoggerFactory.getLogger(TransactionExceptionHandler.class);

    // 全局回滚规则缓存
    private final Map<String, ExceptionRollbackRule> globalRollbackRules = new ConcurrentHashMap<>();

    // 方法级回滚规则缓存
    private final Map<String, ExceptionRollbackRule> methodRollbackRules = new ConcurrentHashMap<>();

    public TransactionExceptionHandler() {
        initializeDefaultRules();
    }

    /**
     * 判断是否需要为指定异常回滚
     */
    public boolean shouldRollback(Throwable throwable, String methodSignature) {
        if (throwable == null) {
            return false;
        }

        // 1. 检查方法级规则
        ExceptionRollbackRule methodRule = methodRollbackRules.get(methodSignature);
        if (methodRule != null) {
            Boolean result = methodRule.shouldRollback(throwable);
            if (result != null) {
                log.debug("方法级回滚规则匹配: {} -> {}", throwable.getClass().getSimpleName(), result);
                return result;
            }
        }

        // 2. 检查全局规则
        for (ExceptionRollbackRule globalRule : globalRollbackRules.values()) {
            Boolean result = globalRule.shouldRollback(throwable);
            if (result != null) {
                log.debug("全局回滚规则匹配: {} -> {}", throwable.getClass().getSimpleName(), result);
                return result;
            }
        }

        // 3. 使用默认规则
        boolean defaultResult = isDefaultRollbackException(throwable);
        log.debug("使用默认回滚规则: {} -> {}", throwable.getClass().getSimpleName(), defaultResult);
        return defaultResult;
    }

    /**
     * 注册全局回滚规则
     */
    public void registerGlobalRollbackRule(String ruleName, ExceptionRollbackRule rule) {
        globalRollbackRules.put(ruleName, rule);
        log.info("注册全局回滚规则: {}", ruleName);
    }

    /**
     * 注册方法级回滚规则
     */
    public void registerMethodRollbackRule(String methodSignature, ExceptionRollbackRule rule) {
        methodRollbackRules.put(methodSignature, rule);
        log.info("注册方法级回滚规则: {}", methodSignature);
    }

    /**
     * 移除全局回滚规则
     */
    public void removeGlobalRollbackRule(String ruleName) {
        globalRollbackRules.remove(ruleName);
        log.info("移除全局回滚规则: {}", ruleName);
    }

    /**
     * 移除方法级回滚规则
     */
    public void removeMethodRollbackRule(String methodSignature) {
        methodRollbackRules.remove(methodSignature);
        log.info("移除方法级回滚规则: {}", methodSignature);
    }

    /**
     * 处理事务异常
     */
    public TransactionExceptionInfo handleTransactionException(Throwable throwable,
                                                               TransactionContext context) {

        String transactionId = context.getTransactionId();
        String exceptionType = throwable.getClass().getSimpleName();
        String exceptionMessage = throwable.getMessage();
        boolean shouldRollback = shouldRollback(throwable, context.getMethodSignature());

        // 创建异常信息
        TransactionExceptionInfo exceptionInfo = TransactionExceptionInfo.builder()
                .transactionId(transactionId)
                .exceptionType(exceptionType)
                .exceptionMessage(exceptionMessage)
                .exceptionClass(throwable.getClass())
                .shouldRollback(shouldRollback)
                .methodSignature(context.getMethodSignature())
                .applicationName(context.getApplicationName())
                .occurredAt(java.time.LocalDateTime.now())
                .stackTrace(getStackTrace(throwable))
                .build();

        log.error("事务异常处理: {} (事务: {}, 回滚: {})",
                exceptionType, transactionId, shouldRollback, throwable);

        return exceptionInfo;
    }

    /**
     * 获取异常统计信息
     */
    public ExceptionStatistics getExceptionStatistics() {
        return ExceptionStatistics.builder()
                .globalRuleCount(globalRollbackRules.size())
                .methodRuleCount(methodRollbackRules.size())
                .globalRules(new HashMap<>(globalRollbackRules))
                .build();
    }

    /**
     * 清理过期的方法级规则
     */
    public void cleanupExpiredMethodRules() {
        // 这里可以实现基于时间或使用频率的清理逻辑
        // 简化实现，暂时不做清理
        log.debug("清理过期方法级规则，当前规则数: {}", methodRollbackRules.size());
    }

    // ==================== 私有方法 ====================

    /**
     * 初始化默认规则
     */
    private void initializeDefaultRules() {
        // 注册常见的业务异常不回滚规则
        ExceptionRollbackRule businessExceptionRule = ExceptionRollbackRule.builder()
                .ruleName("业务异常不回滚")
                .noRollbackFor(
                        IllegalArgumentException.class,
                        IllegalStateException.class
                )
                .build();

        registerGlobalRollbackRule("business-exception-no-rollback", businessExceptionRule);

        // 注册Tipray框架异常回滚规则
        ExceptionRollbackRule tiprayExceptionRule = ExceptionRollbackRule.builder()
                .ruleName("Tipray框架异常回滚")
                .rollbackFor(
                        DistributedTransactionException.class,
                        DistributedTransactionTimeoutException.class,
                        DistributedTransactionSystemException.class,
                        DistributedTransactionNetworkException.class,
                        DistributedTransactionResourceException.class,
                        DistributedTransactionBarrierException.class,
                        DistributedTransactionCoordinatorException.class,
                        DistributedTransactionConfigurationException.class,
                        DistributedBranchTransactionException.class,
                        DistributedIllegalTransactionStateException.class,
                        DistributedTransactionBeginException.class,
                        DistributedTransactionCommitException.class,
                        DistributedRollbackException.class
                )
                .build();

        registerGlobalRollbackRule("tipray-exception-rollback", tiprayExceptionRule);

        // 注册系统异常回滚规则
        ExceptionRollbackRule systemExceptionRule = ExceptionRollbackRule.builder()
                .ruleName("系统异常回滚")
                .rollbackFor(
                        RuntimeException.class,
                        Error.class
                )
                .build();

        registerGlobalRollbackRule("system-exception-rollback", systemExceptionRule);
    }

    /**
     * 判断是否为默认回滚异常
     */
    private boolean isDefaultRollbackException(Throwable throwable) {
        // 优先检查Tipray框架异常
        if (throwable instanceof DistributedTransactionException) {
            return true; // 所有Tipray框架异常都需要回滚
        }

        // 默认规则：RuntimeException和Error需要回滚，Exception不回滚 遵守spring的规则
        return throwable instanceof RuntimeException || throwable instanceof Error;
    }

    /**
     * 获取异常堆栈信息
     */
    private String getStackTrace(Throwable throwable) {
        if (throwable == null) {
            return "";
        }

        java.io.StringWriter sw = new java.io.StringWriter();
        java.io.PrintWriter pw = new java.io.PrintWriter(sw);
        throwable.printStackTrace(pw);
        return sw.toString();
    }

    /**
     * 事务异常信息
     */
    public static class TransactionExceptionInfo {
        private final String transactionId;
        private final String exceptionType;
        private final String exceptionMessage;
        private final Class<? extends Throwable> exceptionClass;
        private final boolean shouldRollback;
        private final String methodSignature;
        private final String applicationName;
        private final java.time.LocalDateTime occurredAt;
        private final String stackTrace;

        private TransactionExceptionInfo(Builder builder) {
            this.transactionId = builder.transactionId;
            this.exceptionType = builder.exceptionType;
            this.exceptionMessage = builder.exceptionMessage;
            this.exceptionClass = builder.exceptionClass;
            this.shouldRollback = builder.shouldRollback;
            this.methodSignature = builder.methodSignature;
            this.applicationName = builder.applicationName;
            this.occurredAt = builder.occurredAt;
            this.stackTrace = builder.stackTrace;
        }

        public static Builder builder() {
            return new Builder();
        }

        // getters
        public String getTransactionId() {
            return transactionId;
        }

        public String getExceptionType() {
            return exceptionType;
        }

        public String getExceptionMessage() {
            return exceptionMessage;
        }

        public Class<? extends Throwable> getExceptionClass() {
            return exceptionClass;
        }

        public boolean isShouldRollback() {
            return shouldRollback;
        }

        public String getMethodSignature() {
            return methodSignature;
        }

        public String getApplicationName() {
            return applicationName;
        }

        public java.time.LocalDateTime getOccurredAt() {
            return occurredAt;
        }

        public String getStackTrace() {
            return stackTrace;
        }

        @Override
        public String toString() {
            return String.format("TransactionExceptionInfo{txId='%s', type='%s', rollback=%s}",
                    transactionId, exceptionType, shouldRollback);
        }

        public static class Builder {
            private String transactionId;
            private String exceptionType;
            private String exceptionMessage;
            private Class<? extends Throwable> exceptionClass;
            private boolean shouldRollback;
            private String methodSignature;
            private String applicationName;
            private java.time.LocalDateTime occurredAt;
            private String stackTrace;

            public Builder transactionId(String transactionId) {
                this.transactionId = transactionId;
                return this;
            }

            public Builder exceptionType(String exceptionType) {
                this.exceptionType = exceptionType;
                return this;
            }

            public Builder exceptionMessage(String exceptionMessage) {
                this.exceptionMessage = exceptionMessage;
                return this;
            }

            public Builder exceptionClass(Class<? extends Throwable> exceptionClass) {
                this.exceptionClass = exceptionClass;
                return this;
            }

            public Builder shouldRollback(boolean shouldRollback) {
                this.shouldRollback = shouldRollback;
                return this;
            }

            public Builder methodSignature(String methodSignature) {
                this.methodSignature = methodSignature;
                return this;
            }

            public Builder applicationName(String applicationName) {
                this.applicationName = applicationName;
                return this;
            }

            public Builder occurredAt(java.time.LocalDateTime occurredAt) {
                this.occurredAt = occurredAt;
                return this;
            }

            public Builder stackTrace(String stackTrace) {
                this.stackTrace = stackTrace;
                return this;
            }

            public TransactionExceptionInfo build() {
                return new TransactionExceptionInfo(this);
            }
        }
    }

    /**
     * 异常统计信息
     */
    public static class ExceptionStatistics {
        private final int globalRuleCount;
        private final int methodRuleCount;
        private final Map<String, ExceptionRollbackRule> globalRules;

        private ExceptionStatistics(Builder builder) {
            this.globalRuleCount = builder.globalRuleCount;
            this.methodRuleCount = builder.methodRuleCount;
            this.globalRules = new HashMap<>(builder.globalRules);
        }

        public static Builder builder() {
            return new Builder();
        }

        // getters
        public int getGlobalRuleCount() {
            return globalRuleCount;
        }

        public int getMethodRuleCount() {
            return methodRuleCount;
        }

        public Map<String, ExceptionRollbackRule> getGlobalRules() {
            return new HashMap<>(globalRules);
        }

        @Override
        public String toString() {
            return String.format("ExceptionStatistics{globalRules=%d, methodRules=%d}",
                    globalRuleCount, methodRuleCount);
        }

        public static class Builder {
            private int globalRuleCount;
            private int methodRuleCount;
            private Map<String, ExceptionRollbackRule> globalRules = new HashMap<>();

            public Builder globalRuleCount(int globalRuleCount) {
                this.globalRuleCount = globalRuleCount;
                return this;
            }

            public Builder methodRuleCount(int methodRuleCount) {
                this.methodRuleCount = methodRuleCount;
                return this;
            }

            public Builder globalRules(Map<String, ExceptionRollbackRule> globalRules) {
                this.globalRules = new HashMap<>(globalRules);
                return this;
            }

            public ExceptionStatistics build() {
                return new ExceptionStatistics(this);
            }
        }
    }
}
