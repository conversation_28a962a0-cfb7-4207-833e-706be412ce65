package com.tipray.transaction.core.infrastructure.event;

import com.tipray.transaction.core.domain.transaction.BranchTransactionDO;

/**
 * 本地分支回滚事件
 *
 * <AUTHOR>
 * @version 2.0
 * @since 2025-01-29
 */
public class LocalBranchRolledBackEvent {

    private final BranchTransactionDO branch;
    private final long timestamp;

    public LocalBranchRolledBackEvent(BranchTransactionDO branch) {
        this.branch = branch;
        this.timestamp = System.currentTimeMillis();
    }

    public BranchTransactionDO getBranch() {
        return branch;
    }

    public long getTimestamp() {
        return timestamp;
    }

    @Override
    public String toString() {
        return String.format("LocalBranchRolledBackEvent{transactionId=%s, branchId=%s, timestamp=%d}",
                branch.getTransactionId(), branch.getBranchTransactionId(), timestamp);
    }
}
