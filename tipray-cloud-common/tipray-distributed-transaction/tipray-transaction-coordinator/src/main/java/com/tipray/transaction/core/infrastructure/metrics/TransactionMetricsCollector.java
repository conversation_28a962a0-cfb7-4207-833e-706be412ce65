package com.tipray.transaction.core.infrastructure.metrics;

import com.tipray.transaction.core.domain.coordinator.BranchTransactionInfo;
import com.tipray.transaction.core.domain.transaction.BranchTransactionDO;
import com.tipray.transaction.core.enums.TransactionStatus;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;
import java.util.concurrent.atomic.LongAdder;

/**
 * 事务指标收集器
 * 收集和统计事务相关的性能指标
 *
 * <AUTHOR>
 * @version 2.0
 * @since 2025-01-23
 */
public class TransactionMetricsCollector {

    private static final Logger log = LoggerFactory.getLogger(TransactionMetricsCollector.class);

    // 状态转换指标
    private final ConcurrentHashMap<String, LongAdder> statusTransitionCounters = new ConcurrentHashMap<>();
    private final ConcurrentHashMap<String, LongAdder> statusTransitionDurations = new ConcurrentHashMap<>();
    private final ConcurrentHashMap<String, LongAdder> statusTransitionFailures = new ConcurrentHashMap<>();

    // 本地事务指标
    private final AtomicLong localTransactionStartCount = new AtomicLong(0);
    private final AtomicLong localTransactionCommitCount = new AtomicLong(0);
    private final AtomicLong localTransactionRollbackCount = new AtomicLong(0);
    private final AtomicLong localTransactionFailureCount = new AtomicLong(0);
    private final LongAdder localTransactionDuration = new LongAdder();

    // 分支事务指标
    private final AtomicLong branchTransactionRegistrationCount = new AtomicLong(0);
    private final AtomicLong branchTransactionExecutionCount = new AtomicLong(0);
    private final AtomicLong branchTransactionCommitCount = new AtomicLong(0);
    private final AtomicLong branchTransactionRollbackCount = new AtomicLong(0);
    private final AtomicLong branchTransactionFailureCount = new AtomicLong(0);
    private final LongAdder branchTransactionDuration = new LongAdder();

    // 系统指标
    private final AtomicLong totalTransactionCount = new AtomicLong(0);
    private final AtomicLong successTransactionCount = new AtomicLong(0);
    private final AtomicLong failureTransactionCount = new AtomicLong(0);

    /**
     * 记录状态转换指标
     */
    public void recordStatusTransition(TransactionStatus fromStatus, TransactionStatus toStatus, long duration) {
        try {
            String transitionKey = fromStatus.name() + "_TO_" + toStatus.name();

            statusTransitionCounters.computeIfAbsent(transitionKey, k -> new LongAdder()).increment();
            statusTransitionDurations.computeIfAbsent(transitionKey, k -> new LongAdder()).add(duration);

            log.debug("记录状态转换指标: {} (耗时: {}ms)", transitionKey, duration);

        } catch (Exception e) {
            log.error("记录状态转换指标失败", e);
        }
    }

    /**
     * 记录状态转换失败指标
     */
    public void recordStatusTransitionFailure(TransactionStatus fromStatus, TransactionStatus toStatus, long duration) {
        try {
            String transitionKey = fromStatus.name() + "_TO_" + toStatus.name();

            statusTransitionFailures.computeIfAbsent(transitionKey, k -> new LongAdder()).increment();

            log.debug("记录状态转换失败指标: {}", transitionKey);

        } catch (Exception e) {
            log.error("记录状态转换失败指标失败", e);
        }
    }

    /**
     * 记录分支事务注册指标
     */
    public void recordBranchTransactionRegistration(BranchTransactionInfo branchInfo) {
        try {
            branchTransactionRegistrationCount.incrementAndGet();

            log.debug("记录分支事务注册指标: {} - {}",
                    branchInfo.getTransactionId(), branchInfo.getBranchId());

        } catch (Exception e) {
            log.error("记录分支事务注册指标失败", e);
        }
    }

    /**
     * 记录分支事务执行指标
     */
    public void recordBranchTransactionExecution(BranchTransactionInfo branchInfo) {
        try {
            branchTransactionExecutionCount.incrementAndGet();

            long duration = branchInfo.getExecutionDuration();
            branchTransactionDuration.add(duration);

            log.debug("记录分支事务执行指标: {} - {} (耗时: {}ms)",
                    branchInfo.getTransactionId(), branchInfo.getBranchId(), duration);

        } catch (Exception e) {
            log.error("记录分支事务执行指标失败", e);
        }
    }

    // ==================== 新增分支指标方法 ====================

    /**
     * 记录本地分支提交指标
     */
    public void recordLocalBranchCommit(BranchTransactionDO branch) {
        try {
            branchTransactionCommitCount.incrementAndGet();

            log.debug("记录本地分支提交指标: {} - {}",
                    branch.getTransactionId(), branch.getBranchTransactionId());

        } catch (Exception e) {
            log.error("记录本地分支提交指标失败", e);
        }
    }

    /**
     * 记录本地分支回滚指标
     */
    public void recordLocalBranchRollback(BranchTransactionDO branch) {
        try {
            branchTransactionRollbackCount.incrementAndGet();

            log.debug("记录本地分支回滚指标: {} - {}",
                    branch.getTransactionId(), branch.getBranchTransactionId());

        } catch (Exception e) {
            log.error("记录本地分支回滚指标失败", e);
        }
    }

    /**
     * 记录远程分支提交指标
     */
    public void recordRemoteBranchCommit(BranchTransactionDO branch) {
        try {
            branchTransactionCommitCount.incrementAndGet();

            log.debug("记录远程分支提交指标: {} - {}",
                    branch.getTransactionId(), branch.getBranchTransactionId());

        } catch (Exception e) {
            log.error("记录远程分支提交指标失败", e);
        }
    }

    /**
     * 记录远程分支回滚指标
     */
    public void recordRemoteBranchRollback(BranchTransactionDO branch) {
        try {
            branchTransactionRollbackCount.incrementAndGet();

            log.debug("记录远程分支回滚指标: {} - {}",
                    branch.getTransactionId(), branch.getBranchTransactionId());

        } catch (Exception e) {
            log.error("记录远程分支回滚指标失败", e);
        }
    }

    /**
     * 记录分支状态转换指标
     */
    public void recordBranchStatusTransition(com.tipray.transaction.core.enums.BranchTransactionStatus fromStatus,
                                           com.tipray.transaction.core.enums.BranchTransactionStatus toStatus,
                                           long duration) {
        try {
            // 可以根据需要添加具体的指标收集逻辑
            log.debug("记录分支状态转换指标: {} -> {} (耗时: {}ms)", fromStatus, toStatus, duration);

        } catch (Exception e) {
            log.error("记录分支状态转换指标失败", e);
        }
    }

    /**
     * 记录分支状态转换失败指标
     */
    public void recordBranchStatusTransitionFailure(com.tipray.transaction.core.enums.BranchTransactionStatus fromStatus,
                                                  com.tipray.transaction.core.enums.BranchTransactionStatus toStatus,
                                                  long duration) {
        try {
            // 可以根据需要添加具体的指标收集逻辑
            log.debug("记录分支状态转换失败指标: {} -> {} (耗时: {}ms)", fromStatus, toStatus, duration);

        } catch (Exception e) {
            log.error("记录分支状态转换失败指标失败", e);
        }
    }

    /**
     * 获取指标统计信息
     */
    public TransactionMetrics getMetrics() {
        return TransactionMetrics.builder()
                .totalTransactionCount(totalTransactionCount.get())
                .successTransactionCount(successTransactionCount.get())
                .failureTransactionCount(failureTransactionCount.get())
                .localTransactionStartCount(localTransactionStartCount.get())
                .localTransactionCommitCount(localTransactionCommitCount.get())
                .localTransactionRollbackCount(localTransactionRollbackCount.get())
                .localTransactionFailureCount(localTransactionFailureCount.get())
                .branchTransactionRegistrationCount(branchTransactionRegistrationCount.get())
                .branchTransactionExecutionCount(branchTransactionExecutionCount.get())
                .branchTransactionCommitCount(branchTransactionCommitCount.get())
                .branchTransactionRollbackCount(branchTransactionRollbackCount.get())
                .branchTransactionFailureCount(branchTransactionFailureCount.get())
                .averageLocalTransactionDuration(calculateAverageLocalTransactionDuration())
                .averageBranchTransactionDuration(calculateAverageBranchTransactionDuration())
                .statusTransitionCounters(new ConcurrentHashMap<>(statusTransitionCounters))
                .build();
    }

    /**
     * 重置所有指标
     */
    public void resetMetrics() {
        try {
            statusTransitionCounters.clear();
            statusTransitionDurations.clear();
            statusTransitionFailures.clear();

            localTransactionStartCount.set(0);
            localTransactionCommitCount.set(0);
            localTransactionRollbackCount.set(0);
            localTransactionFailureCount.set(0);
            localTransactionDuration.reset();

            branchTransactionRegistrationCount.set(0);
            branchTransactionExecutionCount.set(0);
            branchTransactionCommitCount.set(0);
            branchTransactionRollbackCount.set(0);
            branchTransactionFailureCount.set(0);
            branchTransactionDuration.reset();

            totalTransactionCount.set(0);
            successTransactionCount.set(0);
            failureTransactionCount.set(0);

            log.info("重置所有事务指标");

        } catch (Exception e) {
            log.error("重置事务指标失败", e);
        }
    }

    // ==================== 私有方法 ====================

    /**
     * 计算平均本地事务持续时间
     */
    private double calculateAverageLocalTransactionDuration() {
        long commitCount = localTransactionCommitCount.get();
        long rollbackCount = localTransactionRollbackCount.get();
        long totalCount = commitCount + rollbackCount;

        if (totalCount == 0) {
            return 0.0;
        }

        return (double) localTransactionDuration.sum() / totalCount;
    }

    /**
     * 计算平均分支事务持续时间
     */
    private double calculateAverageBranchTransactionDuration() {
        long executionCount = branchTransactionExecutionCount.get();

        if (executionCount == 0) {
            return 0.0;
        }

        return (double) branchTransactionDuration.sum() / executionCount;
    }

    /**
     * 事务指标数据类
     */
    public static class TransactionMetrics {
        private final long totalTransactionCount;
        private final long successTransactionCount;
        private final long failureTransactionCount;
        private final long localTransactionStartCount;
        private final long localTransactionCommitCount;
        private final long localTransactionRollbackCount;
        private final long localTransactionFailureCount;
        private final long branchTransactionRegistrationCount;
        private final long branchTransactionExecutionCount;
        private final long branchTransactionCommitCount;
        private final long branchTransactionRollbackCount;
        private final long branchTransactionFailureCount;
        private final double averageLocalTransactionDuration;
        private final double averageBranchTransactionDuration;
        private final ConcurrentHashMap<String, LongAdder> statusTransitionCounters;

        private TransactionMetrics(Builder builder) {
            this.totalTransactionCount = builder.totalTransactionCount;
            this.successTransactionCount = builder.successTransactionCount;
            this.failureTransactionCount = builder.failureTransactionCount;
            this.localTransactionStartCount = builder.localTransactionStartCount;
            this.localTransactionCommitCount = builder.localTransactionCommitCount;
            this.localTransactionRollbackCount = builder.localTransactionRollbackCount;
            this.localTransactionFailureCount = builder.localTransactionFailureCount;
            this.branchTransactionRegistrationCount = builder.branchTransactionRegistrationCount;
            this.branchTransactionExecutionCount = builder.branchTransactionExecutionCount;
            this.branchTransactionCommitCount = builder.branchTransactionCommitCount;
            this.branchTransactionRollbackCount = builder.branchTransactionRollbackCount;
            this.branchTransactionFailureCount = builder.branchTransactionFailureCount;
            this.averageLocalTransactionDuration = builder.averageLocalTransactionDuration;
            this.averageBranchTransactionDuration = builder.averageBranchTransactionDuration;
            this.statusTransitionCounters = builder.statusTransitionCounters;
        }

        public static Builder builder() {
            return new Builder();
        }

        // getters
        public long getTotalTransactionCount() {
            return totalTransactionCount;
        }

        public long getSuccessTransactionCount() {
            return successTransactionCount;
        }

        public long getFailureTransactionCount() {
            return failureTransactionCount;
        }

        public double getSuccessRate() {
            return totalTransactionCount > 0 ? (double) successTransactionCount / totalTransactionCount * 100 : 0.0;
        }

        public double getFailureRate() {
            return totalTransactionCount > 0 ? (double) failureTransactionCount / totalTransactionCount * 100 : 0.0;
        }

        @Override
        public String toString() {
            return String.format("TransactionMetrics{total=%d, success=%d, failure=%d, successRate=%.2f%%}",
                    totalTransactionCount, successTransactionCount, failureTransactionCount, getSuccessRate());
        }

        public static class Builder {
            private long totalTransactionCount;
            private long successTransactionCount;
            private long failureTransactionCount;
            private long localTransactionStartCount;
            private long localTransactionCommitCount;
            private long localTransactionRollbackCount;
            private long localTransactionFailureCount;
            private long branchTransactionRegistrationCount;
            private long branchTransactionExecutionCount;
            private long branchTransactionCommitCount;
            private long branchTransactionRollbackCount;
            private long branchTransactionFailureCount;
            private double averageLocalTransactionDuration;
            private double averageBranchTransactionDuration;
            private ConcurrentHashMap<String, LongAdder> statusTransitionCounters = new ConcurrentHashMap<>();

            public Builder totalTransactionCount(long totalTransactionCount) {
                this.totalTransactionCount = totalTransactionCount;
                return this;
            }

            public Builder successTransactionCount(long successTransactionCount) {
                this.successTransactionCount = successTransactionCount;
                return this;
            }

            public Builder failureTransactionCount(long failureTransactionCount) {
                this.failureTransactionCount = failureTransactionCount;
                return this;
            }

            public Builder localTransactionStartCount(long localTransactionStartCount) {
                this.localTransactionStartCount = localTransactionStartCount;
                return this;
            }

            public Builder localTransactionCommitCount(long localTransactionCommitCount) {
                this.localTransactionCommitCount = localTransactionCommitCount;
                return this;
            }

            public Builder localTransactionRollbackCount(long localTransactionRollbackCount) {
                this.localTransactionRollbackCount = localTransactionRollbackCount;
                return this;
            }

            public Builder localTransactionFailureCount(long localTransactionFailureCount) {
                this.localTransactionFailureCount = localTransactionFailureCount;
                return this;
            }

            public Builder branchTransactionRegistrationCount(long branchTransactionRegistrationCount) {
                this.branchTransactionRegistrationCount = branchTransactionRegistrationCount;
                return this;
            }

            public Builder branchTransactionExecutionCount(long branchTransactionExecutionCount) {
                this.branchTransactionExecutionCount = branchTransactionExecutionCount;
                return this;
            }

            public Builder branchTransactionCommitCount(long branchTransactionCommitCount) {
                this.branchTransactionCommitCount = branchTransactionCommitCount;
                return this;
            }

            public Builder branchTransactionRollbackCount(long branchTransactionRollbackCount) {
                this.branchTransactionRollbackCount = branchTransactionRollbackCount;
                return this;
            }

            public Builder branchTransactionFailureCount(long branchTransactionFailureCount) {
                this.branchTransactionFailureCount = branchTransactionFailureCount;
                return this;
            }

            public Builder averageLocalTransactionDuration(double averageLocalTransactionDuration) {
                this.averageLocalTransactionDuration = averageLocalTransactionDuration;
                return this;
            }

            public Builder averageBranchTransactionDuration(double averageBranchTransactionDuration) {
                this.averageBranchTransactionDuration = averageBranchTransactionDuration;
                return this;
            }

            public Builder statusTransitionCounters(ConcurrentHashMap<String, LongAdder> statusTransitionCounters) {
                this.statusTransitionCounters = statusTransitionCounters;
                return this;
            }

            public TransactionMetrics build() {
                return new TransactionMetrics(this);
            }
        }
    }
}
