package com.tipray.transaction.core.application.handler.mode;

import com.tipray.transaction.core.enums.TransactionMode;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;

import javax.annotation.PostConstruct;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 事务模式处理器工厂
 * 自动注册Spring环境中所有TransactionHandler实现类
 *
 * <AUTHOR>
 * @version 2.0
 * @since 2025-01-23
 */
public class TransationModeHandlerFactory implements ApplicationContextAware {

    private static final Logger log = LoggerFactory.getLogger(TransationModeHandlerFactory.class);
    private final Map<TransactionMode, TransactionModeHandler> handlerMap = new ConcurrentHashMap<>();
    private ApplicationContext applicationContext;

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
    }

    /**
     * 初始化方法，自动注册所有TransactionHandler
     */
    @PostConstruct
    public void initHandlers() {
        // 获取Spring容器中所有TransactionHandler类型的Bean
        Map<String, TransactionModeHandler> handlerBeans = applicationContext.getBeansOfType(TransactionModeHandler.class);

        if (handlerBeans.isEmpty()) {
            log.warn("未发现任何TransactionHandler实现类");
            return;
        }

        // 注册每个Handler
        for (Map.Entry<String, TransactionModeHandler> entry : handlerBeans.entrySet()) {
            String beanName = entry.getKey();
            TransactionModeHandler handler = entry.getValue();

            try {
                TransactionMode supportedMode = handler.getSupportedMode();
                String handlerName = handler.getHandlerName();

                // 检查是否已有相同模式的处理器
                if (handlerMap.containsKey(supportedMode)) {
                    TransactionModeHandler existingHandler = handlerMap.get(supportedMode);
                    log.warn("事务模式 {} 已存在处理器: {}，跳过注册: {} (Bean: {})",
                            supportedMode, existingHandler.getHandlerName(), handlerName, beanName);
                    continue;
                }

                // 注册处理器
                handlerMap.put(supportedMode, handler);
                log.info("成功注册TransactionHandler: {} -> {} (Bean: {})",
                        supportedMode, handlerName, beanName);

            } catch (Exception e) {
                log.error("注册TransactionHandler失败: {} (Bean: {}), 错误: {}",
                        handler.getClass().getSimpleName(), beanName, e.getMessage(), e);
            }
        }

        log.info("TransactionHandler自动注册完成，共注册 {} 个处理器: {}",
                handlerMap.size(), handlerMap.keySet());
    }

    /**
     * 获取指定模式的事务处理器
     *
     * @param mode 事务模式
     * @return 事务处理器
     * @throws RuntimeException 如果不支持该事务模式
     */
    public TransactionModeHandler getHandler(TransactionMode mode) {
        if (mode == null) {
            throw new IllegalArgumentException("事务模式不能为空");
        }

        TransactionModeHandler transactionModeHandler = handlerMap.get(mode);

        if (transactionModeHandler == null) {
            throw new RuntimeException("不支持的事务模式: " + mode +
                    "，当前支持的模式: " + handlerMap.keySet());
        }

        return transactionModeHandler;
    }
}
