package com.tipray.transaction.core.application.execution;

import com.tipray.transaction.core.domain.execution.BranchTransactionExecutionRecordDO;
import com.tipray.transaction.core.domain.execution.TransactionExecutionRecordDO;
import com.tipray.transaction.core.domain.transaction.BranchTransactionDO;
import com.tipray.transaction.core.domain.transaction.TransactionContext;
import com.tipray.transaction.core.persistence.BranchTransactionExecutionHistoryStorage;
import com.tipray.transaction.core.persistence.TransactionExecutionHistoryStorage;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 执行历史管理器
 * 统一管理全局事务和分支事务的执行记录
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-01-25
 */
public class ExecutionHistoryManager {

    private static final Logger log = LoggerFactory.getLogger(ExecutionHistoryManager.class);

    private final TransactionExecutionHistoryStorage transactionHistoryStorage;
    private final BranchTransactionExecutionHistoryStorage branchHistoryStorage;

    public ExecutionHistoryManager(TransactionExecutionHistoryStorage transactionHistoryStorage,
                                   BranchTransactionExecutionHistoryStorage branchHistoryStorage) {
        this.transactionHistoryStorage = transactionHistoryStorage;
        this.branchHistoryStorage = branchHistoryStorage;
    }

    /**
     * 记录全局事务开始执行
     */
    public TransactionExecutionRecordDO recordTransactionStart(TransactionContext context, String executionType) {
        try {
            TransactionExecutionRecordDO record = new TransactionExecutionRecordDO(
                    context.getTransactionId(),
                    context.getGroupId(),
                    context.getMethodSignature(),
                    getNextAttemptNumber(context.getTransactionId()),
                    executionType
            );

            // 设置事务信息
            record.setTransactionMode(context.getMode().name());
            record.setBusinessKey(context.getTransactionId() + "-" + context.getMethodSignature());
            record.setTimeoutSeconds(context.getTimeout());
            record.setExecutionNode(getExecutionNode());
            record.setInitiatorThread(Thread.currentThread().getName());

            // 设置业务上下文
            record.setBusinessClassName("");
            record.setBusinessMethodName(context.getMethodSignature());

            record.markStarted();
            return transactionHistoryStorage.saveExecutionRecord(record);

        } catch (Exception e) {
            log.error("记录全局事务开始执行失败: 事务[{}] - {}", context.getTransactionId(), e.getMessage(), e);
            return null;
        }
    }

    /**
     * 记录全局事务执行成功
     */
    public void recordTransactionSuccess(String transactionId, Object result) {
        try {
            TransactionExecutionRecordDO record = getLatestTransactionRecord(transactionId);
            if (record != null) {
                record.markSuccess();
                if (result != null) {
                    record.setExecutionResult(result.toString());
                }
                transactionHistoryStorage.updateExecutionRecord(record);
                log.debug("记录全局事务执行成功: 事务[{}]", transactionId);
            }

        } catch (Exception e) {
            log.error("记录全局事务执行成功失败: 事务[{}] - {}", transactionId, e.getMessage(), e);
        }
    }

    /**
     * 记录全局事务执行失败
     */
    public void recordTransactionFailure(String transactionId, Exception exception) {
        try {
            TransactionExecutionRecordDO record = getLatestTransactionRecord(transactionId);
            if (record != null) {
                record.markFailed(
                        exception.getMessage(),
                        exception.getClass().getSimpleName(),
                        getStackTrace(exception)
                );
                transactionHistoryStorage.updateExecutionRecord(record);
                log.debug("记录全局事务执行失败: 事务[{}] - {}", transactionId, exception.getMessage());
            }

        } catch (Exception e) {
            log.error("记录全局事务执行失败失败: 事务[{}] - {}", transactionId, e.getMessage(), e);
        }
    }

    /**
     * 记录全局事务超时
     */
    public void recordTransactionTimeout(String transactionId) {
        try {
            TransactionExecutionRecordDO record = getLatestTransactionRecord(transactionId);
            if (record != null) {
                record.markTimeout();
                transactionHistoryStorage.updateExecutionRecord(record);
                log.debug("记录全局事务超时: 事务[{}]", transactionId);
            }

        } catch (Exception e) {
            log.error("记录全局事务超时失败: 事务[{}] - {}", transactionId, e.getMessage(), e);
        }
    }

    /**
     * 记录全局事务回滚
     */
    public void recordTransactionRollback(String transactionId) {
        try {
            TransactionExecutionRecordDO record = getLatestTransactionRecord(transactionId);
            if (record != null) {
                record.markRollbacked();
                transactionHistoryStorage.updateExecutionRecord(record);
                log.debug("记录全局事务回滚: 事务[{}]", transactionId);
            }

        } catch (Exception e) {
            log.error("记录全局事务回滚失败: 事务[{}] - {}", transactionId, e.getMessage(), e);
        }
    }

    /**
     * 记录手动操作
     */
    public TransactionExecutionRecordDO recordManualOperation(String transactionId, String executionType,
                                                              String triggeredBy, String triggerReason) {
        try {
            TransactionExecutionRecordDO record = new TransactionExecutionRecordDO(
                    transactionId,
                    null, // 手动操作时可能没有组ID
                    "手动操作",
                    getNextAttemptNumber(transactionId),
                    executionType
            );

            record.setTriggeredBy(triggeredBy);
            record.setTriggerReason(triggerReason);
            record.setExecutionNode(getExecutionNode());
            record.markStarted();

            return transactionHistoryStorage.saveExecutionRecord(record);

        } catch (Exception e) {
            log.error("记录手动操作失败: 事务[{}] 操作[{}] - {}", transactionId, executionType, e.getMessage(), e);
            return null;
        }
    }

    /**
     * 记录分支事务开始执行
     */
    public BranchTransactionExecutionRecordDO recordBranchStart(BranchTransactionDO branchTransaction, String executionType) {
        try {
            BranchTransactionExecutionRecordDO record = new BranchTransactionExecutionRecordDO(
                    branchTransaction.getBranchTransactionId(),
                    branchTransaction.getTransactionId(),
                    branchTransaction.getStepName(),
                    getNextBranchAttemptNumber(branchTransaction.getBranchTransactionId()),
                    executionType
            );

            // 设置分支事务信息
            record.setTargetService(branchTransaction.getTargetService());
            record.setTargetMethod(branchTransaction.getTargetMethod());
            record.setExecutionNode(getExecutionNode());

            // 设置请求参数（如果有的话）
            // TODO: 根据实际的BranchTransactionDO字段来设置请求参数
            // record.setRequestParams("请求参数待实现");

            record.markStarted();
            return branchHistoryStorage.saveExecutionRecord(record);

        } catch (Exception e) {
            log.error("记录分支事务开始执行失败: 事务[{}] 分支[{}] - {}",
                    branchTransaction.getTransactionId(), branchTransaction.getBranchTransactionId(), e.getMessage(), e);
            return null;
        }
    }

    /**
     * 记录分支事务执行成功
     */
    public void recordBranchSuccess(Long branchTransactionId, Object response, Long networkLatency) {
        try {
            BranchTransactionExecutionRecordDO record = getLatestBranchRecord(branchTransactionId);
            if (record != null) {
                record.markSuccess(response != null ? response.toString() : null);
                if (networkLatency != null) {
                    record.setNetworkLatency(networkLatency);
                }
                branchHistoryStorage.updateExecutionRecord(record);
                log.debug("记录分支事务执行成功: 分支[{}]", branchTransactionId);
            }

        } catch (Exception e) {
            log.error("记录分支事务执行成功失败: 分支[{}] - {}", branchTransactionId, e.getMessage(), e);
        }
    }

    /**
     * 记录分支事务执行失败
     */
    public void recordBranchFailure(Long branchTransactionId, Exception exception, Integer httpStatusCode) {
        try {
            BranchTransactionExecutionRecordDO record = getLatestBranchRecord(branchTransactionId);
            if (record != null) {
                record.markFailed(
                        exception.getMessage(),
                        exception.getClass().getSimpleName(),
                        getStackTrace(exception)
                );
                if (httpStatusCode != null) {
                    record.setHttpStatusCode(httpStatusCode);
                }
                branchHistoryStorage.updateExecutionRecord(record);
                log.debug("记录分支事务执行失败: 分支[{}] - {}", branchTransactionId, exception.getMessage());
            }

        } catch (Exception e) {
            log.error("记录分支事务执行失败失败: 分支[{}] - {}", branchTransactionId, e.getMessage(), e);
        }
    }

    /**
     * 记录分支事务超时
     */
    public void recordBranchTimeout(Long branchTransactionId) {
        try {
            BranchTransactionExecutionRecordDO record = getLatestBranchRecord(branchTransactionId);
            if (record != null) {
                record.markTimeout();
                branchHistoryStorage.updateExecutionRecord(record);
                log.debug("记录分支事务超时: 分支[{}]", branchTransactionId);
            }

        } catch (Exception e) {
            log.error("记录分支事务超时失败: 分支[{}] - {}", branchTransactionId, e.getMessage(), e);
        }
    }

    /**
     * 记录分支事务重试
     */
    public BranchTransactionExecutionRecordDO recordBranchRetry(Long branchTransactionId, String retryReason) {
        try {
            // 获取原始分支事务信息
            BranchTransactionExecutionRecordDO lastRecord = getLatestBranchRecord(branchTransactionId);
            if (lastRecord == null) {
                log.warn("无法找到分支事务的上次执行记录: {}", branchTransactionId);
                return null;
            }

            BranchTransactionExecutionRecordDO retryRecord = new BranchTransactionExecutionRecordDO(
                    branchTransactionId,
                    lastRecord.getTransactionId(),
                    lastRecord.getStepName(),
                    getNextBranchAttemptNumber(branchTransactionId),
                    "RETRY"
            );

            // 复制基本信息
            retryRecord.setTargetService(lastRecord.getTargetService());
            retryRecord.setTargetMethod(lastRecord.getTargetMethod());
            retryRecord.setRequestUrl(lastRecord.getRequestUrl());
            retryRecord.setRequestParams(lastRecord.getRequestParams());
            retryRecord.setRetryReason(retryReason);
            retryRecord.setExecutionNode(getExecutionNode());

            retryRecord.markStarted();
            return branchHistoryStorage.saveExecutionRecord(retryRecord);

        } catch (Exception e) {
            log.error("记录分支事务重试失败: 分支[{}] - {}", branchTransactionId, e.getMessage(), e);
            return null;
        }
    }

    /**
     * 获取全局事务执行历史
     */
    public List<TransactionExecutionRecordDO> getTransactionHistory(String transactionId) {
        return transactionHistoryStorage.findExecutionRecordsByTransactionId(transactionId);
    }

    /**
     * 获取分支事务执行历史
     */
    public List<BranchTransactionExecutionRecordDO> getBranchHistory(Long branchTransactionId) {
        return branchHistoryStorage.findExecutionRecordsByBranchId(branchTransactionId);
    }

    /**
     * 获取全局事务的所有分支执行历史
     */
    public List<BranchTransactionExecutionRecordDO> getAllBranchHistory(String transactionId) {
        return branchHistoryStorage.findExecutionRecordsByTransactionId(transactionId);
    }

    /**
     * 获取最新的全局事务执行记录
     */
    private TransactionExecutionRecordDO getLatestTransactionRecord(String transactionId) {
        return transactionHistoryStorage.findLatestExecutionRecordByTransactionId(transactionId);
    }

    /**
     * 获取最新的分支事务执行记录
     */
    private BranchTransactionExecutionRecordDO getLatestBranchRecord(Long branchTransactionId) {
        return branchHistoryStorage.findLatestExecutionRecordByBranchId(branchTransactionId);
    }

    /**
     * 获取下一个执行次数
     */
    private Integer getNextAttemptNumber(String transactionId) {
        int currentCount = transactionHistoryStorage.countExecutionRecordsByTransactionId(transactionId);
        return currentCount + 1;
    }

    /**
     * 获取下一个分支执行次数
     */
    private Integer getNextBranchAttemptNumber(Long branchTransactionId) {
        int currentCount = branchHistoryStorage.countExecutionRecordsByBranchId(branchTransactionId);
        return currentCount + 1;
    }

    /**
     * 获取执行节点信息
     */
    private String getExecutionNode() {
        try {
            return java.net.InetAddress.getLocalHost().getHostName();
        } catch (Exception e) {
            return "unknown";
        }
    }

    /**
     * 获取异常堆栈信息
     */
    private String getStackTrace(Exception exception) {
        if (exception == null) {
            return null;
        }

        java.io.StringWriter sw = new java.io.StringWriter();
        java.io.PrintWriter pw = new java.io.PrintWriter(sw);
        exception.printStackTrace(pw);
        String stackTrace = sw.toString();

        // 限制堆栈信息长度，避免过长
        if (stackTrace.length() > 2000) {
            stackTrace = stackTrace.substring(0, 2000) + "...";
        }

        return stackTrace;
    }
}
