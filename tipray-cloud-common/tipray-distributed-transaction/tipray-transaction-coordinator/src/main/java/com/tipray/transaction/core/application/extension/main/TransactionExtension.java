package com.tipray.transaction.core.application.extension.main;

/**
 * 事务扩展接口
 * 定义事务扩展的基本契约
 *
 * <AUTHOR>
 * @version 2.0
 * @since 2025-01-23
 */
public interface TransactionExtension {

    /**
     * 获取扩展ID
     */
    String getExtensionId();

    /**
     * 获取扩展名称
     */
    String getExtensionName();

    /**
     * 获取扩展描述
     */
    String getExtensionDescription();

    /**
     * 获取扩展点
     */
    TransactionExtensionPoint getExtensionPoint();

    /**
     * 获取优先级（数值越大优先级越高）
     */
    int getPriority();

    /**
     * 判断是否启用
     */
    boolean isEnabled();

    /**
     * 启用扩展
     */
    void enable();

    /**
     * 禁用扩展
     */
    void disable();

    /**
     * 判断出错时是否继续执行后续扩展
     */
    boolean isContinueOnError();

    /**
     * 执行扩展逻辑
     *
     * @param context 扩展上下文
     */
    void execute(Object context);

    /**
     * 扩展初始化
     */
    default void initialize() {
        // 默认空实现
    }

    /**
     * 扩展销毁
     */
    default void destroy() {
        // 默认空实现
    }
}
