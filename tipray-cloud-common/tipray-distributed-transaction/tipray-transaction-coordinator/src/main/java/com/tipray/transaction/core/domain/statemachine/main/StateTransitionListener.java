package com.tipray.transaction.core.domain.statemachine.main;

/**
 * 状态转换监听器接口
 * 用于监听状态机的状态转换事件，实现持久化解耦
 *
 * <AUTHOR>
 * @version 2.0
 * @since 2025-01-29
 */
public interface StateTransitionListener {

    /**
     * 状态转换前回调
     * 在状态转换执行前调用，可以进行预处理
     *
     * @param context 状态转换上下文
     */
    default void beforeTransition(StateTransitionContext context) {
        // 默认空实现
    }

    /**
     * 状态转换后回调
     * 在状态转换成功后调用，通常用于持久化操作
     *
     * @param context 状态转换上下文
     */
    void afterTransition(StateTransitionContext context);

    /**
     * 状态转换失败回调
     * 在状态转换失败时调用，用于异常处理
     *
     * @param context 状态转换上下文
     * @param cause   失败原因
     */
    default void onTransitionFailure(StateTransitionContext context, Exception cause) {
        // 默认空实现
    }

    /**
     * 获取监听器优先级
     * 数值越大优先级越高，优先级高的监听器先执行
     *
     * @return 优先级
     */
    default int getPriority() {
        return 0;
    }

    /**
     * 获取监听器名称
     *
     * @return 监听器名称
     */
    default String getListenerName() {
        return this.getClass().getSimpleName();
    }

    /**
     * 判断是否支持指定的状态转换
     * 可以用于过滤不关心的状态转换
     *
     * @param context 状态转换上下文
     * @return true表示支持
     */
    default boolean supports(StateTransitionContext context) {
        return true;
    }
}
