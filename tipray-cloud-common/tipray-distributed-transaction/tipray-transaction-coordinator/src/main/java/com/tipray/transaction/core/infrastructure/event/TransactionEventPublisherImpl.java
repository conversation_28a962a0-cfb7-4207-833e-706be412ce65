package com.tipray.transaction.core.infrastructure.event;

import com.tipray.transaction.core.domain.coordinator.BranchTransactionInfo;
import com.tipray.transaction.core.domain.statemachine.main.StatusTransitionContext;
import com.tipray.transaction.core.domain.transaction.BranchTransactionDO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.ApplicationEventPublisher;

import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.concurrent.ForkJoinPool;

/**
 * 事务事件发布器实现
 * 发布事务生命周期中的各种事件
 *
 * <AUTHOR>
 * @version 2.0
 * @since 2025-01-23
 */
public class TransactionEventPublisherImpl {

    private static final Logger log = LoggerFactory.getLogger(TransactionEventPublisherImpl.class);

    private final ApplicationEventPublisher applicationEventPublisher;
    private final Executor asyncExecutor;

    public TransactionEventPublisherImpl(ApplicationEventPublisher applicationEventPublisher) {
        this.applicationEventPublisher = applicationEventPublisher;
        this.asyncExecutor = ForkJoinPool.commonPool();
    }

    /**
     * 发布状态转换事件
     */
    public void publishStatusTransition(StatusTransitionContext context) {
        try {
            TransactionStatusTransitionEvent event = new TransactionStatusTransitionEvent(context);

            // 同步发布关键事件
            if (context.isTerminalTransition()) {
                applicationEventPublisher.publishEvent(event);
            } else {
                // 异步发布非关键事件
                CompletableFuture.runAsync(() ->
                        applicationEventPublisher.publishEvent(event), asyncExecutor);
            }

            log.debug("发布状态转换事件: {}", context.toLogString());

        } catch (Exception e) {
            log.error("发布状态转换事件失败: {}", context.getTransactionId(), e);
        }
    }

    /**
     * 发布分支事务注册事件
     */
    public void publishBranchTransactionRegistered(BranchTransactionInfo branchInfo) {
        try {
            BranchTransactionRegisteredEvent event = new BranchTransactionRegisteredEvent(branchInfo);
            CompletableFuture.runAsync(() ->
                    applicationEventPublisher.publishEvent(event), asyncExecutor);

            log.debug("发布分支事务注册事件: {} - {}",
                    branchInfo.getTransactionId(), branchInfo.getBranchId());

        } catch (Exception e) {
            log.error("发布分支事务注册事件失败: {} - {}",
                    branchInfo.getTransactionId(), branchInfo.getBranchId(), e);
        }
    }

    /**
     * 发布分支事务执行事件
     */
    public void publishBranchTransactionExecuted(BranchTransactionInfo branchInfo) {
        try {
            BranchTransactionExecutedEvent event = new BranchTransactionExecutedEvent(branchInfo);
            CompletableFuture.runAsync(() ->
                    applicationEventPublisher.publishEvent(event), asyncExecutor);

            log.debug("发布分支事务执行事件: {} - {}",
                    branchInfo.getTransactionId(), branchInfo.getBranchId());

        } catch (Exception e) {
            log.error("发布分支事务执行事件失败: {} - {}",
                    branchInfo.getTransactionId(), branchInfo.getBranchId(), e);
        }
    }

    /**
     * 发布分支事务提交事件
     */
    public void publishBranchTransactionCommitted(BranchTransactionInfo branchInfo) {
        try {
            BranchTransactionCommittedEvent event = new BranchTransactionCommittedEvent(branchInfo);
            applicationEventPublisher.publishEvent(event);

            log.debug("发布分支事务提交事件: {} - {}",
                    branchInfo.getTransactionId(), branchInfo.getBranchId());

        } catch (Exception e) {
            log.error("发布分支事务提交事件失败: {} - {}",
                    branchInfo.getTransactionId(), branchInfo.getBranchId(), e);
        }
    }

    /**
     * 发布分支事务回滚事件
     */
    public void publishBranchTransactionRolledBack(BranchTransactionInfo branchInfo, Exception cause) {
        try {
            BranchTransactionRolledBackEvent event = new BranchTransactionRolledBackEvent(branchInfo, cause);
            applicationEventPublisher.publishEvent(event);

            log.debug("发布分支事务回滚事件: {} - {}",
                    branchInfo.getTransactionId(), branchInfo.getBranchId());

        } catch (Exception e) {
            log.error("发布分支事务回滚事件失败: {} - {}",
                    branchInfo.getTransactionId(), branchInfo.getBranchId(), e);
        }
    }

    /**
     * 发布分支事务提交失败事件
     */
    public void publishBranchTransactionCommitFailed(BranchTransactionInfo branchInfo, Exception cause) {
        try {
            BranchTransactionCommitFailedEvent event = new BranchTransactionCommitFailedEvent(branchInfo, cause);
            applicationEventPublisher.publishEvent(event);

            log.warn("发布分支事务提交失败事件: {} - {}",
                    branchInfo.getTransactionId(), branchInfo.getBranchId());

        } catch (Exception e) {
            log.error("发布分支事务提交失败事件失败: {} - {}",
                    branchInfo.getTransactionId(), branchInfo.getBranchId(), e);
        }
    }

    /**
     * 发布分支事务回滚失败事件
     */
    public void publishBranchTransactionRollbackFailed(BranchTransactionInfo branchInfo, Exception cause) {
        try {
            BranchTransactionRollbackFailedEvent event = new BranchTransactionRollbackFailedEvent(branchInfo, cause);
            applicationEventPublisher.publishEvent(event);

            log.error("发布分支事务回滚失败事件: {} - {}",
                    branchInfo.getTransactionId(), branchInfo.getBranchId());

        } catch (Exception e) {
            log.error("发布分支事务回滚失败事件失败: {} - {}",
                    branchInfo.getTransactionId(), branchInfo.getBranchId(), e);
        }
    }

    // ==================== 本地分支事件 ====================

    /**
     * 发布本地分支提交事件
     */
    public void publishLocalBranchCommitted(BranchTransactionDO branch) {
        try {
            LocalBranchCommittedEvent event = new LocalBranchCommittedEvent(branch);
            applicationEventPublisher.publishEvent(event);

            log.debug("发布本地分支提交事件: {} - {}",
                    branch.getTransactionId(), branch.getBranchTransactionId());

        } catch (Exception e) {
            log.error("发布本地分支提交事件失败: {} - {}",
                    branch.getTransactionId(), branch.getBranchTransactionId(), e);
        }
    }

    /**
     * 发布本地分支回滚事件
     */
    public void publishLocalBranchRolledBack(BranchTransactionDO branch) {
        try {
            LocalBranchRolledBackEvent event = new LocalBranchRolledBackEvent(branch);
            applicationEventPublisher.publishEvent(event);

            log.debug("发布本地分支回滚事件: {} - {}",
                    branch.getTransactionId(), branch.getBranchTransactionId());

        } catch (Exception e) {
            log.error("发布本地分支回滚事件失败: {} - {}",
                    branch.getTransactionId(), branch.getBranchTransactionId(), e);
        }
    }

    /**
     * 发布本地分支提交失败事件
     */
    public void publishLocalBranchCommitFailed(BranchTransactionDO branch, Exception cause) {
        try {
            LocalBranchCommitFailedEvent event = new LocalBranchCommitFailedEvent(branch, cause);
            applicationEventPublisher.publishEvent(event);

            log.warn("发布本地分支提交失败事件: {} - {}",
                    branch.getTransactionId(), branch.getBranchTransactionId());

        } catch (Exception e) {
            log.error("发布本地分支提交失败事件失败: {} - {}",
                    branch.getTransactionId(), branch.getBranchTransactionId(), e);
        }
    }

    // ==================== 远程分支事件 ====================

    /**
     * 发布远程分支提交开始事件
     */
    public void publishRemoteBranchCommitStarted(BranchTransactionDO branch) {
        try {
            RemoteBranchCommitStartedEvent event = new RemoteBranchCommitStartedEvent(branch);
            CompletableFuture.runAsync(() ->
                    applicationEventPublisher.publishEvent(event), asyncExecutor);

            log.debug("发布远程分支提交开始事件: {} - {}",
                    branch.getTransactionId(), branch.getBranchTransactionId());

        } catch (Exception e) {
            log.error("发布远程分支提交开始事件失败: {} - {}",
                    branch.getTransactionId(), branch.getBranchTransactionId(), e);
        }
    }

    /**
     * 发布远程分支提交事件
     */
    public void publishRemoteBranchCommitted(BranchTransactionDO branch) {
        try {
            RemoteBranchCommittedEvent event = new RemoteBranchCommittedEvent(branch);
            applicationEventPublisher.publishEvent(event);

            log.debug("发布远程分支提交事件: {} - {}",
                    branch.getTransactionId(), branch.getBranchTransactionId());

        } catch (Exception e) {
            log.error("发布远程分支提交事件失败: {} - {}",
                    branch.getTransactionId(), branch.getBranchTransactionId(), e);
        }
    }

    /**
     * 发布远程分支提交失败事件
     */
    public void publishRemoteBranchCommitFailed(BranchTransactionDO branch, Exception cause) {
        try {
            RemoteBranchCommitFailedEvent event = new RemoteBranchCommitFailedEvent(branch, cause);
            applicationEventPublisher.publishEvent(event);

            log.warn("发布远程分支提交失败事件: {} - {}",
                    branch.getTransactionId(), branch.getBranchTransactionId());

        } catch (Exception e) {
            log.error("发布远程分支提交失败事件失败: {} - {}",
                    branch.getTransactionId(), branch.getBranchTransactionId(), e);
        }
    }

    /**
     * 发布远程分支回滚开始事件
     */
    public void publishRemoteBranchRollbackStarted(BranchTransactionDO branch) {
        try {
            RemoteBranchRollbackStartedEvent event = new RemoteBranchRollbackStartedEvent(branch);
            CompletableFuture.runAsync(() ->
                    applicationEventPublisher.publishEvent(event), asyncExecutor);

            log.debug("发布远程分支回滚开始事件: {} - {}",
                    branch.getTransactionId(), branch.getBranchTransactionId());

        } catch (Exception e) {
            log.error("发布远程分支回滚开始事件失败: {} - {}",
                    branch.getTransactionId(), branch.getBranchTransactionId(), e);
        }
    }

    /**
     * 发布通用事务事件
     */
    public void publishTransactionEvent(Object event) {
        try {
            CompletableFuture.runAsync(() ->
                    applicationEventPublisher.publishEvent(event), asyncExecutor);

            log.debug("发布通用事务事件: {}", event.getClass().getSimpleName());

        } catch (Exception e) {
            log.error("发布通用事务事件失败: {}", event.getClass().getSimpleName(), e);
        }
    }
}
