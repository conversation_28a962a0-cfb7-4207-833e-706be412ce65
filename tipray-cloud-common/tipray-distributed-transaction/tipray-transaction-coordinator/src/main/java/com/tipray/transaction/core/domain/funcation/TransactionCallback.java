package com.tipray.transaction.core.domain.funcation;

import com.tipray.transaction.core.domain.transaction.TransactionContext;

/**
 * 事务回调接口
 * 定义在事务中执行的业务逻辑
 *
 * @param <T> 返回值类型
 * <AUTHOR>
 * @version 2.0
 * @since 2025-01-23
 */
@FunctionalInterface
public interface TransactionCallback<T> {

    /**
     * 在事务中执行业务逻辑
     *
     * @param context 事务上下文
     * @return 执行结果
     */
    T doInTransaction(TransactionContext context);
}
