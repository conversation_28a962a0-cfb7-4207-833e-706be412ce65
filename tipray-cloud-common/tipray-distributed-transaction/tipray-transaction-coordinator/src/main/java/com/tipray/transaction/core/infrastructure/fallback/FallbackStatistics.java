package com.tipray.transaction.core.infrastructure.fallback;

/**
 * 降级处理统计信息
 *
 * <AUTHOR>
 * @version 2.0
 * @since 2025-01-23
 */
public class FallbackStatistics {

    private final long globalFailureCount;
    private final long branchFailureCount;
    private final long recoveredCount;
    private final int pendingGlobalFailures;
    private final int pendingBranchFailures;
    private final int manualInterventionCount;

    private FallbackStatistics(Builder builder) {
        this.globalFailureCount = builder.globalFailureCount;
        this.branchFailureCount = builder.branchFailureCount;
        this.recoveredCount = builder.recoveredCount;
        this.pendingGlobalFailures = builder.pendingGlobalFailures;
        this.pendingBranchFailures = builder.pendingBranchFailures;
        this.manualInterventionCount = builder.manualInterventionCount;
    }

    public static Builder builder() {
        return new Builder();
    }

    // Getters
    public long getGlobalFailureCount() {
        return globalFailureCount;
    }

    public long getBranchFailureCount() {
        return branchFailureCount;
    }

    public long getRecoveredCount() {
        return recoveredCount;
    }

    public int getPendingGlobalFailures() {
        return pendingGlobalFailures;
    }

    public int getPendingBranchFailures() {
        return pendingBranchFailures;
    }

    public int getManualInterventionCount() {
        return manualInterventionCount;
    }

    /**
     * 获取总失败次数
     */
    public long getTotalFailureCount() {
        return globalFailureCount + branchFailureCount;
    }

    /**
     * 获取总待处理失败数
     */
    public int getTotalPendingFailures() {
        return pendingGlobalFailures + pendingBranchFailures;
    }

    /**
     * 获取恢复率
     */
    public double getRecoveryRate() {
        long totalFailures = getTotalFailureCount();
        if (totalFailures == 0) {
            return 0.0;
        }
        return (double) recoveredCount / totalFailures * 100;
    }

    /**
     * 获取人工干预率
     */
    public double getManualInterventionRate() {
        long totalFailures = getTotalFailureCount();
        if (totalFailures == 0) {
            return 0.0;
        }
        return (double) manualInterventionCount / totalFailures * 100;
    }

    @Override
    public String toString() {
        return String.format("FallbackStatistics{" +
                        "全局失败=%d, 分支失败=%d, 已恢复=%d, " +
                        "待处理全局=%d, 待处理分支=%d, 人工干预=%d, " +
                        "恢复率=%.2f%%, 人工干预率=%.2f%%}",
                globalFailureCount, branchFailureCount, recoveredCount,
                pendingGlobalFailures, pendingBranchFailures, manualInterventionCount,
                getRecoveryRate(), getManualInterventionRate());
    }

    public static class Builder {
        private long globalFailureCount;
        private long branchFailureCount;
        private long recoveredCount;
        private int pendingGlobalFailures;
        private int pendingBranchFailures;
        private int manualInterventionCount;

        public Builder globalFailureCount(long globalFailureCount) {
            this.globalFailureCount = globalFailureCount;
            return this;
        }

        public Builder branchFailureCount(long branchFailureCount) {
            this.branchFailureCount = branchFailureCount;
            return this;
        }

        public Builder recoveredCount(long recoveredCount) {
            this.recoveredCount = recoveredCount;
            return this;
        }

        public Builder pendingGlobalFailures(int pendingGlobalFailures) {
            this.pendingGlobalFailures = pendingGlobalFailures;
            return this;
        }

        public Builder pendingBranchFailures(int pendingBranchFailures) {
            this.pendingBranchFailures = pendingBranchFailures;
            return this;
        }

        public Builder manualInterventionCount(int manualInterventionCount) {
            this.manualInterventionCount = manualInterventionCount;
            return this;
        }

        public FallbackStatistics build() {
            return new FallbackStatistics(this);
        }
    }
}
