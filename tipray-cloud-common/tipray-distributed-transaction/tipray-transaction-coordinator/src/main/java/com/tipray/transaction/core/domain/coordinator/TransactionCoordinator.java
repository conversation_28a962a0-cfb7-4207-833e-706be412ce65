package com.tipray.transaction.core.domain.coordinator;

import com.tipray.transaction.core.domain.transaction.TransactionContext;
import com.tipray.transaction.core.enums.TransactionMode;

/**
 * 事务协调器接口
 * 定义事务协调的基本契约
 *
 * <AUTHOR>
 * @version 2.0
 * @since 2025-01-23
 */
public interface TransactionCoordinator {

    /**
     * 开始事务
     *
     * @param context 事务上下文
     */
    void begin(TransactionContext context);

    /**
     * 初始化协调器（可选）
     *
     * @param context 事务上下文
     */
    default void initialize(TransactionContext context) {
        // 默认空实现
    }

    /**
     * 提交事务
     *
     * @param context 事务上下文
     */
    void commit(TransactionContext context);

    /**
     * 回滚事务
     *
     * @param context 事务上下文
     * @param cause   回滚原因
     */
    void rollback(TransactionContext context, Exception cause);

    /**
     * 获取协调器名称
     */
    default String getCoordinatorName() {
        return this.getClass().getSimpleName();
    }

    /**
     * 判断协调器是否支持指定的事务模式
     *
     * @param mode 事务模式
     * @return true表示支持
     */
    default boolean supports(TransactionMode mode) {
        return true; // 默认支持所有模式
    }

    /**
     * 获取协调器优先级（数值越大优先级越高）
     */
    default int getPriority() {
        return 0;
    }
}
