package com.tipray.transaction.core.infrastructure.event;

import com.tipray.transaction.core.domain.statemachine.main.StatusTransitionContext;
import org.springframework.context.ApplicationEvent;

/**
 * 事务状态转换事件
 *
 * <AUTHOR>
 * @version 2.0
 * @since 2025-01-23
 */
public class TransactionStatusTransitionEvent extends ApplicationEvent {

    private final StatusTransitionContext context;

    public TransactionStatusTransitionEvent(StatusTransitionContext context) {
        super(context);
        this.context = context;
    }

    public StatusTransitionContext getContext() {
        return context;
    }

    public String getTransactionId() {
        return context.getTransactionId();
    }

    @Override
    public String toString() {
        return String.format("TransactionStatusTransitionEvent{%s}", context.toLogString());
    }
}
