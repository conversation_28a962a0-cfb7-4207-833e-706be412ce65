package com.tipray.transaction.core.domain.statemachine.main;

import com.tipray.transaction.core.context.TransactionContextHolder;
import com.tipray.transaction.core.domain.transaction.TransactionContext;
import com.tipray.transaction.core.enums.TransactionEvent;
import com.tipray.transaction.core.enums.TransactionStatus;
import com.tipray.transaction.core.infrastructure.metrics.TransactionMetricsCollector;
import com.tipray.transaction.core.util.LoggingUtils;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.locks.ReentrantReadWriteLock;

/**
 * 事务状态机
 * 管理事务状态转换的核心组件
 *
 * <AUTHOR>
 * @version 2.0
 * @since 2025-01-23
 */
@Slf4j
public class TransactionStateMachine {

    private final TransactionMetricsCollector metricsCollector;

    // 事务状态缓存，提高查询性能
    private final ConcurrentHashMap<String, TransactionStatus> statusCache = new ConcurrentHashMap<>();

    // 读写锁，保证状态转换的线程安全
    private final ReentrantReadWriteLock lock = new ReentrantReadWriteLock();

    // 状态转换监听器列表（按优先级排序）
    private final List<StateTransitionListener> listeners = new ArrayList<>();

    public TransactionStateMachine(TransactionMetricsCollector metricsCollector,
                                   StateTransitionListener listener) {
        this.metricsCollector = metricsCollector;
        registerListener(listener);
    }

    /**
     * 注册状态转换监听器
     *
     * @param listener 监听器
     */
    public void registerListener(StateTransitionListener listener) {
        if (listener == null) {
            log.warn("[{}|{}] [WARN] - 尝试注册空的状态转换监听器",
                    LoggingUtils.getTxId(), LoggingUtils.getBranchId());
            return;
        }

        synchronized (listeners) {
            listeners.add(listener);
            // 按优先级排序，优先级高的先执行
            listeners.sort(Comparator.comparingInt(StateTransitionListener::getPriority).reversed());
        }

        log.debug("[{}|{}] [DEBUG] - 注册状态转换监听器 {}",
                LoggingUtils.getTxId(), LoggingUtils.getBranchId(),
                LoggingUtils.formatContext("name", listener.getListenerName(), "priority", listener.getPriority()));
    }

    /**
     * 移除状态转换监听器
     *
     * @param listener 监听器
     */
    public void removeListener(StateTransitionListener listener) {
        if (listener == null) {
            return;
        }

        synchronized (listeners) {
            boolean removed = listeners.remove(listener);
            if (removed) {
                log.debug("[{}|{}] [DEBUG] - 移除状态转换监听器 {}",
                        LoggingUtils.getTxId(), LoggingUtils.getBranchId(),
                        LoggingUtils.formatContext("name", listener.getListenerName()));
            }
        }
    }

    /**
     * 执行状态转换
     *
     * @param transactionId 事务ID
     * @param event         触发事件
     * @return 转换是否成功
     */
    public boolean transition(String transactionId, TransactionEvent event) {
        return transition(transactionId, event, null);
    }

    /**
     * 状态转换（成功场景，携带数据）
     *
     * @param transactionId 事务ID
     * @param event         触发事件
     * @param data          携带数据
     * @return 转换是否成功
     */
    public boolean transition(String transactionId, TransactionEvent event, Object data) {
        TransactionStatus fromStatus = getCurrentStatus(transactionId);

        // 自动处理null状态（兜底机制）
        if (fromStatus == null) {
            fromStatus = inferInitialStatus(event);
            if (fromStatus != null) {
                // 自动初始化状态到缓存
                statusCache.put(transactionId, fromStatus);
                log.warn("[{}|{}] [WARN] - 事务状态未初始化，自动推断初始状态 {}",
                        LoggingUtils.getTxId(transactionId), LoggingUtils.getBranchId(),
                        LoggingUtils.formatContext("推断状态", fromStatus, "事件", event));
            } else {
                log.error("[{}|{}] [ERROR] - 无法推断事务初始状态 {}",
                        LoggingUtils.getTxId(transactionId), LoggingUtils.getBranchId(),
                        LoggingUtils.formatContext("事件", event));
                return false;
            }
        }

        TransactionStatus toStatus = calculateTargetStatus(fromStatus, event);

        if (toStatus == null) {
            log.warn("[{}|{}] [WARN] - 无法根据事件计算目标状态 {}",
                    LoggingUtils.getTxId(transactionId), LoggingUtils.getBranchId(),
                    LoggingUtils.formatContext("从状态", fromStatus, "事件", event));
            return false;
        }

        StateTransitionContext context = StateTransitionContext.success(
                transactionId, fromStatus, toStatus, event, data);

        boolean success = executeTransition(context);

        // 状态机负责更新内存状态
        if (success) {
            updateTransactionContextStatus(transactionId, toStatus);
        }

        return success;
    }

    /**
     * 状态转换（失败场景）
     *
     * @param transactionId 事务ID
     * @param event         触发事件
     * @param cause         失败原因
     * @param message       失败消息
     * @return 转换是否成功
     */
    public boolean transitionWithFailure(String transactionId, TransactionEvent event,
                                        Exception cause, String message) {
        TransactionStatus fromStatus = getCurrentStatus(transactionId);

        // 自动处理null状态（兜底机制）
        if (fromStatus == null) {
            fromStatus = inferInitialStatus(event);
            if (fromStatus != null) {
                // 自动初始化状态到缓存
                statusCache.put(transactionId, fromStatus);
                log.warn("[{}|{}] [WARN] - 事务状态未初始化，自动推断初始状态 {}",
                        LoggingUtils.getTxId(transactionId), LoggingUtils.getBranchId(),
                        LoggingUtils.formatContext("推断状态", fromStatus, "失败事件", event));
            } else {
                log.error("[{}|{}] [ERROR] - 无法推断事务初始状态 {}",
                        LoggingUtils.getTxId(transactionId), LoggingUtils.getBranchId(),
                        LoggingUtils.formatContext("失败事件", event));
                return false;
            }
        }

        TransactionStatus toStatus = calculateTargetStatus(fromStatus, event);

        if (toStatus == null) {
            log.warn("[{}|{}] [WARN] - 无法根据事件计算目标状态 {}",
                    LoggingUtils.getTxId(transactionId), LoggingUtils.getBranchId(),
                    LoggingUtils.formatContext("从状态", fromStatus, "失败事件", event));
            return false;
        }

        StateTransitionContext context = StateTransitionContext.failure(
                transactionId, fromStatus, toStatus, event, cause, message);

        boolean success = executeTransition(context);

        // 状态机负责更新内存状态
        if (success) {
            updateTransactionContextStatus(transactionId, toStatus);
        }

        return success;
    }

    /**
     * 执行状态转换
     *
     * @param transactionId 事务ID
     * @param event         触发事件
     * @param reason        转换原因
     * @param exception     异常信息
     * @return 转换是否成功
     */
    public boolean transition(String transactionId, TransactionEvent event,
                              String reason, Exception exception) {

        lock.writeLock().lock();
        try {
            // 1. 获取当前状态
            TransactionStatus currentStatus = getCurrentStatus(transactionId);
            if (currentStatus == null) {
                log.warn("[{}|{}] [WARN] - 事务不存在，无法执行状态转换",
                        LoggingUtils.getTxId(), LoggingUtils.getBranchId());
                return false;
            }

            // 2. 计算目标状态
            TransactionStatus targetStatus = calculateTargetStatus(currentStatus, event);
            if (targetStatus == null) {
                log.warn("[{}|{}] [WARN] - 无法根据事件计算目标状态 {}",
                        LoggingUtils.getTxId(), LoggingUtils.getBranchId(),
                        LoggingUtils.formatContext("从状态", currentStatus, "事件", event));
                return false;
            }

            // 3. 验证状态转换是否合法
            if (!currentStatus.canTransitionTo(targetStatus)) {
                log.warn("[{}|{}] [WARN] - 非法状态转换 {}",
                        LoggingUtils.getTxId(), LoggingUtils.getBranchId(),
                        LoggingUtils.formatContext("从状态", currentStatus, "到状态", targetStatus, "事件", event));
                return false;
            }

            // 4. 执行状态转换
            return doTransition(transactionId, currentStatus, targetStatus, event, reason, exception);

        } finally {
            lock.writeLock().unlock();
        }
    }

    /**
     * 初始化事务状态
     * 显式设置事务的初始状态到缓存中
     *
     * @param transactionId 事务ID
     * @param initialStatus 初始状态
     */
    public void initializeTransactionStatus(String transactionId, TransactionStatus initialStatus) {
        if (transactionId == null || initialStatus == null) {
            log.warn("[{}|{}] [WARN] - 初始化事务状态参数不能为空 {}",
                    LoggingUtils.getTxId(), LoggingUtils.getBranchId(),
                    LoggingUtils.formatContext("transactionId", transactionId, "initialStatus", initialStatus));
            return;
        }

        lock.writeLock().lock();
        try {
            statusCache.put(transactionId, initialStatus);
            log.debug("[{}|{}] [DEBUG] - 初始化事务状态 {}",
                    LoggingUtils.getTxId(), LoggingUtils.getBranchId(),
                    LoggingUtils.formatContext("status", initialStatus));
        } finally {
            lock.writeLock().unlock();
        }
    }

    /**
     * 获取当前状态
     */
    public TransactionStatus getCurrentStatus(String transactionId) {
        lock.readLock().lock();
        try {
            // 先从缓存获取
            return statusCache.get(transactionId);
        } finally {
            lock.readLock().unlock();
        }
    }

    /**
     * 批量获取状态
     */
    public ConcurrentHashMap<String, TransactionStatus> getBatchStatus(java.util.Set<String> transactionIds) {
        ConcurrentHashMap<String, TransactionStatus> result = new ConcurrentHashMap<>();

        lock.readLock().lock();
        try {
            for (String transactionId : transactionIds) {
                TransactionStatus status = getCurrentStatus(transactionId);
                if (status != null) {
                    result.put(transactionId, status);
                }
            }
        } finally {
            lock.readLock().unlock();
        }

        return result;
    }

    /**
     * 强制设置状态（用于人工干预）
     */
    public boolean forceSetStatus(String transactionId, TransactionStatus targetStatus,
                                  String reason, String operator) {

        lock.writeLock().lock();
        try {
            TransactionStatus currentStatus = getCurrentStatus(transactionId);
            if (currentStatus == null) {
                log.warn("[{}|{}] [WARN] - 事务不存在，无法强制设置状态",
                        LoggingUtils.getTxId(), LoggingUtils.getBranchId());
                return false;
            }

            log.warn("[{}|{}] [WARN] - 强制状态转换 {}",
                    LoggingUtils.getTxId(), LoggingUtils.getBranchId(),
                    LoggingUtils.formatContext("从状态", currentStatus, "到状态", targetStatus, "操作人", operator, "原因", reason));

            return doTransition(transactionId, currentStatus, targetStatus,
                    TransactionEvent.MANUAL_INTERVENTION_COMPLETED, reason, null);

        } finally {
            lock.writeLock().unlock();
        }
    }

    /**
     * 清理已完成事务的状态缓存
     */
    public void cleanupFinishedTransactions() {
        lock.writeLock().lock();
        try {
            statusCache.entrySet().removeIf(entry -> {
                TransactionStatus status = entry.getValue();
                return status.isTerminal() && status.isCleanupState();
            });

            log.debug("[{}|{}] [DEBUG] - 清理已完成事务状态缓存 {}",
                    LoggingUtils.getTxId(), LoggingUtils.getBranchId(),
                    LoggingUtils.formatContext("当前缓存大小", statusCache.size()));
        } finally {
            lock.writeLock().unlock();
        }
    }

    /**
     * 获取状态统计信息
     */
    public TransactionStatusStatistics getStatistics() {
        lock.readLock().lock();
        try {
            return TransactionStatusStatistics.builder()
                    .totalTransactions(statusCache.size())
                    .statusDistribution(calculateStatusDistribution())
                    .build();
        } finally {
            lock.readLock().unlock();
        }
    }

    /**
     * 执行状态转换（核心方法）
     *
     * @param context 状态转换上下文
     * @return 转换是否成功
     */
    public boolean executeTransition(StateTransitionContext context) {
        String transactionId = context.getTransactionId();
        TransactionStatus fromStatus = context.getFromStatus();
        TransactionStatus toStatus = context.getToStatus();
        TransactionEvent event = context.getEvent();

        lock.writeLock().lock();
        try {
            // 1. 验证状态转换是否合法
            if (fromStatus != null && !fromStatus.canTransitionTo(toStatus)) {
                log.warn("[{}|{}] [WARN] - 非法状态转换 {}",
                        LoggingUtils.getTxId(), LoggingUtils.getBranchId(),
                        LoggingUtils.formatContext("从状态", fromStatus, "到状态", toStatus, "事件", event));
                return false;
            }

            // 2. 执行转换前监听器
            executeBeforeTransitionListeners(context);

            // 3. 执行实际状态转换（同步更新内存状态）
            boolean success = doTransition(transactionId, fromStatus, toStatus, event,
                                         context.getFailureMessage(), context.getFailureCause());

            if (success) {
                // 4. 执行转换后监听器（异步持久化）
                executeAfterTransitionListeners(context);
            }

            return success;

        } catch (Exception e) {
            log.error("[{}|{}] [ERROR] - 状态转换执行失败 {}",
                    LoggingUtils.getTxId(), LoggingUtils.getBranchId(),
                    LoggingUtils.formatException(e), e);

            // 5. 执行失败监听器
            executeFailureListeners(context, e);

            return false;
        } finally {
            lock.writeLock().unlock();
        }
    }

    /**
     * 执行转换前监听器
     */
    private void executeBeforeTransitionListeners(StateTransitionContext context) {
        List<StateTransitionListener> currentListeners;
        synchronized (listeners) {
            currentListeners = new ArrayList<>(listeners);
        }

        for (StateTransitionListener listener : currentListeners) {
            try {
                if (listener.supports(context)) {
                    listener.beforeTransition(context);
                }
            } catch (Exception e) {
                log.error("[{}|{}] [ERROR] - 执行转换前监听器失败 {}",
                        LoggingUtils.getTxId(), LoggingUtils.getBranchId(),
                        LoggingUtils.formatException(e), e);
            }
        }
    }

    /**
     * 执行转换后监听器
     */
    private void executeAfterTransitionListeners(StateTransitionContext context) {
        List<StateTransitionListener> currentListeners;
        synchronized (listeners) {
            currentListeners = new ArrayList<>(listeners);
        }

        for (StateTransitionListener listener : currentListeners) {
            try {
                if (listener.supports(context)) {
                    listener.afterTransition(context);
                }
            } catch (Exception e) {
                log.error("[{}|{}] [ERROR] - 执行转换后监听器失败 {}",
                        LoggingUtils.getTxId(), LoggingUtils.getBranchId(),
                        LoggingUtils.formatException(e), e);
                // 持久化失败不影响业务流程，只记录日志
            }
        }
    }

    /**
     * 执行失败监听器
     */
    private void executeFailureListeners(StateTransitionContext context, Exception cause) {
        List<StateTransitionListener> currentListeners;
        synchronized (listeners) {
            currentListeners = new ArrayList<>(listeners);
        }

        for (StateTransitionListener listener : currentListeners) {
            try {
                if (listener.supports(context)) {
                    listener.onTransitionFailure(context, cause);
                }
            } catch (Exception e) {
                log.error("[{}|{}] [ERROR] - 执行失败监听器失败 {}",
                        LoggingUtils.getTxId(), LoggingUtils.getBranchId(),
                        LoggingUtils.formatException(e), e);
            }
        }
    }

    /**
     * 更新事务上下文状态
     * 状态机负责同步更新内存中的事务上下文状态
     *
     * @param transactionId 事务ID
     * @param newStatus     新状态
     */
    private void updateTransactionContextStatus(String transactionId, TransactionStatus newStatus) {
        try {
            // 获取当前事务上下文
            TransactionContext context = TransactionContextHolder.getCurrentContext();

            if (context != null && transactionId.equals(context.getTransactionId())) {
                // 更新上下文状态
                context.updateStatus(newStatus);
                log.debug("[{}|{}] [DEBUG] - 状态机更新事务上下文状态 {}",
                        LoggingUtils.getTxId(), LoggingUtils.getBranchId(),
                        LoggingUtils.formatContext("status", newStatus));
            } else {
                log.warn("[{}|{}] [WARN] - 未找到匹配的事务上下文或事务ID不匹配 {}",
                        LoggingUtils.getTxId(), LoggingUtils.getBranchId(),
                        LoggingUtils.formatContext("期望", transactionId, "实际", context != null ? context.getTransactionId() : "null"));
            }

        } catch (Exception e) {
            log.error("[{}|{}] [ERROR] - 状态机更新事务上下文状态失败 {}",
                    LoggingUtils.getTxId(), LoggingUtils.getBranchId(),
                    LoggingUtils.formatException(e), e);
            // 更新失败不影响状态机本身的状态转换
        }
    }

    /**
     * 推断初始状态（兜底机制）
     * 根据事件推断可能的初始状态
     *
     * @param event 触发事件
     * @return 推断的初始状态，如果无法推断则返回null
     */
    private TransactionStatus inferInitialStatus(TransactionEvent event) {
        switch (event) {
            case START:
                // 事务开始事件，初始状态应该是UNKNOWN
                return TransactionStatus.UNKNOWN;

            case EXECUTE:
                // 执行事件，说明事务已经开始，初始状态应该是BEGIN
                return TransactionStatus.BEGIN;

            case COMMIT:
                // 提交事件，说明事务正在执行，初始状态应该是EXECUTING
                return TransactionStatus.EXECUTING;

            case ROLLBACK:
                // 回滚事件，可能从多个状态触发，默认为EXECUTING
                return TransactionStatus.EXECUTING;

            case TIMEOUT:
                // 超时事件，通常发生在执行阶段
                return TransactionStatus.EXECUTING;

            default:
                // 其他事件无法推断初始状态
                log.warn("[{}|{}] [WARN] - 无法推断事件的初始状态 {}",
                        LoggingUtils.getTxId(), LoggingUtils.getBranchId(),
                        LoggingUtils.formatContext("事件", event));
                return null;
        }
    }

    // ==================== 私有方法 ====================

    /**
     * 执行状态转换的核心逻辑
     */
    private boolean doTransition(String transactionId, TransactionStatus currentStatus,
                                 TransactionStatus targetStatus, TransactionEvent event,
                                 String reason, Exception exception) {

        long startTime = System.currentTimeMillis();

        try {
            // 1. 更新内存缓存（同步操作）
            statusCache.put(transactionId, targetStatus);

            // 2. 记录指标
            long duration = System.currentTimeMillis() - startTime;
            metricsCollector.recordStatusTransition(currentStatus, targetStatus, duration);

            log.debug("[{}|{}] [STATE] - {} -> {} {}",
                    LoggingUtils.getTxId(), LoggingUtils.getBranchId(),
                    currentStatus, targetStatus,
                    LoggingUtils.formatContext("event", event.toString(), "cost", duration + "ms"));

            return true;

        } catch (Exception e) {
            long duration = System.currentTimeMillis() - startTime;
            metricsCollector.recordStatusTransitionFailure(currentStatus, targetStatus, duration);

            log.error("[{}|{}] [ERROR] - 状态转换失败 {}",
                    LoggingUtils.getTxId(), LoggingUtils.getBranchId(),
                    LoggingUtils.formatException(e), e);

            return false;
        }
    }

    /**
     * 根据当前状态和事件计算目标状态
     */
    private TransactionStatus calculateTargetStatus(TransactionStatus currentStatus, TransactionEvent event) {
        switch (event) {
            case START:
                return currentStatus == TransactionStatus.UNKNOWN ? TransactionStatus.BEGIN : null;

            case EXECUTE:
                return currentStatus == TransactionStatus.BEGIN || currentStatus == TransactionStatus.WAITING ?
                        TransactionStatus.EXECUTING : null;

            case PAUSE:
                return currentStatus == TransactionStatus.EXECUTING ? TransactionStatus.PAUSED : null;

            case RESUME:
                return currentStatus == TransactionStatus.PAUSED ? TransactionStatus.EXECUTING : null;

            case WAIT:
                return currentStatus == TransactionStatus.EXECUTING ? TransactionStatus.WAITING : null;

            case CONDITION_MET:
                return currentStatus == TransactionStatus.WAITING ? TransactionStatus.EXECUTING : null;

            case COMMIT:
                return currentStatus == TransactionStatus.EXECUTING ? TransactionStatus.COMMITTING : null;

            case COMMIT_SUCCESS:
                return currentStatus == TransactionStatus.COMMITTING ||
                        currentStatus == TransactionStatus.COMMIT_RETRYING ||
                        currentStatus == TransactionStatus.ASYNC_COMMITTING ?
                        TransactionStatus.COMMITTED : null;

            case COMMIT_FAILURE:
                return currentStatus == TransactionStatus.COMMITTING ||
                        currentStatus == TransactionStatus.COMMIT_RETRYING ||
                        currentStatus == TransactionStatus.ASYNC_COMMITTING ?
                        TransactionStatus.COMMIT_FAILED : null;

            case COMMIT_RETRY:
                return currentStatus == TransactionStatus.COMMIT_FAILED ?
                        TransactionStatus.COMMIT_RETRYING : null;

            case ASYNC_COMMIT:
                return currentStatus == TransactionStatus.COMMITTING ?
                        TransactionStatus.ASYNC_COMMITTING : null;

            case ROLLBACK:
                return canRollback(currentStatus) ? TransactionStatus.ROLLBACKING : null;

            case ROLLBACK_SUCCESS:
                return currentStatus == TransactionStatus.ROLLBACKING ||
                        currentStatus == TransactionStatus.ROLLBACK_RETRYING ?
                        TransactionStatus.ROLLBACKED : null;

            case ROLLBACK_FAILURE:
                return currentStatus == TransactionStatus.ROLLBACKING ||
                        currentStatus == TransactionStatus.ROLLBACK_RETRYING ?
                        TransactionStatus.ROLLBACK_FAILED : null;

            case ROLLBACK_RETRY:
                return currentStatus == TransactionStatus.ROLLBACK_FAILED ?
                        TransactionStatus.ROLLBACK_RETRYING : null;

            case TIMEOUT_ROLLBACK:
                return canRollback(currentStatus) ? TransactionStatus.TIMEOUT_ROLLBACKING : null;

            case TIMEOUT_ROLLBACK_SUCCESS:
                return currentStatus == TransactionStatus.TIMEOUT_ROLLBACKING ||
                        currentStatus == TransactionStatus.TIMEOUT_ROLLBACK_RETRYING ?
                        TransactionStatus.TIMEOUT_ROLLBACKED : null;

            case TIMEOUT_ROLLBACK_FAILURE:
                return currentStatus == TransactionStatus.TIMEOUT_ROLLBACKING ||
                        currentStatus == TransactionStatus.TIMEOUT_ROLLBACK_RETRYING ?
                        TransactionStatus.TIMEOUT_ROLLBACK_FAILED : null;

            case TIMEOUT_ROLLBACK_RETRY:
                return currentStatus == TransactionStatus.TIMEOUT_ROLLBACK_FAILED ?
                        TransactionStatus.TIMEOUT_ROLLBACK_RETRYING : null;

            case CANCEL:
                return !currentStatus.isTerminal() ? TransactionStatus.CANCELLED : null;

            case REQUIRE_MANUAL_INTERVENTION:
                return currentStatus.isFailure() ? TransactionStatus.MANUAL_INTERVENTION_REQUIRED : null;

            case FINISH:
                return currentStatus.isTerminal() && !currentStatus.isCleanupState() ?
                        TransactionStatus.FINISHED : null;

            default:
                return null;
        }
    }

    /**
     * 判断当前状态是否可以回滚
     */
    private boolean canRollback(TransactionStatus status) {
        return status == TransactionStatus.EXECUTING ||
                status == TransactionStatus.WAITING ||
                status == TransactionStatus.PAUSED ||
                status == TransactionStatus.COMMIT_FAILED;
    }

    /**
     * 计算状态分布统计
     */
    private java.util.Map<TransactionStatus, Integer> calculateStatusDistribution() {
        java.util.Map<TransactionStatus, Integer> distribution = new java.util.HashMap<>();

        for (TransactionStatus status : statusCache.values()) {
            distribution.merge(status, 1, Integer::sum);
        }

        return distribution;
    }
}
