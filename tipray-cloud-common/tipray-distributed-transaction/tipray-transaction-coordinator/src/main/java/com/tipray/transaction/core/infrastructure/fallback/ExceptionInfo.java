package com.tipray.transaction.core.infrastructure.fallback;

import java.io.PrintWriter;
import java.io.StringWriter;
import java.time.LocalDateTime;
import java.util.Objects;

/**
 * 异常信息封装类
 * 用于保存和传递异常的详细信息
 *
 * <AUTHOR>
 * @version 2.0
 * @since 2025-01-23
 */
public class ExceptionInfo {

    private final String exceptionType;
    private final String message;
    private final String stackTrace;
    private final String rootCauseType;
    private final String rootCauseMessage;
    private final LocalDateTime occurTime;

    private ExceptionInfo(String exceptionType, String message, String stackTrace,
                          String rootCauseType, String rootCauseMessage, LocalDateTime occurTime) {
        this.exceptionType = exceptionType;
        this.message = message;
        this.stackTrace = stackTrace;
        this.rootCauseType = rootCauseType;
        this.rootCauseMessage = rootCauseMessage;
        this.occurTime = occurTime;
    }

    /**
     * 从异常对象创建异常信息
     */
    public static ExceptionInfo from(Throwable throwable) {
        if (throwable == null) {
            return null;
        }

        String exceptionType = throwable.getClass().getName();
        String message = throwable.getMessage();
        String stackTrace = getStackTrace(throwable);

        // 获取根本原因
        Throwable rootCause = getRootCause(throwable);
        String rootCauseType = rootCause.getClass().getName();
        String rootCauseMessage = rootCause.getMessage();

        return new ExceptionInfo(exceptionType, message, stackTrace,
                rootCauseType, rootCauseMessage, LocalDateTime.now());
    }

    /**
     * 获取异常的堆栈跟踪字符串
     */
    private static String getStackTrace(Throwable throwable) {
        StringWriter sw = new StringWriter();
        PrintWriter pw = new PrintWriter(sw);
        throwable.printStackTrace(pw);
        return sw.toString();
    }

    /**
     * 获取异常的根本原因
     */
    private static Throwable getRootCause(Throwable throwable) {
        Throwable cause = throwable;
        while (cause.getCause() != null && cause.getCause() != cause) {
            cause = cause.getCause();
        }
        return cause;
    }

    // Getters
    public String getExceptionType() {
        return exceptionType;
    }

    public String getMessage() {
        return message;
    }

    public String getStackTrace() {
        return stackTrace;
    }

    public String getRootCauseType() {
        return rootCauseType;
    }

    public String getRootCauseMessage() {
        return rootCauseMessage;
    }

    public LocalDateTime getOccurTime() {
        return occurTime;
    }

    /**
     * 获取简化的异常信息（不包含堆栈跟踪）
     */
    public String getSimpleInfo() {
        return String.format("%s: %s",
                exceptionType.substring(exceptionType.lastIndexOf('.') + 1),
                message != null ? message : "无详细信息");
    }

    /**
     * 获取根本原因的简化信息
     */
    public String getRootCauseSimpleInfo() {
        return String.format("%s: %s",
                rootCauseType.substring(rootCauseType.lastIndexOf('.') + 1),
                rootCauseMessage != null ? rootCauseMessage : "无详细信息");
    }

    /**
     * 判断是否为指定类型的异常
     */
    public boolean isInstanceOf(Class<? extends Throwable> exceptionClass) {
        return exceptionType.equals(exceptionClass.getName()) ||
                rootCauseType.equals(exceptionClass.getName());
    }

    /**
     * 判断异常信息中是否包含指定关键字
     */
    public boolean contains(String keyword) {
        if (keyword == null || keyword.trim().isEmpty()) {
            return false;
        }

        String lowerKeyword = keyword.toLowerCase();
        return (message != null && message.toLowerCase().contains(lowerKeyword)) ||
                (rootCauseMessage != null && rootCauseMessage.toLowerCase().contains(lowerKeyword)) ||
                exceptionType.toLowerCase().contains(lowerKeyword) ||
                rootCauseType.toLowerCase().contains(lowerKeyword);
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        ExceptionInfo that = (ExceptionInfo) o;
        return Objects.equals(exceptionType, that.exceptionType) &&
                Objects.equals(message, that.message) &&
                Objects.equals(rootCauseType, that.rootCauseType) &&
                Objects.equals(rootCauseMessage, that.rootCauseMessage);
    }

    @Override
    public int hashCode() {
        return Objects.hash(exceptionType, message, rootCauseType, rootCauseMessage);
    }

    @Override
    public String toString() {
        return String.format("ExceptionInfo{type='%s', message='%s', rootCause='%s: %s', time=%s}",
                exceptionType.substring(exceptionType.lastIndexOf('.') + 1),
                message,
                rootCauseType.substring(rootCauseType.lastIndexOf('.') + 1),
                rootCauseMessage,
                occurTime);
    }
}
