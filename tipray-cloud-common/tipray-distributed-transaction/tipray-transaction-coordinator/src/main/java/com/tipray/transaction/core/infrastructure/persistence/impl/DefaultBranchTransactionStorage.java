package com.tipray.transaction.core.infrastructure.persistence.impl;

import cn.hutool.cache.Cache;
import cn.hutool.cache.CacheUtil;
import com.tipray.transaction.core.domain.transaction.BranchTransactionDO;
import com.tipray.transaction.core.enums.BranchTransactionStatus;
import com.tipray.transaction.core.persistence.BranchTransactionStorage;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

/**
 * 默认分支事务存储实现
 * 基于Hutool缓存的实现，支持过期时间防止内存无限膨胀
 *
 * <AUTHOR>
 * @version 2.0
 * @since 2025-01-23
 */
public class DefaultBranchTransactionStorage implements BranchTransactionStorage {

    private static final Logger log = LoggerFactory.getLogger(DefaultBranchTransactionStorage.class);

    // 使用Hutool的LRU缓存，支持过期时间
    // 默认容量2000，过期时间30分钟
    private final Cache<Long, BranchTransactionDO> branchTransactionCache = CacheUtil.newTimedCache(30 * 60 * 1000L);

    @Override
    public void saveBranchTransaction(BranchTransactionDO branchTransactionDO) {
        if (branchTransactionDO == null || branchTransactionDO.getBranchTransactionId() == null) {
            log.warn("保存分支事务失败：分支事务对象或分支事务ID为空");
            return;
        }

        branchTransactionCache.put(branchTransactionDO.getBranchTransactionId(), branchTransactionDO);
        log.debug("保存分支事务成功: branchId={}, transactionId={}",
                branchTransactionDO.getBranchTransactionId(), branchTransactionDO.getTransactionId());
    }

    @Override
    public void updateBranchTransactionStatus(Long branchId, BranchTransactionStatus status) {
        if (branchId == null || status == null) {
            log.warn("更新分支事务状态失败：分支事务ID或状态为空");
            return;
        }

        BranchTransactionDO branchTransaction = branchTransactionCache.get(branchId);
        if (branchTransaction != null) {
            branchTransaction.setStatus(status);
            // 重新放入缓存以更新过期时间
            branchTransactionCache.put(branchId, branchTransaction);
            log.debug("更新分支事务状态成功: branchId={} -> {}", branchId, status);
        } else {
            log.warn("更新分支事务状态失败：分支事务不存在: branchId={}", branchId);
        }
    }

    @Override
    public void saveBranchRollbackFailure(Object record) {
        log.debug("保存分支事务回滚失败记录: {}", record);
        // TODO: 实现回滚失败记录的持久化
    }

    @Override
    public void deleteBranchTransaction(Long branchId) {
        if (branchId == null) {
            log.warn("删除分支事务失败：分支事务ID为空");
            return;
        }

        BranchTransactionDO removed = branchTransactionCache.get(branchId);
        branchTransactionCache.remove(branchId);

        if (removed != null) {
            log.debug("删除分支事务成功: branchId={}", branchId);
        } else {
            log.warn("删除分支事务失败：分支事务不存在: branchId={}", branchId);
        }
    }

    @Override
    public BranchTransactionDO findBranchTransactionById(Long branchId) {
        if (branchId == null) {
            log.warn("查询分支事务失败：分支事务ID为空");
            return null;
        }

        BranchTransactionDO branchTransaction = branchTransactionCache.get(branchId);
        log.debug("查询分支事务: branchId={} -> {}", branchId, branchTransaction != null ? "找到" : "未找到");
        return branchTransaction;
    }

    @Override
    public Object getStatistics() {
        // 启动缓存的定时清理
//        branchTransactionCache.schedulePrune();

        int totalBranchTransactions = branchTransactionCache.size();
        log.debug("获取分支事务存储统计信息: 总分支事务数={}", totalBranchTransactions);

        return new BranchStorageStatistics(totalBranchTransactions);
    }

    /**
     * 分支事务存储统计信息
     */
    public static class BranchStorageStatistics {
        private final int totalBranchTransactions;

        public BranchStorageStatistics(int totalBranchTransactions) {
            this.totalBranchTransactions = totalBranchTransactions;
        }

        public int getTotalBranchTransactions() {
            return totalBranchTransactions;
        }

        @Override
        public String toString() {
            return "BranchStorageStatistics{totalBranchTransactions=" + totalBranchTransactions + '}';
        }
    }
}
