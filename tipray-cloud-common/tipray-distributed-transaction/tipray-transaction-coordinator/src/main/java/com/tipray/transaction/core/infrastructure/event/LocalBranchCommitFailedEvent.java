package com.tipray.transaction.core.infrastructure.event;

import com.tipray.transaction.core.domain.transaction.BranchTransactionDO;

/**
 * 本地分支提交失败事件
 *
 * <AUTHOR>
 * @version 2.0
 * @since 2025-01-29
 */
public class LocalBranchCommitFailedEvent {

    private final BranchTransactionDO branch;
    private final Exception cause;
    private final long timestamp;

    public LocalBranchCommitFailedEvent(BranchTransactionDO branch, Exception cause) {
        this.branch = branch;
        this.cause = cause;
        this.timestamp = System.currentTimeMillis();
    }

    public BranchTransactionDO getBranch() {
        return branch;
    }

    public Exception getCause() {
        return cause;
    }

    public long getTimestamp() {
        return timestamp;
    }

    @Override
    public String toString() {
        return String.format("LocalBranchCommitFailedEvent{transactionId=%s, branchId=%s, cause=%s, timestamp=%d}",
                branch.getTransactionId(), branch.getBranchTransactionId(), 
                cause != null ? cause.getMessage() : "null", timestamp);
    }
}
