package com.tipray.transaction.core.api.template;

import com.tipray.transaction.core.application.engine.DistributedTransactionEngine;
import com.tipray.transaction.core.domain.funcation.TransactionCallback;
import com.tipray.transaction.core.domain.transaction.TransactionDefinition;
import com.tipray.transaction.core.enums.TransactionMode;
import com.tipray.transaction.core.enums.TransactionPropagation;
import org.springframework.stereotype.Component;

/**
 * 分布式事务模板
 * 提供编程式事务API
 *
 * <AUTHOR>
 * @version 2.0
 * @since 2025-01-23
 */
public class DistributedTransactionTemplate {

    private final DistributedTransactionEngine transactionEngine;

    // 默认配置
    private TransactionMode defaultMode = TransactionMode.AT;
    private TransactionPropagation defaultPropagation = TransactionPropagation.REQUIRED;
    private int defaultTimeout = 30;
    private boolean defaultReadOnly = false;
    private String defaultApplicationName = "tipray-application";

    public DistributedTransactionTemplate(DistributedTransactionEngine transactionEngine) {
        this.transactionEngine = transactionEngine;
    }

    /**
     * 执行分布式事务（使用默认配置）
     */
    public <T> T execute(TransactionCallback<T> callback) {
        TransactionDefinition definition = createDefaultDefinition();
        return transactionEngine.execute(definition, callback);
    }

    /**
     * 执行分布式事务（指定事务模式）
     */
    public <T> T execute(TransactionMode mode, TransactionCallback<T> callback) {
        TransactionDefinition definition = TransactionDefinition.builder()
                .name("ProgrammaticTransaction")
                .mode(mode)
                .propagation(defaultPropagation)
                .timeout(defaultTimeout)
                .readOnly(defaultReadOnly)
                .applicationName(defaultApplicationName)
                .methodSignature("DistributedTransactionTemplate.execute")
                .build();

        return transactionEngine.execute(definition, callback);
    }

    /**
     * 执行分布式事务（指定传播行为）
     */
    public <T> T execute(TransactionPropagation propagation, TransactionCallback<T> callback) {
        TransactionDefinition definition = TransactionDefinition.builder()
                .name("ProgrammaticTransaction")
                .mode(defaultMode)
                .propagation(propagation)
                .timeout(defaultTimeout)
                .readOnly(defaultReadOnly)
                .applicationName(defaultApplicationName)
                .methodSignature("DistributedTransactionTemplate.execute")
                .build();

        return transactionEngine.execute(definition, callback);
    }

    /**
     * 执行分布式事务（完整配置）
     */
    public <T> T execute(TransactionMode mode, TransactionPropagation propagation,
                         int timeout, TransactionCallback<T> callback) {
        TransactionDefinition definition = TransactionDefinition.builder()
                .name("ProgrammaticTransaction")
                .mode(mode)
                .propagation(propagation)
                .timeout(timeout)
                .readOnly(defaultReadOnly)
                .applicationName(defaultApplicationName)
                .methodSignature("DistributedTransactionTemplate.execute")
                .build();

        return transactionEngine.execute(definition, callback);
    }

    /**
     * 执行分布式事务（使用事务定义）
     */
    public <T> T execute(TransactionDefinition definition, TransactionCallback<T> callback) {
        return transactionEngine.execute(definition, callback);
    }

    /**
     * 执行AT模式事务
     */
    public <T> T executeAt(TransactionCallback<T> callback) {
        return execute(TransactionMode.AT, callback);
    }

    /**
     * 执行Saga模式事务
     */
    public <T> T executeSaga(TransactionCallback<T> callback) {
        return execute(TransactionMode.SAGA, callback);
    }

    /**
     * 执行TCC模式事务
     */
    public <T> T executeTcc(TransactionCallback<T> callback) {
        return execute(TransactionMode.TCC, callback);
    }

    /**
     * 执行XA模式事务
     */
    public <T> T executeXa(TransactionCallback<T> callback) {
        return execute(TransactionMode.XA, callback);
    }

    /**
     * 执行新事务（REQUIRES_NEW传播）
     */
    public <T> T executeInNewTransaction(TransactionCallback<T> callback) {
        return execute(TransactionPropagation.REQUIRES_NEW, callback);
    }

    /**
     * 执行嵌套事务
     */
    public <T> T executeNested(TransactionCallback<T> callback) {
        return execute(TransactionPropagation.NESTED, callback);
    }

    /**
     * 执行只读事务
     */
    public <T> T executeReadOnly(TransactionCallback<T> callback) {
        TransactionDefinition definition = TransactionDefinition.builder()
                .name("ReadOnlyTransaction")
                .mode(defaultMode)
                .propagation(defaultPropagation)
                .timeout(defaultTimeout)
                .readOnly(true)
                .applicationName(defaultApplicationName)
                .methodSignature("DistributedTransactionTemplate.executeReadOnly")
                .build();

        return transactionEngine.execute(definition, callback);
    }

    /**
     * 创建事务定义构建器
     */
    public TransactionDefinitionBuilder newDefinition() {
        return new TransactionDefinitionBuilder(this);
    }

    /**
     * 创建默认事务定义
     */
    private TransactionDefinition createDefaultDefinition() {
        return TransactionDefinition.builder()
                .name("ProgrammaticTransaction")
                .mode(defaultMode)
                .propagation(defaultPropagation)
                .timeout(defaultTimeout)
                .readOnly(defaultReadOnly)
                .applicationName(defaultApplicationName)
                .methodSignature("DistributedTransactionTemplate.execute")
                .build();
    }

    // 配置方法
    public DistributedTransactionTemplate setDefaultMode(TransactionMode mode) {
        this.defaultMode = mode;
        return this;
    }

    public DistributedTransactionTemplate setDefaultPropagation(TransactionPropagation propagation) {
        this.defaultPropagation = propagation;
        return this;
    }

    public DistributedTransactionTemplate setDefaultTimeout(int timeout) {
        this.defaultTimeout = timeout;
        return this;
    }

    public DistributedTransactionTemplate setDefaultReadOnly(boolean readOnly) {
        this.defaultReadOnly = readOnly;
        return this;
    }

    public DistributedTransactionTemplate setDefaultApplicationName(String applicationName) {
        this.defaultApplicationName = applicationName;
        return this;
    }

    /**
     * 事务定义构建器
     */
    public static class TransactionDefinitionBuilder {
        private final DistributedTransactionTemplate template;
        private final TransactionDefinition.Builder definitionBuilder;

        private TransactionDefinitionBuilder(DistributedTransactionTemplate template) {
            this.template = template;
            this.definitionBuilder = TransactionDefinition.builder()
                    .mode(template.defaultMode)
                    .propagation(template.defaultPropagation)
                    .timeout(template.defaultTimeout)
                    .readOnly(template.defaultReadOnly)
                    .applicationName(template.defaultApplicationName)
                    .methodSignature("DistributedTransactionTemplate.newDefinition");
        }

        public TransactionDefinitionBuilder name(String name) {
            definitionBuilder.name(name);
            return this;
        }

        public TransactionDefinitionBuilder mode(TransactionMode mode) {
            definitionBuilder.mode(mode);
            return this;
        }

        public TransactionDefinitionBuilder propagation(TransactionPropagation propagation) {
            definitionBuilder.propagation(propagation);
            return this;
        }

        public TransactionDefinitionBuilder timeout(int timeout) {
            definitionBuilder.timeout(timeout);
            return this;
        }

        public TransactionDefinitionBuilder readOnly(boolean readOnly) {
            definitionBuilder.readOnly(readOnly);
            return this;
        }

        public TransactionDefinitionBuilder rollbackFor(Class<? extends Throwable>... exceptions) {
            definitionBuilder.rollbackFor(exceptions);
            return this;
        }

        public TransactionDefinitionBuilder noRollbackFor(Class<? extends Throwable>... exceptions) {
            definitionBuilder.noRollbackFor(exceptions);
            return this;
        }

        public <T> T execute(TransactionCallback<T> callback) {
            TransactionDefinition definition = definitionBuilder.build();
            return template.transactionEngine.execute(definition, callback);
        }
    }
}
