package com.tipray.transaction.core.infrastructure.async;

import com.tipray.transaction.core.context.TransactionContextHolder;
import com.tipray.transaction.core.domain.transaction.TransactionContext;
import com.tipray.transaction.core.infrastructure.pool.TransactionConnectionPoolManager;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.jdbc.datasource.ConnectionHolder;
import org.springframework.transaction.support.TransactionSynchronizationManager;

import javax.sql.DataSource;
import java.sql.Connection;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * AsyncTransactionExecutor测试类
 * 验证Spring事务上下文在异步线程中的传递
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-01-30
 */
@Slf4j
public class AsyncTransactionExecutorTest {

    @Mock
    private TransactionConnectionPoolManager poolManager;

    @Mock
    private DataSource dataSource;

    @Mock
    private Connection connection;

    private AsyncTransactionExecutor asyncExecutor;
    private ExecutorService testExecutor;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        
        testExecutor = Executors.newFixedThreadPool(2);
        when(poolManager.getTransactionExecutor()).thenReturn(testExecutor);
        
        asyncExecutor = new AsyncTransactionExecutor();
        // 使用反射设置私有字段
        setField(asyncExecutor, "poolManager", poolManager);
        setField(asyncExecutor, "dataSource", dataSource);
        
        asyncExecutor.init();
    }

    /**
     * 测试异步执行中Spring事务上下文的传递
     */
    @Test
    void testSpringTransactionContextPropagation() throws Exception {
        // 准备测试数据
        String transactionId = "test-tx-001";
        TransactionContext context = TransactionContext.builder()
                .transactionId(transactionId)
                .build();
        
        // 设置当前事务上下文
        TransactionContextHolder.setCurrentContext(context);
        
        // 模拟Spring事务环境
        ConnectionHolder connectionHolder = new ConnectionHolder(connection);
        TransactionSynchronizationManager.bindResource(dataSource, connectionHolder);
        TransactionSynchronizationManager.setActualTransactionActive(true);
        
        try {
            // 验证主线程中的事务上下文
            assertTrue(TransactionSynchronizationManager.isActualTransactionActive());
            assertNotNull(TransactionSynchronizationManager.getResource(dataSource));
            assertEquals(transactionId, TransactionContextHolder.getCurrentTransactionId());
            
            // 异步执行任务
            CompletableFuture<String> future = asyncExecutor.executeCommitAsync(() -> {
                // 在异步线程中验证事务上下文
                log.info("异步线程中的事务状态: {}", 
                        TransactionSynchronizationManager.isActualTransactionActive());
                log.info("异步线程中的连接资源: {}", 
                        TransactionSynchronizationManager.getResource(dataSource));
                log.info("异步线程中的事务ID: {}", 
                        TransactionContextHolder.getCurrentTransactionId());
                
                // 验证事务上下文是否正确传递
                assertTrue(TransactionSynchronizationManager.getResource(dataSource) != null, 
                          "异步线程中应该能获取到数据库连接资源");
                assertEquals(transactionId, TransactionContextHolder.getCurrentTransactionId(),
                           "异步线程中的事务ID应该与主线程一致");
                
                return "异步任务执行成功";
            });
            
            // 等待异步任务完成
            String result = future.get();
            assertEquals("异步任务执行成功", result);
            
            log.info("测试完成：Spring事务上下文成功传递到异步线程");
            
        } finally {
            // 清理事务上下文
            TransactionSynchronizationManager.unbindResource(dataSource);
            TransactionSynchronizationManager.setActualTransactionActive(false);
            TransactionContextHolder.clearCurrentContext();
        }
    }

    /**
     * 测试无事务环境下的异步执行
     */
    @Test
    void testAsyncExecutionWithoutTransaction() throws Exception {
        // 确保没有事务上下文
        assertFalse(TransactionSynchronizationManager.isActualTransactionActive());
        assertNull(TransactionContextHolder.getCurrentContext());
        
        // 异步执行任务
        CompletableFuture<String> future = asyncExecutor.executeCommitAsync(() -> {
            // 在异步线程中验证无事务状态
            assertFalse(TransactionSynchronizationManager.isActualTransactionActive());
            assertNull(TransactionContextHolder.getCurrentContext());
            
            return "无事务异步任务执行成功";
        });
        
        String result = future.get();
        assertEquals("无事务异步任务执行成功", result);
        
        log.info("测试完成：无事务环境下异步执行正常");
    }

    /**
     * 测试异步执行中的异常处理
     */
    @Test
    void testAsyncExecutionExceptionHandling() {
        // 异步执行会抛异常的任务
        CompletableFuture<Void> future = asyncExecutor.executeCommitAsync(() -> {
            throw new RuntimeException("测试异常");
        });
        
        // 验证异常被正确处理
        assertThrows(Exception.class, future::get);
        
        log.info("测试完成：异步执行异常处理正常");
    }

    /**
     * 使用反射设置私有字段
     */
    private void setField(Object target, String fieldName, Object value) {
        try {
            java.lang.reflect.Field field = target.getClass().getDeclaredField(fieldName);
            field.setAccessible(true);
            field.set(target, value);
        } catch (Exception e) {
            throw new RuntimeException("设置字段失败: " + fieldName, e);
        }
    }
}
