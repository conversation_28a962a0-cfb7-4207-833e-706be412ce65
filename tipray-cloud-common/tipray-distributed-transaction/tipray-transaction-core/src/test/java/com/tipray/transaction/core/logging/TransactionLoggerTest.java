package com.tipray.transaction.core.logging;

import com.tipray.transaction.core.enums.TransactionMode;
import org.junit.jupiter.api.Test;

import java.time.LocalDateTime;

/**
 * TransactionLogger 测试类
 * 验证静态工具类是否正常工作
 */
public class TransactionLoggerTest {

    @Test
    public void testBasicLogging() {
        String txId = "tx-test-123456789";
        Long branchId = 987654321L;
        
        // 测试事务开始日志
        TransactionLogger.logTransactionBegin(txId, TransactionMode.AT, "testMethod");
        
        // 测试分支注册日志
        TransactionLogger.logBranchRegister(txId, branchId, "TestService", "testMethod");
        
        // 测试分支执行日志
        TransactionLogger.logBranchExecute(txId, branchId, "开始执行", "step", 1, "timeout", "30s");
        
        // 测试状态转换日志
        TransactionLogger.logStateTransition(txId, branchId, "UNKNOWN", "REGISTERED", "REGISTER", 5);
        
        // 测试分支提交日志
        TransactionLogger.logBranchCommit(txId, branchId, 100);
        
        // 测试事务提交日志
        TransactionLogger.logTransactionCommit(txId, 3, 250);
        
        // 测试错误日志
        Exception testException = new RuntimeException("测试异常");
        TransactionLogger.logError(txId, branchId, "测试操作", testException);
        
        // 测试警告日志
        TransactionLogger.logWarn(txId, branchId, "测试警告", "这是一个测试警告消息");
        
        // 测试调试日志
        TransactionLogger.logDebug(txId, branchId, "测试调试", "param1", "value1", "param2", "value2");
        
        // 测试性能日志
        TransactionLogger.logPerformance(txId, "测试阶段", 150, "metric1", "value1");
    }
    
    @Test
    public void testAutoTimingMethods() {
        String txId = "tx-timing-test-123";
        Long branchId = 111222333L;
        
        // 测试自动计算耗时的方法
        LocalDateTime startTime = LocalDateTime.now().minusSeconds(2);
        
        // 测试事务提交自动计算耗时
        TransactionLogger.logTransactionCommitWithStartTime(txId, 2, startTime);
        
        // 测试分支提交自动计算耗时
        TransactionLogger.logBranchCommitWithStartTime(txId, branchId, startTime);
        
        // 测试分支回滚自动计算耗时
        TransactionLogger.logBranchRollbackWithStartTime(txId, branchId, startTime);
        
        // 测试性能日志自动计算耗时
        TransactionLogger.logPerformanceWithStartTime(txId, "性能测试", startTime, "metric", "test");
        
        // 测试毫秒时间戳方式
        long startTimeMillis = System.currentTimeMillis() - 1000; // 1秒前
        TransactionLogger.logBranchCommitWithStartTime(txId, branchId, startTimeMillis);
    }
    
    @Test
    public void testNullValues() {
        // 测试空值处理
        TransactionLogger.logBranchRegister("tx-null-test", null, "TestService", "testMethod");
        TransactionLogger.logBranchExecute("tx-null-test", null, "测试空分支ID");
        TransactionLogger.logError("tx-null-test", null, "测试操作", new RuntimeException("测试"));
    }
    
    @Test
    public void testLongMessages() {
        String txId = "tx-long-message-test";
        String longMessage = "这是一个很长的消息".repeat(20); // 创建一个很长的消息
        
        TransactionLogger.logWarn(txId, null, "长消息测试", longMessage);
        
        Exception longException = new RuntimeException(longMessage);
        TransactionLogger.logError(txId, null, "长异常消息测试", longException);
    }
}
