package com.tipray.transaction.core.persistence;

import com.tipray.transaction.core.domain.transaction.BranchTransactionDO;
import com.tipray.transaction.core.enums.BranchTransactionStatus;

/**
 * 事务存储接口
 * 定义事务数据持久化的基本操作
 *
 * <AUTHOR>
 * @version 2.0
 * @since 2025-01-23
 */
public interface BranchTransactionStorage {

    /**
     * 保存事务
     */
    void saveBranchTransaction(BranchTransactionDO branchTransactionDO);

    /**
     * 更新事务状态
     */
    void updateBranchTransactionStatus(Long branchId, BranchTransactionStatus status);

    /**
     * 保存回滚失败记录
     */
    void saveBranchRollbackFailure(Object record);

    /**
     * 删除事务
     */
    void deleteBranchTransaction(Long branchId);

    /**
     * 根据事务ID查询事务
     */
    BranchTransactionDO findBranchTransactionById(Long branchId);

    /**
     * 获取存储统计信息
     */
    Object getStatistics();
}
