package com.tipray.transaction.core.exception;

/**
 * Tipray分布式事务超时异常
 * 框架内部事务超时异常，区别于业务HTTP超时异常
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-06-20
 */
public class DistributedTransactionTimeoutException extends DistributedTransactionException {

    /**
     * 序列化版本号
     */
    private static final long serialVersionUID = 1L;

    /**
     * 超时时间（毫秒）
     */
    private long timeoutMillis;

    /**
     * 实际执行时间（毫秒）
     */
    private long actualExecutionTime;

    /**
     * 超时类型
     */
    private TimeoutType timeoutType;

    /**
     * 操作名称
     */
    private String operationName;

    /**
     * 默认构造函数
     */
    public DistributedTransactionTimeoutException() {
        super();
        this.timeoutType = TimeoutType.UNKNOWN;
    }

    /**
     * 构造函数
     *
     * @param message 错误信息
     */
    public DistributedTransactionTimeoutException(String message) {
        super(message);
        this.timeoutType = TimeoutType.UNKNOWN;
    }

    /**
     * 构造函数
     *
     * @param message 错误信息
     * @param cause   原因异常
     */
    public DistributedTransactionTimeoutException(String message, Throwable cause) {
        super(message, cause);
        this.timeoutType = TimeoutType.UNKNOWN;
    }

    /**
     * 构造函数
     *
     * @param message       错误信息
     * @param timeoutMillis 超时时间
     * @param timeoutType   超时类型
     */
    public DistributedTransactionTimeoutException(String message, long timeoutMillis, TimeoutType timeoutType) {
        super("TRANSACTION_TIMEOUT", message);
        this.timeoutMillis = timeoutMillis;
        this.timeoutType = timeoutType;
    }

    /**
     * 构造函数
     *
     * @param message             错误信息
     * @param timeoutMillis       超时时间
     * @param actualExecutionTime 实际执行时间
     * @param timeoutType         超时类型
     * @param operationName       操作名称
     */
    public DistributedTransactionTimeoutException(String message, long timeoutMillis, long actualExecutionTime,
                                                  TimeoutType timeoutType, String operationName) {
        super("TRANSACTION_TIMEOUT", message);
        this.timeoutMillis = timeoutMillis;
        this.actualExecutionTime = actualExecutionTime;
        this.timeoutType = timeoutType;
        this.operationName = operationName;
    }

    /**
     * 构造函数
     *
     * @param message             错误信息
     * @param cause               原因异常
     * @param timeoutMillis       超时时间
     * @param actualExecutionTime 实际执行时间
     * @param timeoutType         超时类型
     * @param operationName       操作名称
     */
    public DistributedTransactionTimeoutException(String message, Throwable cause, long timeoutMillis, long actualExecutionTime,
                                                  TimeoutType timeoutType, String operationName) {
        super("TRANSACTION_TIMEOUT", message, cause);
        this.timeoutMillis = timeoutMillis;
        this.actualExecutionTime = actualExecutionTime;
        this.timeoutType = timeoutType;
        this.operationName = operationName;
    }

    /**
     * 构造函数
     *
     * @param message             错误信息
     * @param cause               原因异常
     * @param transactionId       事务ID
     * @param groupId             事务组ID
     * @param timeoutMillis       超时时间
     * @param actualExecutionTime 实际执行时间
     * @param timeoutType         超时类型
     * @param operationName       操作名称
     */
    public DistributedTransactionTimeoutException(String message, Throwable cause, String transactionId, String groupId,
                                                  long timeoutMillis, long actualExecutionTime, TimeoutType timeoutType, String operationName) {
        super("TRANSACTION_TIMEOUT", message, cause, transactionId, groupId);
        this.timeoutMillis = timeoutMillis;
        this.actualExecutionTime = actualExecutionTime;
        this.timeoutType = timeoutType;
        this.operationName = operationName;
    }

    /**
     * 获取超时时间
     *
     * @return 超时时间（毫秒）
     */
    public long getTimeoutMillis() {
        return timeoutMillis;
    }

    /**
     * 设置超时时间
     *
     * @param timeoutMillis 超时时间（毫秒）
     */
    public void setTimeoutMillis(long timeoutMillis) {
        this.timeoutMillis = timeoutMillis;
    }

    /**
     * 获取实际执行时间
     *
     * @return 实际执行时间（毫秒）
     */
    public long getActualExecutionTime() {
        return actualExecutionTime;
    }

    /**
     * 设置实际执行时间
     *
     * @param actualExecutionTime 实际执行时间（毫秒）
     */
    public void setActualExecutionTime(long actualExecutionTime) {
        this.actualExecutionTime = actualExecutionTime;
    }

    /**
     * 获取超时类型
     *
     * @return 超时类型
     */
    public TimeoutType getTimeoutType() {
        return timeoutType;
    }

    /**
     * 设置超时类型
     *
     * @param timeoutType 超时类型
     */
    public void setTimeoutType(TimeoutType timeoutType) {
        this.timeoutType = timeoutType;
    }

    /**
     * 获取操作名称
     *
     * @return 操作名称
     */
    public String getOperationName() {
        return operationName;
    }

    /**
     * 设置操作名称
     *
     * @param operationName 操作名称
     */
    public void setOperationName(String operationName) {
        this.operationName = operationName;
    }

    /**
     * 判断是否为可重试的超时
     *
     * @return true表示可重试
     */
    public boolean isRetryable() {
        return timeoutType == TimeoutType.NETWORK_TIMEOUT ||
                timeoutType == TimeoutType.RESOURCE_TIMEOUT;
    }

    /**
     * 判断是否为致命超时（需要回滚）
     *
     * @return true表示致命超时
     */
    public boolean isFatal() {
        return timeoutType == TimeoutType.GLOBAL_TRANSACTION_TIMEOUT ||
                timeoutType == TimeoutType.BRANCH_TRANSACTION_TIMEOUT;
    }

    @Override
    public String getFullMessage() {
        StringBuilder sb = new StringBuilder();
        sb.append(super.getFullMessage());

        if (operationName != null) {
            sb.append(" [操作: ").append(operationName).append("]");
        }

        if (timeoutType != null) {
            sb.append(" [超时类型: ").append(timeoutType.getDescription()).append("]");
        }

        if (timeoutMillis > 0) {
            sb.append(" [超时时间: ").append(timeoutMillis).append("ms]");
        }

        if (actualExecutionTime > 0) {
            sb.append(" [实际执行: ").append(actualExecutionTime).append("ms]");
        }

        return sb.toString();
    }

    /**
     * 超时类型枚举
     */
    public enum TimeoutType {
        /**
         * 全局事务超时
         */
        GLOBAL_TRANSACTION_TIMEOUT("GLOBAL_TRANSACTION_TIMEOUT", "全局事务超时"),

        /**
         * 分支事务超时
         */
        BRANCH_TRANSACTION_TIMEOUT("BRANCH_TRANSACTION_TIMEOUT", "分支事务超时"),

        /**
         * 网络超时
         */
        NETWORK_TIMEOUT("NETWORK_TIMEOUT", "网络通信超时"),

        /**
         * 资源超时
         */
        RESOURCE_TIMEOUT("RESOURCE_TIMEOUT", "资源获取超时"),

        /**
         * 提交超时
         */
        COMMIT_TIMEOUT("COMMIT_TIMEOUT", "事务提交超时"),

        /**
         * 回滚超时
         */
        ROLLBACK_TIMEOUT("ROLLBACK_TIMEOUT", "事务回滚超时"),

        /**
         * 其他未知超时
         */
        UNKNOWN("UNKNOWN", "未知超时");

        private final String code;
        private final String description;

        TimeoutType(String code, String description) {
            this.code = code;
            this.description = description;
        }

        public String getCode() {
            return code;
        }

        public String getDescription() {
            return description;
        }
    }
}
