package com.tipray.transaction.core.domain.propagation;

import com.tipray.transaction.core.domain.transaction.TransactionContext;

/**
 * 传播处理结果
 * 包含传播处理的结果信息
 *
 * <AUTHOR>
 * @version 2.0
 * @since 2025-01-23
 */
public class PropagationResult {

    private final boolean newTransactionRequired;
    private final TransactionContext existingContext;
    private final TransactionContext suspendedContext;
    private final boolean nested;

    private PropagationResult(Builder builder) {
        this.newTransactionRequired = builder.newTransactionRequired;
        this.existingContext = builder.existingContext;
        this.suspendedContext = builder.suspendedContext;
        this.nested = builder.nested;
    }

    public static Builder builder() {
        return new Builder();
    }

    /**
     * 判断是否需要创建新事务
     */
    public boolean isNewTransactionRequired() {
        return newTransactionRequired;
    }

    /**
     * 获取现有事务上下文
     */
    public TransactionContext getExistingContext() {
        return existingContext;
    }

    /**
     * 获取挂起的事务上下文
     */
    public TransactionContext getSuspendedContext() {
        return suspendedContext;
    }

    /**
     * 判断是否为嵌套事务
     */
    public boolean isNested() {
        return nested;
    }

    /**
     * 判断是否有挂起的事务
     */
    public boolean hasSuspendedTransaction() {
        return suspendedContext != null;
    }

    /**
     * 判断是否加入现有事务
     */
    public boolean isJoiningExistingTransaction() {
        return !newTransactionRequired && existingContext != null;
    }

    /**
     * 判断是否非事务执行
     */
    public boolean isNonTransactional() {
        return !newTransactionRequired && existingContext == null;
    }

    @Override
    public String toString() {
        return String.format("PropagationResult{newTransaction=%s, existing=%s, suspended=%s, nested=%s}",
                newTransactionRequired,
                existingContext != null ? existingContext.getTransactionId() : "null",
                suspendedContext != null ? suspendedContext.getTransactionId() : "null",
                nested);
    }

    public static class Builder {
        private boolean newTransactionRequired = false;
        private TransactionContext existingContext;
        private TransactionContext suspendedContext;
        private boolean nested = false;

        public Builder newTransactionRequired(boolean newTransactionRequired) {
            this.newTransactionRequired = newTransactionRequired;
            return this;
        }

        public Builder existingContext(TransactionContext existingContext) {
            this.existingContext = existingContext;
            return this;
        }

        public Builder suspendedContext(TransactionContext suspendedContext) {
            this.suspendedContext = suspendedContext;
            return this;
        }

        public Builder nested(boolean nested) {
            this.nested = nested;
            return this;
        }

        public PropagationResult build() {
            return new PropagationResult(this);
        }
    }
}
