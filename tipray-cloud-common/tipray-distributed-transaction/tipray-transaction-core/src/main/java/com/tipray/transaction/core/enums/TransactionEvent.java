package com.tipray.transaction.core.enums;

/**
 * 事务事件枚举
 * 定义触发状态转换的各种事件
 *
 * <AUTHOR>
 * @version 2.0
 * @since 2025-01-23
 */
public enum TransactionEvent {

    // ========== 基础事件 ==========
    /**
     * 开始事务
     */
    START("开始事务", "事务开始执行"),

    /**
     * 执行事务
     */
    EXECUTE("执行事务", "开始执行业务逻辑"),

    /**
     * 暂停事务
     */
    PAUSE("暂停事务", "事务被暂停"),

    /**
     * 恢复事务
     */
    RESUME("恢复事务", "事务从暂停状态恢复"),

    /**
     * 等待条件
     */
    WAIT("等待条件", "事务等待某些条件满足"),

    /**
     * 条件满足
     */
    CONDITION_MET("条件满足", "等待的条件已满足"),

    // ========== 提交相关事件 ==========
    /**
     * 提交事务
     */
    COMMIT("提交事务", "开始提交事务"),

    /**
     * 提交成功
     */
    COMMIT_SUCCESS("提交成功", "事务提交成功"),

    /**
     * 提交失败
     */
    COMMIT_FAILURE("提交失败", "事务提交失败"),

    /**
     * 提交重试
     */
    COMMIT_RETRY("提交重试", "重试提交事务"),

    /**
     * 异步提交
     */
    ASYNC_COMMIT("异步提交", "开始异步提交"),

    // ========== 回滚相关事件 ==========
    /**
     * 回滚事务
     */
    ROLLBACK("回滚事务", "开始回滚事务"),

    /**
     * 回滚成功
     */
    ROLLBACK_SUCCESS("回滚成功", "事务回滚成功"),

    /**
     * 回滚失败
     */
    ROLLBACK_FAILURE("回滚失败", "事务回滚失败"),

    /**
     * 回滚重试
     */
    ROLLBACK_RETRY("回滚重试", "重试回滚事务"),

    /**
     * 超时回滚
     */
    TIMEOUT_ROLLBACK("超时回滚", "因超时触发回滚"),

    /**
     * 超时回滚成功
     */
    TIMEOUT_ROLLBACK_SUCCESS("超时回滚成功", "超时回滚成功"),

    /**
     * 超时回滚失败
     */
    TIMEOUT_ROLLBACK_FAILURE("超时回滚失败", "超时回滚失败"),

    /**
     * 超时回滚重试
     */
    TIMEOUT_ROLLBACK_RETRY("超时回滚重试", "重试超时回滚"),

    // ========== 特殊事件 ==========
    /**
     * 取消事务
     */
    CANCEL("取消事务", "事务被取消"),

    /**
     * 超时事件
     */
    TIMEOUT("超时事件", "事务执行超时"),

    /**
     * 需要人工干预
     */
    REQUIRE_MANUAL_INTERVENTION("需要人工干预", "需要人工干预处理"),

    /**
     * 人工干预完成
     */
    MANUAL_INTERVENTION_COMPLETED("人工干预完成", "人工干预处理完成"),

    /**
     * 清理资源
     */
    CLEANUP("清理资源", "清理事务相关资源"),

    /**
     * 完成事务
     */
    FINISH("完成事务", "事务处理完成");

    private final String name;
    private final String description;

    TransactionEvent(String name, String description) {
        this.name = name;
        this.description = description;
    }

    /**
     * 获取事件名称
     */
    public String getName() {
        return name;
    }

    /**
     * 获取事件描述
     */
    public String getDescription() {
        return description;
    }

    /**
     * 判断是否为提交相关事件
     */
    public boolean isCommitRelated() {
        return this == COMMIT || this == COMMIT_SUCCESS || this == COMMIT_FAILURE ||
                this == COMMIT_RETRY || this == ASYNC_COMMIT;
    }

    /**
     * 判断是否为回滚相关事件
     */
    public boolean isRollbackRelated() {
        return this == ROLLBACK || this == ROLLBACK_SUCCESS || this == ROLLBACK_FAILURE ||
                this == ROLLBACK_RETRY || this == TIMEOUT_ROLLBACK ||
                this == TIMEOUT_ROLLBACK_SUCCESS || this == TIMEOUT_ROLLBACK_FAILURE ||
                this == TIMEOUT_ROLLBACK_RETRY;
    }

    /**
     * 判断是否为成功事件
     */
    public boolean isSuccessEvent() {
        return this == COMMIT_SUCCESS || this == ROLLBACK_SUCCESS ||
                this == TIMEOUT_ROLLBACK_SUCCESS || this == MANUAL_INTERVENTION_COMPLETED;
    }

    /**
     * 判断是否为失败事件
     */
    public boolean isFailureEvent() {
        return this == COMMIT_FAILURE || this == ROLLBACK_FAILURE ||
                this == TIMEOUT_ROLLBACK_FAILURE;
    }

    /**
     * 判断是否为重试事件
     */
    public boolean isRetryEvent() {
        return this == COMMIT_RETRY || this == ROLLBACK_RETRY ||
                this == TIMEOUT_ROLLBACK_RETRY;
    }

    /**
     * 判断是否为终结事件
     */
    public boolean isTerminalEvent() {
        return this == FINISH || this == CANCEL || this == REQUIRE_MANUAL_INTERVENTION;
    }
}
