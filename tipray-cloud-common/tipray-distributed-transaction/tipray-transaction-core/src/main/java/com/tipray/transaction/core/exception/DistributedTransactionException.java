package com.tipray.transaction.core.exception;

/**
 * Tipray分布式事务基础异常
 * 所有分布式事务相关异常的基类
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-06-20
 */
public class DistributedTransactionException extends RuntimeException {

    /**
     * 序列化版本号
     */
    private static final long serialVersionUID = 1L;

    /**
     * 错误代码
     */
    private String errorCode;

    /**
     * 事务ID
     */
    private String transactionId;

    /**
     * 事务组ID
     */
    private String groupId;

    /**
     * 默认构造函数
     */
    public DistributedTransactionException() {
        super();
    }

    /**
     * 构造函数
     *
     * @param message 错误信息
     */
    public DistributedTransactionException(String message) {
        super(message);
    }

    /**
     * 构造函数
     *
     * @param message 错误信息
     * @param cause   原因异常
     */
    public DistributedTransactionException(String message, Throwable cause) {
        super(message, cause);
    }

    /**
     * 构造函数
     *
     * @param cause 原因异常
     */
    public DistributedTransactionException(Throwable cause) {
        super(cause);
    }

    /**
     * 构造函数
     *
     * @param errorCode 错误代码
     * @param message   错误信息
     */
    public DistributedTransactionException(String errorCode, String message) {
        super(message);
        this.errorCode = errorCode;
    }

    /**
     * 构造函数
     *
     * @param errorCode 错误代码
     * @param message   错误信息
     * @param cause     原因异常
     */
    public DistributedTransactionException(String errorCode, String message, Throwable cause) {
        super(message, cause);
        this.errorCode = errorCode;
    }

    /**
     * 构造函数
     *
     * @param errorCode     错误代码
     * @param message       错误信息
     * @param transactionId 事务ID
     * @param groupId       事务组ID
     */
    public DistributedTransactionException(String errorCode, String message, String transactionId, String groupId) {
        super(message);
        this.errorCode = errorCode;
        this.transactionId = transactionId;
        this.groupId = groupId;
    }

    /**
     * 构造函数
     *
     * @param errorCode     错误代码
     * @param message       错误信息
     * @param cause         原因异常
     * @param transactionId 事务ID
     * @param groupId       事务组ID
     */
    public DistributedTransactionException(String errorCode, String message, Throwable cause, String transactionId, String groupId) {
        super(message, cause);
        this.errorCode = errorCode;
        this.transactionId = transactionId;
        this.groupId = groupId;
    }

    /**
     * 获取错误代码
     *
     * @return 错误代码
     */
    public String getErrorCode() {
        return errorCode;
    }

    /**
     * 设置错误代码
     *
     * @param errorCode 错误代码
     */
    public void setErrorCode(String errorCode) {
        this.errorCode = errorCode;
    }

    /**
     * 获取事务ID
     *
     * @return 事务ID
     */
    public String getTransactionId() {
        return transactionId;
    }

    /**
     * 设置事务ID
     *
     * @param transactionId 事务ID
     */
    public void setTransactionId(String transactionId) {
        this.transactionId = transactionId;
    }

    /**
     * 获取事务组ID
     *
     * @return 事务组ID
     */
    public String getGroupId() {
        return groupId;
    }

    /**
     * 设置事务组ID
     *
     * @param groupId 事务组ID
     */
    public void setGroupId(String groupId) {
        this.groupId = groupId;
    }

    /**
     * 获取完整的错误信息
     * 包含错误代码、事务信息等
     *
     * @return 完整错误信息
     */
    public String getFullMessage() {
        StringBuilder sb = new StringBuilder();

        if (errorCode != null) {
            sb.append("[错误代码: ").append(errorCode).append("] ");
        }

        if (transactionId != null) {
            sb.append("[事务ID: ").append(transactionId).append("] ");
        }

        if (groupId != null) {
            sb.append("[事务组: ").append(groupId).append("] ");
        }

        sb.append(getMessage());

        return sb.toString();
    }

    @Override
    public String toString() {
        return getClass().getSimpleName() + ": " + getFullMessage();
    }
}
