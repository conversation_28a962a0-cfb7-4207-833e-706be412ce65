package com.tipray.transaction.core.exception;

/**
 * Tipray分布式事务配置异常
 * 配置错误或配置缺失时抛出的异常
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-06-20
 */
public class DistributedTransactionConfigurationException extends DistributedTransactionException {

    /**
     * 序列化版本号
     */
    private static final long serialVersionUID = 1L;

    /**
     * 配置异常类型
     */
    private ConfigurationExceptionType configurationExceptionType;

    /**
     * 配置项名称
     */
    private String configurationKey;

    /**
     * 配置项值
     */
    private String configurationValue;

    /**
     * 配置文件路径
     */
    private String configurationFilePath;

    /**
     * 默认构造函数
     */
    public DistributedTransactionConfigurationException() {
        super();
        this.configurationExceptionType = ConfigurationExceptionType.UNKNOWN;
    }

    /**
     * 构造函数
     *
     * @param message 错误信息
     */
    public DistributedTransactionConfigurationException(String message) {
        super(message);
        this.configurationExceptionType = ConfigurationExceptionType.UNKNOWN;
    }

    /**
     * 构造函数
     *
     * @param message 错误信息
     * @param cause   原因异常
     */
    public DistributedTransactionConfigurationException(String message, Throwable cause) {
        super(message, cause);
        this.configurationExceptionType = ConfigurationExceptionType.UNKNOWN;
    }

    /**
     * 构造函数
     *
     * @param message                    错误信息
     * @param configurationExceptionType 配置异常类型
     * @param configurationKey           配置项名称
     */
    public DistributedTransactionConfigurationException(String message, ConfigurationExceptionType configurationExceptionType, String configurationKey) {
        super("CONFIGURATION_ERROR", message);
        this.configurationExceptionType = configurationExceptionType;
        this.configurationKey = configurationKey;
    }

    /**
     * 构造函数
     *
     * @param message                    错误信息
     * @param configurationExceptionType 配置异常类型
     * @param configurationKey           配置项名称
     * @param configurationValue         配置项值
     */
    public DistributedTransactionConfigurationException(String message, ConfigurationExceptionType configurationExceptionType,
                                                        String configurationKey, String configurationValue) {
        super("CONFIGURATION_ERROR", message);
        this.configurationExceptionType = configurationExceptionType;
        this.configurationKey = configurationKey;
        this.configurationValue = configurationValue;
    }

    /**
     * 构造函数
     *
     * @param message                    错误信息
     * @param cause                      原因异常
     * @param configurationExceptionType 配置异常类型
     * @param configurationKey           配置项名称
     * @param configurationValue         配置项值
     * @param configurationFilePath      配置文件路径
     */
    public DistributedTransactionConfigurationException(String message, Throwable cause, ConfigurationExceptionType configurationExceptionType,
                                                        String configurationKey, String configurationValue, String configurationFilePath) {
        super("CONFIGURATION_ERROR", message, cause);
        this.configurationExceptionType = configurationExceptionType;
        this.configurationKey = configurationKey;
        this.configurationValue = configurationValue;
        this.configurationFilePath = configurationFilePath;
    }

    /**
     * 获取配置异常类型
     *
     * @return 配置异常类型
     */
    public ConfigurationExceptionType getConfigurationExceptionType() {
        return configurationExceptionType;
    }

    /**
     * 设置配置异常类型
     *
     * @param configurationExceptionType 配置异常类型
     */
    public void setConfigurationExceptionType(ConfigurationExceptionType configurationExceptionType) {
        this.configurationExceptionType = configurationExceptionType;
    }

    /**
     * 获取配置项名称
     *
     * @return 配置项名称
     */
    public String getConfigurationKey() {
        return configurationKey;
    }

    /**
     * 设置配置项名称
     *
     * @param configurationKey 配置项名称
     */
    public void setConfigurationKey(String configurationKey) {
        this.configurationKey = configurationKey;
    }

    /**
     * 获取配置项值
     *
     * @return 配置项值
     */
    public String getConfigurationValue() {
        return configurationValue;
    }

    /**
     * 设置配置项值
     *
     * @param configurationValue 配置项值
     */
    public void setConfigurationValue(String configurationValue) {
        this.configurationValue = configurationValue;
    }

    /**
     * 获取配置文件路径
     *
     * @return 配置文件路径
     */
    public String getConfigurationFilePath() {
        return configurationFilePath;
    }

    /**
     * 设置配置文件路径
     *
     * @param configurationFilePath 配置文件路径
     */
    public void setConfigurationFilePath(String configurationFilePath) {
        this.configurationFilePath = configurationFilePath;
    }

    /**
     * 判断是否为致命配置错误
     *
     * @return true表示致命错误
     */
    public boolean isFatal() {
        return configurationExceptionType == ConfigurationExceptionType.MISSING_REQUIRED_CONFIGURATION ||
                configurationExceptionType == ConfigurationExceptionType.INVALID_CONFIGURATION_FORMAT ||
                configurationExceptionType == ConfigurationExceptionType.CONFIGURATION_FILE_NOT_FOUND;
    }

    @Override
    public String getFullMessage() {
        StringBuilder sb = new StringBuilder();
        sb.append(super.getFullMessage());

        if (configurationKey != null) {
            sb.append(" [配置项: ").append(configurationKey).append("]");
        }

        if (configurationValue != null) {
            sb.append(" [配置值: ").append(configurationValue).append("]");
        }

        if (configurationFilePath != null) {
            sb.append(" [配置文件: ").append(configurationFilePath).append("]");
        }

        if (configurationExceptionType != null) {
            sb.append(" [配置异常类型: ").append(configurationExceptionType.getDescription()).append("]");
        }

        return sb.toString();
    }

    /**
     * 配置异常类型枚举
     */
    public enum ConfigurationExceptionType {
        /**
         * 缺少必需配置
         */
        MISSING_REQUIRED_CONFIGURATION("MISSING_REQUIRED_CONFIGURATION", "缺少必需配置"),

        /**
         * 配置值无效
         */
        INVALID_CONFIGURATION_VALUE("INVALID_CONFIGURATION_VALUE", "配置值无效"),

        /**
         * 配置格式错误
         */
        INVALID_CONFIGURATION_FORMAT("INVALID_CONFIGURATION_FORMAT", "配置格式错误"),

        /**
         * 配置文件未找到
         */
        CONFIGURATION_FILE_NOT_FOUND("CONFIGURATION_FILE_NOT_FOUND", "配置文件未找到"),

        /**
         * 配置文件读取失败
         */
        CONFIGURATION_FILE_READ_ERROR("CONFIGURATION_FILE_READ_ERROR", "配置文件读取失败"),

        /**
         * 配置冲突
         */
        CONFIGURATION_CONFLICT("CONFIGURATION_CONFLICT", "配置冲突"),

        /**
         * 配置版本不兼容
         */
        CONFIGURATION_VERSION_INCOMPATIBLE("CONFIGURATION_VERSION_INCOMPATIBLE", "配置版本不兼容"),

        /**
         * 其他未知配置异常
         */
        UNKNOWN("UNKNOWN", "未知配置异常");

        private final String code;
        private final String description;

        ConfigurationExceptionType(String code, String description) {
            this.code = code;
            this.description = description;
        }

        public String getCode() {
            return code;
        }

        public String getDescription() {
            return description;
        }
    }
}
