package com.tipray.transaction.core.exception;

/**
 * Tipray非法事务状态异常
 * 当事务状态不符合传播行为要求时抛出
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-06-20
 */
public class DistributedIllegalTransactionStateException extends DistributedTransactionException {

    /**
     * 序列化版本号
     */
    private static final long serialVersionUID = 1L;

    /**
     * 当前事务状态
     */
    private String currentState;

    /**
     * 期望的事务状态
     */
    private String expectedState;

    /**
     * 传播行为
     */
    private String propagationBehavior;

    /**
     * 默认构造函数
     */
    public DistributedIllegalTransactionStateException() {
        super();
    }

    /**
     * 构造函数
     *
     * @param message 错误信息
     */
    public DistributedIllegalTransactionStateException(String message) {
        super(message);
    }

    /**
     * 构造函数
     *
     * @param message 错误信息
     * @param cause   原因异常
     */
    public DistributedIllegalTransactionStateException(String message, Throwable cause) {
        super(message, cause);
    }

    /**
     * 构造函数
     *
     * @param cause 原因异常
     */
    public DistributedIllegalTransactionStateException(Throwable cause) {
        super(cause);
    }

    /**
     * 构造函数
     *
     * @param message       错误信息
     * @param currentState  当前状态
     * @param expectedState 期望状态
     */
    public DistributedIllegalTransactionStateException(String message, String currentState, String expectedState) {
        super("ILLEGAL_TRANSACTION_STATE", message);
        this.currentState = currentState;
        this.expectedState = expectedState;
    }

    /**
     * 构造函数
     *
     * @param message             错误信息
     * @param currentState        当前状态
     * @param expectedState       期望状态
     * @param propagationBehavior 传播行为
     */
    public DistributedIllegalTransactionStateException(String message, String currentState, String expectedState, String propagationBehavior) {
        super("ILLEGAL_TRANSACTION_STATE", message);
        this.currentState = currentState;
        this.expectedState = expectedState;
        this.propagationBehavior = propagationBehavior;
    }

    /**
     * 构造函数
     *
     * @param message             错误信息
     * @param cause               原因异常
     * @param transactionId       事务ID
     * @param groupId             事务组ID
     * @param currentState        当前状态
     * @param expectedState       期望状态
     * @param propagationBehavior 传播行为
     */
    public DistributedIllegalTransactionStateException(String message, Throwable cause, String transactionId, String groupId,
                                                       String currentState, String expectedState, String propagationBehavior) {
        super("ILLEGAL_TRANSACTION_STATE", message, cause, transactionId, groupId);
        this.currentState = currentState;
        this.expectedState = expectedState;
        this.propagationBehavior = propagationBehavior;
    }

    /**
     * 获取当前状态
     *
     * @return 当前状态
     */
    public String getCurrentState() {
        return currentState;
    }

    /**
     * 设置当前状态
     *
     * @param currentState 当前状态
     */
    public void setCurrentState(String currentState) {
        this.currentState = currentState;
    }

    /**
     * 获取期望状态
     *
     * @return 期望状态
     */
    public String getExpectedState() {
        return expectedState;
    }

    /**
     * 设置期望状态
     *
     * @param expectedState 期望状态
     */
    public void setExpectedState(String expectedState) {
        this.expectedState = expectedState;
    }

    /**
     * 获取传播行为
     *
     * @return 传播行为
     */
    public String getPropagationBehavior() {
        return propagationBehavior;
    }

    /**
     * 设置传播行为
     *
     * @param propagationBehavior 传播行为
     */
    public void setPropagationBehavior(String propagationBehavior) {
        this.propagationBehavior = propagationBehavior;
    }

    @Override
    public String getFullMessage() {
        StringBuilder sb = new StringBuilder();
        sb.append(super.getFullMessage());

        if (currentState != null) {
            sb.append(" [当前状态: ").append(currentState).append("]");
        }

        if (expectedState != null) {
            sb.append(" [期望状态: ").append(expectedState).append("]");
        }

        if (propagationBehavior != null) {
            sb.append(" [传播行为: ").append(propagationBehavior).append("]");
        }

        return sb.toString();
    }
}
