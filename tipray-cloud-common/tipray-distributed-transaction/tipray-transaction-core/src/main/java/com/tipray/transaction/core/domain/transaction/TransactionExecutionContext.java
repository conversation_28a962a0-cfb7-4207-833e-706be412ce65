package com.tipray.transaction.core.domain.transaction;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 事务执行上下文
 * 记录事务执行的完整环境信息，用于手动操作时恢复执行环境
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-01-20
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class TransactionExecutionContext {

    /**
     * 基础信息
     */
    private String transactionId;
    private String groupId;
    private String mode;

    /**
     * 执行环境信息
     */
    private String businessClassName;      // 业务类名
    private String businessMethodName;     // 业务方法名
    private String businessMethodSignature; // 方法签名
    private String businessMethodArgs;     // 方法参数（JSON序列化后）
    private String executionType;          // ANNOTATION/PROGRAMMATIC

    /**
     * 调用栈信息
     */
    private List<StackTraceInfo> callStack; // 完整调用栈
    private String initiatorThread;         // 发起线程
    private Map<String, Object> contextData; // 上下文数据

    /**
     * 执行环境
     */
    private String springProfileActive;     // Spring环境
    private Map<String, String> systemProperties; // 系统属性
    private String jvmInfo;                // JVM信息

    /**
     * 时间信息
     */
    private LocalDateTime createTime;
    private LocalDateTime updateTime;

    /**
     * 创建注解式事务上下文
     */
    public static TransactionExecutionContext createAnnotationContext(String transactionId, String groupId, String mode) {
        return TransactionExecutionContext.builder()
                .transactionId(transactionId)
                .groupId(groupId)
                .mode(mode)
                .executionType("ANNOTATION")
                .createTime(LocalDateTime.now())
                .build();
    }

    /**
     * 创建编程式事务上下文
     */
    public static TransactionExecutionContext createProgrammaticContext(String transactionId, String groupId, String mode) {
        return TransactionExecutionContext.builder()
                .transactionId(transactionId)
                .groupId(groupId)
                .mode(mode)
                .executionType("PROGRAMMATIC")
                .createTime(LocalDateTime.now())
                .build();
    }

    /**
     * 初始化上下文数据
     */
    public void initContextData() {
        if (this.contextData == null) {
            this.contextData = new ConcurrentHashMap<>();
        }
        if (this.systemProperties == null) {
            this.systemProperties = new ConcurrentHashMap<>();
        }
    }

    /**
     * 添加上下文数据
     */
    public void putContextData(String key, Object value) {
        initContextData();
        this.contextData.put(key, value);
    }

    /**
     * 获取上下文数据
     */
    public Object getContextData(String key) {
        if (this.contextData == null) {
            return null;
        }
        return this.contextData.get(key);
    }

    /**
     * 添加系统属性
     */
    public void putSystemProperty(String key, String value) {
        initContextData();
        this.systemProperties.put(key, value);
    }

    /**
     * 获取系统属性
     */
    public String getSystemProperty(String key) {
        if (this.systemProperties == null) {
            return null;
        }
        return this.systemProperties.get(key);
    }

    /**
     * 检查是否为注解式事务
     */
    public boolean isAnnotationBased() {
        return "ANNOTATION".equals(this.executionType);
    }

    /**
     * 检查是否为编程式事务
     */
    public boolean isProgrammaticBased() {
        return "PROGRAMMATIC".equals(this.executionType);
    }

    /**
     * 调用栈信息
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class StackTraceInfo {
        private String className;
        private String methodName;
        private String fileName;
        private int lineNumber;
        private boolean isBusinessMethod;      // 是否为业务方法
        private boolean isFrameworkMethod;     // 是否为框架方法

        /**
         * 从StackTraceElement创建
         */
        public static StackTraceInfo fromStackTraceElement(StackTraceElement element) {
            return StackTraceInfo.builder()
                    .className(element.getClassName())
                    .methodName(element.getMethodName())
                    .fileName(element.getFileName())
                    .lineNumber(element.getLineNumber())
                    .isBusinessMethod(isBusinessMethod(element.getClassName()))
                    .isFrameworkMethod(isFrameworkMethod(element.getClassName()))
                    .build();
        }

        /**
         * 判断是否为业务方法
         */
        private static boolean isBusinessMethod(String className) {
            return !isFrameworkMethod(className) &&
                    !className.startsWith("java.") &&
                    !className.startsWith("javax.") &&
                    !className.startsWith("sun.") &&
                    !className.startsWith("com.sun.");
        }

        /**
         * 判断是否为框架方法
         */
        private static boolean isFrameworkMethod(String className) {
            return className.startsWith("com.tipray.transaction.") ||
                    className.startsWith("org.springframework.") ||
                    className.startsWith("org.aspectj.") ||
                    className.startsWith("cglib.") ||
                    className.startsWith("net.sf.cglib.");
        }
    }
}
