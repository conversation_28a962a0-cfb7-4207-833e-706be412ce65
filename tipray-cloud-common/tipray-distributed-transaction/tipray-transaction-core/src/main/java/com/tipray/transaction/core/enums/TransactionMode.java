package com.tipray.transaction.core.enums;

/**
 * 分布式事务模式枚举
 * 定义支持的事务一致性模型
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-05-20
 */
public enum TransactionMode {

    /**
     * AT模式 - 强一致性事务
     * 基于二阶段提交(2PC)和UndoLog机制
     * 适用于对数据一致性要求严格的场景
     */
    AT("AT", "AT模式", "强一致性事务，基于2PC+UndoLog机制"),

    /**
     * Saga模式 - 最终一致性事务
     * 基于补偿机制的长事务处理
     * 适用于业务流程复杂、对性能要求较高的场景
     */
    SAGA("SAGA", "Saga模式", "最终一致性事务，基于补偿机制"),

    /**
     * TCC模式 - 补偿事务模式
     * 需要业务方实现Try、Confirm、Cancel三个方法
     * 适用于需要精确控制的业务场景
     */
    TCC("TCC", "TCC模式", "补偿事务模式，需要业务方实现Try、Confirm、Cancel三个方法"),

    /**
     * XA模式 - 基于XA协议的两阶段提交
     * 基于XA协议的分布式事务处理
     * 适用于强一致性要求的场景
     */
    XA("XA", "XA模式", "基于XA协议的两阶段提交模式");

    /**
     * 模式代码
     */
    private final String code;

    /**
     * 模式名称
     */
    private final String name;

    /**
     * 模式描述
     */
    private final String description;

    /**
     * 构造函数
     *
     * @param code        模式代码
     * @param name        模式名称
     * @param description 模式描述
     */
    TransactionMode(String code, String name, String description) {
        this.code = code;
        this.name = name;
        this.description = description;
    }

    /**
     * 根据模式代码获取枚举值
     *
     * @param code 模式代码
     * @return 对应的枚举值，如果不存在则返回null
     */
    public static TransactionMode fromCode(String code) {
        if (code == null || code.trim().isEmpty()) {
            return null;
        }

        for (TransactionMode mode : values()) {
            if (mode.code.equalsIgnoreCase(code)) {
                return mode;
            }
        }
        return null;
    }

    /**
     * 根据模式名称获取枚举值
     *
     * @param name 模式名称
     * @return 对应的枚举值，如果不存在则返回null
     */
    public static TransactionMode fromName(String name) {
        if (name == null || name.trim().isEmpty()) {
            return null;
        }

        for (TransactionMode mode : values()) {
            if (mode.name.equalsIgnoreCase(name)) {
                return mode;
            }
        }
        return null;
    }

    /**
     * 获取默认的事务模式
     *
     * @return 默认为AT模式
     */
    public static TransactionMode getDefault() {
        return AT;
    }

    /**
     * 获取模式代码
     *
     * @return 模式代码
     */
    public String getCode() {
        return code;
    }

    /**
     * 获取模式名称
     *
     * @return 模式名称
     */
    public String getName() {
        return name;
    }

    /**
     * 获取模式描述
     *
     * @return 模式描述
     */
    public String getDescription() {
        return description;
    }

    /**
     * 判断是否为AT模式
     *
     * @return true表示AT模式
     */
    public boolean isAt() {
        return this == AT;
    }

    /**
     * 判断是否为Saga模式
     *
     * @return true表示Saga模式
     */
    public boolean isSaga() {
        return this == SAGA;
    }

    /**
     * 判断是否为TCC模式
     *
     * @return true表示TCC模式
     */
    public boolean isTcc() {
        return this == TCC;
    }

    /**
     * 判断是否为XA模式
     *
     * @return true表示XA模式
     */
    public boolean isXa() {
        return this == XA;
    }

    /**
     * 判断是否支持强一致性
     *
     * @return true表示支持强一致性
     */
    public boolean isStrongConsistency() {
        return this == AT || this == XA;
    }

    /**
     * 判断是否为最终一致性
     *
     * @return true表示最终一致性
     */
    public boolean isEventualConsistency() {
        return this == SAGA || this == TCC;
    }

    /**
     * 判断是否支持自动回滚
     *
     * @return true表示支持自动回滚
     */
    public boolean supportAutoRollback() {
        return this == AT || this == XA;
    }

    /**
     * 判断是否为补偿模式
     *
     * @return true表示补偿模式
     */
    public boolean isCompensationMode() {
        return this == TCC || this == SAGA;
    }

    /**
     * 判断是否为自动模式
     *
     * @return true表示自动模式
     */
    public boolean isAutomaticMode() {
        return this == AT || this == XA;
    }

    /**
     * 获取推荐的超时时间（秒）
     *
     * @return 推荐超时时间
     */
    public int getRecommendedTimeout() {
        switch (this) {
            case AT:
            case XA:
                return 30;  // 自动模式推荐较短超时
            case TCC:
                return 60;  // TCC模式需要更多时间处理
            case SAGA:
                return 300; // Saga模式支持长事务
            default:
                return 30;
        }
    }

    @Override
    public String toString() {
        return String.format("%s(%s)", code, name);
    }
}
