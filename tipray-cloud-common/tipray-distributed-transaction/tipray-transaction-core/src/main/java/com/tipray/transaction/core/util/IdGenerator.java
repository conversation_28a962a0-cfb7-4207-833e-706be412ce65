package com.tipray.transaction.core.util;

import java.util.concurrent.ThreadLocalRandom;

public class IdGenerator {
    /**
     * 生成唯一ID（时间戳 + 4位随机数）
     * 示例输出：1729934970123456（16位，时间戳12位 + 随机数4位）
     */
    public static Long generateBranchId() {
        long timestamp = System.currentTimeMillis();  // 13位时间戳
        int randomNum = ThreadLocalRandom.current().nextInt(1000, 10000); // 1000~9999随机数
        return Long.parseLong(
                String.valueOf(timestamp).substring(0, 12) + randomNum // 拼接后转long
        );
    }
}
