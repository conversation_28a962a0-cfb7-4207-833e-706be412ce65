package com.tipray.transaction.core.domain.transaction;

import com.tipray.transaction.core.domain.propagation.TransactionPropagationContext;
import com.tipray.transaction.core.enums.TransactionMode;
import com.tipray.transaction.core.enums.TransactionPropagation;

import java.util.*;

/**
 * 事务定义
 * 定义事务的各种属性和配置
 *
 * <AUTHOR>
 * @version 2.0
 * @since 2025-01-23
 */
public class TransactionDefinition {

    private final String name;
    private final String groupId;
    private final TransactionMode mode;
    private final TransactionPropagation propagation;
    private final int timeout;
    private final boolean readOnly;
    private final String applicationName;
    private final String methodSignature;
    private final String description;
    private final int retryCount;
    private final long retryInterval;
    private final boolean enableBarrier;
    private final boolean asyncPersistence;
    private final boolean asyncCommitOrRollback;
    private final Class<? extends Throwable>[] rollbackFor;
    private final Class<? extends Throwable>[] noRollbackFor;
    private final List<String> serviceEndpoints;
    private final Map<String, Object> attributes;

    private TransactionDefinition(Builder builder) {
        this.name = builder.name;
        this.groupId = builder.groupId;
        this.mode = builder.mode;
        this.propagation = builder.propagation;
        this.timeout = builder.timeout;
        this.readOnly = builder.readOnly;
        this.applicationName = builder.applicationName;
        this.methodSignature = builder.methodSignature;
        this.description = builder.description;
        this.retryCount = builder.retryCount;
        this.retryInterval = builder.retryInterval;
        this.enableBarrier = builder.enableBarrier;
        this.asyncPersistence = builder.asyncPersistence;
        this.asyncCommitOrRollback = builder.asyncCommitOrRollback;
        this.rollbackFor = builder.rollbackFor.clone();
        this.noRollbackFor = builder.noRollbackFor.clone();
        this.serviceEndpoints = new ArrayList<>(builder.serviceEndpoints);
        this.attributes = new HashMap<>(builder.attributes);
    }

    public static Builder builder() {
        return new Builder();
    }

    /**
     * 判断是否需要为指定异常回滚
     */
    public boolean shouldRollbackFor(Throwable throwable) {
        Class<?> throwableClass = throwable.getClass();

        // 检查noRollbackFor规则
        for (Class<? extends Throwable> noRollbackClass : noRollbackFor) {
            if (noRollbackClass.isAssignableFrom(throwableClass)) {
                return false;
            }
        }

        // 检查rollbackFor规则
        for (Class<? extends Throwable> rollbackClass : rollbackFor) {
            if (rollbackClass.isAssignableFrom(throwableClass)) {
                return true;
            }
        }

        // 默认规则：RuntimeException和Error需要回滚
        return throwable instanceof RuntimeException || throwable instanceof Error;
    }

    /**
     * 获取有效的超时时间（毫秒）
     */
    public long getEffectiveTimeoutMillis() {
        return timeout > 0 ? timeout * 1000L : Long.MAX_VALUE;
    }

    /**
     * 判断是否启用重试
     */
    public boolean isRetryEnabled() {
        return retryCount > 0;
    }

    /**
     * 获取下一次重试间隔（毫秒）
     */
    public long getNextRetryInterval(int currentRetryCount) {
        if (currentRetryCount <= 0) {
            return retryInterval;
        }

        // 指数退避策略
        return Math.min(retryInterval * (1L << currentRetryCount), 30000L);
    }

    /**
     * 转换为传播上下文
     */
    public TransactionPropagationContext toPropagationContext() {
        return TransactionPropagationContext.builder()
                .groupId(groupId)
                .mode(mode)
                .propagation(propagation)
                .transactionName(name)
                .timeout(timeout)
                .readOnly(readOnly)
                .initiator(methodSignature)
                .rollbackFor(rollbackFor)
                .noRollbackFor(noRollbackFor)
                .build();
    }

    /**
     * 验证定义的有效性
     */
    public void validate() {
        if (name == null || name.trim().isEmpty()) {
            throw new IllegalArgumentException("事务名称不能为空");
        }

        if (applicationName == null || applicationName.trim().isEmpty()) {
            throw new IllegalArgumentException("应用名称不能为空");
        }

        if (timeout <= 0) {
            throw new IllegalArgumentException("超时时间必须大于0");
        }

        if (retryCount < 0) {
            throw new IllegalArgumentException("重试次数不能为负数");
        }

        if (retryInterval < 0) {
            throw new IllegalArgumentException("重试间隔不能为负数");
        }

        // 验证回滚规则冲突
        Set<Class<? extends Throwable>> rollbackSet = new HashSet<>(Arrays.asList(rollbackFor));
        Set<Class<? extends Throwable>> noRollbackSet = new HashSet<>(Arrays.asList(noRollbackFor));

        for (Class<? extends Throwable> rollbackClass : rollbackSet) {
            for (Class<? extends Throwable> noRollbackClass : noRollbackSet) {
                if (rollbackClass.isAssignableFrom(noRollbackClass) ||
                        noRollbackClass.isAssignableFrom(rollbackClass)) {
                    throw new IllegalArgumentException(
                            String.format("回滚规则冲突: %s 和 %s",
                                    rollbackClass.getSimpleName(), noRollbackClass.getSimpleName()));
                }
            }
        }
    }

    /**
     * 创建副本
     */
    public TransactionDefinition copy() {
        return builder()
                .name(name)
                .groupId(groupId)
                .mode(mode)
                .propagation(propagation)
                .timeout(timeout)
                .readOnly(readOnly)
                .applicationName(applicationName)
                .methodSignature(methodSignature)
                .description(description)
                .retryCount(retryCount)
                .retryInterval(retryInterval)
                .enableBarrier(enableBarrier)
                .asyncPersistence(asyncPersistence)
                .rollbackFor(rollbackFor)
                .noRollbackFor(noRollbackFor)
                .serviceEndpoints(serviceEndpoints)
                .attributes(attributes)
                .build();
    }

    // getters
    public String getName() {
        return name;
    }

    public String getGroupId() {
        return groupId;
    }

    public TransactionMode getMode() {
        return mode;
    }

    public TransactionPropagation getPropagation() {
        return propagation;
    }

    public int getTimeout() {
        return timeout;
    }

    public boolean isReadOnly() {
        return readOnly;
    }

    public String getApplicationName() {
        return applicationName;
    }

    public String getMethodSignature() {
        return methodSignature;
    }

    public String getDescription() {
        return description;
    }

    public int getRetryCount() {
        return retryCount;
    }

    public long getRetryInterval() {
        return retryInterval;
    }

    public boolean isEnableBarrier() {
        return enableBarrier;
    }

    public boolean isAsyncPersistence() {
        return asyncPersistence;
    }

    public boolean isAsyncCommitOrRollback() {
        return asyncCommitOrRollback;
    }

    public Class<? extends Throwable>[] getRollbackFor() {
        return rollbackFor.clone();
    }

    public Class<? extends Throwable>[] getNoRollbackFor() {
        return noRollbackFor.clone();
    }

    public List<String> getServiceEndpoints() {
        return new ArrayList<>(serviceEndpoints);
    }

    public Map<String, Object> getAttributes() {
        return new HashMap<>(attributes);
    }

    @Override
    public String toString() {
        return String.format("TransactionDefinition{name='%s', mode=%s, propagation=%s, timeout=%d}",
                name, mode, propagation, timeout);
    }

    public static class Builder {
        private String name;
        private String groupId;
        private TransactionMode mode = TransactionMode.AT;
        private TransactionPropagation propagation = TransactionPropagation.REQUIRED;
        private int timeout = 30;
        private boolean readOnly = false;
        private String applicationName;
        private String methodSignature;
        private String description = "";
        private int retryCount = 0;
        private long retryInterval = 1000L;
        private boolean enableBarrier = true;
        private boolean asyncPersistence = true;
        private boolean asyncCommitOrRollback = false;
        private Class<? extends Throwable>[] rollbackFor = new Class[0];
        private Class<? extends Throwable>[] noRollbackFor = new Class[0];
        private List<String> serviceEndpoints = new ArrayList<>();
        private Map<String, Object> attributes = new HashMap<>();

        public Builder name(String name) {
            this.name = name;
            return this;
        }

        public Builder groupId(String groupId) {
            this.groupId = groupId;
            return this;
        }

        public Builder mode(TransactionMode mode) {
            this.mode = mode;
            return this;
        }

        public Builder propagation(TransactionPropagation propagation) {
            this.propagation = propagation;
            return this;
        }

        public Builder timeout(int timeout) {
            this.timeout = timeout;
            return this;
        }

        public Builder readOnly(boolean readOnly) {
            this.readOnly = readOnly;
            return this;
        }

        public Builder applicationName(String applicationName) {
            this.applicationName = applicationName;
            return this;
        }

        public Builder methodSignature(String methodSignature) {
            this.methodSignature = methodSignature;
            return this;
        }

        public Builder description(String description) {
            this.description = description;
            return this;
        }

        public Builder retryCount(int retryCount) {
            this.retryCount = retryCount;
            return this;
        }

        public Builder retryInterval(long retryInterval) {
            this.retryInterval = retryInterval;
            return this;
        }

        public Builder enableBarrier(boolean enableBarrier) {
            this.enableBarrier = enableBarrier;
            return this;
        }

        public Builder asyncPersistence(boolean asyncPersistence) {
            this.asyncPersistence = asyncPersistence;
            return this;
        }

        public Builder asyncCommitOrRollback(boolean asyncCommitOrRollback) {
            this.asyncCommitOrRollback = asyncCommitOrRollback;
            return this;
        }

        @SuppressWarnings("unchecked")
        public Builder rollbackFor(Class<? extends Throwable>... rollbackFor) {
            this.rollbackFor = rollbackFor;
            return this;
        }

        @SuppressWarnings("unchecked")
        public Builder noRollbackFor(Class<? extends Throwable>... noRollbackFor) {
            this.noRollbackFor = noRollbackFor;
            return this;
        }

        public Builder serviceEndpoint(String endpoint) {
            this.serviceEndpoints.add(endpoint);
            return this;
        }

        public Builder serviceEndpoints(List<String> endpoints) {
            this.serviceEndpoints = new ArrayList<>(endpoints);
            return this;
        }

        public Builder attribute(String key, Object value) {
            this.attributes.put(key, value);
            return this;
        }

        public Builder attributes(Map<String, Object> attributes) {
            this.attributes = new HashMap<>(attributes);
            return this;
        }

        public TransactionDefinition build() {
            TransactionDefinition definition = new TransactionDefinition(this);
            definition.validate();
            return definition;
        }
    }
}
