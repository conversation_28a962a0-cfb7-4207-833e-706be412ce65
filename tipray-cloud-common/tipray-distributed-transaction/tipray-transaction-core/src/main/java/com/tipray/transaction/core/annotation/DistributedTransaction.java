package com.tipray.transaction.core.annotation;


import com.tipray.transaction.core.enums.TransactionMode;
import com.tipray.transaction.core.enums.TransactionPropagation;

import java.lang.annotation.*;

/**
 * 分布式事务注解
 * 支持完整的事务配置（新版本，替换旧版本）
 *
 * <AUTHOR>
 * @version 2.0
 * @since 2025-01-23
 */
@Target({ElementType.METHOD, ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface DistributedTransaction {

    /**
     * 事务模式
     */
    TransactionMode mode() default TransactionMode.AT;

    /**
     * 传播行为
     */
    TransactionPropagation propagation() default TransactionPropagation.REQUIRED;

    /**
     * 事务名称
     */
    String name() default "";

    /**
     * 事务组ID
     */
    String groupId() default "";

    /**
     * 超时时间（秒）
     */
    int timeout() default 30;

    /**
     * 是否只读
     */
    boolean readOnly() default false;

    /**
     * 需要回滚的异常类型
     */
    Class<? extends Throwable>[] rollbackFor() default {};

    /**
     * 不需要回滚的异常类型
     */
    Class<? extends Throwable>[] noRollbackFor() default {};

    /**
     * 事务描述
     */
    String description() default "";

    /**
     * 重试次数
     */
    int retryCount() default 0;

    /**
     * 重试间隔（毫秒）
     */
    long retryInterval() default 1000;

    /**
     * 是否启用事务屏障
     * true: Saga模式启用L1屏障，AT模式启用L1+L2双重屏障
     * false: 不启用屏障（默认）
     */
    boolean enableBarrier() default false;

    /**
     * 是否异步持久化
     */
    boolean asyncPersistence() default true;

    /**
     * 是否异步提交/回滚  只针对分支事务来说
     */
    boolean asyncCommitOrRollback() default false;

    /**
     * 自定义属性
     */
    String[] attributes() default {};
}
