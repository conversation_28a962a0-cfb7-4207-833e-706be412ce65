package com.tipray.transaction.core.enums;

/**
 * 事务传播行为枚举
 * 定义事务在方法调用链中的传播方式
 *
 * <AUTHOR>
 * @version 2.0
 * @since 2025-01-23
 */
public enum TransactionPropagation {

    /**
     * REQUIRED - 必需事务
     * 如果当前存在事务，则加入该事务；如果当前没有事务，则创建一个新的事务
     * 这是最常用的传播行为
     */
    REQUIRED(0, "REQUIRED", "必需事务", "如果当前存在事务，则加入该事务；如果当前没有事务，则创建一个新的事务"),

    /**
     * SUPPORTS - 支持事务
     * 如果当前存在事务，则加入该事务；如果当前没有事务，则以非事务方式执行
     */
    SUPPORTS(1, "SUPPORTS", "支持事务", "如果当前存在事务，则加入该事务；如果当前没有事务，则以非事务方式执行"),

    /**
     * MANDATORY - 强制事务
     * 如果当前存在事务，则加入该事务；如果当前没有事务，则抛出异常
     */
    MANDATORY(2, "MANDATORY", "强制事务", "如果当前存在事务，则加入该事务；如果当前没有事务，则抛出异常"),

    /**
     * REQUIRES_NEW - 需要新事务
     * 创建一个新的事务，如果当前存在事务，则把当前事务挂起
     */
    REQUIRES_NEW(3, "REQUIRES_NEW", "需要新事务", "创建一个新的事务，如果当前存在事务，则把当前事务挂起"),

    /**
     * NOT_SUPPORTED - 不支持事务
     * 以非事务方式执行操作，如果当前存在事务，则把当前事务挂起
     */
    NOT_SUPPORTED(4, "NOT_SUPPORTED", "不支持事务", "以非事务方式执行操作，如果当前存在事务，则把当前事务挂起"),

    /**
     * NEVER - 从不使用事务
     * 以非事务方式执行，如果当前存在事务，则抛出异常
     */
    NEVER(5, "NEVER", "从不使用事务", "以非事务方式执行，如果当前存在事务，则抛出异常"),

    /**
     * NESTED - 嵌套事务
     * 如果当前存在事务，则创建一个嵌套事务；如果当前没有事务，则创建一个新的事务
     * 嵌套事务可以独立回滚，但不会影响外层事务
     */
    NESTED(6, "NESTED", "嵌套事务", "如果当前存在事务，则创建一个嵌套事务；如果当前没有事务，则创建一个新的事务");

    private final int value;
    private final String code;
    private final String displayName;
    private final String description;

    TransactionPropagation(int value, String code, String displayName, String description) {
        this.value = value;
        this.code = code;
        this.displayName = displayName;
        this.description = description;
    }

    /**
     * 根据Spring传播行为值获取枚举
     */
    public static TransactionPropagation fromSpringValue(int value) {
        for (TransactionPropagation propagation : values()) {
            if (propagation.value == value) {
                return propagation;
            }
        }
        throw new IllegalArgumentException("不支持的传播行为值: " + value);
    }

    /**
     * 根据代码获取枚举
     */
    public static TransactionPropagation fromCode(String code) {
        if (code == null || code.trim().isEmpty()) {
            return REQUIRED; // 默认传播行为
        }

        String upperCode = code.trim().toUpperCase();
        for (TransactionPropagation propagation : values()) {
            if (propagation.code.equals(upperCode)) {
                return propagation;
            }
        }

        throw new IllegalArgumentException("不支持的传播行为代码: " + code);
    }

    /**
     * 获取默认传播行为
     */
    public static TransactionPropagation getDefault() {
        return REQUIRED;
    }

    /**
     * 获取所有需要新事务的传播行为
     */
    public static TransactionPropagation[] getNewTransactionPropagations() {
        return java.util.Arrays.stream(values())
                .filter(TransactionPropagation::requiresNewTransaction)
                .toArray(TransactionPropagation[]::new);
    }

    /**
     * 获取所有支持现有事务的传播行为
     */
    public static TransactionPropagation[] getSupportingPropagations() {
        return java.util.Arrays.stream(values())
                .filter(TransactionPropagation::supportsExistingTransaction)
                .toArray(TransactionPropagation[]::new);
    }

    /**
     * 判断是否需要创建新事务
     */
    public boolean requiresNewTransaction() {
        return this == REQUIRED || this == REQUIRES_NEW || this == NESTED;
    }

    /**
     * 判断是否支持现有事务
     */
    public boolean supportsExistingTransaction() {
        return this == REQUIRED || this == SUPPORTS || this == MANDATORY || this == NESTED;
    }

    /**
     * 判断是否需要挂起现有事务
     */
    public boolean requiresSuspension() {
        return this == REQUIRES_NEW || this == NOT_SUPPORTED;
    }

    /**
     * 判断是否强制要求事务存在
     */
    public boolean requiresExistingTransaction() {
        return this == MANDATORY;
    }

    /**
     * 判断是否禁止事务存在
     */
    public boolean forbidsExistingTransaction() {
        return this == NEVER;
    }

    /**
     * 判断是否为嵌套事务
     */
    public boolean isNested() {
        return this == NESTED;
    }

    /**
     * 判断是否支持非事务执行
     */
    public boolean supportsNonTransactional() {
        return this == SUPPORTS || this == NOT_SUPPORTED || this == NEVER;
    }

    /**
     * 获取对应的Spring事务传播行为值
     */
    public int getSpringPropagationValue() {
        return value;
    }

    /**
     * 获取传播行为的详细说明
     */
    public String getDetailedDescription() {
        StringBuilder detail = new StringBuilder(description);

        detail.append("\n特性: ");
        if (requiresNewTransaction()) {
            detail.append("需要新事务 ");
        }
        if (supportsExistingTransaction()) {
            detail.append("支持现有事务 ");
        }
        if (requiresSuspension()) {
            detail.append("需要挂起 ");
        }
        if (requiresExistingTransaction()) {
            detail.append("强制要求事务 ");
        }
        if (forbidsExistingTransaction()) {
            detail.append("禁止事务 ");
        }
        if (isNested()) {
            detail.append("嵌套事务 ");
        }

        return detail.toString();
    }

    // getters
    public int getValue() {
        return value;
    }

    public String getCode() {
        return code;
    }

    public String getDisplayName() {
        return displayName;
    }

    public String getDescription() {
        return description;
    }

    @Override
    public String toString() {
        return displayName;
    }
}
