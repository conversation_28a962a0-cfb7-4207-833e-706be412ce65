package com.tipray.transaction.core.enums;

/**
 * 分支事务事件枚举
 * 定义分支事务生命周期中的各种事件
 *
 * <AUTHOR>
 * @version 2.0
 * @since 2025-01-29
 */
public enum BranchTransactionEvent {

    /**
     * 分支注册事件
     */
    REGISTER("分支注册", "分支事务注册到协调器"),

    /**
     * 分支注册失败
     */
    REGISTER_FAILURE("分支注册失败", "分支事务注册到协调器失败"),

    /**
     * 开始执行事件
     */
    START_EXECUTE("开始执行", "分支事务开始执行业务逻辑"),

    /**
     * 执行成功事件
     */
    EXECUTE_SUCCESS("执行成功", "分支事务业务逻辑执行成功"),

    /**
     * 执行失败事件
     */
    EXECUTE_FAILURE("执行失败", "分支事务业务逻辑执行失败"),

    /**
     * 开始提交事件
     */
    START_COMMIT("开始提交", "分支事务开始提交"),

    /**
     * 提交成功事件
     */
    COMMIT_SUCCESS("提交成功", "分支事务提交成功"),

    /**
     * 提交失败事件
     */
    COMMIT_FAILURE("提交失败", "分支事务提交失败"),

    /**
     * 开始回滚事件
     */
    START_ROLLBACK("开始回滚", "分支事务开始回滚"),

    /**
     * 回滚成功事件
     */
    ROLLBACK_SUCCESS("回滚成功", "分支事务回滚成功"),

    /**
     * 回滚失败事件
     */
    ROLLBACK_FAILURE("回滚失败", "分支事务回滚失败"),

    /**
     * 超时事件
     */
    TIMEOUT("超时", "分支事务执行超时"),

    /**
     * 重试事件
     */
    RETRY("重试", "分支事务重试执行"),

    /**
     * 人工干预事件
     */
    MANUAL_INTERVENTION("人工干预", "需要人工干预处理");

    private final String displayName;
    private final String description;

    BranchTransactionEvent(String displayName, String description) {
        this.displayName = displayName;
        this.description = description;
    }

    /**
     * 获取显示名称
     *
     * @return 显示名称
     */
    public String getDisplayName() {
        return displayName;
    }

    /**
     * 获取描述信息
     *
     * @return 描述信息
     */
    public String getDescription() {
        return description;
    }

    /**
     * 判断是否为执行相关事件
     *
     * @return true表示执行相关
     */
    public boolean isExecuteRelated() {
        return this == START_EXECUTE || this == EXECUTE_SUCCESS || this == EXECUTE_FAILURE;
    }

    /**
     * 判断是否为提交相关事件
     *
     * @return true表示提交相关
     */
    public boolean isCommitRelated() {
        return this == START_COMMIT || this == COMMIT_SUCCESS || this == COMMIT_FAILURE;
    }

    /**
     * 判断是否为回滚相关事件
     *
     * @return true表示回滚相关
     */
    public boolean isRollbackRelated() {
        return this == START_ROLLBACK || this == ROLLBACK_SUCCESS || this == ROLLBACK_FAILURE;
    }

    /**
     * 判断是否为失败事件
     *
     * @return true表示失败事件
     */
    public boolean isFailureEvent() {
        return this == EXECUTE_FAILURE || this == COMMIT_FAILURE ||
               this == ROLLBACK_FAILURE || this == TIMEOUT;
    }

    /**
     * 判断是否为成功事件
     *
     * @return true表示成功事件
     */
    public boolean isSuccessEvent() {
        return this == REGISTER || this == EXECUTE_SUCCESS ||
               this == COMMIT_SUCCESS || this == ROLLBACK_SUCCESS;
    }

    @Override
    public String toString() {
        return String.format("%s(%s)", displayName, name());
    }
}
