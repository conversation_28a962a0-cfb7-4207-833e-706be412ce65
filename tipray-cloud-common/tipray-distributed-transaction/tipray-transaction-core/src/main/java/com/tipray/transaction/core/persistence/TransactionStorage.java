package com.tipray.transaction.core.persistence;

import com.tipray.transaction.core.domain.transaction.TransactionDO;
import com.tipray.transaction.core.enums.TransactionStatus;

/**
 * 事务存储接口
 * 定义事务数据持久化的基本操作
 *
 * <AUTHOR>
 * @version 2.0
 * @since 2025-01-23
 */
public interface TransactionStorage {

    /**
     * 保存事务
     */
    void saveTransaction(TransactionDO transaction);

    /**
     * 更新事务状态
     */
    void updateTransactionStatus(String transactionId, TransactionStatus status);

    /**
     * 保存回滚失败记录
     */
    void saveRollbackFailure(Object record);

    /**
     * 删除事务
     */
    void deleteTransaction(String transactionId);

    /**
     * 根据事务ID查询事务
     */
    TransactionDO findTransactionById(String transactionId);

    /**
     * 获取存储统计信息
     */
    Object getStatistics();
}
