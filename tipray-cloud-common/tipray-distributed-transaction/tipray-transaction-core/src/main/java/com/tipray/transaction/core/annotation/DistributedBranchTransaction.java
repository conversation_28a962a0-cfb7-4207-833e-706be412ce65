package com.tipray.transaction.core.annotation;

import java.lang.annotation.*;

/**
 * 统一事务步骤注解
 * 合并了原有的AtService和TransactionStep注解功能
 * 根据外层@DistributedTransaction的模式自动适配AT或Saga模式
 * <p>
 * 使用场景：
 * - AT模式：标记云服务调用，自动注册分支事务和UndoLog管理
 * - Saga模式：标记事务步骤，支持补偿机制和步骤编排
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-01-20
 */
@Target({ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface DistributedBranchTransaction {

    /**
     * 步骤名称或服务名称
     * AT模式：作为云服务名称，用于标识不同的云服务
     * Saga模式：作为步骤名称，如果不指定则使用方法名
     *
     * @return 步骤名称或服务名称
     */
    String value() default "";

    /**
     * 步骤描述
     * 用于监控和日志记录
     *
     * @return 步骤描述
     */
    String description() default "";

    /**
     * 补偿方法（仅Saga模式有效）
     * 指定Saga模式下的补偿方法
     * 格式：beanName.methodName 或 methodName（同一个类中的方法）
     *
     * @return 补偿方法
     */
    String compensation() default "";

    /**
     * 是否为关键步骤
     * 关键步骤失败会导致整个事务失败
     * 非关键步骤失败可以继续执行后续步骤
     *
     * @return 是否为关键步骤
     */
    boolean critical() default true;

    /**
     * 超时时间（毫秒）
     * AT模式：HTTP调用的总超时时间，默认30秒
     * Saga模式：步骤执行的超时时间，默认使用全局配置
     *
     * @return 超时时间
     */
    long timeout() default 30000;

    /**
     * 连接超时时间（毫秒）
     * 仅AT模式有效，建立HTTP连接的最大等待时间
     * 默认5秒
     *
     * @return 连接超时时间
     */
    long connectTimeout() default 5000;

    /**
     * 读取超时时间（毫秒）
     * 仅AT模式有效，从HTTP连接中读取数据的最大等待时间
     * 默认30秒
     *
     * @return 读取超时时间
     */
    long readTimeout() default 30000;

    /**
     * 重试次数
     * 当步骤执行失败时的重试次数
     * 默认为0，表示不重试
     *
     * @return 重试次数
     */
    int retryCount() default 0;

    /**
     * 重试间隔（毫秒）
     * 重试之间的等待时间
     * 默认1秒
     *
     * @return 重试间隔
     */
    long retryInterval() default 1000;

    /**
     * 目标服务名称
     * 用于标识步骤调用的目标服务，支持服务发现和负载均衡
     *
     * @return 目标服务名称
     */
    String targetService() default "";

    /**
     * 服务URL
     * AT模式：云服务的基础URL
     * 如果不指定，将通过服务发现获取
     *
     * @return 服务URL
     */
    String url() default "";

    /**
     * 提交接口路径
     * AT模式：云服务的提交接口路径
     * 默认：/api/transaction/commit
     *
     * @return 提交接口路径
     */
    String commitPath() default "/api/transaction/commit";

    /**
     * 回滚接口路径
     * AT模式：云服务的回滚接口路径
     * 默认：/api/transaction/rollback
     *
     * @return 回滚接口路径
     */
    String rollbackPath() default "/api/transaction/rollback";

    /**
     * 步骤顺序
     * Saga模式：指定步骤的执行顺序
     * 数值越小越先执行
     *
     * @return 步骤顺序
     */
    int order() default 0;

    /**
     * 是否异步执行
     * Saga模式：是否异步执行此步骤
     *
     * @return 是否异步执行
     */
    boolean async() default false;

    /**
     * 并行组
     * Saga模式：相同并行组的步骤可以并行执行
     *
     * @return 并行组
     */
    String parallelGroup() default "";

    /**
     * 依赖步骤
     * Saga模式：当前步骤依赖的其他步骤名称
     *
     * @return 依赖步骤
     */
    String[] dependsOn() default {};

    /**
     * 是否忽略异常
     * 当设置为true时，步骤执行异常不会导致事务失败
     *
     * @return 是否忽略异常
     */
    boolean ignoreException() default false;

    /**
     * 自定义属性
     * 用于传递自定义配置信息
     *
     * @return 自定义属性
     */
    String[] attributes() default {};
}
