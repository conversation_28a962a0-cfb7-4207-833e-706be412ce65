package com.tipray.transaction.core.exception;

/**
 * Tipray事务开始异常
 * 当事务开始失败时抛出
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-06-20
 */
public class DistributedTransactionBeginException extends DistributedTransactionException {

    /**
     * 序列化版本号
     */
    private static final long serialVersionUID = 1L;

    /**
     * 失败原因类型
     */
    private BeginFailureType failureType;

    /**
     * 默认构造函数
     */
    public DistributedTransactionBeginException() {
        super();
        this.failureType = BeginFailureType.UNKNOWN;
    }

    /**
     * 构造函数
     *
     * @param message 错误信息
     */
    public DistributedTransactionBeginException(String message) {
        super(message);
        this.failureType = BeginFailureType.UNKNOWN;
    }

    /**
     * 构造函数
     *
     * @param message 错误信息
     * @param cause   原因异常
     */
    public DistributedTransactionBeginException(String message, Throwable cause) {
        super(message, cause);
        this.failureType = BeginFailureType.UNKNOWN;
    }

    /**
     * 构造函数
     *
     * @param cause 原因异常
     */
    public DistributedTransactionBeginException(Throwable cause) {
        super(cause);
        this.failureType = BeginFailureType.UNKNOWN;
    }

    /**
     * 构造函数
     *
     * @param message     错误信息
     * @param failureType 失败类型
     */
    public DistributedTransactionBeginException(String message, BeginFailureType failureType) {
        super("TRANSACTION_BEGIN_FAILED", message);
        this.failureType = failureType;
    }

    /**
     * 构造函数
     *
     * @param message     错误信息
     * @param cause       原因异常
     * @param failureType 失败类型
     */
    public DistributedTransactionBeginException(String message, Throwable cause, BeginFailureType failureType) {
        super("TRANSACTION_BEGIN_FAILED", message, cause);
        this.failureType = failureType;
    }

    /**
     * 构造函数
     *
     * @param message       错误信息
     * @param cause         原因异常
     * @param transactionId 事务ID
     * @param groupId       事务组ID
     * @param failureType   失败类型
     */
    public DistributedTransactionBeginException(String message, Throwable cause, String transactionId, String groupId, BeginFailureType failureType) {
        super("TRANSACTION_BEGIN_FAILED", message, cause, transactionId, groupId);
        this.failureType = failureType;
    }

    /**
     * 获取失败类型
     *
     * @return 失败类型
     */
    public BeginFailureType getFailureType() {
        return failureType;
    }

    /**
     * 设置失败类型
     *
     * @param failureType 失败类型
     */
    public void setFailureType(BeginFailureType failureType) {
        this.failureType = failureType;
    }

    /**
     * 判断是否为可重试的异常
     *
     * @return true表示可重试
     */
    public boolean isRetryable() {
        return failureType == BeginFailureType.RESOURCE_UNAVAILABLE ||
                failureType == BeginFailureType.NETWORK_ERROR ||
                failureType == BeginFailureType.TIMEOUT;
    }

    @Override
    public String getFullMessage() {
        StringBuilder sb = new StringBuilder();
        sb.append(super.getFullMessage());

        if (failureType != null) {
            sb.append(" [失败类型: ").append(failureType.getDescription()).append("]");
        }

        return sb.toString();
    }

    /**
     * 事务开始失败原因类型枚举
     */
    public enum BeginFailureType {
        /**
         * 资源不可用
         */
        RESOURCE_UNAVAILABLE("RESOURCE_UNAVAILABLE", "资源不可用"),

        /**
         * 网络错误
         */
        NETWORK_ERROR("NETWORK_ERROR", "网络连接错误"),

        /**
         * 超时
         */
        TIMEOUT("TIMEOUT", "事务开始超时"),

        /**
         * 配置错误
         */
        CONFIGURATION_ERROR("CONFIGURATION_ERROR", "配置错误"),

        /**
         * 权限不足
         */
        PERMISSION_DENIED("PERMISSION_DENIED", "权限不足"),

        /**
         * 其他未知异常
         */
        UNKNOWN("UNKNOWN", "未知异常");

        private final String code;
        private final String description;

        BeginFailureType(String code, String description) {
            this.code = code;
            this.description = description;
        }

        public String getCode() {
            return code;
        }

        public String getDescription() {
            return description;
        }
    }
}
