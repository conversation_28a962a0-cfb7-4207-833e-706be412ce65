package com.tipray.transaction.core.enums;

/**
 * 分支事务类型枚举
 * 定义分支事务的类型，用于区分本地分支和远程分支
 *
 * <AUTHOR>
 * @version 2.0
 * @since 2025-01-29
 */
public enum BranchType {

    /**
     * 本地分支 - 发起方分支事务
     * 使用Spring事务管理器进行本地事务管理
     */
    LOCAL("LOCAL", "本地分支", "发起方分支事务，使用Spring事务管理器"),

    /**
     * 远程分支 - 参与方分支事务
     * 通过HTTP调用远程服务进行分支事务管理
     */
    REMOTE("REMOTE", "远程分支", "参与方分支事务，通过HTTP调用远程服务");

    private final String code;
    private final String displayName;
    private final String description;

    BranchType(String code, String displayName, String description) {
        this.code = code;
        this.displayName = displayName;
        this.description = description;
    }

    /**
     * 根据代码获取分支类型
     *
     * @param code 类型代码
     * @return 分支类型枚举
     */
    public static BranchType fromCode(String code) {
        if (code == null || code.trim().isEmpty()) {
            return REMOTE; // 默认为远程分支，保持向后兼容
        }

        String upperCode = code.trim().toUpperCase();
        for (BranchType type : values()) {
            if (type.code.equals(upperCode)) {
                return type;
            }
        }

        throw new IllegalArgumentException("不支持的分支类型代码: " + code);
    }

    /**
     * 根据名称获取分支类型
     *
     * @param name 类型名称
     * @return 分支类型枚举
     */
    public static BranchType fromName(String name) {
        if (name == null || name.trim().isEmpty()) {
            return REMOTE; // 默认为远程分支
        }

        String upperName = name.trim().toUpperCase();
        for (BranchType type : values()) {
            if (type.name().equals(upperName)) {
                return type;
            }
        }

        throw new IllegalArgumentException("不支持的分支类型名称: " + name);
    }

    /**
     * 判断是否为本地分支
     *
     * @return true表示本地分支
     */
    public boolean isLocal() {
        return this == LOCAL;
    }

    /**
     * 判断是否为远程分支
     *
     * @return true表示远程分支
     */
    public boolean isRemote() {
        return this == REMOTE;
    }

    /**
     * 获取类型代码
     *
     * @return 类型代码
     */
    public String getCode() {
        return code;
    }

    /**
     * 获取显示名称
     *
     * @return 显示名称
     */
    public String getDisplayName() {
        return displayName;
    }

    /**
     * 获取描述信息
     *
     * @return 描述信息
     */
    public String getDescription() {
        return description;
    }

    @Override
    public String toString() {
        return String.format("%s(%s)", displayName, code);
    }
}
