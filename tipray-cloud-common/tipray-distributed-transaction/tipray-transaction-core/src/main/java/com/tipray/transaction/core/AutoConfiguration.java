package com.tipray.transaction.core;

import com.tipray.transaction.core.config.ConfigurationResolver;
import com.tipray.transaction.core.config.propertie.TiprayTransactionProperties;
import com.tipray.transaction.core.config.TransactionConfigurationManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;

/**
 * 核心配置类
 *
 * <AUTHOR>
 */
@Slf4j
@EnableConfigurationProperties(TiprayTransactionProperties.class)
public class AutoConfiguration {

    @Bean
    @ConditionalOnMissingBean
    public TransactionConfigurationManager unifiedConfigurationManager() {
        return new TransactionConfigurationManager();
    }

    @Bean
    @ConditionalOnMissingBean
    public ConfigurationResolver configurationResolver() {
        return new ConfigurationResolver();
    }
}
