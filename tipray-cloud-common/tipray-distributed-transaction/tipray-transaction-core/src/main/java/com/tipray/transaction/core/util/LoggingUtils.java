package com.tipray.transaction.core.util;

import com.tipray.transaction.core.context.TransactionContextHolder;
import com.tipray.transaction.core.domain.transaction.TransactionContext;
import lombok.experimental.UtilityClass;

import java.time.Duration;
import java.time.LocalDateTime;

/**
 * 分布式事务日志工具类
 * 提供统一的日志格式化方法，简化日志记录
 *
 * <AUTHOR>
 * @version 2.0
 * @since 2025-01-29
 */
@UtilityClass
public class LoggingUtils {

    /**
     * 获取事务ID（无参数版本，从上下文获取）
     *
     * @return 事务ID，无事务上下文时返回 "UNKNOWN"
     */
    public static String getTxId() {
        TransactionContext context = TransactionContextHolder.getCurrentContext();
        return context != null ? context.getTransactionId() : "UNKNOWN";
    }

    /**
     * 获取事务ID（支持多个候选参数，按优先级顺序取值）
     *
     * @param candidates 候选事务ID参数，按优先级从高到低排列
     * @return 事务ID，所有参数都无效时从上下文获取，最后返回 "UNKNOWN"
     */
    public static String getTxId(String... candidates) {
        // 按优先级顺序检查候选参数
        if (candidates != null) {
            for (String candidate : candidates) {
                if (candidate != null && !candidate.trim().isEmpty()) {
                    return candidate;
                }
            }
        }

        // 所有候选参数都无效，从上下文获取
        return getTxId();
    }

    /**
     * 获取分支ID（无参数版本，从上下文获取）
     *
     * @return 分支ID字符串，无分支时返回 "----"
     */
    public static String getBranchId() {
        TransactionContext context = TransactionContextHolder.getCurrentContext();
        return context != null && context.getBranchId() != null ?
               context.getBranchId().toString() : "----";
    }

    /**
     * 获取分支ID（支持多个候选参数，按优先级顺序取值）
     *
     * @param candidates 候选分支ID参数，按优先级从高到低排列，支持Long、String类型
     * @return 分支ID字符串，所有参数都无效时从上下文获取，最后返回 "----"
     */
    public static String getBranchId(Object... candidates) {
        // 按优先级顺序检查候选参数
        if (candidates != null) {
            for (Object candidate : candidates) {
                if (candidate != null) {
                    if (candidate instanceof Long) {
                        return candidate.toString();
                    } else if (candidate instanceof String) {
                        String strCandidate = (String) candidate;
                        if (!strCandidate.trim().isEmpty()) {
                            return strCandidate;
                        }
                    } else if (candidate instanceof Number) {
                        return candidate.toString();
                    }
                }
            }
        }

        // 所有候选参数都无效，从上下文获取
        return getBranchId();
    }

    /**
     * 格式化上下文信息为键值对格式
     * 
     * @param params 参数数组，偶数位为key，奇数位为value
     * @return 格式化后的字符串，如：[key1=value1, key2=value2]
     */
    public static String formatContext(Object... params) {
        if (params == null || params.length == 0) {
            return "";
        }
        
        StringBuilder sb = new StringBuilder("[");
        for (int i = 0; i < params.length; i += 2) {
            if (i > 0) sb.append(", ");
            if (i + 1 < params.length) {
                sb.append(params[i]).append("=").append(params[i + 1]);
            } else {
                sb.append(params[i]);
            }
        }
        sb.append("]");
        return sb.toString();
    }

    /**
     * 格式化耗时信息（毫秒时间戳）
     * 
     * @param startTime 开始时间（毫秒时间戳）
     * @return 耗时字符串，如：218ms
     */
    public static String formatDuration(long startTime) {
        return (System.currentTimeMillis() - startTime) + "ms";
    }

    /**
     * 格式化耗时信息（LocalDateTime）
     * 
     * @param startTime 开始时间（LocalDateTime）
     * @return 耗时字符串，如：218ms
     */
    public static String formatDuration(LocalDateTime startTime) {
        return Duration.between(startTime, LocalDateTime.now()).toMillis() + "ms";
    }

    /**
     * 格式化异常信息
     * 
     * @param e 异常对象
     * @return 格式化后的异常信息
     */
    public static String formatException(Exception e) {
        if (e == null) {
            return "null";
        }
        return formatContext("error", e.getClass().getSimpleName(), "msg", e.getMessage());
    }

    /**
     * 构建事务开始日志
     * 
     * @param mode 事务模式
     * @param method 业务方法名
     * @return 格式化的日志消息
     */
    public static String buildBeginLog(String mode, String method) {
        return String.format("[%s|%s] [BEGIN] - 事务开始 %s", 
                getTxId(), getBranchId(), 
                formatContext("mode", mode, "method", method));
    }

    /**
     * 构建事务提交日志
     * 
     * @param branchCount 分支数量
     * @param startTime 开始时间
     * @return 格式化的日志消息
     */
    public static String buildCommitLog(int branchCount, long startTime) {
        return String.format("[%s|%s] [COMMIT] - 事务提交成功 %s", 
                getTxId(), getBranchId(), 
                formatContext("branches", branchCount, "total", formatDuration(startTime)));
    }

    /**
     * 构建事务回滚日志
     * 
     * @param reason 回滚原因
     * @param cause 异常原因
     * @return 格式化的日志消息
     */
    public static String buildRollbackLog(String reason, Exception cause) {
        return String.format("[%s|%s] [ROLLBACK] - 事务回滚 %s", 
                getTxId(), getBranchId(), 
                formatContext("reason", reason, "cause", cause != null ? cause.getClass().getSimpleName() : "unknown"));
    }

    /**
     * 构建分支注册日志
     * 
     * @param service 服务名
     * @param method 方法名
     * @return 格式化的日志消息
     */
    public static String buildRegisterLog(String service, String method) {
        return String.format("[%s|%s] [REGISTER] - 分支注册 %s", 
                getTxId(), getBranchId(), 
                formatContext("service", service, "method", method));
    }

    /**
     * 构建状态转换日志
     * 
     * @param fromStatus 原状态
     * @param toStatus 目标状态
     * @param event 触发事件
     * @param startTime 开始时间
     * @return 格式化的日志消息
     */
    public static String buildStateLog(Object fromStatus, Object toStatus, String event, long startTime) {
        return String.format("[%s|%s] [STATE] - %s -> %s %s", 
                getTxId(), getBranchId(), fromStatus, toStatus,
                formatContext("event", event, "cost", formatDuration(startTime)));
    }

    /**
     * 构建错误日志
     * 
     * @param action 执行动作
     * @param e 异常
     * @return 格式化的日志消息
     */
    public static String buildErrorLog(String action, Exception e) {
        return String.format("[%s|%s] [ERROR] - %s %s", 
                getTxId(), getBranchId(), action + "失败", formatException(e));
    }

    /**
     * 构建性能日志
     * 
     * @param phase 阶段名称
     * @param startTime 开始时间
     * @param additionalParams 额外参数
     * @return 格式化的日志消息
     */
    public static String buildPerfLog(String phase, long startTime, Object... additionalParams) {
        Object[] params = new Object[additionalParams.length + 2];
        System.arraycopy(additionalParams, 0, params, 0, additionalParams.length);
        params[additionalParams.length] = "cost";
        params[additionalParams.length + 1] = formatDuration(startTime);
        
        return String.format("[%s|%s] [PERF] - %s性能 %s", 
                getTxId(), getBranchId(), phase, formatContext(params));
    }

    /**
     * 构建调试日志
     * 
     * @param action 执行动作
     * @param params 参数信息
     * @return 格式化的日志消息
     */
    public static String buildDebugLog(String action, Object... params) {
        return String.format("[%s|%s] [DEBUG] - %s %s", 
                getTxId(), getBranchId(), action, formatContext(params));
    }

    /**
     * 构建警告日志
     * 
     * @param action 执行动作
     * @param reason 警告原因
     * @return 格式化的日志消息
     */
    public static String buildWarnLog(String action, String reason) {
        return String.format("[%s|%s] [WARN] - %s %s", 
                getTxId(), getBranchId(), action, formatContext("reason", reason));
    }

    /**
     * 截断长消息
     * 
     * @param message 原始消息
     * @param maxLength 最大长度
     * @return 截断后的消息
     */
    public static String truncateMessage(String message, int maxLength) {
        if (message == null) {
            return "null";
        }
        if (message.length() <= maxLength) {
            return message;
        }
        return message.substring(0, maxLength - 3) + "...";
    }
}
