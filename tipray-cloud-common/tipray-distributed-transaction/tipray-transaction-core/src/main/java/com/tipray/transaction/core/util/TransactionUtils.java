package com.tipray.transaction.core.util;

import cn.hutool.json.JSONUtil;
import com.tipray.transaction.core.context.TransactionContextHolder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Optional;
import java.util.concurrent.ThreadLocalRandom;
import java.util.function.Supplier;

/**
 * 分布式事务工具类
 * 统一管理事务相关的工具方法
 *
 * <AUTHOR>
 */
@Slf4j
public final class TransactionUtils {

    private static final DateTimeFormatter FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    private TransactionUtils() {
        // 工具类不允许实例化
    }

    /**
     * 生成分布式事务ID
     * 使用时间戳 + 线程ID + 随机数确保唯一性
     *
     * @param prefix 前缀（可选）
     * @return 事务ID
     */
    public static String generateTransactionId(String prefix) {
        long timestamp = System.currentTimeMillis();
        long threadId = Thread.currentThread().getId();
        // 使用ThreadLocalRandom提高性能
        int random = ThreadLocalRandom.current().nextInt(10000);

        if (StringUtils.hasText(prefix)) {
            return String.format("%s-%d-%d-%04d", prefix, timestamp, threadId, random);
        } else {
            return String.format("TXG-%d-%d-%04d", timestamp, threadId, random);
        }
    }

    /**
     * 生成分支事务ID
     * 基于时间戳和随机数生成
     *
     * @return 分支事务ID
     */
    public static long generateBranchId() {
        long timestamp = System.currentTimeMillis();
        int random = ThreadLocalRandom.current().nextInt(1000);
        return timestamp * 1000 + random;
    }

    /**
     * 安全执行操作，返回Optional
     * 避免异常导致程序中断
     *
     * @param supplier 操作供应商
     * @param <T>      返回类型
     * @return 执行结果
     */
    public static <T> Optional<T> safeExecute(Supplier<T> supplier) {
        try {
            return Optional.ofNullable(supplier.get());
        } catch (Exception e) {
            TransactionLogger.logWarn(TransactionContextHolder.getCurrentTransactionId(), null, "安全执行操作失败", e.getMessage());
            return Optional.empty();
        }
    }

    /**
     * 带重试的安全执行
     *
     * @param supplier   操作供应商
     * @param maxRetries 最大重试次数
     * @param <T>        返回类型
     * @return 执行结果
     */
    public static <T> Optional<T> executeWithRetry(Supplier<T> supplier, int maxRetries) {
        Exception lastException = null;

        for (int i = 0; i <= maxRetries; i++) {
            try {
                return Optional.ofNullable(supplier.get());
            } catch (Exception e) {
                lastException = e;
                if (i < maxRetries) {
                    TransactionLogger.logDebug(TransactionContextHolder.getCurrentTransactionId(), null, "重试操作",
                            "第" + (i + 1) + "次尝试失败，继续重试", e.getMessage());
                    try {
                        Thread.sleep(1000 * (i + 1)); // 递增延迟
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        break;
                    }
                }
            }
        }

        TransactionLogger.logError(TransactionContextHolder.getCurrentTransactionId(), null, "重试操作最终失败",
                new RuntimeException("重试" + maxRetries + "次后仍然失败", lastException));
        return Optional.empty();
    }

    /**
     * 条件执行
     *
     * @param condition 条件
     * @param supplier  操作供应商
     * @param <T>       返回类型
     * @return 执行结果
     */
    public static <T> Optional<T> executeIf(boolean condition, Supplier<T> supplier) {
        return condition ? Optional.ofNullable(supplier.get()) : Optional.empty();
    }

    /**
     * 格式化时间
     *
     * @param timestamp 时间戳
     * @return 格式化后的时间字符串
     */
    public static String formatTime(long timestamp) {
        if (timestamp <= 0) {
            return "未知时间";
        }
        return LocalDateTime.ofInstant(
                java.time.Instant.ofEpochMilli(timestamp),
                java.time.ZoneId.systemDefault()
        ).format(FORMATTER);
    }

    /**
     * 计算耗时并格式化
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 格式化的耗时字符串
     */
    public static String formatDuration(long startTime, long endTime) {
        if (startTime <= 0 || endTime <= 0 || endTime < startTime) {
            return "未知";
        }

        long duration = endTime - startTime;
        if (duration < 1000) {
            return duration + "ms";
        } else if (duration < 60000) {
            return String.format("%.2fs", duration / 1000.0);
        } else {
            return String.format("%.2fm", duration / 60000.0);
        }
    }

    /**
     * 安全的JSON序列化
     *
     * @param obj 对象
     * @return JSON字符串
     */
    public static String toJsonSafely(Object obj) {
        if (obj == null) {
            return "null";
        }
        try {
            return JSONUtil.toJsonStr(obj);
        } catch (Exception e) {
            TransactionLogger.logWarn(TransactionContextHolder.getCurrentTransactionId(), null, "JSON序列化失败", e.getMessage());
            return obj.toString();
        }
    }

    /**
     * 安全的JSON反序列化
     *
     * @param json  JSON字符串
     * @param clazz 目标类型
     * @param <T>   类型参数
     * @return 反序列化结果
     */
    public static <T> Optional<T> fromJsonSafely(String json, Class<T> clazz) {
        if (!StringUtils.hasText(json)) {
            return Optional.empty();
        }
        try {
            return Optional.ofNullable(JSONUtil.toBean(json, clazz));
        } catch (Exception e) {
            log.warn("[{}|{}] [WARN] - JSON反序列化失败 {}",
                    LoggingUtils.getTxId(), LoggingUtils.getBranchId(),
                    LoggingUtils.formatContext("error", e.getMessage()));
            return Optional.empty();
        }
    }

    /**
     * 构建事务日志消息
     *
     * @param groupId   事务ID
     * @param operation 操作
     * @param success   是否成功
     * @return 日志消息
     */
    public static String buildLogMessage(String groupId, String operation, boolean success) {
        return String.format("[%s] %s - %s", groupId, operation, success ? "成功" : "失败");
    }

    /**
     * 构建事务日志消息（带详细信息）
     *
     * @param groupId   事务ID
     * @param operation 操作
     * @param success   是否成功
     * @param details   详细信息
     * @return 日志消息
     */
    public static String buildLogMessage(String groupId, String operation, boolean success, String details) {
        String baseMessage = buildLogMessage(groupId, operation, success);
        return StringUtils.hasText(details) ? baseMessage + " - " + details : baseMessage;
    }

    /**
     * 安全的字符串截取
     *
     * @param str       原字符串
     * @param maxLength 最大长度
     * @return 截取后的字符串
     */
    public static String safeSub(String str, int maxLength) {
        if (str == null) {
            return null;
        }
        if (str.length() <= maxLength) {
            return str;
        }
        return str.substring(0, maxLength) + "...";
    }

    /**
     * 检查字符串是否有效（非空且非空白）
     *
     * @param str 字符串
     * @return 是否有效
     */
    public static boolean isValid(String str) {
        return StringUtils.hasText(str);
    }

    /**
     * 获取安全的字符串（避免null）
     *
     * @param str          原字符串
     * @param defaultValue 默认值
     * @return 安全的字符串
     */
    public static String safeString(String str, String defaultValue) {
        return StringUtils.hasText(str) ? str : defaultValue;
    }

    /**
     * 获取安全的字符串（默认为空字符串）
     *
     * @param str 原字符串
     * @return 安全的字符串
     */
    public static String safeString(String str) {
        return safeString(str, "");
    }

    /**
     * 构建缓存键
     *
     * @param parts 键的各个部分
     * @return 缓存键
     */
    public static String buildCacheKey(String... parts) {
        if (parts == null || parts.length == 0) {
            return "";
        }
        return String.join(":", parts);
    }

    /**
     * 标准化SQL（用于缓存键）
     *
     * @param sql SQL语句
     * @return 标准化的SQL
     */
    public static String normalizeSQL(String sql) {
        if (sql == null) {
            return "";
        }
        return sql.trim().replaceAll("\\s+", " ").toLowerCase();
    }
}
