package com.tipray.transaction.core.persistence;

import com.tipray.transaction.core.domain.execution.BranchTransactionExecutionRecordDO;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 分支事务执行历史存储接口
 * 定义分支事务执行历史数据的持久化操作
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-01-25
 */
public interface BranchTransactionExecutionHistoryStorage {

    /**
     * 保存分支事务执行记录
     *
     * @param record 执行记录
     * @return 保存后的执行记录
     */
    BranchTransactionExecutionRecordDO saveExecutionRecord(BranchTransactionExecutionRecordDO record);

    /**
     * 批量保存分支事务执行记录
     *
     * @param records 执行记录列表
     * @return 保存后的执行记录列表
     */
    List<BranchTransactionExecutionRecordDO> saveExecutionRecords(List<BranchTransactionExecutionRecordDO> records);

    /**
     * 更新分支事务执行记录
     *
     * @param record 执行记录
     * @return 更新后的执行记录
     */
    BranchTransactionExecutionRecordDO updateExecutionRecord(BranchTransactionExecutionRecordDO record);

    /**
     * 根据记录ID查找执行记录
     *
     * @param recordId 记录ID
     * @return 执行记录
     */
    BranchTransactionExecutionRecordDO findExecutionRecordById(Long recordId);

    /**
     * 根据分支事务ID查找所有执行记录
     *
     * @param branchTransactionId 分支事务ID
     * @return 执行记录列表，按执行次数排序
     */
    List<BranchTransactionExecutionRecordDO> findExecutionRecordsByBranchId(Long branchTransactionId);

    /**
     * 根据分支事务ID和执行次数查找执行记录
     *
     * @param branchTransactionId 分支事务ID
     * @param attemptNumber       执行次数
     * @return 执行记录
     */
    BranchTransactionExecutionRecordDO findExecutionRecordByBranchIdAndAttempt(Long branchTransactionId, Integer attemptNumber);

    /**
     * 根据全局事务ID查找所有分支执行记录
     *
     * @param transactionId 全局事务ID
     * @return 执行记录列表
     */
    List<BranchTransactionExecutionRecordDO> findExecutionRecordsByTransactionId(String transactionId);

    /**
     * 根据步骤名称查找执行记录
     *
     * @param stepName 步骤名称
     * @return 执行记录列表
     */
    List<BranchTransactionExecutionRecordDO> findExecutionRecordsByStepName(String stepName);

    /**
     * 根据执行类型查找执行记录
     *
     * @param executionType 执行类型
     * @return 执行记录列表
     */
    List<BranchTransactionExecutionRecordDO> findExecutionRecordsByExecutionType(String executionType);

    /**
     * 根据执行状态查找执行记录
     *
     * @param status 执行状态
     * @return 执行记录列表
     */
    List<BranchTransactionExecutionRecordDO> findExecutionRecordsByStatus(String status);

    /**
     * 根据时间范围查找执行记录
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 执行记录列表
     */
    List<BranchTransactionExecutionRecordDO> findExecutionRecordsByTimeRange(LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 根据目标服务查找执行记录
     *
     * @param targetService 目标服务
     * @return 执行记录列表
     */
    List<BranchTransactionExecutionRecordDO> findExecutionRecordsByTargetService(String targetService);

    /**
     * 查找失败的执行记录
     *
     * @return 失败的执行记录列表
     */
    List<BranchTransactionExecutionRecordDO> findFailedExecutionRecords();

    /**
     * 查找超时的执行记录
     *
     * @return 超时的执行记录列表
     */
    List<BranchTransactionExecutionRecordDO> findTimeoutExecutionRecords();

    /**
     * 查找需要重试的执行记录
     *
     * @return 需要重试的执行记录列表
     */
    List<BranchTransactionExecutionRecordDO> findRetryableExecutionRecords();

    /**
     * 获取分支事务的最新执行记录
     *
     * @param branchTransactionId 分支事务ID
     * @return 最新的执行记录
     */
    BranchTransactionExecutionRecordDO findLatestExecutionRecordByBranchId(Long branchTransactionId);

    /**
     * 统计分支事务的执行次数
     *
     * @param branchTransactionId 分支事务ID
     * @return 执行次数
     */
    int countExecutionRecordsByBranchId(Long branchTransactionId);

    /**
     * 统计分支事务的重试次数
     *
     * @param branchTransactionId 分支事务ID
     * @return 重试次数
     */
    int countRetryExecutionRecordsByBranchId(Long branchTransactionId);

    /**
     * 统计全局事务的分支执行记录数
     *
     * @param transactionId 全局事务ID
     * @return 分支执行记录数
     */
    int countExecutionRecordsByTransactionId(String transactionId);

    /**
     * 统计指定状态的执行记录数
     *
     * @param status 执行状态
     * @return 执行记录数
     */
    int countExecutionRecordsByStatus(String status);

    /**
     * 统计指定时间范围内的执行记录数
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 执行记录数
     */
    int countExecutionRecordsByTimeRange(LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 删除指定分支事务的所有执行记录
     *
     * @param branchTransactionId 分支事务ID
     * @return 删除的记录数
     */
    int deleteExecutionRecordsByBranchId(Long branchTransactionId);

    /**
     * 删除指定全局事务的所有分支执行记录
     *
     * @param transactionId 全局事务ID
     * @return 删除的记录数
     */
    int deleteExecutionRecordsByTransactionId(String transactionId);

    /**
     * 删除指定时间之前的执行记录（用于数据清理）
     *
     * @param beforeTime 时间点
     * @return 删除的记录数
     */
    int deleteExecutionRecordsBefore(LocalDateTime beforeTime);

    /**
     * 分页查询执行记录
     *
     * @param offset 偏移量
     * @param limit  限制数量
     * @return 执行记录列表
     */
    List<BranchTransactionExecutionRecordDO> findExecutionRecordsWithPagination(int offset, int limit);

    /**
     * 根据分支事务ID分页查询执行记录
     *
     * @param branchTransactionId 分支事务ID
     * @param offset              偏移量
     * @param limit               限制数量
     * @return 执行记录列表
     */
    List<BranchTransactionExecutionRecordDO> findExecutionRecordsByBranchIdWithPagination(Long branchTransactionId, int offset, int limit);

    /**
     * 根据全局事务ID分页查询分支执行记录
     *
     * @param transactionId 全局事务ID
     * @param offset        偏移量
     * @param limit         限制数量
     * @return 执行记录列表
     */
    List<BranchTransactionExecutionRecordDO> findExecutionRecordsByTransactionIdWithPagination(String transactionId, int offset, int limit);

    /**
     * 获取存储统计信息
     *
     * @return 统计信息
     */
    Object getStatistics();
}
