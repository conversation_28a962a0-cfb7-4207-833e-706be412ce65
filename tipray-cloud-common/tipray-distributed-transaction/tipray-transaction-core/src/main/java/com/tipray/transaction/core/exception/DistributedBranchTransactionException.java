package com.tipray.transaction.core.exception;

/**
 * Tipray分支事务异常
 * 分支事务执行过程中发生的异常
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-06-20
 */
public class DistributedBranchTransactionException extends DistributedTransactionException {

    /**
     * 序列化版本号
     */
    private static final long serialVersionUID = 1L;

    /**
     * 分支事务ID
     */
    private String branchId;

    /**
     * 分支事务类型
     */
    private String branchType;

    /**
     * 资源ID
     */
    private String resourceId;

    /**
     * 默认构造函数
     */
    public DistributedBranchTransactionException() {
        super();
    }

    /**
     * 构造函数
     *
     * @param message 错误信息
     */
    public DistributedBranchTransactionException(String message) {
        super(message);
    }

    /**
     * 构造函数
     *
     * @param message 错误信息
     * @param cause   原因异常
     */
    public DistributedBranchTransactionException(String message, Throwable cause) {
        super(message, cause);
    }

    /**
     * 构造函数
     *
     * @param errorCode 错误代码
     * @param message   错误信息
     * @param branchId  分支事务ID
     */
    public DistributedBranchTransactionException(String errorCode, String message, String branchId) {
        super(errorCode, message);
        this.branchId = branchId;
    }

    /**
     * 构造函数
     *
     * @param errorCode 错误代码
     * @param message   错误信息
     * @param cause     原因异常
     * @param branchId  分支事务ID
     */
    public DistributedBranchTransactionException(String errorCode, String message, Throwable cause, String branchId) {
        super(errorCode, message, cause);
        this.branchId = branchId;
    }

    /**
     * 构造函数
     *
     * @param errorCode     错误代码
     * @param message       错误信息
     * @param transactionId 事务ID
     * @param groupId       事务组ID
     * @param branchId      分支事务ID
     * @param branchType    分支事务类型
     * @param resourceId    资源ID
     */
    public DistributedBranchTransactionException(String errorCode, String message, String transactionId, String groupId,
                                                 String branchId, String branchType, String resourceId) {
        super(errorCode, message, transactionId, groupId);
        this.branchId = branchId;
        this.branchType = branchType;
        this.resourceId = resourceId;
    }

    /**
     * 构造函数
     *
     * @param errorCode     错误代码
     * @param message       错误信息
     * @param cause         原因异常
     * @param transactionId 事务ID
     * @param groupId       事务组ID
     * @param branchId      分支事务ID
     * @param branchType    分支事务类型
     * @param resourceId    资源ID
     */
    public DistributedBranchTransactionException(String errorCode, String message, Throwable cause, String transactionId, String groupId,
                                                 String branchId, String branchType, String resourceId) {
        super(errorCode, message, cause, transactionId, groupId);
        this.branchId = branchId;
        this.branchType = branchType;
        this.resourceId = resourceId;
    }

    /**
     * 获取分支事务ID
     *
     * @return 分支事务ID
     */
    public String getBranchId() {
        return branchId;
    }

    /**
     * 设置分支事务ID
     *
     * @param branchId 分支事务ID
     */
    public void setBranchId(String branchId) {
        this.branchId = branchId;
    }

    /**
     * 获取分支事务类型
     *
     * @return 分支事务类型
     */
    public String getBranchType() {
        return branchType;
    }

    /**
     * 设置分支事务类型
     *
     * @param branchType 分支事务类型
     */
    public void setBranchType(String branchType) {
        this.branchType = branchType;
    }

    /**
     * 获取资源ID
     *
     * @return 资源ID
     */
    public String getResourceId() {
        return resourceId;
    }

    /**
     * 设置资源ID
     *
     * @param resourceId 资源ID
     */
    public void setResourceId(String resourceId) {
        this.resourceId = resourceId;
    }

    @Override
    public String getFullMessage() {
        StringBuilder sb = new StringBuilder();
        sb.append(super.getFullMessage());

        if (branchId != null) {
            sb.append(" [分支ID: ").append(branchId).append("]");
        }

        if (branchType != null) {
            sb.append(" [分支类型: ").append(branchType).append("]");
        }

        if (resourceId != null) {
            sb.append(" [资源ID: ").append(resourceId).append("]");
        }

        return sb.toString();
    }
}
