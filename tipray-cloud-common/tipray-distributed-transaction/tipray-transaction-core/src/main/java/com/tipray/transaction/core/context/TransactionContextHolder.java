package com.tipray.transaction.core.context;

import com.alibaba.ttl.TransmittableThreadLocal;
import com.tipray.transaction.core.domain.transaction.TransactionContext;
import com.tipray.transaction.core.util.LoggingUtils;
import lombok.extern.slf4j.Slf4j;

import java.util.Stack;

/**
 * 事务上下文持有者
 * 使用TransmittableThreadLocal保证上下文在线程间传递
 * 支持嵌套事务的上下文管理
 *
 * <AUTHOR>
 * @version 2.0
 * @since 2025-01-23
 */
@Slf4j
public class TransactionContextHolder {

    // 使用TransmittableThreadLocal支持线程池场景下的上下文传递
    private static final TransmittableThreadLocal<Stack<TransactionContext>> CONTEXT_HOLDER =
            new TransmittableThreadLocal<Stack<TransactionContext>>() {
                @Override
                protected Stack<TransactionContext> initialValue() {
                    return new Stack<>();
                }
            };

    /**
     * 获取当前事务上下文
     */
    public static TransactionContext getCurrentContext() {
        Stack<TransactionContext> stack = CONTEXT_HOLDER.get();
        return stack.isEmpty() ? null : stack.peek();
    }

    /**
     * 设置当前事务上下文
     */
    public static void setCurrentContext(TransactionContext context) {
        if (context == null) {
            throw new IllegalArgumentException("事务上下文不能为空");
        }

        Stack<TransactionContext> stack = CONTEXT_HOLDER.get();
        stack.push(context);

        log.debug("[{}|{}] [DEBUG] - 设置事务上下文 {}",
                LoggingUtils.getTxId(context.getTransactionId()),
                LoggingUtils.getBranchId(context.getBranchId()),
                LoggingUtils.formatContext("嵌套层级", stack.size()));
    }

    /**
     * 清理当前事务上下文
     */
    public static TransactionContext clearCurrentContext() {
        Stack<TransactionContext> stack = CONTEXT_HOLDER.get();
        if (stack.isEmpty()) {
            return null;
        }

        TransactionContext context = stack.pop();

        log.debug("[{}|{}] [DEBUG] - 清理事务上下文 {}",
                LoggingUtils.getTxId(context.getTransactionId()),
                LoggingUtils.getBranchId(context.getBranchId()),
                LoggingUtils.formatContext("剩余嵌套层级", stack.size()));

        // 如果栈为空，清理ThreadLocal
        if (stack.isEmpty()) {
            CONTEXT_HOLDER.remove();
        }

        return context;
    }

    /**
     * 获取当前事务ID
     */
    public static String getCurrentTransactionId() {
        TransactionContext context = getCurrentContext();
        return context != null ? context.getTransactionId() : null;
    }

    /**
     * 判断当前是否存在事务
     */
    public static boolean hasCurrentTransaction() {
        return getCurrentContext() != null;
    }

    /**
     * 获取当前嵌套层级
     */
    public static int getCurrentNestingLevel() {
        Stack<TransactionContext> stack = CONTEXT_HOLDER.get();
        return stack.size();
    }

    /**
     * 获取根事务上下文
     */
    public static TransactionContext getRootContext() {
        Stack<TransactionContext> stack = CONTEXT_HOLDER.get();
        if (stack.isEmpty()) {
            return null;
        }

        // 返回栈底的元素（根事务）
        return stack.firstElement();
    }

    /**
     * 获取父事务上下文
     */
    public static TransactionContext getParentContext() {
        Stack<TransactionContext> stack = CONTEXT_HOLDER.get();
        if (stack.size() < 2) {
            return null;
        }

        // 返回倒数第二个元素（父事务）
        return stack.get(stack.size() - 2);
    }

    /**
     * 获取所有嵌套事务上下文
     */
    public static TransactionContext[] getAllContexts() {
        Stack<TransactionContext> stack = CONTEXT_HOLDER.get();
        return stack.toArray(new TransactionContext[0]);
    }

    /**
     * 判断是否为根事务
     */
    public static boolean isRootTransaction() {
        Stack<TransactionContext> stack = CONTEXT_HOLDER.get();
        return stack.size() == 1;
    }

    /**
     * 判断是否为嵌套事务
     */
    public static boolean isNestedTransaction() {
        Stack<TransactionContext> stack = CONTEXT_HOLDER.get();
        return stack.size() > 1;
    }

    /**
     * 获取事务层级路径
     */
    public static String getTransactionHierarchyPath() {
        Stack<TransactionContext> stack = CONTEXT_HOLDER.get();
        if (stack.isEmpty()) {
            return "";
        }

        StringBuilder path = new StringBuilder();
        for (int i = 0; i < stack.size(); i++) {
            if (i > 0) {
                path.append(" -> ");
            }
            path.append(stack.get(i).getTransactionId());
        }

        return path.toString();
    }

    /**
     * 替换当前事务上下文（用于传播行为处理）
     */
    public static TransactionContext replaceCurrentContext(TransactionContext newContext) {
        if (newContext == null) {
            throw new IllegalArgumentException("新事务上下文不能为空");
        }

        Stack<TransactionContext> stack = CONTEXT_HOLDER.get();
        TransactionContext oldContext = stack.isEmpty() ? null : stack.pop();
        stack.push(newContext);

        String oldTxId = oldContext != null ? oldContext.getTransactionId() : "null";
        log.debug("[{}|{}] [DEBUG] - 替换事务上下文 {}",
                LoggingUtils.getTxId(newContext.getTransactionId()),
                LoggingUtils.getBranchId(newContext.getBranchId()),
                LoggingUtils.formatContext("从", oldTxId, "到", newContext.getTransactionId()));

        return oldContext;
    }

    /**
     * 挂起当前事务上下文
     */
    public static TransactionContext suspendCurrentContext() {
        Stack<TransactionContext> stack = CONTEXT_HOLDER.get();
        if (stack.isEmpty()) {
            return null;
        }

        TransactionContext context = stack.pop();
        context.suspend("上下文挂起");

        log.debug("[{}|{}] [DEBUG] - 挂起事务上下文 {}",
                LoggingUtils.getTxId(context.getTransactionId()),
                LoggingUtils.getBranchId(context.getBranchId()),
                LoggingUtils.formatContext("剩余嵌套层级", stack.size()));

        return context;
    }

    /**
     * 恢复挂起的事务上下文
     */
    public static void resumeContext(TransactionContext context) {
        if (context == null) {
            return;
        }

        context.resume();
        Stack<TransactionContext> stack = CONTEXT_HOLDER.get();
        stack.push(context);

        log.debug("[{}|{}] [DEBUG] - 恢复事务上下文 {}",
                LoggingUtils.getTxId(context.getTransactionId()),
                LoggingUtils.getBranchId(context.getBranchId()),
                LoggingUtils.formatContext("嵌套层级", stack.size()));
    }

    /**
     * 清理所有上下文（用于异常情况下的强制清理）
     */
    public static void clearAllContexts() {
        Stack<TransactionContext> stack = CONTEXT_HOLDER.get();
        int size = stack.size();

        if (size > 0) {
            log.warn("[{}|{}] [WARN] - 强制清理所有事务上下文 {}",
                    LoggingUtils.getTxId(), LoggingUtils.getBranchId(),
                    LoggingUtils.formatContext("数量", size));
            stack.clear();
            CONTEXT_HOLDER.remove();
        }
    }

    /**
     * 获取上下文统计信息
     */
    public static ContextStatistics getContextStatistics() {
        Stack<TransactionContext> stack = CONTEXT_HOLDER.get();

        return ContextStatistics.builder()
                .nestingLevel(stack.size())
                .hasContext(!stack.isEmpty())
                .isRoot(stack.size() == 1)
                .isNested(stack.size() > 1)
                .hierarchyPath(getTransactionHierarchyPath())
                .build();
    }

    /**
     * 上下文统计信息
     */
    public static class ContextStatistics {
        private final int nestingLevel;
        private final boolean hasContext;
        private final boolean isRoot;
        private final boolean isNested;
        private final String hierarchyPath;

        private ContextStatistics(Builder builder) {
            this.nestingLevel = builder.nestingLevel;
            this.hasContext = builder.hasContext;
            this.isRoot = builder.isRoot;
            this.isNested = builder.isNested;
            this.hierarchyPath = builder.hierarchyPath;
        }

        public static Builder builder() {
            return new Builder();
        }

        // getters
        public int getNestingLevel() {
            return nestingLevel;
        }

        public boolean isHasContext() {
            return hasContext;
        }

        public boolean isRoot() {
            return isRoot;
        }

        public boolean isNested() {
            return isNested;
        }

        public String getHierarchyPath() {
            return hierarchyPath;
        }

        @Override
        public String toString() {
            return String.format("ContextStatistics{level=%d, hasContext=%s, path='%s'}",
                    nestingLevel, hasContext, hierarchyPath);
        }

        public static class Builder {
            private int nestingLevel;
            private boolean hasContext;
            private boolean isRoot;
            private boolean isNested;
            private String hierarchyPath;

            public Builder nestingLevel(int nestingLevel) {
                this.nestingLevel = nestingLevel;
                return this;
            }

            public Builder hasContext(boolean hasContext) {
                this.hasContext = hasContext;
                return this;
            }

            public Builder isRoot(boolean isRoot) {
                this.isRoot = isRoot;
                return this;
            }

            public Builder isNested(boolean isNested) {
                this.isNested = isNested;
                return this;
            }

            public Builder hierarchyPath(String hierarchyPath) {
                this.hierarchyPath = hierarchyPath;
                return this;
            }

            public ContextStatistics build() {
                return new ContextStatistics(this);
            }
        }
    }
}
