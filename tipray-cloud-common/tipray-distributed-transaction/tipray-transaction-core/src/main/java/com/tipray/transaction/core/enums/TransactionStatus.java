package com.tipray.transaction.core.enums;

import java.util.*;

/**
 * 分布式事务状态枚举
 * 参考Seata的GlobalStatus设计，结合通用分布式事务场景优化
 *
 * <AUTHOR>
 * @version 2.0
 * @since 2025-01-23
 */
public enum TransactionStatus {

    // ========== 初始阶段 ==========
    /**
     * 未知状态 - 通常在事务开始前使用
     */
    UNKNOWN(0, "未知状态", false, false, false),

    /**
     * 开始状态 - 全局事务开始，可以接受新的分支注册
     */
    BEGIN(1, "开始", false, false, false),

    // ========== 执行阶段 ==========
    /**
     * 执行中状态 - 事务正在执行业务逻辑
     */
    EXECUTING(2, "执行中", false, false, false),

    /**
     * 等待中状态 - 事务暂时等待某些条件满足
     */
    WAITING(3, "等待中", false, false, false),

    /**
     * 暂停状态 - 事务被手动暂停或因异常暂停
     */
    PAUSED(4, "暂停", false, false, false),

    // ========== 提交阶段 ==========
    /**
     * 提交中状态 - 两阶段提交进行中
     */
    COMMITTING(5, "提交中", false, false, false),

    /**
     * 提交重试中状态 - 提交失败后重试中
     */
    COMMIT_RETRYING(6, "提交重试中", false, false, false),

    /**
     * 异步提交中状态 - 异步提交进行中（主要用于Saga模式）
     */
    ASYNC_COMMITTING(7, "异步提交中", false, false, false),

    // ========== 回滚阶段 ==========
    /**
     * 回滚中状态 - 事务回滚进行中
     */
    ROLLBACKING(8, "回滚中", false, false, false),

    /**
     * 回滚重试中状态 - 回滚失败后重试中
     */
    ROLLBACK_RETRYING(9, "回滚重试中", false, false, false),

    /**
     * 超时回滚中状态 - 因超时触发的回滚进行中
     */
    TIMEOUT_ROLLBACKING(10, "超时回滚中", false, false, false),

    /**
     * 超时回滚重试中状态 - 超时回滚失败后重试中
     */
    TIMEOUT_ROLLBACK_RETRYING(11, "超时回滚重试中", false, false, false),

    // ========== 终态 ==========
    /**
     * 已提交状态 - 全局事务成功提交完成
     */
    COMMITTED(12, "已提交", true, false, false),

    /**
     * 提交失败状态 - 两阶段提交失败
     */
    COMMIT_FAILED(13, "提交失败", true, true, false),

    /**
     * 已回滚状态 - 全局事务成功回滚完成
     */
    ROLLBACKED(14, "已回滚", true, false, false),

    /**
     * 回滚失败状态 - 回滚失败
     */
    ROLLBACK_FAILED(15, "回滚失败", true, true, false),

    /**
     * 超时回滚状态 - 因超时成功回滚完成
     */
    TIMEOUT_ROLLBACKED(16, "超时回滚", true, false, false),

    /**
     * 超时回滚失败状态 - 超时回滚失败
     */
    TIMEOUT_ROLLBACK_FAILED(17, "超时回滚失败", true, true, false),

    /**
     * 已完成状态 - 事务完成，资源已清理
     */
    FINISHED(18, "已完成", true, false, true),

    // ========== 特殊状态 ==========
    /**
     * 需要人工干预状态 - 自动处理失败，需要人工介入
     */
    MANUAL_INTERVENTION_REQUIRED(19, "需要人工干预", true, true, false),

    /**
     * 已取消状态 - 事务被手动取消
     */
    CANCELLED(20, "已取消", true, false, false);

    // 状态转换规则映射
    private static final Map<TransactionStatus, Set<TransactionStatus>> TRANSITION_RULES;

    static {
        Map<TransactionStatus, Set<TransactionStatus>> rules = new HashMap<>();

        // UNKNOWN状态转换
        rules.put(UNKNOWN, createSet(BEGIN, CANCELLED));

        // BEGIN状态转换
        rules.put(BEGIN, createSet(EXECUTING, CANCELLED, ROLLBACKING));

        // EXECUTING状态转换
        rules.put(EXECUTING, createSet(
                COMMITTING, ROLLBACKING, WAITING, PAUSED,
                TIMEOUT_ROLLBACKING, CANCELLED
        ));

        // WAITING状态转换
        rules.put(WAITING, createSet(
                EXECUTING, COMMITTING, ROLLBACKING,
                TIMEOUT_ROLLBACKING, CANCELLED
        ));

        // PAUSED状态转换
        rules.put(PAUSED, createSet(
                EXECUTING, ROLLBACKING, CANCELLED
        ));

        // COMMITTING状态转换
        rules.put(COMMITTING, createSet(
                COMMITTED, COMMIT_FAILED, COMMIT_RETRYING,
                ROLLBACKING, ASYNC_COMMITTING
        ));

        // COMMIT_RETRYING状态转换
        rules.put(COMMIT_RETRYING, createSet(
                COMMITTED, COMMIT_FAILED, ROLLBACKING
        ));

        // ASYNC_COMMITTING状态转换
        rules.put(ASYNC_COMMITTING, createSet(
                COMMITTED, COMMIT_FAILED
        ));

        // ROLLBACKING状态转换
        rules.put(ROLLBACKING, createSet(
                ROLLBACKED, ROLLBACK_FAILED, ROLLBACK_RETRYING
        ));

        // ROLLBACK_RETRYING状态转换
        rules.put(ROLLBACK_RETRYING, createSet(
                ROLLBACKED, ROLLBACK_FAILED, MANUAL_INTERVENTION_REQUIRED
        ));

        // TIMEOUT_ROLLBACKING状态转换
        rules.put(TIMEOUT_ROLLBACKING, createSet(
                TIMEOUT_ROLLBACKED, TIMEOUT_ROLLBACK_FAILED, TIMEOUT_ROLLBACK_RETRYING
        ));

        // TIMEOUT_ROLLBACK_RETRYING状态转换
        rules.put(TIMEOUT_ROLLBACK_RETRYING, createSet(
                TIMEOUT_ROLLBACKED, TIMEOUT_ROLLBACK_FAILED, MANUAL_INTERVENTION_REQUIRED
        ));

        // 终态转换（只能转换为清理状态）
        rules.put(COMMITTED, createSet(FINISHED));
        rules.put(ROLLBACKED, createSet(FINISHED));
        rules.put(TIMEOUT_ROLLBACKED, createSet(FINISHED));
        rules.put(CANCELLED, createSet(FINISHED));

        // 失败状态可以转换为需要人工干预或重试
        rules.put(COMMIT_FAILED, createSet(MANUAL_INTERVENTION_REQUIRED, ROLLBACKING));
        rules.put(ROLLBACK_FAILED, createSet(MANUAL_INTERVENTION_REQUIRED));
        rules.put(TIMEOUT_ROLLBACK_FAILED, createSet(MANUAL_INTERVENTION_REQUIRED));

        // 人工干预状态可以转换为任何状态（人工操作）
        rules.put(MANUAL_INTERVENTION_REQUIRED, createSet(
                ROLLBACKING, COMMITTING, CANCELLED, FINISHED
        ));

        // FINISHED状态不能再转换
        rules.put(FINISHED, Collections.emptySet());

        TRANSITION_RULES = Collections.unmodifiableMap(rules);
    }

    private final int code;
    private final String description;
    private final boolean isTerminal;  // 是否为终态
    private final boolean needsIntervention;  // 是否需要人工干预
    private final boolean isCleanupState;  // 是否为清理状态

    TransactionStatus(int code, String description, boolean isTerminal,
                      boolean needsIntervention, boolean isCleanupState) {
        this.code = code;
        this.description = description;
        this.isTerminal = isTerminal;
        this.needsIntervention = needsIntervention;
        this.isCleanupState = isCleanupState;
    }

    /**
     * 创建不可变Set的辅助方法（Java 8兼容）
     */
    @SafeVarargs
    private static Set<TransactionStatus> createSet(TransactionStatus... elements) {
        Set<TransactionStatus> set = new HashSet<>();
        Collections.addAll(set, elements);
        return Collections.unmodifiableSet(set);
    }

    /**
     * 判断是否可以转换到目标状态
     */
    public boolean canTransitionTo(TransactionStatus target) {
        return TRANSITION_RULES.getOrDefault(this, Collections.emptySet()).contains(target);
    }

    /**
     * 获取下一个可能的状态
     */
    public Set<TransactionStatus> getNextPossibleStatuses() {
        return TRANSITION_RULES.getOrDefault(this, Collections.emptySet());
    }

    /**
     * 判断是否为成功状态
     */
    public boolean isSuccess() {
        return this == COMMITTED || this == FINISHED;
    }

    /**
     * 判断是否为失败状态
     */
    public boolean isFailure() {
        return this == COMMIT_FAILED || this == ROLLBACK_FAILED ||
                this == TIMEOUT_ROLLBACK_FAILED || this == MANUAL_INTERVENTION_REQUIRED;
    }

    /**
     * 判断是否为回滚相关状态
     */
    public boolean isRollbackRelated() {
        return this == ROLLBACKING || this == ROLLBACK_RETRYING ||
                this == TIMEOUT_ROLLBACKING || this == TIMEOUT_ROLLBACK_RETRYING ||
                this == ROLLBACKED || this == ROLLBACK_FAILED ||
                this == TIMEOUT_ROLLBACKED || this == TIMEOUT_ROLLBACK_FAILED;
    }

    /**
     * 判断是否为提交相关状态
     */
    public boolean isCommitRelated() {
        return this == COMMITTING || this == COMMIT_RETRYING ||
                this == ASYNC_COMMITTING || this == COMMITTED || this == COMMIT_FAILED;
    }

    // getters
    public int getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    public boolean isTerminal() {
        return isTerminal;
    }

    public boolean needsIntervention() {
        return needsIntervention;
    }

    public boolean isCleanupState() {
        return isCleanupState;
    }
}
