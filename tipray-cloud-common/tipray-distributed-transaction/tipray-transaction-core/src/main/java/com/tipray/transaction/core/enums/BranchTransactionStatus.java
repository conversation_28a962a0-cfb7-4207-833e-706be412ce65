package com.tipray.transaction.core.enums;

/**
 * 分支事务状态枚举
 * 定义分支事务的各种状态
 *
 * <AUTHOR>
 * @version 2.0
 * @since 2025-01-23
 */
public enum BranchTransactionStatus {

    UNKNOWN("初始化状态", "初始化"),

    /**
     * 已注册 - 分支事务已注册但未执行
     */
    REGISTERED("已注册", "分支事务已注册但未执行"),

    /**
     * 执行中 - 分支事务正在执行
     */
    EXECUTING("执行中", "分支事务正在执行"),

    /**
     * 已执行 - 分支事务执行成功
     */
    EXECUTED("已执行", "分支事务执行成功"),

    /**
     * 执行失败 - 分支事务执行失败
     */
    FAILED("执行失败", "分支事务执行失败"),

    /**
     * 提交中 - 分支事务正在提交
     */
    COMMITTING("提交中", "分支事务正在提交"),

    /**
     * 已提交 - 分支事务提交成功
     */
    COMMITTED("已提交", "分支事务提交成功"),

    /**
     * 提交失败 - 分支事务提交失败
     */
    COMMIT_FAILED("提交失败", "分支事务提交失败"),

    /**
     * 回滚中 - 分支事务正在回滚
     */
    ROLLBACKING("回滚中", "分支事务正在回滚"),

    /**
     * 已回滚 - 分支事务回滚成功
     */
    ROLLBACKED("已回滚", "分支事务回滚成功"),

    /**
     * 回滚失败 - 分支事务回滚失败
     */
    ROLLBACK_FAILED("回滚失败", "分支事务回滚失败");

    private final String displayName;
    private final String description;

    BranchTransactionStatus(String displayName, String description) {
        this.displayName = displayName;
        this.description = description;
    }

    /**
     * 根据名称获取状态
     */
    public static BranchTransactionStatus fromName(String name) {
        if (name == null || name.trim().isEmpty()) {
            return REGISTERED; // 默认状态
        }

        String upperName = name.trim().toUpperCase();
        for (BranchTransactionStatus status : values()) {
            if (status.name().equals(upperName)) {
                return status;
            }
        }

        throw new IllegalArgumentException("不支持的分支事务状态: " + name);
    }

    /**
     * 获取所有终态
     */
    public static BranchTransactionStatus[] getTerminalStatuses() {
        return java.util.Arrays.stream(values())
                .filter(BranchTransactionStatus::isTerminal)
                .toArray(BranchTransactionStatus[]::new);
    }

    /**
     * 获取所有成功状态
     */
    public static BranchTransactionStatus[] getSuccessStatuses() {
        return java.util.Arrays.stream(values())
                .filter(BranchTransactionStatus::isSuccess)
                .toArray(BranchTransactionStatus[]::new);
    }

    /**
     * 获取所有失败状态
     */
    public static BranchTransactionStatus[] getFailureStatuses() {
        return java.util.Arrays.stream(values())
                .filter(BranchTransactionStatus::isFailure)
                .toArray(BranchTransactionStatus[]::new);
    }

    /**
     * 判断是否为终态
     */
    public boolean isTerminal() {
        return this == COMMITTED || this == ROLLBACKED ||
                this == FAILED || this == COMMIT_FAILED || this == ROLLBACK_FAILED;
    }

    /**
     * 判断是否为成功状态
     */
    public boolean isSuccess() {
        return this == EXECUTED || this == COMMITTED;
    }

    /**
     * 判断是否为失败状态
     */
    public boolean isFailure() {
        return this == FAILED || this == COMMIT_FAILED || this == ROLLBACK_FAILED;
    }

    /**
     * 判断是否为进行中状态
     */
    public boolean isInProgress() {
        return this == EXECUTING || this == COMMITTING || this == ROLLBACKING;
    }

    /**
     * 判断是否可以提交
     */
    public boolean canCommit() {
        return this == EXECUTED;
    }

    /**
     * 判断是否可以回滚
     */
    public boolean canRollback() {
        return this == EXECUTED || this == COMMIT_FAILED;
    }

    /**
     * 判断是否需要重试
     */
    public boolean needsRetry() {
        return this == COMMIT_FAILED || this == ROLLBACK_FAILED;
    }

    /**
     * 获取下一个可能的状态
     */
    public BranchTransactionStatus[] getNextPossibleStatuses() {
        switch (this) {
            case REGISTERED:
                return new BranchTransactionStatus[]{EXECUTING};
            case EXECUTING:
                return new BranchTransactionStatus[]{EXECUTED, FAILED};
            case EXECUTED:
                return new BranchTransactionStatus[]{COMMITTING, ROLLBACKING};
            case COMMITTING:
                return new BranchTransactionStatus[]{COMMITTED, COMMIT_FAILED};
            case ROLLBACKING:
                return new BranchTransactionStatus[]{ROLLBACKED, ROLLBACK_FAILED};
            case COMMIT_FAILED:
                return new BranchTransactionStatus[]{COMMITTING, ROLLBACKING};
            case ROLLBACK_FAILED:
                return new BranchTransactionStatus[]{ROLLBACKING};
            default:
                return new BranchTransactionStatus[0]; // 终态
        }
    }

    /**
     * 判断是否可以转换到目标状态
     */
    public boolean canTransitionTo(BranchTransactionStatus target) {
        BranchTransactionStatus[] possibleStatuses = getNextPossibleStatuses();
        for (BranchTransactionStatus status : possibleStatuses) {
            if (status == target) {
                return true;
            }
        }
        return false;
    }

    // getters
    public String getDisplayName() {
        return displayName;
    }

    public String getDescription() {
        return description;
    }

    @Override
    public String toString() {
        return displayName;
    }
}
