package com.tipray.transaction.core.util;

import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.digest.HMac;
import cn.hutool.crypto.digest.HmacAlgorithm;

import java.nio.charset.StandardCharsets;
import java.util.Map;
import java.util.TreeMap;

/**
 * 分布式事务安全工具类
 * 提供签名生成等安全相关的工具方法
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-01-20
 */
public class TransactionSecurityUtils {

    /**
     * 固定的安全密钥（DLP和云服务约定）
     * 注意：这是写死的，不从配置文件读取，确保DLP和云服务一致
     */
    public static final String SECRET_KEY = "tipray-transaction-security-key-2025";

    /**
     * 签名算法
     */
    public static final String SIGNATURE_ALGORITHM = "HmacSHA256";

    /**
     * 时间戳容忍度（5分钟）
     */
    public static final long TIMESTAMP_TOLERANCE = 5 * 60 * 1000L;

    /**
     * 请求头常量
     */
    public static final String HEADER_TRANSACTION_ID = "X-Tipray-Transaction-Id";
    public static final String HEADER_BRANCH_ID = "X-Tipray-Branch-Id";
    public static final String HEADER_OPERATION = "X-Tipray-Operation";
    public static final String HEADER_TIMESTAMP = "X-Tipray-Timestamp";
    public static final String HEADER_SIGNATURE = "X-Tipray-Signature";
    public static final String HEADER_NONCE = "X-Tipray-Nonce";

    /**
     * 操作类型常量
     */
    public static final String OPERATION_ROLLBACK = "rollback";
    public static final String OPERATION_COMMIT = "commit";

    /**
     * 生成请求签名
     *
     * @param transactionId 事务ID
     * @param branchId      分支ID
     * @param operation     操作类型
     * @param timestamp     时间戳
     * @param nonce         随机数
     * @param requestBody   请求体
     * @return 签名
     */
    public static String generateSignature(String transactionId, Long branchId, String operation,
                                           long timestamp, String nonce, String requestBody) {
        // 构建签名原文
        Map<String, Object> params = new TreeMap<>();
        params.put("transactionId", StrUtil.nullToEmpty(transactionId));
        params.put("branchId", branchId);
        params.put("operation", StrUtil.nullToEmpty(operation));
        params.put("timestamp", String.valueOf(timestamp));
        params.put("nonce", StrUtil.nullToEmpty(nonce));

        StringBuilder sb = new StringBuilder();
        for (Map.Entry<String, Object> entry : params.entrySet()) {
            sb.append(entry.getKey()).append("=").append(entry.getValue()).append("&");
        }

        // 添加请求体
        if (StrUtil.isNotBlank(requestBody)) {
            sb.append("body=").append(requestBody);
        } else {
            // 移除最后的&符号
            if (sb.length() > 0 && sb.charAt(sb.length() - 1) == '&') {
                sb.deleteCharAt(sb.length() - 1);
            }
        }

        String signatureData = sb.toString();

        // 生成HMAC签名
        HMac hMac = new HMac(HmacAlgorithm.HmacSHA256, SECRET_KEY.getBytes(StandardCharsets.UTF_8));
        return hMac.digestHex(signatureData);
    }

    /**
     * 生成随机数
     */
    public static String generateNonce() {
        return String.valueOf(System.currentTimeMillis()) + "_" +
                String.valueOf((int) (Math.random() * 100000));
    }

    /**
     * 验证时间戳是否在容忍范围内
     *
     * @param timestamp 请求时间戳
     * @return 是否有效
     */
    public static boolean isTimestampValid(long timestamp) {
        long currentTime = System.currentTimeMillis();
        return Math.abs(currentTime - timestamp) <= TIMESTAMP_TOLERANCE;
    }

    /**
     * 验证签名
     *
     * @param transactionId     事务ID
     * @param branchId          分支ID
     * @param operation         操作类型
     * @param timestamp         时间戳
     * @param nonce             随机数
     * @param requestBody       请求体
     * @param expectedSignature 期望的签名
     * @return 验证结果
     */
    public static boolean verifySignature(String transactionId, Long branchId, String operation,
                                          long timestamp, String nonce, String requestBody,
                                          String expectedSignature) {
        if (StrUtil.isBlank(expectedSignature)) {
            return false;
        }

        String actualSignature = generateSignature(transactionId, branchId, operation,
                timestamp, nonce, requestBody);

        return expectedSignature.equals(actualSignature);
    }

    /**
     * 验证操作类型是否有效
     *
     * @param operation 操作类型
     * @return 是否有效
     */
    public static boolean isValidOperation(String operation) {
        return OPERATION_ROLLBACK.equals(operation) || OPERATION_COMMIT.equals(operation);
    }

    /**
     * 构建请求唯一键（用于防重复）
     *
     * @param transactionId 事务ID
     * @param branchId      分支ID
     * @param operation     操作类型
     * @param nonce         随机数
     * @return 唯一键
     */
    public static String buildRequestKey(String transactionId, Long branchId, String operation, String nonce) {
        return transactionId + ":" + branchId + ":" + operation + ":" + nonce;
    }
}
