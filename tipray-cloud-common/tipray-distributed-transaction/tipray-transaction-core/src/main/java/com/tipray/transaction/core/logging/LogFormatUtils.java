package com.tipray.transaction.core.logging;

import com.tipray.transaction.core.context.TransactionContextHolder;
import com.tipray.transaction.core.domain.transaction.TransactionContext;
import lombok.experimental.UtilityClass;

import java.time.Duration;
import java.time.LocalDateTime;

/**
 * 分布式事务日志格式工具类
 * 提供统一的日志格式化方法，让各个类直接使用slf4j Logger但遵循统一格式
 *
 * <AUTHOR>
 * @version 2.0
 * @since 2025-01-29
 */
@UtilityClass
public class LogFormatUtils {

    /**
     * 获取当前事务ID
     */
    public static String getTxId() {
        TransactionContext context = TransactionContextHolder.getCurrentContext();
        return context != null ? context.getTransactionId() : "----";
    }

    /**
     * 获取当前分支ID
     */
    public static String getBranchId() {
        TransactionContext context = TransactionContextHolder.getCurrentContext();
        return context != null && context.getBranchId() != null ? 
               context.getBranchId().toString() : "----";
    }

    /**
     * 格式化上下文信息为键值对格式
     */
    public static String formatContext(Object... params) {
        if (params == null || params.length == 0) {
            return "";
        }
        
        StringBuilder sb = new StringBuilder("[");
        for (int i = 0; i < params.length; i += 2) {
            if (i > 0) sb.append(", ");
            if (i + 1 < params.length) {
                sb.append(params[i]).append("=").append(params[i + 1]);
            } else {
                sb.append(params[i]);
            }
        }
        sb.append("]");
        return sb.toString();
    }

    /**
     * 格式化耗时信息
     */
    public static String formatDuration(long startTime) {
        return (System.currentTimeMillis() - startTime) + "ms";
    }

    /**
     * 格式化耗时信息（LocalDateTime）
     */
    public static String formatDuration(LocalDateTime startTime) {
        return Duration.between(startTime, LocalDateTime.now()).toMillis() + "ms";
    }

    /**
     * 构建事务开始日志消息
     */
    public static String buildBeginMessage(String mode, String method) {
        return String.format("[%s|%s] [BEGIN] - 事务开始 %s", 
                getTxId(), getBranchId(), 
                formatContext("mode", mode, "method", method));
    }

    /**
     * 构建事务提交日志消息
     */
    public static String buildCommitMessage(int branchCount, long startTime) {
        return String.format("[%s|%s] [COMMIT] - 事务提交成功 %s", 
                getTxId(), getBranchId(), 
                formatContext("branches", branchCount, "total", formatDuration(startTime)));
    }

    /**
     * 构建事务回滚日志消息
     */
    public static String buildRollbackMessage(String reason, Exception cause) {
        return String.format("[%s|%s] [ROLLBACK] - 事务回滚 %s", 
                getTxId(), getBranchId(), 
                formatContext("reason", reason, "cause", cause != null ? cause.getClass().getSimpleName() : "unknown"));
    }

    /**
     * 构建分支注册日志消息
     */
    public static String buildRegisterMessage(String service, String method) {
        return String.format("[%s|%s] [REGISTER] - 分支注册 %s", 
                getTxId(), getBranchId(), 
                formatContext("service", service, "method", method));
    }

    /**
     * 构建状态转换日志消息
     */
    public static String buildStateMessage(Object fromStatus, Object toStatus, String event, long startTime) {
        return String.format("[%s|%s] [STATE] - %s -> %s %s", 
                getTxId(), getBranchId(), fromStatus, toStatus,
                formatContext("event", event, "cost", formatDuration(startTime)));
    }

    /**
     * 构建错误日志消息
     */
    public static String buildErrorMessage(String action, Exception e) {
        return String.format("[%s|%s] [ERROR] - %s %s", 
                getTxId(), getBranchId(), action + "失败", 
                formatContext("error", e.getClass().getSimpleName(), "msg", e.getMessage()));
    }

    /**
     * 构建性能日志消息
     */
    public static String buildPerfMessage(String phase, long startTime, Object... additionalParams) {
        Object[] params = new Object[additionalParams.length + 2];
        System.arraycopy(additionalParams, 0, params, 0, additionalParams.length);
        params[additionalParams.length] = "cost";
        params[additionalParams.length + 1] = formatDuration(startTime);
        
        return String.format("[%s|%s] [PERF] - %s性能 %s", 
                getTxId(), getBranchId(), phase, formatContext(params));
    }

    /**
     * 构建调试日志消息
     */
    public static String buildDebugMessage(String action, Object... params) {
        return String.format("[%s|%s] [DEBUG] - %s %s", 
                getTxId(), getBranchId(), action, formatContext(params));
    }

    /**
     * 构建警告日志消息
     */
    public static String buildWarnMessage(String action, String reason) {
        return String.format("[%s|%s] [WARN] - %s %s", 
                getTxId(), getBranchId(), action, formatContext("reason", reason));
    }

    /**
     * 构建执行日志消息
     */
    public static String buildExecMessage(String action, Object... params) {
        return String.format("[%s|%s] [EXEC] - %s %s", 
                getTxId(), getBranchId(), action, formatContext(params));
    }
} 