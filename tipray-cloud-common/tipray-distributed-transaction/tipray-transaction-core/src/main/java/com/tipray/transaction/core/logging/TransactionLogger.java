package com.tipray.transaction.core.logging;

import com.tipray.transaction.core.enums.TransactionMode;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.time.LocalDateTime;

/**
 * 分布式事务统一日志工具
 * 提供统一的日志格式和输出规范
 *
 * 静态工具类，无需注入即可使用
 *
 * <AUTHOR>
 * @version 2.0
 * @since 2025-01-29
 */
public final class TransactionLogger {

    private static final Logger log = LoggerFactory.getLogger("TIPRAY-TRANSACTION");

    // 私有构造函数，防止实例化
    private TransactionLogger() {
        throw new UnsupportedOperationException("TransactionLogger is a utility class and cannot be instantiated");
    }

    // 日志格式模板: [TX-ID] [BRANCH-ID] [PHASE] - MESSAGE [CONTEXT]
    private static final String LOG_TEMPLATE = "[{}] [{}] [{}] - {} {}";

    // ==================== 事务生命周期日志 ====================

    /**
     * 事务开始日志
     *
     * @param txId   事务ID
     * @param mode   事务模式
     * @param method 业务方法
     */
    public static void logTransactionBegin(String txId, TransactionMode mode, String method) {
        String context = buildContext("mode", mode, "method", method);
        log.info(LOG_TEMPLATE, txId, "----", "BEGIN", "事务开始", context);
    }

    /**
     * 事务执行日志
     *
     * @param txId 事务ID
     */
    public static void logTransactionExecute(String txId) {
        log.info(LOG_TEMPLATE, txId, "----", "EXEC", "开始执行业务逻辑", "");
    }

    /**
     * 事务提交日志
     *
     * @param txId          事务ID
     * @param branchCount   分支数量
     * @param totalDuration 总耗时
     */
    public static void logTransactionCommit(String txId, int branchCount, long totalDuration) {
        String context = buildContext("branches", branchCount, "total", totalDuration + "ms");
        log.info(LOG_TEMPLATE, txId, "----", "COMMIT", "事务提交成功", context);
    }

    /**
     * 事务提交日志（自动计算耗时 - 毫秒时间戳）
     *
     * @param txId        事务ID
     * @param branchCount 分支数量
     * @param startTimeMillis 开始时间（毫秒时间戳）
     */
    public static void logTransactionCommitWithStartTime(String txId, int branchCount, long startTimeMillis) {
        long totalDuration = System.currentTimeMillis() - startTimeMillis;
        logTransactionCommit(txId, branchCount, totalDuration);
    }

    /**
     * 事务提交日志（自动计算耗时 - LocalDateTime）
     *
     * @param txId        事务ID
     * @param branchCount 分支数量
     * @param startTime   开始时间（LocalDateTime）
     */
    public static void logTransactionCommitWithStartTime(String txId, int branchCount, LocalDateTime startTime) {
        long totalDuration = Duration.between(startTime, LocalDateTime.now()).toMillis();
        logTransactionCommit(txId, branchCount, totalDuration);
    }

    /**
     * 事务回滚日志
     *
     * @param txId   事务ID
     * @param reason 回滚原因
     * @param cause  异常原因
     */
    public static void logTransactionRollback(String txId, String reason, Exception cause) {
        String context = buildContext("reason", reason, "cause", 
                cause != null ? cause.getClass().getSimpleName() : "unknown");
        log.warn(LOG_TEMPLATE, txId, "----", "ROLLBACK", "事务回滚", context);
    }

    // ==================== 分支事务日志 ====================

    /**
     * 分支注册日志
     *
     * @param txId     事务ID
     * @param branchId 分支ID
     * @param service  服务名
     * @param method   方法名
     */
    public static void logBranchRegister(String txId, Long branchId, String service, String method) {
        String branchIdStr = branchId != null ? branchId.toString() : "----";
        String context = buildContext("service", service, "method", method);
        log.info(LOG_TEMPLATE, txId, branchIdStr, "REGISTER", "分支注册", context);
    }

    /**
     * 分支执行日志
     *
     * @param txId     事务ID
     * @param branchId 分支ID
     * @param action   执行动作
     * @param params   参数信息
     */
    public static void logBranchExecute(String txId, Long branchId, String action, Object... params) {
        String branchIdStr = branchId != null ? branchId.toString() : "----";
        String context = buildContext(params);
        log.info(LOG_TEMPLATE, txId, branchIdStr, "EXEC", action, context);
    }

    /**
     * 分支提交日志
     *
     * @param txId     事务ID
     * @param branchId 分支ID
     * @param duration 耗时
     */
    public static void logBranchCommit(String txId, Long branchId, long duration) {
        String branchIdStr = branchId != null ? branchId.toString() : "----";
        String context = buildContext("cost", duration + "ms");
        log.info(LOG_TEMPLATE, txId, branchIdStr, "COMMIT", "分支提交成功", context);
    }

    /**
     * 分支提交日志（自动计算耗时 - 毫秒时间戳）
     *
     * @param txId      事务ID
     * @param branchId  分支ID
     * @param startTimeMillis 开始时间（毫秒时间戳）
     */
    public static void logBranchCommitWithStartTime(String txId, Long branchId, long startTimeMillis) {
        long duration = System.currentTimeMillis() - startTimeMillis;
        logBranchCommit(txId, branchId, duration);
    }

    /**
     * 分支提交日志（自动计算耗时 - LocalDateTime）
     *
     * @param txId      事务ID
     * @param branchId  分支ID
     * @param startTime 开始时间（LocalDateTime）
     */
    public static void logBranchCommitWithStartTime(String txId, Long branchId, LocalDateTime startTime) {
        long duration = Duration.between(startTime, LocalDateTime.now()).toMillis();
        logBranchCommit(txId, branchId, duration);
    }

    /**
     * 分支回滚日志
     *
     * @param txId     事务ID
     * @param branchId 分支ID
     * @param duration 耗时
     */
    public static void logBranchRollback(String txId, Long branchId, long duration) {
        String branchIdStr = branchId != null ? branchId.toString() : "----";
        String context = buildContext("cost", duration + "ms");
        log.info(LOG_TEMPLATE, txId, branchIdStr, "ROLLBACK", "分支回滚成功", context);
    }

    /**
     * 分支回滚日志（自动计算耗时 - 毫秒时间戳）
     *
     * @param txId      事务ID
     * @param branchId  分支ID
     * @param startTimeMillis 开始时间（毫秒时间戳）
     */
    public static void logBranchRollbackWithStartTime(String txId, Long branchId, long startTimeMillis) {
        long duration = System.currentTimeMillis() - startTimeMillis;
        logBranchRollback(txId, branchId, duration);
    }

    /**
     * 分支回滚日志（自动计算耗时 - LocalDateTime）
     *
     * @param txId      事务ID
     * @param branchId  分支ID
     * @param startTime 开始时间（LocalDateTime）
     */
    public static void logBranchRollbackWithStartTime(String txId, Long branchId, LocalDateTime startTime) {
        long duration = Duration.between(startTime, LocalDateTime.now()).toMillis();
        logBranchRollback(txId, branchId, duration);
    }

    // ==================== 状态转换日志 ====================

    /**
     * 状态转换日志
     *
     * @param txId       事务ID
     * @param branchId   分支ID（可为null）
     * @param fromStatus 原状态
     * @param toStatus   目标状态
     * @param event      触发事件
     * @param duration   耗时
     */
    public static void logStateTransition(String txId, Long branchId,
                                   Object fromStatus, Object toStatus,
                                   String event, long duration) {
        String branchIdStr = branchId != null ? branchId.toString() : "----";
        String message = String.format("%s -> %s", fromStatus, toStatus);
        String context = buildContext("event", event, "cost", duration + "ms");
        log.debug(LOG_TEMPLATE, txId, branchIdStr, "STATE", message, context);
    }

    // ==================== 错误日志 ====================

    /**
     * 错误日志
     *
     * @param txId     事务ID
     * @param branchId 分支ID（可为null）
     * @param action   执行动作
     * @param e        异常
     */
    public static void logError(String txId, Long branchId, String action, Exception e) {
        String branchIdStr = branchId != null ? branchId.toString() : "----";
        String context = buildContext("error", e.getClass().getSimpleName(),
                "msg", truncateMessage(e.getMessage(), 100));
        log.error(LOG_TEMPLATE, txId, branchIdStr, "ERROR", action + "失败", context);
    }

    /**
     * 警告日志
     *
     * @param txId     事务ID
     * @param branchId 分支ID（可为null）
     * @param action   执行动作
     * @param message  警告信息
     */
    public static void logWarn(String txId, Long branchId, String action, String message) {
        String branchIdStr = branchId != null ? branchId.toString() : "----";
        String context = buildContext("msg", truncateMessage(message, 100));
        log.warn(LOG_TEMPLATE, txId, branchIdStr, "WARN", action, context);
    }

    // ==================== 性能日志 ====================

    /**
     * 性能日志
     *
     * @param txId     事务ID
     * @param phase    阶段
     * @param duration 耗时
     * @param metrics  指标信息
     */
    public static void logPerformance(String txId, String phase, long duration, Object... metrics) {
        Object[] allMetrics = new Object[metrics.length + 2];
        System.arraycopy(metrics, 0, allMetrics, 0, metrics.length);
        allMetrics[metrics.length] = "cost";
        allMetrics[metrics.length + 1] = duration + "ms";
        String context = buildContext(allMetrics);
        log.info(LOG_TEMPLATE, txId, "----", "PERF", phase + "性能", context);
    }

    /**
     * 性能日志（自动计算耗时 - 毫秒时间戳）
     *
     * @param txId      事务ID
     * @param phase     阶段
     * @param startTimeMillis 开始时间（毫秒时间戳）
     * @param metrics   指标信息
     */
    public static void logPerformanceWithStartTime(String txId, String phase, long startTimeMillis, Object... metrics) {
        long duration = System.currentTimeMillis() - startTimeMillis;
        logPerformance(txId, phase, duration, metrics);
    }

    /**
     * 性能日志（自动计算耗时 - LocalDateTime）
     *
     * @param txId      事务ID
     * @param phase     阶段
     * @param startTime 开始时间（LocalDateTime）
     * @param metrics   指标信息
     */
    public static void logPerformanceWithStartTime(String txId, String phase, LocalDateTime startTime, Object... metrics) {
        long duration = Duration.between(startTime, LocalDateTime.now()).toMillis();
        logPerformance(txId, phase, duration, metrics);
    }

    /**
     * 性能日志（自动计算耗时 - LocalDateTime）
     *
     * @param txId      事务ID
     * @param phase     阶段
     * @param startTime 开始时间（LocalDateTime）
     * @param metrics   指标信息
     */
    public static void logPerformance(String txId, String phase, LocalDateTime startTime, Object... metrics) {
        long duration = Duration.between(startTime, LocalDateTime.now()).toMillis();
        logPerformance(txId, phase, duration, metrics);
    }

    // ==================== 调试日志 ====================

    /**
     * 调试日志
     *
     * @param txId     事务ID
     * @param branchId 分支ID（可为null）
     * @param action   执行动作
     * @param params   参数信息
     */
    public static void logDebug(String txId, Long branchId, String action, Object... params) {
        if (!log.isDebugEnabled()) {
            return;
        }
        String branchIdStr = branchId != null ? branchId.toString() : "----";
        String context = buildContext(params);
        log.debug(LOG_TEMPLATE, txId, branchIdStr, "DEBUG", action, context);
    }

    // ==================== 工具方法 ====================

    /**
     * 构建上下文信息
     */
    private static String buildContext(Object... params) {
        if (params == null || params.length == 0) {
            return "";
        }

        StringBuilder sb = new StringBuilder("[");
        for (int i = 0; i < params.length; i += 2) {
            if (i > 0) sb.append(", ");
            if (i + 1 < params.length) {
                sb.append(params[i]).append("=").append(params[i + 1]);
            }
        }
        sb.append("]");
        return sb.toString();
    }

    /**
     * 截断消息长度
     */
    private static String truncateMessage(String message, int maxLength) {
        if (message == null) {
            return "null";
        }
        if (message.length() <= maxLength) {
            return message;
        }
        return message.substring(0, maxLength) + "...";
    }
}
