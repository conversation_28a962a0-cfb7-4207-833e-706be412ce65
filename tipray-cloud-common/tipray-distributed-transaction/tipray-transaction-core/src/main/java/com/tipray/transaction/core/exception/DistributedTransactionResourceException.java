package com.tipray.transaction.core.exception;

/**
 * Tipray分布式事务资源异常
 * 资源获取、释放或操作失败时抛出的异常
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-06-20
 */
public class DistributedTransactionResourceException extends DistributedTransactionException {

    /**
     * 序列化版本号
     */
    private static final long serialVersionUID = 1L;

    /**
     * 资源异常类型
     */
    private ResourceExceptionType resourceExceptionType;

    /**
     * 资源ID
     */
    private String resourceId;

    /**
     * 资源类型
     */
    private String resourceType;

    /**
     * 资源URL
     */
    private String resourceUrl;

    /**
     * 默认构造函数
     */
    public DistributedTransactionResourceException() {
        super();
        this.resourceExceptionType = ResourceExceptionType.UNKNOWN;
    }

    /**
     * 构造函数
     *
     * @param message 错误信息
     */
    public DistributedTransactionResourceException(String message) {
        super(message);
        this.resourceExceptionType = ResourceExceptionType.UNKNOWN;
    }

    /**
     * 构造函数
     *
     * @param message 错误信息
     * @param cause   原因异常
     */
    public DistributedTransactionResourceException(String message, Throwable cause) {
        super(message, cause);
        this.resourceExceptionType = ResourceExceptionType.UNKNOWN;
    }

    /**
     * 构造函数
     *
     * @param message               错误信息
     * @param resourceExceptionType 资源异常类型
     * @param resourceId            资源ID
     * @param resourceType          资源类型
     */
    public DistributedTransactionResourceException(String message, ResourceExceptionType resourceExceptionType,
                                                   String resourceId, String resourceType) {
        super("RESOURCE_ERROR", message);
        this.resourceExceptionType = resourceExceptionType;
        this.resourceId = resourceId;
        this.resourceType = resourceType;
    }

    /**
     * 构造函数
     *
     * @param message               错误信息
     * @param cause                 原因异常
     * @param resourceExceptionType 资源异常类型
     * @param resourceId            资源ID
     * @param resourceType          资源类型
     * @param resourceUrl           资源URL
     */
    public DistributedTransactionResourceException(String message, Throwable cause, ResourceExceptionType resourceExceptionType,
                                                   String resourceId, String resourceType, String resourceUrl) {
        super("RESOURCE_ERROR", message, cause);
        this.resourceExceptionType = resourceExceptionType;
        this.resourceId = resourceId;
        this.resourceType = resourceType;
        this.resourceUrl = resourceUrl;
    }

    /**
     * 构造函数
     *
     * @param message               错误信息
     * @param cause                 原因异常
     * @param transactionId         事务ID
     * @param groupId               事务组ID
     * @param resourceExceptionType 资源异常类型
     * @param resourceId            资源ID
     * @param resourceType          资源类型
     * @param resourceUrl           资源URL
     */
    public DistributedTransactionResourceException(String message, Throwable cause, String transactionId, String groupId,
                                                   ResourceExceptionType resourceExceptionType, String resourceId, String resourceType, String resourceUrl) {
        super("RESOURCE_ERROR", message, cause, transactionId, groupId);
        this.resourceExceptionType = resourceExceptionType;
        this.resourceId = resourceId;
        this.resourceType = resourceType;
        this.resourceUrl = resourceUrl;
    }

    /**
     * 获取资源异常类型
     *
     * @return 资源异常类型
     */
    public ResourceExceptionType getResourceExceptionType() {
        return resourceExceptionType;
    }

    /**
     * 设置资源异常类型
     *
     * @param resourceExceptionType 资源异常类型
     */
    public void setResourceExceptionType(ResourceExceptionType resourceExceptionType) {
        this.resourceExceptionType = resourceExceptionType;
    }

    /**
     * 获取资源ID
     *
     * @return 资源ID
     */
    public String getResourceId() {
        return resourceId;
    }

    /**
     * 设置资源ID
     *
     * @param resourceId 资源ID
     */
    public void setResourceId(String resourceId) {
        this.resourceId = resourceId;
    }

    /**
     * 获取资源类型
     *
     * @return 资源类型
     */
    public String getResourceType() {
        return resourceType;
    }

    /**
     * 设置资源类型
     *
     * @param resourceType 资源类型
     */
    public void setResourceType(String resourceType) {
        this.resourceType = resourceType;
    }

    /**
     * 获取资源URL
     *
     * @return 资源URL
     */
    public String getResourceUrl() {
        return resourceUrl;
    }

    /**
     * 设置资源URL
     *
     * @param resourceUrl 资源URL
     */
    public void setResourceUrl(String resourceUrl) {
        this.resourceUrl = resourceUrl;
    }

    /**
     * 判断是否为可重试的异常
     *
     * @return true表示可重试
     */
    public boolean isRetryable() {
        return resourceExceptionType == ResourceExceptionType.CONNECTION_POOL_EXHAUSTED ||
                resourceExceptionType == ResourceExceptionType.RESOURCE_TEMPORARILY_UNAVAILABLE ||
                resourceExceptionType == ResourceExceptionType.RESOURCE_BUSY;
    }

    /**
     * 判断是否为致命资源异常
     *
     * @return true表示致命异常
     */
    public boolean isFatal() {
        return resourceExceptionType == ResourceExceptionType.RESOURCE_NOT_FOUND ||
                resourceExceptionType == ResourceExceptionType.RESOURCE_ACCESS_DENIED ||
                resourceExceptionType == ResourceExceptionType.RESOURCE_CORRUPTED;
    }

    @Override
    public String getFullMessage() {
        StringBuilder sb = new StringBuilder();
        sb.append(super.getFullMessage());

        if (resourceId != null) {
            sb.append(" [资源ID: ").append(resourceId).append("]");
        }

        if (resourceType != null) {
            sb.append(" [资源类型: ").append(resourceType).append("]");
        }

        if (resourceUrl != null) {
            sb.append(" [资源URL: ").append(resourceUrl).append("]");
        }

        if (resourceExceptionType != null) {
            sb.append(" [资源异常类型: ").append(resourceExceptionType.getDescription()).append("]");
        }

        return sb.toString();
    }

    /**
     * 资源异常类型枚举
     */
    public enum ResourceExceptionType {
        /**
         * 资源未找到
         */
        RESOURCE_NOT_FOUND("RESOURCE_NOT_FOUND", "资源未找到"),

        /**
         * 资源访问被拒绝
         */
        RESOURCE_ACCESS_DENIED("RESOURCE_ACCESS_DENIED", "资源访问被拒绝"),

        /**
         * 资源已损坏
         */
        RESOURCE_CORRUPTED("RESOURCE_CORRUPTED", "资源已损坏"),

        /**
         * 连接池耗尽
         */
        CONNECTION_POOL_EXHAUSTED("CONNECTION_POOL_EXHAUSTED", "连接池耗尽"),

        /**
         * 资源暂时不可用
         */
        RESOURCE_TEMPORARILY_UNAVAILABLE("RESOURCE_TEMPORARILY_UNAVAILABLE", "资源暂时不可用"),

        /**
         * 资源繁忙
         */
        RESOURCE_BUSY("RESOURCE_BUSY", "资源繁忙"),

        /**
         * 资源锁定失败
         */
        RESOURCE_LOCK_FAILED("RESOURCE_LOCK_FAILED", "资源锁定失败"),

        /**
         * 资源释放失败
         */
        RESOURCE_RELEASE_FAILED("RESOURCE_RELEASE_FAILED", "资源释放失败"),

        /**
         * 数据库连接失败
         */
        DATABASE_CONNECTION_FAILED("DATABASE_CONNECTION_FAILED", "数据库连接失败"),

        /**
         * 其他未知资源异常
         */
        UNKNOWN("UNKNOWN", "未知资源异常");

        private final String code;
        private final String description;

        ResourceExceptionType(String code, String description) {
            this.code = code;
            this.description = description;
        }

        public String getCode() {
            return code;
        }

        public String getDescription() {
            return description;
        }
    }
}
