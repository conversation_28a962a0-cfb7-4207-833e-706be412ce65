package com.tipray.transaction.at.http;

import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.json.JSONUtil;
import com.tipray.transaction.at.context.AtConfigContext;
import com.tipray.transaction.core.config.ConfigurationResolver;
import com.tipray.transaction.core.context.TransactionContextHolder;
import com.tipray.transaction.core.util.LoggingUtils;
import com.tipray.transaction.core.util.TransactionSecurityUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Map;

/**
 * AT模式HTTP客户端
 * 负责云服务的HTTP调用，自动添加事务上下文信息
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-05-20
 */
@Slf4j
public class AtHttpClient {

    /**
     * AT事务头常量 - 与安全验证保持一致
     */
    public static final String HEADER_GLOBAL_TX_ID = TransactionSecurityUtils.HEADER_TRANSACTION_ID;
    public static final String HEADER_BRANCH_TX_ID = TransactionSecurityUtils.HEADER_BRANCH_ID;
    public static final String HEADER_TX_TYPE = "X-Transaction-Type";
    public static final String HEADER_CONTENT_TYPE = "Content-Type";
    public static final String APPLICATION_JSON = "application/json";

    @Autowired
    private ConfigurationResolver configResolver;

    /**
     * 发送POST请求
     * 自动从AtConfigContext获取超时配置
     *
     * @param url  请求URL
     * @param body 请求体
     * @return HTTP响应
     */
    public HttpResponse post(String url, Object body) {
        AtConfigContext context = AtConfigContext.getContext();
        String serviceName = extractServiceNameFromUrl(url);

        if (context != null) {
            // 使用配置解析器解析超时时间，优先级：注解 > 配置 > 默认值
            long connectTimeout = configResolver.resolveConnectTimeout(
                    context.getConnectTimeout(), serviceName);
            long readTimeout = configResolver.resolveReadTimeout(
                    context.getReadTimeout(), serviceName);
            return post(url, body, connectTimeout, readTimeout);
        } else {
            // 没有上下文时，使用配置解析器获取默认配置
            long defaultTimeout = configResolver.resolveTimeout(0, "at.default-timeout", 30000);
            return post(url, body, (int) defaultTimeout);
        }
    }

    /**
     * POST请求（支持超时配置）
     *
     * @param url            请求URL
     * @param requestBody    请求体
     * @param connectTimeout 连接超时时间（毫秒）
     * @param readTimeout    读取超时时间（毫秒）
     * @return HTTP响应
     */
    public HttpResponse post(String url, Object requestBody, long connectTimeout, long readTimeout) {
        return executeRequest("POST", url, requestBody, connectTimeout, readTimeout);
    }

    /**
     * 执行HTTP请求（支持超时配置）
     *
     * @param method         HTTP方法
     * @param url            请求URL
     * @param requestBody    请求体
     * @param connectTimeout 连接超时时间（毫秒）
     * @param readTimeout    读取超时时间（毫秒）
     * @return HTTP响应
     */
    private HttpResponse executeRequest(String method, String url, Object requestBody,
                                        long connectTimeout, long readTimeout) {
        // 获取事务上下文信息用于日志
        com.tipray.transaction.core.domain.transaction.TransactionContext context = TransactionContextHolder.getCurrentContext();
        String txId = context != null ? context.getTransactionId() : "UNKNOWN";
        String branchId = context != null ? String.valueOf(context.getBranchId()) : "UNKNOWN";

        log.debug("[{}|{}] [DEBUG] - AT HTTP请求 {}",
                LoggingUtils.getTxId(txId), LoggingUtils.getBranchId(branchId),
                LoggingUtils.formatContext("method", method, "url", url));

        try {
            HttpRequest request;
            switch (method.toUpperCase()) {
                case "POST":
                    request = HttpRequest.post(url);
                    break;
                case "GET":
                    request = HttpRequest.get(url);
                    break;
                case "PUT":
                    request = HttpRequest.put(url);
                    break;
                case "DELETE":
                    request = HttpRequest.delete(url);
                    break;
                default:
                    throw new IllegalArgumentException("不支持的HTTP方法: " + method);
            }

            // 设置超时时间
            request.setConnectionTimeout((int) connectTimeout)
                    .setReadTimeout((int) readTimeout)
                    .header(HEADER_CONTENT_TYPE, APPLICATION_JSON);

            // 添加事务上下文头
            addTransactionHeaders(request);

            // 设置请求体
            if (requestBody != null && !"GET".equalsIgnoreCase(method)) {
                String jsonBody = JSONUtil.toJsonStr(requestBody);
                request.body(jsonBody);
                log.debug("[{}|{}] [DEBUG] - AT HTTP请求体 {}",
                        LoggingUtils.getTxId(txId), LoggingUtils.getBranchId(branchId),
                        LoggingUtils.formatContext("body", jsonBody));
            }

            // 执行请求
            HttpResponse response = request.execute();

            // 自动检查响应状态和业务结果
            checkResponseAndThrowException(response, url);

            log.debug("[{}|{}] [DEBUG] - AT HTTP响应 {}",
                    LoggingUtils.getTxId(txId), LoggingUtils.getBranchId(branchId),
                    LoggingUtils.formatContext("method", method, "status", response.getStatus()));

            return response;

        } catch (Exception e) {
            log.error("[{}|{}] [ERROR] - AT HTTP请求失败 {}",
                    LoggingUtils.getTxId(txId), LoggingUtils.getBranchId(branchId),
                    LoggingUtils.formatException(e), e);
            throw new RuntimeException("AT模式HTTP请求失败: " + e.getMessage(), e);
        }
    }

    /**
     * 发送POST请求
     *
     * @param url     请求URL
     * @param body    请求体
     * @param timeout 超时时间（毫秒）
     * @return HTTP响应
     */
    public HttpResponse post(String url, Object body, int timeout) {
        // 获取事务上下文信息用于日志
        com.tipray.transaction.core.domain.transaction.TransactionContext context = TransactionContextHolder.getCurrentContext();
        String txId = context != null ? context.getTransactionId() : "UNKNOWN";
        String branchId = context != null ? context.getBranchId() + "" : "UNKNOWN";

        log.debug("[{}|{}] [DEBUG] - AT POST请求 {}",
                LoggingUtils.getTxId(txId), LoggingUtils.getBranchId(branchId),
                LoggingUtils.formatContext("url", url));

        try {
            HttpRequest request = HttpRequest.post(url)
                    .header(HEADER_CONTENT_TYPE, APPLICATION_JSON)
                    .timeout(timeout);

            // 添加事务上下文头
            addTransactionHeaders(request);

            // 设置请求体
            if (body != null) {
                String jsonBody = JSONUtil.toJsonStr(body);
                request.body(jsonBody);
                log.debug("[{}|{}] [DEBUG] - AT POST请求体 {}",
                        LoggingUtils.getTxId(txId), LoggingUtils.getBranchId(branchId),
                        LoggingUtils.formatContext("body", jsonBody));
            }

            // 执行请求
            HttpResponse response = request.execute();

            log.debug("[{}|{}] [DEBUG] - AT POST响应 {}",
                    LoggingUtils.getTxId(txId), LoggingUtils.getBranchId(branchId),
                    LoggingUtils.formatContext("status", response.getStatus()));

            // 自动检查响应状态和业务结果
            checkResponseAndThrowException(response, url);

            return response;

        } catch (Exception e) {
            log.error("[{}|{}] [ERROR] - AT POST请求失败 {}",
                    LoggingUtils.getTxId(txId), LoggingUtils.getBranchId(branchId),
                    LoggingUtils.formatException(e), e);
            throw new RuntimeException("AT模式HTTP请求失败: " + e.getMessage(), e);
        }
    }

    /**
     * 发送GET请求
     *
     * @param url 请求URL
     * @return HTTP响应
     */
    public HttpResponse get(String url) {
        return get(url, 30000);
    }

    /**
     * 发送GET请求
     *
     * @param url     请求URL
     * @param timeout 超时时间（毫秒）
     * @return HTTP响应
     */
    public HttpResponse get(String url, int timeout) {
        // 获取事务上下文信息用于日志
        com.tipray.transaction.core.domain.transaction.TransactionContext context = TransactionContextHolder.getCurrentContext();
        String txId = context != null ? context.getTransactionId() : "UNKNOWN";
        Long branchId = context != null ? context.getBranchId() : null;

        log.debug("[{}|{}] [DEBUG] - AT GET请求 {}",
                LoggingUtils.getTxId(txId), LoggingUtils.getBranchId(branchId),
                LoggingUtils.formatContext("url", url));

        try {
            HttpRequest request = HttpRequest.get(url)
                    .timeout(timeout);

            // 添加事务上下文头
            addTransactionHeaders(request);

            // 执行请求
            HttpResponse response = request.execute();

            log.debug("[{}|{}] [DEBUG] - AT GET响应 {}",
                    LoggingUtils.getTxId(txId), LoggingUtils.getBranchId(branchId),
                    LoggingUtils.formatContext("status", response.getStatus(), "body", response.body()));

            // 自动检查响应状态和业务结果
            checkResponseAndThrowException(response, url);

            return response;

        } catch (Exception e) {
            log.error("[{}|{}] [ERROR] - AT GET请求失败 {}",
                    LoggingUtils.getTxId(txId), LoggingUtils.getBranchId(branchId),
                    LoggingUtils.formatException(e), e);
            throw new RuntimeException("AT模式HTTP请求失败: " + e.getMessage(), e);
        }
    }

    /**
     * 发送PUT请求
     *
     * @param url  请求URL
     * @param body 请求体
     * @return HTTP响应
     */
    public HttpResponse put(String url, Object body) {
        return put(url, body, 30000);
    }

    /**
     * 发送PUT请求
     *
     * @param url     请求URL
     * @param body    请求体
     * @param timeout 超时时间（毫秒）
     * @return HTTP响应
     */
    public HttpResponse put(String url, Object body, int timeout) {
        // 获取事务上下文信息用于日志
        com.tipray.transaction.core.domain.transaction.TransactionContext context = TransactionContextHolder.getCurrentContext();
        String txId = context != null ? context.getTransactionId() : "UNKNOWN";
        Long branchId = context != null ? context.getBranchId() : null;

        log.debug("[{}|{}] [DEBUG] - AT PUT请求 {}",
                LoggingUtils.getTxId(txId), LoggingUtils.getBranchId(branchId),
                LoggingUtils.formatContext("url", url));

        try {
            HttpRequest request = HttpRequest.put(url)
                    .header(HEADER_CONTENT_TYPE, APPLICATION_JSON)
                    .timeout(timeout);

            // 添加事务上下文头
            addTransactionHeaders(request);

            // 设置请求体
            if (body != null) {
                String jsonBody = JSONUtil.toJsonStr(body);
                request.body(jsonBody);
                log.debug("[{}|{}] [DEBUG] - AT PUT请求体 {}",
                        LoggingUtils.getTxId(txId), LoggingUtils.getBranchId(branchId),
                        LoggingUtils.formatContext("body", jsonBody));
            }

            // 执行请求
            HttpResponse response = request.execute();

            log.debug("[{}|{}] [DEBUG] - AT PUT响应 {}",
                    LoggingUtils.getTxId(txId), LoggingUtils.getBranchId(branchId),
                    LoggingUtils.formatContext("status", response.getStatus(), "body", response.body()));

            // 自动检查响应状态和业务结果
            checkResponseAndThrowException(response, url);

            return response;

        } catch (Exception e) {
            log.error("[{}|{}] [ERROR] - AT PUT请求失败 {}",
                    LoggingUtils.getTxId(txId), LoggingUtils.getBranchId(branchId),
                    LoggingUtils.formatException(e), e);
            throw new RuntimeException("AT模式HTTP请求失败: " + e.getMessage(), e);
        }
    }

    /**
     * 发送DELETE请求
     *
     * @param url 请求URL
     * @return HTTP响应
     */
    public HttpResponse delete(String url) {
        return delete(url, 30000);
    }

    /**
     * 发送DELETE请求
     *
     * @param url     请求URL
     * @param timeout 超时时间（毫秒）
     * @return HTTP响应
     */
    public HttpResponse delete(String url, int timeout) {
        // 获取事务上下文信息用于日志
        com.tipray.transaction.core.domain.transaction.TransactionContext context = TransactionContextHolder.getCurrentContext();
        String txId = context != null ? context.getTransactionId() : "UNKNOWN";
        Long branchId = context != null ? context.getBranchId() : null;

        log.debug("[{}|{}] [DEBUG] - AT DELETE请求 {}",
                LoggingUtils.getTxId(txId), LoggingUtils.getBranchId(branchId),
                LoggingUtils.formatContext("url", url));

        try {
            HttpRequest request = HttpRequest.delete(url)
                    .timeout(timeout);

            // 添加事务上下文头
            addTransactionHeaders(request);

            // 执行请求
            HttpResponse response = request.execute();

            log.debug("[{}|{}] [DEBUG] - AT DELETE响应 {}",
                    LoggingUtils.getTxId(txId), LoggingUtils.getBranchId(branchId),
                    LoggingUtils.formatContext("status", response.getStatus(), "body", response.body()));

            // 自动检查响应状态和业务结果
            checkResponseAndThrowException(response, url);

            return response;

        } catch (Exception e) {
            log.error("[{}|{}] [ERROR] - AT DELETE请求失败 {}",
                    LoggingUtils.getTxId(txId), LoggingUtils.getBranchId(branchId),
                    LoggingUtils.formatException(e), e);
            throw new RuntimeException("AT模式HTTP请求失败: " + e.getMessage(), e);
        }
    }

    /**
     * 添加事务上下文头
     *
     * @param request HTTP请求
     */
    private void addTransactionHeaders(HttpRequest request) {
        com.tipray.transaction.core.domain.transaction.TransactionContext context = TransactionContextHolder.getCurrentContext();
        if (context != null) {
            String currentStepId = context.getBranchId() + "";
            String transactionId = context.getTransactionId();
            request.header(HEADER_GLOBAL_TX_ID, transactionId);
            request.header(HEADER_BRANCH_TX_ID, currentStepId);
            request.header(HEADER_TX_TYPE, "AT");

            // 添加屏障信息头（如果启用屏障）
            if (context.isEnableBarrier()) {
                request.header("X-Barrier-GID", transactionId);
                request.header("X-Barrier-Branch-ID", currentStepId);
                request.header("X-Barrier-Operation", "try"); // 业务请求都是try操作
                request.header("X-Barrier-Mode", "AT");
                request.header("X-Barrier-Timestamp", String.valueOf(System.currentTimeMillis()));
            }

            log.debug("[{}|{}] [DEBUG] - 添加事务上下文头 {}",
                    LoggingUtils.getTxId(transactionId), LoggingUtils.getBranchId(context.getBranchId()),
                    LoggingUtils.formatContext("屏障", context.isEnableBarrier() ? "启用" : "禁用"));
        } else {
            log.debug("[{}|{}] [DEBUG] - 当前无事务上下文，跳过添加事务头",
                    LoggingUtils.getTxId(), LoggingUtils.getBranchId());
        }
    }

    /**
     * 检查响应状态和业务结果，自动抛出异常
     *
     * @param response HTTP响应
     * @param url      请求URL
     */
    private void checkResponseAndThrowException(HttpResponse response, String url) {
        // 1. 检查HTTP状态码
        int status = response.getStatus();
        if (status < 200 || status >= 300) {
            String errorMsg = String.format("云服务HTTP请求失败: url=%s, status=%d, body=%s",
                    url, status, response.body());
            log.error("[{}|{}] [ERROR] - 云服务HTTP请求失败 {}",
                    LoggingUtils.getTxId(), LoggingUtils.getBranchId(),
                    LoggingUtils.formatContext("error", errorMsg));
            throw new RuntimeException(errorMsg);
        }

        // 2. 检查响应体是否为空
        String responseBody = response.body();
        if (responseBody == null || responseBody.trim().isEmpty()) {
            String errorMsg = String.format("云服务返回空响应: url=%s", url);
            log.error("[{}|{}] [ERROR] - 云服务返回空响应 {}",
                    LoggingUtils.getTxId(), LoggingUtils.getBranchId(),
                    LoggingUtils.formatContext("error", errorMsg));
            throw new RuntimeException(errorMsg);
        }

        // 3. 检查业务结果
        try {
            // 尝试解析为JSON并检查success字段
            Map<String, Object> result = JSONUtil.toBean(responseBody, Map.class);

            // 检查success字段
            Object successObj = result.get("success");
            if (successObj != null) {
                boolean success = false;
                if (successObj instanceof Boolean) {
                    success = (Boolean) successObj;
                } else if (successObj instanceof String) {
                    success = Boolean.parseBoolean((String) successObj);
                }

                // 如果success为false，抛出业务异常
                if (!success) {
                    String message = (String) result.get("message");
                    String code = (String) result.get("code");

                    // 创建详细的错误信息，保留原始错误
                    String errorMsg = String.format("云服务业务处理失败: url=%s, code=%s, message=%s",
                            url, code, message);
                    log.error("[{}|{}] [ERROR] - 云服务业务处理失败 {}",
                            LoggingUtils.getTxId(), LoggingUtils.getBranchId(),
                            LoggingUtils.formatContext("error", errorMsg));

                    // 创建包含原始错误信息的异常
                    RuntimeException businessException = new RuntimeException(errorMsg);

                    // 如果原始错误信息中包含具体的业务异常，尝试保留
                    if (message != null && !message.trim().isEmpty()) {
                        // 将原始业务错误作为suppressed异常添加
                        businessException.addSuppressed(new RuntimeException("原始业务错误: " + message));
                    }

                    throw businessException;
                }
            }

            log.debug("[{}|{}] [DEBUG] - 云服务调用成功 {}",
                    LoggingUtils.getTxId(), LoggingUtils.getBranchId(),
                    LoggingUtils.formatContext("url", url));

        } catch (Exception e) {
            // 如果不是JSON格式或解析失败，只要HTTP状态码正常就认为成功
            if (e instanceof RuntimeException && e.getMessage().contains("云服务业务处理失败")) {
                // 重新抛出业务异常
                throw e;
            }
            // 其他解析异常忽略，认为响应正常
            log.debug("[{}|{}] [DEBUG] - 响应体解析失败，但HTTP状态码正常，认为请求成功 {}",
                    LoggingUtils.getTxId(), LoggingUtils.getBranchId(),
                    LoggingUtils.formatContext("url", url));
        }
    }

    /**
     * 从URL中提取服务名称
     * 用于配置解析时的服务特定配置
     */
    private String extractServiceNameFromUrl(String url) {
        try {
            // 简单的服务名提取逻辑，可以根据实际URL格式调整
            // 例如：http://user-service/api/users -> user-service
            if (url.startsWith("http://") || url.startsWith("https://")) {
                String[] parts = url.split("/");
                if (parts.length > 2) {
                    String hostPart = parts[2];
                    // 移除端口号
                    if (hostPart.contains(":")) {
                        hostPart = hostPart.split(":")[0];
                    }
                    return hostPart;
                }
            }
            return "default";
        } catch (Exception e) {
            log.debug("[{}|{}] [DEBUG] - 无法从URL提取服务名 {}",
                    LoggingUtils.getTxId(), LoggingUtils.getBranchId(),
                    LoggingUtils.formatContext("url", url));
            return "default";
        }
    }
}
