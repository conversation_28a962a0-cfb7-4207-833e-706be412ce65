-- 全局锁表
-- 用于实现AT模式的全局锁机制，防止脏写

CREATE TABLE IF NOT EXISTS `global_lock`
(
    `id`          BIGINT(20)   NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `xid`         VARCHAR(128) NOT NULL COMMENT '全局事务ID',
    `branch_id`   BIGINT(20)   NOT NULL COMMENT '分支事务ID',
    `resource_id` VARCHAR(256) NOT NULL COMMENT '资源ID',
    `lock_key`    VARCHAR(512) NOT NULL COMMENT '锁键',
    `create_time` DATETIME     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` DATETIME     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `lock_status` TINYINT(4)   NOT NULL DEFAULT 1 COMMENT '锁状态：1-活跃，2-等待，3-已释放，4-已过期',
    `lock_type`   VARCHAR(32)  NOT NULL DEFAULT 'ROW' COMMENT '锁类型：ROW-行锁，TABLE-表锁，RANGE-范围锁，CUSTOM-自定义锁',
    `remark`      VARCHAR(512)          DEFAULT NULL COMMENT '备注信息',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_resource_lock` (`resource_id`, `lock_key`) COMMENT '资源锁键唯一索引',
    KEY `idx_xid` (`xid`) COMMENT '全局事务ID索引',
    KEY `idx_branch_id` (`branch_id`) COMMENT '分支事务ID索引',
    KEY `idx_resource_id` (`resource_id`) COMMENT '资源ID索引',
    KEY `idx_lock_key` (`lock_key`) COMMENT '锁键索引',
    KEY `idx_create_time` (`create_time`) COMMENT '创建时间索引',
    KEY `idx_lock_status` (`lock_status`) COMMENT '锁状态索引'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci COMMENT ='全局锁表';

-- 创建复合索引优化查询性能
-- 事务ID + 资源ID复合索引
CREATE INDEX `idx_xid_resource` ON `global_lock` (`xid`, `resource_id`);

-- 事务ID + 分支ID复合索引
CREATE INDEX `idx_xid_branch` ON `global_lock` (`xid`, `branch_id`);

-- 资源ID + 锁状态复合索引
CREATE INDEX `idx_resource_status` ON `global_lock` (`resource_id`, `lock_status`);

-- 创建时间 + 锁状态复合索引（用于清理过期锁）
CREATE INDEX `idx_time_status` ON `global_lock` (`create_time`, `lock_status`);

-- 插入示例数据（可选，用于测试）
-- INSERT INTO global_lock (xid, branch_id, resource_id, lock_key, lock_type) VALUES
-- ('tx_001', 1001, 'db1.table1', 'table1:1', 'ROW'),
-- ('tx_001', 1001, 'db1.table1', 'table1:2', 'ROW'),
-- ('tx_002', 1002, 'db1.table2', 'table2:1', 'ROW'),
-- ('tx_003', 1003, 'db1.table1', 'table1:3', 'ROW');

-- 创建锁冲突检查的存储过程
DELIMITER $$
CREATE PROCEDURE IF NOT EXISTS `CheckLockConflict`(
    IN p_xid VARCHAR(128),
    IN p_resource_id VARCHAR(256),
    IN p_lock_key VARCHAR(512),
    OUT p_conflict_xid VARCHAR(128)
)
BEGIN
    DECLARE done INT DEFAULT FALSE;
    DECLARE conflict_xid VARCHAR(128) DEFAULT NULL;

    -- 查询是否存在锁冲突
    SELECT xid
    INTO conflict_xid
    FROM global_lock
    WHERE resource_id = p_resource_id
      AND lock_key = p_lock_key
      AND xid != p_xid
      AND lock_status = 1
    LIMIT 1;

    SET p_conflict_xid = conflict_xid;
END$$
DELIMITER ;

-- 创建批量释放锁的存储过程
DELIMITER $$
CREATE PROCEDURE IF NOT EXISTS `ReleaseLocksByXid`(
    IN p_xid VARCHAR(128),
    OUT p_released_count INT
)
BEGIN
    DECLARE released_count INT DEFAULT 0;

    -- 更新锁状态为已释放
    UPDATE global_lock
    SET lock_status = 3,
        update_time = NOW()
    WHERE xid = p_xid
      AND lock_status = 1;

    SET released_count = ROW_COUNT();

    -- 删除已释放的锁记录
    DELETE
    FROM global_lock
    WHERE xid = p_xid
      AND lock_status = 3;

    SET p_released_count = released_count;
END$$
DELIMITER ;

-- 创建清理过期锁的存储过程
DELIMITER $$
CREATE PROCEDURE IF NOT EXISTS `CleanupExpiredLocks`(
    IN p_expire_hours INT,
    OUT p_cleaned_count INT
)
BEGIN
    DECLARE cleaned_count INT DEFAULT 0;
    DECLARE expire_time DATETIME;

    -- 计算过期时间
    SET expire_time = DATE_SUB(NOW(), INTERVAL p_expire_hours HOUR);

    -- 删除过期的锁记录
    DELETE
    FROM global_lock
    WHERE create_time < expire_time;

    SET cleaned_count = ROW_COUNT();
    SET p_cleaned_count = cleaned_count;

    -- 输出清理结果
    SELECT cleaned_count AS cleaned_locks, expire_time AS expire_before_time;
END$$
DELIMITER ;

-- 创建锁统计的存储过程
DELIMITER $$
CREATE PROCEDURE IF NOT EXISTS `GetLockStatistics`(
    IN p_xid VARCHAR(128)
)
BEGIN
    SELECT p_xid                                            AS xid,
           COUNT(*)                                         AS total_locks,
           COUNT(DISTINCT resource_id)                      AS resource_count,
           COUNT(DISTINCT lock_type)                        AS lock_type_count,
           MIN(create_time)                                 AS first_lock_time,
           MAX(create_time)                                 AS last_lock_time,
           SUM(CASE WHEN lock_status = 1 THEN 1 ELSE 0 END) AS active_locks,
           SUM(CASE WHEN lock_status = 2 THEN 1 ELSE 0 END) AS waiting_locks
    FROM global_lock
    WHERE xid = p_xid;

    -- 按资源分组的锁统计
    SELECT resource_id,
           COUNT(*)                         AS lock_count,
           GROUP_CONCAT(DISTINCT lock_type) AS lock_types,
           MIN(create_time)                 AS first_lock_time,
           MAX(create_time)                 AS last_lock_time
    FROM global_lock
    WHERE xid = p_xid
    GROUP BY resource_id
    ORDER BY lock_count DESC;
END$$
DELIMITER ;

-- 创建定时清理事件（可选，需要开启事件调度器）
-- SET GLOBAL event_scheduler = ON;
--
-- CREATE EVENT IF NOT EXISTS `event_cleanup_expired_locks`
-- ON SCHEDULE EVERY 1 HOUR
-- STARTS CURRENT_TIMESTAMP
-- DO
--   CALL CleanupExpiredLocks(24, @cleaned_count); -- 清理24小时前的锁

-- 创建锁监控视图
CREATE OR REPLACE VIEW `v_lock_monitor` AS
SELECT xid,
       COUNT(*)                                         AS total_locks,
       COUNT(DISTINCT resource_id)                      AS resource_count,
       COUNT(DISTINCT branch_id)                        AS branch_count,
       MIN(create_time)                                 AS first_lock_time,
       MAX(create_time)                                 AS last_lock_time,
       TIMESTAMPDIFF(SECOND, MIN(create_time), NOW())   AS duration_seconds,
       SUM(CASE WHEN lock_status = 1 THEN 1 ELSE 0 END) AS active_locks,
       SUM(CASE WHEN lock_status = 2 THEN 1 ELSE 0 END) AS waiting_locks,
       SUM(CASE WHEN lock_status = 3 THEN 1 ELSE 0 END) AS released_locks,
       SUM(CASE WHEN lock_status = 4 THEN 1 ELSE 0 END) AS expired_locks
FROM global_lock
GROUP BY xid
ORDER BY first_lock_time DESC;
