//package com.tipray.transaction.autoconfigure;
//import com.tipray.transaction.core.infrastructure.persistence.TransactionRepository;
//import com.tipray.transaction.core.infrastructure.persistence.impl.InMemoryTransactionRepository;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
//import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
//import org.springframework.context.annotation.Bean;
//import org.springframework.context.annotation.Configuration;
//
/// **
// * Tipray分布式事务存储自动配置类
// * 负责存储相关组件的Bean装配
// *
// * <AUTHOR>
// * @version 1.0
// * @since 2025-06-20
// */
//@Slf4j
//@Configuration
//public class TiprayTransactionStorageAutoConfiguration {
//
//    /**
//     * 内存存储实现
//     */
//    @Bean
//    @ConditionalOnMissingBean
//    @ConditionalOnProperty(prefix = "tipray.transaction.storage", name = "type", havingValue = "MEMORY")
//    public InMemoryTransactionRepository inMemoryTransactionRepository() {
//        log.info("创建内存事务存储库");
//        return new InMemoryTransactionRepository();
//    }
//
//    /**
//     * 数据库存储配置
//     */
//    @Configuration
//    @ConditionalOnProperty(prefix = "tipray.transaction.storage", name = "type", havingValue = "DATABASE", matchIfMissing = true)
//    public static class DatabaseStorageConfiguration {
//
//        /**
//         * 数据库事务存储库
//         * 实际实现由tipray-transaction-storage模块提供
//         */
//        @Bean
//        @ConditionalOnMissingBean
//        public TransactionRepository databaseTransactionRepository() {
//            log.info("数据库事务存储库将由tipray-transaction-storage模块提供");
//            // 这里返回null，实际的Bean由tipray-transaction-storage模块的自动配置提供
//            return null;
//        }
//    }
//}
