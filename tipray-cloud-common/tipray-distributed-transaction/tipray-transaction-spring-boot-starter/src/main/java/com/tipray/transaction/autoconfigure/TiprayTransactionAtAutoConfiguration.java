package com.tipray.transaction.autoconfigure;

import com.tipray.transaction.at.config.TransactionAtConfiguration;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;

/**
 * Tipray分布式事务AT模式自动配置类
 * 负责AT模式相关组件的Bean装配
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-06-20
 */
@Slf4j
@Configuration
@ConditionalOnProperty(prefix = "tipray.transaction.at", name = "enabled", havingValue = "true", matchIfMissing = true)
@Import(TransactionAtConfiguration.class)
public class TiprayTransactionAtAutoConfiguration {

}
