<?xml version="1.0" encoding="UTF-8"?>

<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.tipray</groupId>
        <artifactId>tipray-distributed-transaction</artifactId>
        <version>${revision}</version>
    </parent>

    <artifactId>tipray-transaction-spring-boot-starter</artifactId>
    <packaging>jar</packaging>

    <description>tipray 分布式事务Spring Boot Starter</description>

    <dependencies>
        <!-- 核心模块 -->
        <dependency>
            <groupId>com.tipray</groupId>
            <artifactId>tipray-transaction-coordinator</artifactId>
        </dependency>

        <!-- AT模式模块 -->
        <dependency>
            <groupId>com.tipray</groupId>
            <artifactId>tipray-transaction-at</artifactId>
        </dependency>

        <!-- Saga模式模块 -->
        <dependency>
            <groupId>com.tipray</groupId>
            <artifactId>tipray-transaction-saga</artifactId>
        </dependency>

        <!-- Spring Boot自动配置 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-autoconfigure</artifactId>
        </dependency>

        <!-- Spring Boot配置处理器 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-configuration-processor</artifactId>
            <optional>true</optional>
        </dependency>

        <!-- Spring Boot Starter -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter</artifactId>
        </dependency>

        <!-- AOP支持 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-aop</artifactId>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <source>1.8</source>
                    <target>1.8</target>
                    <encoding>UTF-8</encoding>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>
