//package com.tipray.transaction.ui.service.impl;
//
//import cn.hutool.core.date.DateUtil;
//import com.tipray.transaction.core.domain.transaction.DistributedTransactionDO;
//import com.tipray.transaction.core.domain.transaction.TransactionExecutionContext;
//import com.tipray.transaction.core.domain.transaction.TransactionStep;
//import com.tipray.transaction.core.infrastructure.persistence.TransactionRepository;
//import com.tipray.transaction.core.infrastructure.persistence.TransactionStepRepository;
//import com.tipray.transaction.ui.dto.*;
//import com.tipray.transaction.ui.service.TransactionQueryService;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
//import org.springframework.stereotype.Service;
//
//import java.time.Duration;
//import java.time.LocalDateTime;
//import java.util.*;
//import java.util.stream.Collectors;
//
/// **
// * 基于Repository的事务查询服务实现
// *
// * 使用抽象的Repository接口，支持不同的存储实现
// * 遵循依赖倒置原则，不依赖具体的存储技术
// *
// * <AUTHOR>
// * @version 1.0
// * @since 2025-05-27
// */
//@Slf4j
//@Service
//@ConditionalOnProperty(prefix = "tipray.transaction.ui", name = "query-service", havingValue = "repository", matchIfMissing = true)
//public class RepositoryBasedTransactionQueryService implements TransactionQueryService {
//
//    @Autowired
//    private TransactionRepository transactionRepository;
//
//    @Autowired
//    private TransactionStepRepository transactionStepRepository;
//
//    @Override
//    public TransactionListResponse queryTransactionList(TransactionQueryRequest request) {
//        try {
//
//            // 先获取总数
//            long total = countTransactionsWithFilter(request);
//
//            // 分页查询
//            List<DistributedTransactionDO> transactions = queryTransactionsWithPagination(request);
//
//            // 转换为TransactionInfo并填充步骤信息
//            List<TransactionInfo> transactionInfos = new ArrayList<>();
//            for (DistributedTransactionDO transaction : transactions) {
//                TransactionInfo info = convertToTransactionInfo(transaction);
//                transactionInfos.add(info);
//            }
//
//            // 转换为响应对象
//            TransactionListResponse response = new TransactionListResponse();
//            response.setList(transactionInfos);  // 使用list字段
//            response.setTotal(total);
//            response.setPageNum(request.getPageNum());
//            response.setPageSize(request.getPageSize());
//
//            return response;
//
//        } catch (Exception e) {
//            log.error("查询事务列表失败", e);
//            throw new RuntimeException("查询事务列表失败: " + e.getMessage(), e);
//        }
//    }
//
//    @Override
//    public TransactionDetailResponse getTransactionDetail(String transactionId) {
//        try {
//            // 查询事务信息
//            DistributedTransactionDO transaction = transactionRepository.findByTransactionId(transactionId);
//            if (transaction == null) {
//                log.warn("事务不存在: {}", transactionId);
//                throw new RuntimeException("事务不存在: " + transactionId);
//            }
//
//            // 转换为TransactionInfo以获取完整信息
//            TransactionInfo transactionInfo = convertToTransactionInfo(transaction);
//
//            // 构建完整的TransactionDetailResponse
//            TransactionDetailResponse response = new TransactionDetailResponse();
//
//            // 基本信息
//            response.setTransactionId(transactionInfo.getTransactionId());
//            response.setGroupId(transactionInfo.getGroupId());
//            response.setTransactionMode(transactionInfo.getMode());
//            response.setStatus(transactionInfo.getStatus());
//            response.setTransactionName(transactionInfo.getTransactionName());
//            response.setServiceName(transactionInfo.getServiceName());
//            response.setBusinessKey(transactionInfo.getBusinessKey());
//
//            // 时间信息
//            response.setCreateTime(transactionInfo.getCreateTime());
//            response.setStartTime(transactionInfo.getStartTime());
//            response.setEndTime(transactionInfo.getEndTime());
//            response.setUpdateTime(transactionInfo.getUpdateTime());
//            response.setDuration(transactionInfo.getDuration());
//
//            // 配置信息
//            response.setTimeoutSeconds(transactionInfo.getTimeoutSeconds());
//            response.setRetryCount(transactionInfo.getRetryCount());
//            response.setCurrentRetryCount(transactionInfo.getCurrentRetryCount());
//            response.setTimeout(transactionInfo.getTimeout());
//            response.setRetryExceeded(transactionInfo.getRetryExceeded());
//
//            // 错误信息 - 完善详细错误信息
//            response.setErrorMessage(transactionInfo.getErrorMessage());
//
//            // 从数据库获取详细错误信息
//            if (transaction.getErrorMessage() != null && !transaction.getErrorMessage().trim().isEmpty()) {
//                String errorMsg = transaction.getErrorMessage();
//
//                response.setDetailedErrorMessage(errorMsg);
//                response.setOriginalException(errorMsg);
//
//                // 解析异常类型和堆栈信息
//                parseExceptionDetails(errorMsg, response);
//
//                // 设置失败原因
//                determineFailureReason(errorMsg, response);
//            } else {
//                log.warn("事务[{}] 没有错误信息或错误信息为空", transactionId);
//            }
//
//            // 从失败的步骤中获取更详细的错误信息
//            if (transactionInfo.getFailedSteps() != null && !transactionInfo.getFailedSteps().isEmpty()) {
//                StringBuilder detailedError = new StringBuilder();
//                for (TransactionStep failedStep : transactionInfo.getFailedSteps()) {
//                    if (failedStep.getErrorMessage() != null) {
//                        detailedError.append("步骤[").append(failedStep.getStepName()).append("]: ")
//                                   .append(failedStep.getErrorMessage()).append("\n");
//                    }
//                }
//                if (detailedError.length() > 0) {
//                    response.setDetailedErrorMessage(detailedError.toString());
//                }
//            }
//            response.setExtendProperties(transactionInfo.getExtendProperties());
//
//            // 解析并设置事务执行上下文信息
//            parseAndSetExecutionContext(transaction, response);
//
//            // 步骤信息
//            response.setSteps(transactionInfo.getSteps());
//            response.setSuccessfulSteps(transactionInfo.getSuccessfulSteps());
//            response.setFailedSteps(transactionInfo.getFailedSteps());
//            response.setStepCount(transactionInfo.getStepCount());
//            response.setSuccessSteps(transactionInfo.getSuccessSteps());
//            response.setFailedStepsCount(transactionInfo.getFailedStepsCount());
//
//            return response;
//
//        } catch (Exception e) {
//            log.error("查询事务详情失败: {}", transactionId, e);
//            throw new RuntimeException("查询事务详情失败: " + e.getMessage(), e);
//        }
//    }
//
//    @Override
//    public TransactionStatisticsResponse getTransactionStatistics(int days) {
//        try {
//            LocalDateTime endTime = LocalDateTime.now();
//            LocalDateTime startTime = endTime.minusDays(days);
//
//            // 查询统计数据
//            long totalCount = transactionRepository.countByTimeRange(startTime, endTime);
//
//            // 成功状态：SUCCESS 和 UNDONE（撤销成功也算成功）
//            long successCount = transactionRepository.countByStatusAndTimeRange(DistributedTransactionStatus.SUCCESS, startTime, endTime) +
//                               transactionRepository.countByStatusAndTimeRange(DistributedTransactionStatus.UNDONE, startTime, endTime);
//
//            // 失败状态：FAILED、UNDO_FAILED、TIMEOUT、CANCELLED、MANUAL_INTERVENTION_REQUIRED
//            long failedCount = transactionRepository.countByStatusAndTimeRange(DistributedTransactionStatus.FAILED, startTime, endTime) +
//                              transactionRepository.countByStatusAndTimeRange(DistributedTransactionStatus.UNDO_FAILED, startTime, endTime) +
//                              transactionRepository.countByStatusAndTimeRange(DistributedTransactionStatus.TIMEOUT, startTime, endTime) +
//                              transactionRepository.countByStatusAndTimeRange(DistributedTransactionStatus.CANCELLED, startTime, endTime) +
//                              transactionRepository.countByStatusAndTimeRange(DistributedTransactionStatus.MANUAL_INTERVENTION_REQUIRED, startTime, endTime);
//
//            // 执行中状态：EXECUTING、PENDING、WAITING、PAUSED、PREPARING、UNDOING
//            long runningCount = transactionRepository.countByStatusAndTimeRange(DistributedTransactionStatus.EXECUTING, startTime, endTime) +
//                               transactionRepository.countByStatusAndTimeRange(DistributedTransactionStatus.PENDING, startTime, endTime) +
//                               transactionRepository.countByStatusAndTimeRange(DistributedTransactionStatus.WAITING, startTime, endTime) +
//                               transactionRepository.countByStatusAndTimeRange(DistributedTransactionStatus.PAUSED, startTime, endTime) +
//                               transactionRepository.countByStatusAndTimeRange(DistributedTransactionStatus.PREPARING, startTime, endTime) +
//                               transactionRepository.countByStatusAndTimeRange(DistributedTransactionStatus.UNDOING, startTime, endTime);
//
//            // 构建响应
//            TransactionStatisticsResponse response = new TransactionStatisticsResponse();
//            response.setTotal((int) totalCount);
//            response.setSuccess((int) successCount);
//            response.setFailed((int) failedCount);
//            response.setRunning((int) runningCount);
//
//            // 设置其他统计信息
//            response.setStatisticsDays(days);
//            response.setStartTime(startTime);
//            response.setEndTime(endTime);
//
//            // 计算平均持续时间（简化实现）
/// /            response.setAvgDuration(1500L); // 默认1.5秒
/// /            response.setPeakTps(Math.max(1, (int) totalCount / (days * 24))); // 简化的TPS计算
//
////            // 生成趋势数据（简化实现）
////            response.setTrends(generateTrendData(days));
//
//            return response;
//
//        } catch (Exception e) {
//            log.error("查询事务统计失败", e);
//            throw new RuntimeException("查询事务统计失败: " + e.getMessage(), e);
//        }
//    }
//
//    @Override
//    public Map<String, Object> getRealtimeData() {
//        try {
//            Map<String, Object> data = new HashMap<>();
//
//            // 获取最新的事务列表（最近10条）
//            LocalDateTime since = LocalDateTime.now().minusMinutes(5); // 最近5分钟的事务
//            List<DistributedTransactionDO> recentTransactions = transactionRepository.findByTimeRange(since, LocalDateTime.now());
//
//            // 限制数量，只返回最新的10条
//            List<DistributedTransactionDO> limitedTransactions = recentTransactions.stream()
//                .sorted((t1, t2) -> t2.getCreateTime().compareTo(t1.getCreateTime()))
//                .limit(10)
//                .collect(Collectors.toList());
//
//            // 转换为前端需要的格式
//            List<Map<String, Object>> transactionList = limitedTransactions.stream()
//                .map(this::convertToRealtimeTransaction)
//                .collect(Collectors.toList());
//
//            // 实时统计数据
//            Map<String, Object> realtimeStats = calculateRealtimeStatistics(since);
//
//            // 系统性能指标
//            Map<String, Object> performanceMetrics = calculatePerformanceMetrics();
//
//            // 告警信息
//            List<Map<String, Object>> alerts = getActiveAlerts();
//
//            data.put("transactions", transactionList);
//            data.put("statistics", realtimeStats);
//            data.put("performance", performanceMetrics);
//            data.put("alerts", alerts);
//            data.put("timestamp", System.currentTimeMillis());
//
//            log.debug("获取实时数据成功: 事务数量={}, 告警数量={}", transactionList.size(), alerts.size());
//            return data;
//
//        } catch (Exception e) {
//            log.error("获取实时数据失败", e);
//            throw new RuntimeException("获取实时数据失败: " + e.getMessage(), e);
//        }
//    }
//
//    /**
//     * 转换事务为实时数据格式
//     */
//    private Map<String, Object> convertToRealtimeTransaction(DistributedTransactionDO transaction) {
//        Map<String, Object> data = new HashMap<>();
//        data.put("transactionId", transaction.getTransactionId());
//        data.put("mode", transaction.getMode().getCode());
//        data.put("status", transaction.getStatus().getCode());
//        data.put("serviceName", extractServiceName(transaction.getTransactionName()));
//        data.put("startTime", transaction.getStartTime());
//        data.put("createTime", transaction.getCreateTime());
//
//        // 计算持续时间
//        if (transaction.getStartTime() != null && transaction.getEndTime() != null) {
//            data.put("duration", Duration.between(transaction.getStartTime(), transaction.getEndTime()).toMillis());
//        } else if (transaction.getStartTime() != null) {
//            data.put("duration", Duration.between(transaction.getStartTime(), LocalDateTime.now()).toMillis());
//        }
//
//        return data;
//    }
//
//    /**
//     * 从事务名称中提取服务名称
//     */
//    private String extractServiceName(String transactionName) {
//        if (transactionName != null && transactionName.contains(".")) {
//            return transactionName.substring(0, transactionName.lastIndexOf("."));
//        }
//        return transactionName != null ? transactionName : "未知服务";
//    }
//
//    @Override
//    public Object getExceptionList(TransactionQueryRequest request) {
//        try {
//            // 查询失败的事务
//            List<DistributedTransactionDO> failedTransactions = transactionRepository.findByStatus(DistributedTransactionStatus.FAILED);
//
//            // 分页处理
//            int start = (request.getPageNum() - 1) * request.getPageSize();
//            int end = Math.min(start + request.getPageSize(), failedTransactions.size());
//
//            List<Map<String, Object>> exceptions = failedTransactions.subList(start, end).stream()
//                    .map(this::convertToExceptionInfo)
//                    .collect(Collectors.toList());
//
//            Map<String, Object> data = new HashMap<>();
//            data.put("list", exceptions);
//            data.put("total", (long) failedTransactions.size());
//            data.put("pageNum", request.getPageNum());
//            data.put("pageSize", request.getPageSize());
//
//            return data;
//
//        } catch (Exception e) {
//            log.error("获取异常列表失败", e);
//            throw new RuntimeException("获取异常列表失败: " + e.getMessage(), e);
//        }
//    }
//
//    @Override
//    public String getSystemHealth() {
//        try {
//            long count = transactionRepository.countAll();
//            return String.format("系统运行正常 - 当前事务总数: %d, 检查时间: %s",
//                    count, DateUtil.formatDateTime(DateUtil.date()));
//        } catch (Exception e) {
//            log.error("获取系统健康状态失败", e);
//            return "系统异常: " + e.getMessage();
//        }
//    }
//
//    @Override
//    public int cleanHistoryData(int days) {
//        try {
//            LocalDateTime cutoffTime = LocalDateTime.now().minusDays(days);
//            int cleanedCount = transactionRepository.deleteBeforeTime(cutoffTime);
//            transactionStepRepository.deleteBeforeTime(cutoffTime);
//
//            return cleanedCount;
//        } catch (Exception e) {
//            log.error("清理历史数据失败", e);
//            throw new RuntimeException("清理历史数据失败: " + e.getMessage(), e);
//        }
//    }
//
//    @Override
//    public Map<String, Object> getConfigInfo() {
//        Map<String, Object> config = new HashMap<>();
//        config.put("version", "1.0.0");
//        config.put("mode", "Repository-Based");
//        config.put("storage", "Abstract Repository");
//        return config;
//    }
//
//    @Override
//    public boolean updateConfig(Map<String, Object> config) {
//        // Repository模式下配置更新的实现
//        return true;
//    }
//
//    @Override
//    public Map<String, Object> batchRetryTransactions(List<String> transactionIds) {
//        Map<String, Object> result = new HashMap<>();
//        result.put("success", false);
//        result.put("message", "Repository模式下暂不支持批量重试");
//        return result;
//    }
//
//    @Override
//    public Map<String, Object> batchCancelTransactions(List<String> transactionIds) {
//        Map<String, Object> result = new HashMap<>();
//        result.put("success", false);
//        result.put("message", "Repository模式下暂不支持批量取消");
//        return result;
//    }
//
//    @Override
//    public List<Map<String, Object>> getTransactionTrends(LocalDateTime startTime, LocalDateTime endTime, String groupBy) {
//        // 简化实现，返回空列表
//        return new ArrayList<>();
//    }
//
//    @Override
//    public Map<String, Object> getPerformanceMetrics(LocalDateTime startTime, LocalDateTime endTime) {
//        Map<String, Object> metrics = new HashMap<>();
//        metrics.put("avgResponseTime", 0);
//        metrics.put("tps", 0);
//        metrics.put("successRate", 0.0);
//        return metrics;
//    }
//
//    // ==================== 私有辅助方法 ====================
//
//    /**
//     * 计算实时统计数据
//     */
//    private Map<String, Object> calculateRealtimeStatistics(LocalDateTime since) {
//        Map<String, Object> stats = new HashMap<>();
//
//        try {
//            // 获取指定时间范围内的所有事务
//            List<DistributedTransactionDO> transactions = transactionRepository.findByTimeRange(since, LocalDateTime.now());
//
//            // 基础统计
//            long totalCount = transactions.size();
//            long successCount = transactions.stream().mapToLong(t ->
//                DistributedTransactionStatus.SUCCESS.equals(t.getStatus()) ? 1 : 0).sum();
//            long failedCount = transactions.stream().mapToLong(t ->
//                DistributedTransactionStatus.FAILED.equals(t.getStatus()) ? 1 : 0).sum();
//            long runningCount = transactions.stream().mapToLong(t ->
//                DistributedTransactionStatus.EXECUTING.equals(t.getStatus()) ? 1 : 0).sum();
//
//            stats.put("totalCount", totalCount);
//            stats.put("successCount", successCount);
//            stats.put("failedCount", failedCount);
//            stats.put("runningCount", runningCount);
//            stats.put("successRate", totalCount > 0 ? (double) successCount / totalCount * 100 : 0.0);
//
//            // 按模式统计
//            Map<String, Long> modeStats = transactions.stream()
//                .collect(Collectors.groupingBy(t -> t.getMode().getCode(), Collectors.counting()));
//            stats.put("modeStatistics", modeStats);
//
//            // 平均执行时间
//            double avgDuration = transactions.stream()
//                .filter(t -> t.getStartTime() != null && t.getEndTime() != null)
//                .mapToLong(t -> java.time.Duration.between(t.getStartTime(), t.getEndTime()).toMillis())
//                .average()
//                .orElse(0.0);
//            stats.put("avgDuration", avgDuration);
//
//        } catch (Exception e) {
//            log.error("计算实时统计数据失败", e);
//            // 返回默认值
//            stats.put("totalCount", 0);
//            stats.put("successCount", 0);
//            stats.put("failedCount", 0);
//            stats.put("runningCount", 0);
//            stats.put("successRate", 0.0);
//            stats.put("modeStatistics", new HashMap<>());
//            stats.put("avgDuration", 0.0);
//        }
//
//        return stats;
//    }
//
//    /**
//     * 计算系统性能指标
//     */
//    private Map<String, Object> calculatePerformanceMetrics() {
//        Map<String, Object> metrics = new HashMap<>();
//
//        try {
//            // 获取系统运行时信息
//            Runtime runtime = Runtime.getRuntime();
//            long totalMemory = runtime.totalMemory();
//            long freeMemory = runtime.freeMemory();
//            long usedMemory = totalMemory - freeMemory;
//
//            metrics.put("memoryUsed", usedMemory / 1024 / 1024); // MB
//            metrics.put("memoryTotal", totalMemory / 1024 / 1024); // MB
//            metrics.put("memoryUsagePercent", (double) usedMemory / totalMemory * 100);
//
//            // CPU使用率（简化实现）
//            metrics.put("cpuUsage", Math.random() * 30 + 10); // 模拟10-40%的CPU使用率
//
//            // 当前活跃事务数
//            long activeTransactions = transactionRepository.countByStatus(DistributedTransactionStatus.EXECUTING);
//            metrics.put("activeTransactions", activeTransactions);
//
//            // 数据库连接状态（简化实现）
//            metrics.put("dbConnectionStatus", "HEALTHY");
//
//        } catch (Exception e) {
//            log.error("计算性能指标失败", e);
//            metrics.put("memoryUsed", 0);
//            metrics.put("memoryTotal", 0);
//            metrics.put("memoryUsagePercent", 0.0);
//            metrics.put("cpuUsage", 0.0);
//            metrics.put("activeTransactions", 0);
//            metrics.put("dbConnectionStatus", "UNKNOWN");
//        }
//
//        return metrics;
//    }
//
//    /**
//     * 获取活跃告警信息
//     */
//    private List<Map<String, Object>> getActiveAlerts() {
//        List<Map<String, Object>> alerts = new ArrayList<>();
//
//        try {
//            // 检查失败事务告警
//            long failedCount = transactionRepository.countByStatus(DistributedTransactionStatus.FAILED);
//            if (failedCount > 10) { // 失败事务超过10个
//                Map<String, Object> alert = new HashMap<>();
//                alert.put("id", "FAILED_TRANSACTIONS_HIGH");
//                alert.put("level", "WARNING");
//                alert.put("title", "失败事务数量过高");
//                alert.put("message", String.format("当前失败事务数量: %d", failedCount));
//                alert.put("timestamp", System.currentTimeMillis());
//                alerts.add(alert);
//            }
//
//            // 检查长时间运行事务告警
//            LocalDateTime threshold = LocalDateTime.now().minusMinutes(30);
//            List<DistributedTransactionDO> longRunningTransactions = transactionRepository.findByStatusAndTimeRange(
//                DistributedTransactionStatus.EXECUTING, null, threshold);
//
//            if (!longRunningTransactions.isEmpty()) {
//                Map<String, Object> alert = new HashMap<>();
//                alert.put("id", "LONG_RUNNING_TRANSACTIONS");
//                alert.put("level", "INFO");
//                alert.put("title", "长时间运行事务");
//                alert.put("message", String.format("发现 %d 个运行超过30分钟的事务", longRunningTransactions.size()));
//                alert.put("timestamp", System.currentTimeMillis());
//                alerts.add(alert);
//            }
//
//            // 检查内存使用率告警
//            Runtime runtime = Runtime.getRuntime();
//            double memoryUsage = (double) (runtime.totalMemory() - runtime.freeMemory()) / runtime.totalMemory();
//            if (memoryUsage > 0.8) { // 内存使用率超过80%
//                Map<String, Object> alert = new HashMap<>();
//                alert.put("id", "HIGH_MEMORY_USAGE");
//                alert.put("level", "ERROR");
//                alert.put("title", "内存使用率过高");
//                alert.put("message", String.format("当前内存使用率: %.1f%%", memoryUsage * 100));
//                alert.put("timestamp", System.currentTimeMillis());
//                alerts.add(alert);
//            }
//
//        } catch (Exception e) {
//            log.error("获取告警信息失败", e);
//        }
//
//        return alerts;
//    }
//
//    /**
//     * 解析异常详情
//     */
//    private void parseExceptionDetails(String errorMsg, TransactionDetailResponse response) {
//        try {
//            // 解析异常类型
//            if (errorMsg.contains("Exception")) {
//                // 查找异常类型，例如: java.lang.RuntimeException: 模拟本地处理异常
//                String[] lines = errorMsg.split("\n");
//                if (lines.length > 0) {
//                    String firstLine = lines[0].trim();
//                    // 提取异常类型
//                    if (firstLine.contains(":")) {
//                        String exceptionType = firstLine.substring(0, firstLine.indexOf(":")).trim();
//                        response.setErrorType(exceptionType);
//
//                        // 提取异常消息
//                        String exceptionMessage = firstLine.substring(firstLine.indexOf(":") + 1).trim();
//                        response.setErrorMessage(exceptionMessage);
//                    } else {
//                        response.setErrorType(firstLine);
//                        log.debug("解析出异常类型: {}", firstLine);
//                    }
//
//                    // 提取堆栈跟踪 - 改进逻辑，处理更多格式
//                    StringBuilder stackTrace = new StringBuilder();
//                    for (int i = 0; i < lines.length; i++) {
//                        String line = lines[i].trim();
//                        // 检查是否是堆栈行：以"at "开头，或者包含".java:"
//                        if (line.startsWith("at ") ||
//                            (line.contains(".java:") && (line.contains("(") || line.contains("Native Method")))) {
//                            // 确保堆栈行格式正确
//                            if (!line.startsWith("at ")) {
//                                line = "at " + line;
//                            }
//                            stackTrace.append(line).append("\n");
//                        }
//                        // 处理"Caused by:"行
//                        else if (line.startsWith("Caused by:")) {
//                            stackTrace.append(line).append("\n");
//                        }
//                        // 处理"... X more"行
//                        else if (line.matches(".*\\d+\\s+more.*")) {
//                            stackTrace.append("\t").append(line).append("\n");
//                        }
//                    }
//
//                    if (stackTrace.length() > 0) {
//                        String finalStackTrace = stackTrace.toString().trim();
//                        response.setErrorStack(finalStackTrace);
//                    } else {
//                        log.debug("未找到有效的堆栈跟踪信息");
//                    }
//                }
//            } else {
//                log.debug("错误信息中不包含'Exception'，跳过解析");
//            }
//        } catch (Exception e) {
//            log.warn("解析异常详情失败: {}", e.getMessage(), e);
//        }
//    }
//
//    /**
//     * 确定失败原因
//     */
//    private void determineFailureReason(String errorMsg, TransactionDetailResponse response) {
//        try {
//            String lowerErrorMsg = errorMsg.toLowerCase();
//
//            if (lowerErrorMsg.contains("timeout") || lowerErrorMsg.contains("超时")) {
//                response.setFailureReason("执行超时");
//            } else if (lowerErrorMsg.contains("connection") || lowerErrorMsg.contains("连接")) {
//                response.setFailureReason("网络连接失败");
//            } else if (lowerErrorMsg.contains("rollback") || lowerErrorMsg.contains("回滚")) {
//                response.setFailureReason("事务回滚失败");
//                response.setRollbackReason(errorMsg);
//            } else if (lowerErrorMsg.contains("compensate") || lowerErrorMsg.contains("补偿")) {
//                response.setFailureReason("补偿执行失败");
//                response.setCompensateReason(errorMsg);
//            } else if (lowerErrorMsg.contains("模拟") || lowerErrorMsg.contains("mock")) {
//                response.setFailureReason("模拟异常");
//            } else if (lowerErrorMsg.contains("runtimeexception")) {
//                response.setFailureReason("运行时异常");
//            } else if (lowerErrorMsg.contains("nullpointerexception")) {
//                response.setFailureReason("空指针异常");
//            } else if (lowerErrorMsg.contains("illegalargumentexception")) {
//                response.setFailureReason("参数异常");
//            } else {
//                response.setFailureReason("业务执行失败");
//            }
//        } catch (Exception e) {
//            log.warn("确定失败原因失败: {}", e.getMessage());
//            response.setFailureReason("未知异常");
//        }
//    }
//
//    /**
//     * 分页查询事务
//     */
//    private List<DistributedTransactionDO> queryTransactionsWithPagination(TransactionQueryRequest request) {
//        // 计算分页参数
//        int offset = (request.getPageNum() - 1) * request.getPageSize();
//
//        // 使用Repository的条件查询方法
//        List<DistributedTransactionDO> allTransactions = transactionRepository.findByConditions(
//            request.getStartTime(),
//            request.getEndTime(),
//            request.getMode(),
//            request.getStatus()
//        );
//
//        // 过滤和排序
//        return allTransactions.stream()
//                .filter(transaction -> matchesFilter(transaction, request))
//                .sorted((t1, t2) -> {
//                    // 按创建时间倒序排列
//                    if (t1.getCreateTime() == null && t2.getCreateTime() == null) return 0;
//                    if (t1.getCreateTime() == null) return 1;
//                    if (t2.getCreateTime() == null) return -1;
//                    return t2.getCreateTime().compareTo(t1.getCreateTime());
//                })
//                .skip(offset)
//                .limit(request.getPageSize())
//                .collect(Collectors.toList());
//    }
//
//    /**
//     * 计算符合条件的事务总数
//     */
//    private long countTransactionsWithFilter(TransactionQueryRequest request) {
//        List<DistributedTransactionDO> allTransactions = transactionRepository.findByConditions(
//            request.getStartTime(),
//            request.getEndTime(),
//            request.getMode(),
//            request.getStatus()
//        );
//
//        return allTransactions.stream()
//                .filter(transaction -> matchesFilter(transaction, request))
//                .count();
//    }
//
//    private List<DistributedTransactionDO> queryTransactionsWithFilter(TransactionQueryRequest request) {
//        // 简化实现：先查询所有，然后过滤
//        // 生产环境应该在Repository层实现复杂查询
//        List<DistributedTransactionDO> allTransactions = transactionRepository.findAll();
//
//        return allTransactions.stream()
//                .filter(t -> matchesFilter(t, request))
//                .collect(Collectors.toList());
//    }
//
//    private boolean matchesFilter(DistributedTransactionDO transaction, TransactionQueryRequest request) {
//        // 实现过滤逻辑
//        if (request.getTransactionId() != null && !request.getTransactionId().isEmpty()) {
//            if (!transaction.getTransactionId().contains(request.getTransactionId())) {
//                return false;
//            }
//        }
//
//        if (request.getStatus() != null && !request.getStatus().isEmpty()) {
//            if (!request.getStatus().equals(transaction.getStatus().getCode())) {
//                return false;
//            }
//        }
//
//        return true;
//    }
//
//    /**
//     * 转换DistributedTransactionDO为TransactionInfo
//     */
//    private TransactionInfo convertToTransactionInfo(DistributedTransactionDO transaction) {
//        TransactionInfo info = new TransactionInfo();
//
//        // 基本信息
//        info.setTransactionId(transaction.getTransactionId());
//        info.setGroupId(transaction.getGroupId() != null ? transaction.getGroupId() : "默认组");
//        info.setMode(transaction.getMode() != null ? transaction.getMode().getCode() : "UNKNOWN");
//        info.setStatus(transaction.getStatus() != null ? transaction.getStatus().getCode() : "UNKNOWN");
//        info.setTransactionName(transaction.getTransactionName() != null ? transaction.getTransactionName() : "未命名事务");
//        info.setBusinessKey(transaction.getBusinessKey());
//        info.setTimeoutSeconds(transaction.getTimeoutSeconds() != null ? transaction.getTimeoutSeconds() : 30);
//        info.setRetryCount(transaction.getRetryCount() != null ? transaction.getRetryCount() : 0);
//        info.setCurrentRetryCount(transaction.getCurrentRetryCount() != null ? transaction.getCurrentRetryCount() : 0);
//        info.setCreateTime(transaction.getCreateTime());
//        // 如果startTime为空，使用createTime作为开始时间
//        info.setStartTime(transaction.getStartTime() != null ? transaction.getStartTime() : transaction.getCreateTime());
//        info.setEndTime(transaction.getEndTime());
//        info.setUpdateTime(transaction.getUpdateTime());
//        info.setErrorMessage(transaction.getErrorMessage());
//        info.setExtendProperties(transaction.getExtendProperties());
//
//        // 设置前端需要的字段
//        // 从事务名称中提取服务名称（格式通常是 ServiceName.methodName）
//        String serviceName = transaction.getTransactionName();
//        if (serviceName != null && serviceName.contains(".")) {
//            serviceName = serviceName.substring(0, serviceName.lastIndexOf("."));
//        }
//        info.setServiceName(serviceName != null ? serviceName : "未知服务");
//
//        // 计算持续时间
//        if (transaction.getStartTime() != null && transaction.getEndTime() != null) {
//            info.setDuration(java.time.Duration.between(transaction.getStartTime(), transaction.getEndTime()).toMillis());
//        } else if (transaction.getCreateTime() != null && transaction.getUpdateTime() != null) {
//            // 如果没有开始/结束时间，使用创建/更新时间
//            info.setDuration(java.time.Duration.between(transaction.getCreateTime(), transaction.getUpdateTime()).toMillis());
//        } else {
//            // 如果都没有，设置为null，前端会显示为'-'
//            info.setDuration(null);
//        }
//
//        // 设置默认值，避免null
//        if (info.getTimeoutSeconds() == null) {
//            info.setTimeoutSeconds(30); // 默认30秒
//        }
//        if (info.getRetryCount() == null) {
//            info.setRetryCount(0);
//        }
//        if (info.getCurrentRetryCount() == null) {
//            info.setCurrentRetryCount(0);
//        }
//
//        // 查询并设置步骤信息
//        try {
//            List<TransactionStep> steps = transactionStepRepository.findByTransactionId(transaction.getTransactionId());
//
//            if (steps != null && !steps.isEmpty()) {
//                info.setSteps(steps);
//
//                // 分类步骤
//                List<TransactionStep> successfulSteps = new ArrayList<>();
//                List<TransactionStep> failedSteps = new ArrayList<>();
//
//                boolean hasTimeout = false;
//                boolean retryExceeded = false;
//
//                for (TransactionStep step : steps) {
//
//                    if (step.getStatus().isSuccess()) {
//                        successfulSteps.add(step);
//                    } else if (step.getStatus().isFailure()) {
//                        failedSteps.add(step);
//                    }
//
//                    if (step.isTimeout()) {
//                        hasTimeout = true;
//                    }
//                    if (step.isRetryExceeded()) {
//                        retryExceeded = true;
//                    }
//                }
//
//                info.setSuccessfulSteps(successfulSteps);
//                info.setFailedSteps(failedSteps);
//                info.setTimeout(hasTimeout);
//                info.setRetryExceeded(retryExceeded);
//
//                // 设置前端需要的步骤统计字段
//                info.setStepCount(steps.size());
//                info.setSuccessSteps(successfulSteps.size());
//                info.setFailedStepsCount(failedSteps.size());
//
//            } else {
//                // 如果没有步骤，设置默认值
//                info.setSteps(new ArrayList<>());
//                info.setSuccessfulSteps(new ArrayList<>());
//                info.setFailedSteps(new ArrayList<>());
//                info.setStepCount(0);
//                info.setSuccessSteps(0);
//                info.setFailedStepsCount(0);
//                info.setTimeout(false);
//                info.setRetryExceeded(false);
//            }
//        } catch (Exception e) {
//            log.error("查询事务步骤失败: transactionId={}", transaction.getTransactionId(), e);
//            // 设置默认值
//            info.setSteps(new ArrayList<>());
//            info.setSuccessfulSteps(new ArrayList<>());
//            info.setFailedSteps(new ArrayList<>());
//            info.setStepCount(0);
//            info.setSuccessSteps(0);
//            info.setFailedStepsCount(0);
//            info.setTimeout(false);
//            info.setRetryExceeded(false);
//        }
//
//        return info;
//    }
//
//    private Map<String, Object> convertToExceptionInfo(DistributedTransactionDO transaction) {
//        Map<String, Object> info = new HashMap<>();
//        info.put("transactionId", transaction.getTransactionId());
//        info.put("errorMessage", transaction.getErrorMessage());
//        info.put("createTime", transaction.getCreateTime());
//        info.put("updateTime", transaction.getUpdateTime());
//        return info;
//    }
//
//    @Override
//    public TransactionStatisticsResponse getTransactionStatisticsWithConditions(TransactionQueryRequest request) {
//        try {
//
//            // 构建查询条件
//            LocalDateTime startTime = request.getStartTime();
//            LocalDateTime endTime = request.getEndTime();
//
//            // 如果没有指定时间范围，默认使用最近7天
//            if (startTime == null && endTime == null) {
//                endTime = LocalDateTime.now();
//                startTime = endTime.minusDays(7);
//            } else if (startTime == null) {
//                startTime = endTime.minusDays(7);
//            } else if (endTime == null) {
//                endTime = LocalDateTime.now();
//            }
//
//            // 查询事务数据
//            List<DistributedTransactionDO> transactions = transactionRepository.findByConditions(
//                startTime, endTime, request.getMode(), request.getStatus());
//
//            // 计算统计信息
//            TransactionStatisticsResponse response = new TransactionStatisticsResponse();
//            response.setTotal(transactions.size());
//
//            long successCount = transactions.stream()
//                .mapToLong(t -> t.getStatus() == DistributedTransactionStatus.SUCCESS ? 1 : 0)
//                .sum();
//            long failedCount = transactions.stream()
//                .mapToLong(t -> t.getStatus() == DistributedTransactionStatus.FAILED ? 1 : 0)
//                .sum();
//            long runningCount = transactions.stream()
//                .mapToLong(t -> t.getStatus() == DistributedTransactionStatus.EXECUTING ? 1 : 0)
//                .sum();
//
//            response.setSuccess((int) successCount);
//            response.setFailed((int) failedCount);
//            response.setRunning((int) runningCount);
//
//            // 计算平均执行时间
//            OptionalDouble avgDuration = transactions.stream()
//                .filter(t -> t.getStartTime() != null && t.getEndTime() != null)
//                .mapToLong(t -> Duration.between(t.getStartTime(), t.getEndTime()).toMillis())
//                .average();
//            response.setAvgDuration(avgDuration.isPresent() ? (long) avgDuration.getAsDouble() : 0L);
//
//            // 设置峰值TPS（简化实现）
//            response.setPeakTps(Math.max(1, response.getTotal() / 24)); // 假设24小时内的平均TPS
//
//            return response;
//
//        } catch (Exception e) {
//            log.error("根据复杂条件获取事务统计信息失败", e);
//            throw new RuntimeException("获取统计信息失败: " + e.getMessage(), e);
//        }
//    }
//
//    /**
//     * 生成趋势数据（简化实现）
//     */
//    private List<Object> generateTrendData(int days) {
//        List<Object> trends = new ArrayList<>();
//        LocalDateTime now = LocalDateTime.now();
//
//        for (int i = days - 1; i >= 0; i--) {
//            LocalDateTime date = now.minusDays(i);
//            Map<String, Object> dayData = new HashMap<>();
//            dayData.put("date", date.toLocalDate().toString());
//            dayData.put("total", 10 + (int)(Math.random() * 20)); // 模拟数据
//            dayData.put("success", 8 + (int)(Math.random() * 10));
//            dayData.put("failed", 1 + (int)(Math.random() * 3));
//            dayData.put("running", (int)(Math.random() * 2));
//            trends.add(dayData);
//        }
//
//        return trends;
//    }
//
//    @Override
//    public TransactionStep getStepById(String stepId) {
//        try {
//            log.debug("根据步骤ID查询步骤信息: stepId={}", stepId);
//
//            TransactionStep step = transactionStepRepository.findByStepId(Long.valueOf(stepId));
//
//            if (step != null) {
//                log.debug("查询到步骤信息: stepId={}, stepName={}", stepId, step.getStepName());
//            } else {
//                log.warn("未找到步骤信息: stepId={}", stepId);
//            }
//
//            return step;
//
//        } catch (Exception e) {
//            log.error("根据步骤ID查询步骤信息失败: stepId={}, error={}", stepId, e.getMessage(), e);
//            return null;
//        }
//    }
//
//    /**
//     * 解析并设置事务执行上下文信息
//     */
//    private void parseAndSetExecutionContext(DistributedTransactionDO transaction, TransactionDetailResponse response) {
//        try {
//            String extendProperties = transaction.getExtendProperties();
//            if (extendProperties != null && !extendProperties.trim().isEmpty()) {
//                // 尝试解析为TransactionExecutionContext
//                TransactionExecutionContext context =
//                    cn.hutool.json.JSONUtil.toBean(extendProperties,
//                        TransactionExecutionContext.class);
//
//                if (context != null) {
//                    // 设置执行上下文信息
//                    response.setExecutionType(context.getExecutionType());
//                    response.setBusinessClassName(context.getBusinessClassName());
//                    response.setBusinessMethodName(context.getBusinessMethodName());
//                    response.setBusinessMethodSignature(context.getBusinessMethodSignature());
//                    response.setBusinessMethodArgs(context.getBusinessMethodArgs());
//                    response.setInitiatorThread(context.getInitiatorThread());
//                    response.setSpringProfileActive(context.getSpringProfileActive());
//                    response.setJvmInfo(context.getJvmInfo());
//
//                    // 序列化调用栈信息
//                    if (context.getCallStack() != null && !context.getCallStack().isEmpty()) {
//                        response.setCallStackInfo(cn.hutool.json.JSONUtil.toJsonStr(context.getCallStack()));
//                    }
//
//                    // 序列化系统属性
//                    if (context.getSystemProperties() != null && !context.getSystemProperties().isEmpty()) {
//                        response.setSystemProperties(cn.hutool.json.JSONUtil.toJsonStr(context.getSystemProperties()));
//                    }
//
//                    // 序列化上下文数据
//                    if (context.getContextData() != null && !context.getContextData().isEmpty()) {
//                        response.setContextData(cn.hutool.json.JSONUtil.toJsonStr(context.getContextData()));
//                    }
//
//                    log.debug("事务执行上下文信息解析完成: transactionId={}, executionType={}",
//                        transaction.getTransactionId(), context.getExecutionType());
//                }
//            }
//        } catch (Exception e) {
//            log.warn("解析事务执行上下文信息失败: transactionId={}, error={}",
//                transaction.getTransactionId(), e.getMessage(), e);
//        }
//    }
//}
