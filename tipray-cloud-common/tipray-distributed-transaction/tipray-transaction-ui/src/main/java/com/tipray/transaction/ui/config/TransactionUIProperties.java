package com.tipray.transaction.ui.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * Tipray分布式事务UI配置属性
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-05-27
 */
@Data
@ConfigurationProperties(prefix = "tipray.transaction.ui")
public class TransactionUIProperties {

    /**
     * 是否启用UI模块
     */
    private boolean enabled = true;

    /**
     * UI访问路径前缀
     */
    private String pathPrefix = "/ui";

    /**
     * UI基础路径
     */
    private String basePath = "/tipray-transaction-ui";

    /**
     * API路径
     */
    private String apiPath = "/api/transaction-ui";

    /**
     * 页面标题
     */
    private String title = "Tipray分布式事务监控";

    /**
     * 自动刷新间隔（秒）
     */
    private int refreshInterval = 5;

    /**
     * 每页显示数量
     */
    private int pageSize = 20;

    /**
     * 静态资源缓存时间（秒）
     */
    private int cachePeriod = 3600;

    /**
     * 是否启用API文档
     */
    private boolean enableApiDoc = true;

    /**
     * 是否启用首页重定向
     */
    private boolean enableIndexRedirect = true;

    /**
     * Swagger配置
     */
    private SwaggerConfig swagger = new SwaggerConfig();

    /**
     * CORS配置
     */
    private CorsConfig cors = new CorsConfig();

    @Data
    public static class SwaggerConfig {
        /**
         * 是否启用Swagger
         */
        private boolean enabled = true;
    }

    @Data
    public static class CorsConfig {
        /**
         * 是否启用CORS
         */
        private boolean enabled = true;

        /**
         * 允许的源
         */
        private String[] allowedOrigins = {"*"};

        /**
         * 允许的方法
         */
        private String[] allowedMethods = {"GET", "POST", "PUT", "DELETE", "OPTIONS"};

        /**
         * 允许的头部
         */
        private String[] allowedHeaders = {"*"};

        /**
         * 是否允许凭证
         */
        private boolean allowCredentials = true;

        /**
         * 预检请求缓存时间
         */
        private long maxAge = 3600;
    }
}
