//package com.tipray.transaction.ui.dto;
//
//import com.tipray.transaction.core.domain.transaction.TransactionStep;
//import lombok.Data;
//
//import java.time.LocalDateTime;
//import java.util.List;
//
/// **
// * 事务信息DTO
// * 专门为UI展示设计的数据传输对象
// *
// * <AUTHOR>
// * @version 1.0
// * @since 2025-05-27
// */
//@Data
//public class TransactionInfo {
//
//    /**
//     * 全局事务ID
//     */
//    private String transactionId;
//
//    /**
//     * 事务组ID
//     */
//    private String groupId;
//
//    /**
//     * 事务模式
//     */
//    private String mode;
//
//    /**
//     * 事务状态
//     */
//    private String status;
//
//    /**
//     * 事务名称
//     */
//    private String transactionName;
//
//    /**
//     * 业务键
//     */
//    private String businessKey;
//
//    /**
//     * 超时时间（秒）
//     */
//    private Integer timeoutSeconds;
//
//    /**
//     * 重试次数
//     */
//    private Integer retryCount;
//
//    /**
//     * 当前重试次数
//     */
//    private Integer currentRetryCount;
//
//    /**
//     * 事务步骤列表
//     */
//    private List<TransactionStep> steps;
//
//    /**
//     * 创建时间
//     */
//    private LocalDateTime createTime;
//
//    /**
//     * 开始时间
//     */
//    private LocalDateTime startTime;
//
//    /**
//     * 结束时间
//     */
//    private LocalDateTime endTime;
//
//    /**
//     * 更新时间
//     */
//    private LocalDateTime updateTime;
//
//    /**
//     * 错误信息
//     */
//    private String errorMessage;
//
//    /**
//     * 扩展属性
//     */
//    private String extendProperties;
//
//    /**
//     * 成功的步骤列表
//     */
//    private List<TransactionStep> successfulSteps;
//
//    /**
//     * 失败的步骤列表
//     */
//    private List<TransactionStep> failedSteps;
//
//    /**
//     * 是否超时
//     */
//    private Boolean timeout;
//
//    /**
//     * 是否重试超限
//     */
//    private Boolean retryExceeded;
//
//    // ==================== 前端需要的额外字段 ====================
//
//    /**
//     * 服务名称（前端显示用）
//     */
//    private String serviceName;
//
//    /**
//     * 持续时间（毫秒）
//     */
//    private Long duration;
//
//    /**
//     * 步骤总数
//     */
//    private Integer stepCount;
//
//    /**
//     * 成功步骤数
//     */
//    private Integer successSteps;
//
//    /**
//     * 失败步骤数
//     */
//    private Integer failedStepsCount;
//}
