package com.tipray.transaction.ui.controller;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;

/**
 * UI页面控制器
 * 提供静态页面访问
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-05-20
 */
@Slf4j
@Controller
@RequestMapping("/ui")
public class UIController {

    /**
     * 事务监控主页面
     *
     * @return 主页面
     */
    @GetMapping({"", "/", "/index", "/dashboard"})
    public String index() {
        return "forward:/index.html";
    }

    /**
     * 事务列表页面
     *
     * @return 事务列表页面
     */
    @GetMapping("/transactions")
    public String transactions() {
        return "forward:/index.html#transactions";
    }

    /**
     * 统计分析页面
     *
     * @return 统计分析页面
     */
    @GetMapping("/statistics")
    public String statistics() {
        return "forward:/index.html#statistics";
    }

    /**
     * AT模式管理页面
     *
     * @return AT模式管理页面
     */
    @GetMapping("/at-mode")
    public String atMode() {
        return "forward:/index.html#at-mode";
    }

    /**
     * Saga模式管理页面
     *
     * @return Saga模式管理页面
     */
    @GetMapping("/saga-mode")
    public String sagaMode() {
        return "forward:/index.html#saga-mode";
    }

    /**
     * 异常管理页面
     *
     * @return 异常管理页面
     */
    @GetMapping("/exceptions")
    public String exceptions() {
        return "forward:/index.html#exceptions";
    }

    /**
     * 系统设置页面
     *
     * @return 系统设置页面
     */
    @GetMapping("/settings")
    public String settings() {
        return "forward:/index.html#settings";
    }

    /**
     * 系统日志页面
     *
     * @return 系统日志页面
     */
    @GetMapping("/logs")
    public String logs() {
        return "forward:/index.html#logs";
    }
}
