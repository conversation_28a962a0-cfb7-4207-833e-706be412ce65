//package com.tipray.transaction.ui.controller;
//
//import com.tipray.transaction.core.domain.enums.TransactionMode;
//import com.tipray.transaction.ui.dto.*;
//import com.tipray.transaction.ui.service.TransactionUIService;
//import io.swagger.v3.oas.annotations.Operation;
//import io.swagger.v3.oas.annotations.Parameter;
//import io.swagger.v3.oas.annotations.tags.Tag;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.validation.annotation.Validated;
//import org.springframework.web.bind.annotation.*;
//
//import javax.validation.Valid;
//import javax.validation.constraints.NotBlank;
//import java.util.List;
//import java.util.Map;
//
/// **
// * 分布式事务监控控制器
// * 提供事务查询、统计、管理等功能的REST API
// *
// * <AUTHOR>
// * @version 1.0
// * @since 2025-05-20
// */
//@Tag(name = "分布式事务监控", description = "分布式事务监控和管理接口")
//@RestController
//@RequestMapping("/api/transaction")
//@Validated
//public class TransactionController {
//
//    private static final org.slf4j.Logger log = org.slf4j.LoggerFactory.getLogger(TransactionController.class);
//
//    @Autowired
//    private TransactionUIService transactionUIService;
//
//    /**
//     * 查询事务列表
//     *
//     * @param request 查询请求
//     * @return 事务列表响应
//     */
//    @Operation(summary = "查询事务列表", description = "根据条件分页查询分布式事务列表")
//    @PostMapping("/list")
//    public ApiResponse<TransactionListResponse> queryTransactionList(@Valid @RequestBody TransactionQueryRequest request) {
//        try {
//            TransactionListResponse response = transactionUIService.queryTransactionList(request);
//            return ApiResponse.success("查询成功", response);
//        } catch (Exception e) {
//            return ApiResponse.error("QUERY_FAILED", "查询事务列表失败: " + e.getMessage());
//        }
//    }
//
//    /**
//     * 获取实时事务数据
//     *
//     * @return 实时事务数据
//     */
//    @Operation(summary = "获取实时事务数据", description = "获取最新的事务执行数据，用于实时监控")
//    @GetMapping("/realtime")
//    public ApiResponse<TransactionListResponse> getRealtimeTransactions() {
//        try {
//            TransactionQueryRequest request = new TransactionQueryRequest();
//            request.setPageSize(10); // 只获取最新的10条
//            request.setOrderBy("startTime");
//            request.setOrderDirection("DESC");
//            TransactionListResponse response = transactionUIService.queryTransactionList(request);
//            return ApiResponse.success("获取实时数据成功", response);
//        } catch (Exception e) {
//            return ApiResponse.error("REALTIME_FAILED", "获取实时数据失败: " + e.getMessage());
//        }
//    }
//
//    /**
//     * 获取事务详情
//     *
//     * @param transactionId 事务ID
//     * @return 事务详情响应
//     */
//    @Operation(summary = "获取事务详情", description = "根据事务ID获取详细信息")
//    @GetMapping("/detail/{transactionId}")
//    public ApiResponse<TransactionDetailResponse> getTransactionDetail(
//            @Parameter(description = "事务ID", required = true)
//            @PathVariable @NotBlank String transactionId) {
//        try {
//            TransactionDetailResponse response = transactionUIService.getTransactionDetail(transactionId);
//            return ApiResponse.success("获取事务详情成功", response);
//        } catch (Exception e) {
//            return ApiResponse.error("DETAIL_FAILED", "获取事务详情失败: " + e.getMessage());
//        }
//    }
//
//    /**
//     * 获取事务统计信息
//     *
//     * @param days 统计天数，默认7天
//     * @return 统计信息响应
//     */
//    @Operation(summary = "获取事务统计信息", description = "获取指定天数内的事务统计数据")
//    @GetMapping("/statistics")
//    public ApiResponse<TransactionStatisticsResponse> getTransactionStatistics(
//            @Parameter(description = "统计天数", example = "7")
//            @RequestParam(defaultValue = "7") Integer days) {
//        try {
//            TransactionStatisticsResponse response = transactionUIService.getTransactionStatistics(days);
//            return ApiResponse.success("获取统计信息成功", response);
//        } catch (Exception e) {
//            return ApiResponse.error("STATISTICS_FAILED", "获取统计信息失败: " + e.getMessage());
//        }
//    }
//
//    /**
//     * 获取事务统计信息（POST方式，支持复杂查询）
//     *
//     * @param request 统计查询请求
//     * @return 统计信息响应
//     */
//    @Operation(summary = "获取事务统计信息（POST）", description = "支持复杂查询条件的事务统计数据获取")
//    @PostMapping("/statistics")
//    public ApiResponse<TransactionStatisticsResponse> getTransactionStatisticsPost(
//            @Valid @RequestBody(required = false) TransactionQueryRequest request) {
//        try {
//            // 如果没有传入请求参数，使用默认值
//            if (request == null) {
//                request = new TransactionQueryRequest();
//            }
//
//            // 从请求中提取统计天数，默认为7天
//            Integer days = request.getDays() != null ? request.getDays() : 7;
//
//            TransactionStatisticsResponse response = transactionUIService.getTransactionStatistics(days);
//            return ApiResponse.success("获取统计信息成功", response);
//        } catch (Exception e) {
//            return ApiResponse.error("STATISTICS_FAILED", "获取统计信息失败: " + e.getMessage());
//        }
//    }
//
//
//
//    /**
//     * 获取所有事务模式
//     *
//     * @return 事务模式列表
//     */
//    @Operation(summary = "获取事务模式列表", description = "获取系统支持的所有事务模式")
//    @GetMapping("/modes")
//    public List<TransactionMode> getTransactionModes() {
//        return transactionUIService.getTransactionModes();
//    }
//
//    /**
//     * 获取所有事务状态
//     *
//     * @return 事务状态列表
//     */
//    @Operation(summary = "获取事务状态列表", description = "获取系统支持的所有事务状态")
//    @GetMapping("/statuses")
//    public List<DistributedTransactionStatus> getTransactionStatuses() {
//        return transactionUIService.getTransactionStatuses();
//    }
//
//    /**
//     * 清理历史事务数据
//     *
//     * @param days 保留天数
//     * @return 操作结果
//     */
//    @Operation(summary = "清理历史事务", description = "清理指定天数之前的历史事务数据")
//    @PostMapping("/cleanup")
//    public String cleanupHistoryTransactions(
//            @Parameter(description = "保留天数", example = "30")
//            @RequestParam(defaultValue = "30") Integer days) {
//        int cleanedCount = transactionUIService.cleanupHistoryTransactions(days);
//        return String.format("已清理 %d 条历史事务记录", cleanedCount);
//    }
//
//    /**
//     * 获取系统健康状态
//     *
//     * @return 健康状态信息
//     */
//    @Operation(summary = "获取系统健康状态", description = "获取分布式事务系统的健康状态")
//    @GetMapping("/health")
//    public String getSystemHealth() {
//        return transactionUIService.getSystemHealth();
//    }
//
//    /**
//     * 获取系统配置
//     *
//     * @return 系统配置信息
//     */
//    @Operation(summary = "获取系统配置", description = "获取分布式事务系统的配置信息")
//    @GetMapping("/config")
//    public Object getSystemConfig() {
//        return transactionUIService.getSystemConfig();
//    }
//
//    /**
//     * 更新系统配置
//     *
//     * @param config 配置信息
//     * @return 更新结果
//     */
//    @Operation(summary = "更新系统配置", description = "更新分布式事务系统的配置信息")
//    @PutMapping("/config")
//    public String updateSystemConfig(@RequestBody Object config) {
//        transactionUIService.updateSystemConfig(config);
//        return "配置更新成功";
//    }
//
//    /**
//     * 获取异常信息列表
//     *
//     * @param request 查询请求
//     * @return 异常信息列表
//     */
//    @Operation(summary = "获取异常信息列表", description = "查询事务执行过程中的异常信息")
//    @PostMapping("/exceptions")
//    public Object getExceptionList(@RequestBody TransactionQueryRequest request) {
//        return transactionUIService.getExceptionList(request);
//    }
//
//    /**
//     * 获取异常详情
//     *
//     * @param exceptionId 异常ID
//     * @return 异常详情
//     */
//    @Operation(summary = "获取异常详情", description = "获取指定异常的详细信息")
//    @GetMapping("/exception/{exceptionId}")
//    public Object getExceptionDetail(@PathVariable String exceptionId) {
//        return transactionUIService.getExceptionDetail(exceptionId);
//    }
//
//    /**
//     * 标记异常为已处理
//     *
//     * @param exceptionId 异常ID
//     * @return 处理结果
//     */
//    @Operation(summary = "标记异常为已处理", description = "将指定异常标记为已处理状态")
//    @PutMapping("/exception/{exceptionId}/resolve")
//    public ApiResponse<String> markExceptionResolved(@PathVariable String exceptionId) {
//        try {
//            transactionUIService.markExceptionResolved(exceptionId);
//            return ApiResponse.success("异常已标记为已处理");
//        } catch (Exception e) {
//            return ApiResponse.error("RESOLVE_FAILED", "标记异常失败: " + e.getMessage());
//        }
//    }
//
//    /**
//     * 忽略异常
//     *
//     * @param exceptionId 异常ID
//     * @return 处理结果
//     */
//    @Operation(summary = "忽略异常", description = "将指定异常标记为已忽略状态")
//    @PutMapping("/exception/{exceptionId}/ignore")
//    public ApiResponse<String> ignoreException(@PathVariable String exceptionId) {
//        try {
//            transactionUIService.ignoreException(exceptionId);
//            return ApiResponse.success("异常已忽略");
//        } catch (Exception e) {
//            return ApiResponse.error("IGNORE_FAILED", "忽略异常失败: " + e.getMessage());
//        }
//    }
//
//    /**
//     * 批量重试事务
//     *
//     * @param request 批量重试请求
//     * @return 处理结果
//     */
//    @Operation(summary = "批量重试事务", description = "批量重试失败的事务")
//    @PostMapping("/batch/retry")
//    public ApiResponse<String> batchRetryTransactions(@RequestBody BatchOperationRequest request) {
//        try {
//            Map<String, Object> result = transactionUIService.batchRetryTransactions(request.getTransactionIds());
//            int count = request.getTransactionIds().size();
//            return ApiResponse.success(String.format("已提交 %d 个事务的重试操作", count));
//        } catch (Exception e) {
//            return ApiResponse.error("BATCH_RETRY_FAILED", "批量重试失败: " + e.getMessage());
//        }
//    }
//
//    /**
//     * 批量回滚事务
//     *
//     * @param request 批量回滚请求
//     * @return 处理结果
//     */
//    @Operation(summary = "批量回滚事务", description = "批量回滚执行中的事务")
//    @PostMapping("/batch/rollback")
//    public ApiResponse<String> batchRollbackTransactions(@RequestBody BatchOperationRequest request) {
//        try {
//            Map<String, Object> objectMap = transactionUIService.batchRollbackTransactions(request.getTransactionIds());
//            return ApiResponse.success(String.format("已提交 %d 个事务的回滚操作", objectMap.size()));
//        } catch (Exception e) {
//            return ApiResponse.error("BATCH_ROLLBACK_FAILED", "批量回滚失败: " + e.getMessage());
//        }
//    }
//
//    /**
//     * 批量处理异常
//     *
//     * @param request 批量处理请求
//     * @return 处理结果
//     */
//    @Operation(summary = "批量处理异常", description = "批量标记异常为已处理")
//    @PostMapping("/exceptions/batch/resolve")
//    public ApiResponse<String> batchResolveExceptions(@RequestBody BatchOperationRequest request) {
//        try {
//            Map<String, Object> objectMap = transactionUIService.batchResolveExceptions(request.getExceptionIds());
//            return ApiResponse.success(String.format("已处理 %d 个异常", objectMap.size()));
//        } catch (Exception e) {
//            return ApiResponse.error("BATCH_RESOLVE_FAILED", "批量处理异常失败: " + e.getMessage());
//        }
//    }
//
//    /**
//     * 导出事务数据
//     *
//     * @param request 导出请求
//     * @return 导出结果
//     */
//    @Operation(summary = "导出事务数据", description = "导出事务数据为Excel文件")
//    @PostMapping("/export")
//    public ApiResponse<String> exportTransactions(@RequestBody TransactionQueryRequest request) {
//        try {
/// /            byte[] bytes = transactionUIService.exportTransactions(request);
//            return ApiResponse.success("导出成功", null);
//        } catch (Exception e) {
//            return ApiResponse.error("EXPORT_FAILED", "导出失败: " + e.getMessage());
//        }
//    }
//
//    /**
//     * 导出异常数据
//     *
//     * @param request 导出请求
//     * @return 导出结果
//     */
//    @Operation(summary = "导出异常数据", description = "导出异常数据为Excel文件")
//    @PostMapping("/exceptions/export")
//    public ApiResponse<String> exportExceptions(@RequestBody TransactionQueryRequest request) {
//        try {
//            transactionUIService.exportExceptions(request);
//            return ApiResponse.success("导出成功", null);
//        } catch (Exception e) {
//            return ApiResponse.error("EXPORT_FAILED", "导出失败: " + e.getMessage());
//        }
//    }
//
//    /**
//     * 获取系统信息
//     *
//     * @return 系统信息
//     */
//    @Operation(summary = "获取系统信息", description = "获取系统运行信息和状态")
//    @GetMapping("/system/info")
//    public ApiResponse<Object> getSystemInfo() {
//        try {
//            Object systemInfo = transactionUIService.getSystemInfo();
//            return ApiResponse.success("获取系统信息成功", systemInfo);
//        } catch (Exception e) {
//            return ApiResponse.error("SYSTEM_INFO_FAILED", "获取系统信息失败: " + e.getMessage());
//        }
//    }
//
//    /**
//     * 获取步骤执行历史
//     *
//     * @param stepId 步骤ID
//     * @param transactionId 事务ID（可选参数）
//     * @return 步骤执行历史
//     */
//    @Operation(summary = "获取步骤执行历史", description = "获取指定步骤的详细执行历史记录")
//    @GetMapping("/steps/{stepId}/history")
//    public ApiResponse<Object> getStepExecutionHistory(
//            @Parameter(description = "步骤ID", required = true)
//            @PathVariable @NotBlank String stepId,
//            @Parameter(description = "事务ID", required = false)
//            @RequestParam(required = false) String transactionId) {
//        try {
//            log.info("获取步骤执行历史 - 步骤ID: {}, 事务ID: {}", stepId, transactionId);
//            Object history = transactionUIService.getStepExecutionHistory(stepId);
//            return ApiResponse.success("获取步骤执行历史成功", history);
//        } catch (Exception e) {
//            log.error("获取步骤执行历史失败 - 步骤ID: {}", stepId, e);
//            return ApiResponse.error("STEP_HISTORY_FAILED", "获取步骤执行历史失败: " + e.getMessage());
//        }
//    }
//
//
//    // ==================== 重试历史相关API ====================
//
//    /**
//     * 获取事务重试历史
//     *
//     * @param transactionId 事务ID
//     * @return 重试历史列表
//     */
//    @Operation(summary = "获取事务重试历史", description = "获取指定事务的所有重试执行历史记录")
//    @GetMapping("/retry-history/{transactionId}")
//    public ApiResponse<List<Map<String, Object>>> getTransactionRetryHistory(
//            @Parameter(description = "事务ID", required = true)
//            @PathVariable @NotBlank String transactionId) {
//        try {
//            List<Map<String, Object>> retryHistory = transactionUIService.getTransactionRetryHistory(transactionId);
//            return ApiResponse.success("获取重试历史成功", retryHistory);
//        } catch (Exception e) {
//            return ApiResponse.error("RETRY_HISTORY_FAILED", "获取重试历史失败: " + e.getMessage());
//        }
//    }
//
//    /**
//     * 获取步骤重试历史
//     *
//     * @param stepId 步骤ID
//     * @return 步骤重试历史列表
//     */
//    @Operation(summary = "获取步骤重试历史", description = "获取指定步骤的所有重试执行历史记录")
//    @GetMapping("/step-retry-history/{stepId}")
//    public ApiResponse<List<Map<String, Object>>> getStepRetryHistory(
//            @Parameter(description = "步骤ID", required = true)
//            @PathVariable @NotBlank Long stepId) {
//        try {
//            List<Map<String, Object>> retryHistory = transactionUIService.getStepRetryHistory(stepId);
//            return ApiResponse.success("获取步骤重试历史成功", retryHistory);
//        } catch (Exception e) {
//            return ApiResponse.error("STEP_RETRY_HISTORY_FAILED", "获取步骤重试历史失败: " + e.getMessage());
//        }
//    }
//
//
//
//    /**
//     * 获取重试统计信息
//     *
//     * @param transactionId 事务ID（可选）
//     * @return 重试统计信息
//     */
//    @Operation(summary = "获取重试统计信息", description = "获取重试相关的统计数据")
//    @GetMapping("/retry-statistics")
//    public ApiResponse<Map<String, Object>> getRetryStatistics(
//            @Parameter(description = "事务ID（可选）", required = false)
//            @RequestParam(required = false) String transactionId) {
//        try {
//            Map<String, Object> statistics = transactionUIService.getRetryStatistics(transactionId);
//            return ApiResponse.success("获取重试统计信息成功", statistics);
//        } catch (Exception e) {
//            return ApiResponse.error("RETRY_STATISTICS_FAILED", "获取重试统计信息失败: " + e.getMessage());
//        }
//    }
//}
