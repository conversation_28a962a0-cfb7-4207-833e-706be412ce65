//package com.tipray.transaction.ui.dto;
//
//import com.tipray.transaction.core.domain.transaction.TransactionStep;
//import io.swagger.v3.oas.annotations.media.Schema;
//import lombok.AllArgsConstructor;
//import lombok.Builder;
//import lombok.Data;
//import lombok.NoArgsConstructor;
//
//import java.time.LocalDateTime;
//import java.util.List;
//
/// **
// * 事务详情响应DTO
// *
// * <AUTHOR>
// * @version 1.0
// * @since 2025-05-20
// */
//@Data
//@Builder
//@NoArgsConstructor
//@AllArgsConstructor
//@Schema(description = "事务详情响应")
//public class TransactionDetailResponse {
//
//    @Schema(description = "事务ID")
//    private String transactionId;
//
//    @Schema(description = "事务组ID")
//    private String groupId;
//
//    @Schema(description = "事务模式")
//    private String transactionMode;
//
//    @Schema(description = "事务状态")
//    private String status;
//
//    @Schema(description = "开始时间")
//    private LocalDateTime startTime;
//
//    @Schema(description = "结束时间")
//    private LocalDateTime endTime;
//
//    @Schema(description = "持续时间(毫秒)")
//    private Long duration;
//
//    @Schema(description = "步骤数量")
//    private Integer stepCount;
//
//    @Schema(description = "错误信息")
//    private String errorMessage;
//
//    @Schema(description = "详细错误信息")
//    private String detailedErrorMessage;
//
//    @Schema(description = "错误类型")
//    private String errorType;
//
//    @Schema(description = "错误堆栈")
//    private String errorStack;
//
//    @Schema(description = "失败原因")
//    private String failureReason;
//
//    @Schema(description = "回滚原因")
//    private String rollbackReason;
//
//    @Schema(description = "补偿原因")
//    private String compensateReason;
//
//    @Schema(description = "原始异常信息")
//    private String originalException;
//
//    @Schema(description = "创建时间")
//    private LocalDateTime createTime;
//
//    @Schema(description = "更新时间")
//    private LocalDateTime updateTime;
//
//    // ==================== 扩展字段，支持完整的事务详情 ====================
//
//    @Schema(description = "事务名称")
//    private String transactionName;
//
//    @Schema(description = "服务名称")
//    private String serviceName;
//
//    @Schema(description = "业务键")
//    private String businessKey;
//
//    @Schema(description = "超时时间(秒)")
//    private Integer timeoutSeconds;
//
//    @Schema(description = "重试次数")
//    private Integer retryCount;
//
//    @Schema(description = "当前重试次数")
//    private Integer currentRetryCount;
//
//    @Schema(description = "是否超时")
//    private Boolean timeout;
//
//    @Schema(description = "是否重试超限")
//    private Boolean retryExceeded;
//
//    @Schema(description = "扩展属性")
//    private String extendProperties;
//
//    @Schema(description = "事务步骤列表")
//    private List<TransactionStep> steps;
//
//    @Schema(description = "成功的步骤列表")
//    private List<TransactionStep> successfulSteps;
//
//    @Schema(description = "失败的步骤列表")
//    private List<TransactionStep> failedSteps;
//
//    @Schema(description = "成功步骤数")
//    private Integer successSteps;
//
//    @Schema(description = "失败步骤数")
//    private Integer failedStepsCount;
//
//    // ==================== 事务执行上下文信息 ====================
//
//    @Schema(description = "执行类型")
//    private String executionType;
//
//    @Schema(description = "业务类名")
//    private String businessClassName;
//
//    @Schema(description = "业务方法名")
//    private String businessMethodName;
//
//    @Schema(description = "业务方法签名")
//    private String businessMethodSignature;
//
//    @Schema(description = "业务方法参数")
//    private String businessMethodArgs;
//
//    @Schema(description = "发起线程")
//    private String initiatorThread;
//
//    @Schema(description = "Spring环境")
//    private String springProfileActive;
//
//    @Schema(description = "JVM信息")
//    private String jvmInfo;
//
//    @Schema(description = "调用栈信息")
//    private String callStackInfo;
//
//    @Schema(description = "系统属性")
//    private String systemProperties;
//
//    @Schema(description = "上下文数据")
//    private String contextData;
//}
