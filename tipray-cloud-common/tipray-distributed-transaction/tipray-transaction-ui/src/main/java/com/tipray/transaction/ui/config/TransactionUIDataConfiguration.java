//package com.tipray.transaction.ui.config;
//
//import com.tipray.transaction.core.infrastructure.persistence.TransactionRepository;
//import com.tipray.transaction.core.infrastructure.persistence.TransactionStepRepository;
//import com.tipray.transaction.core.infrastructure.persistence.impl.InMemoryTransactionRepository;
//import com.tipray.transaction.core.infrastructure.persistence.impl.InMemoryTransactionStepRepository;
//import com.tipray.transaction.ui.dto.TransactionStatisticsResponse;
//import com.tipray.transaction.ui.service.TransactionQueryService;
//import com.tipray.transaction.ui.service.impl.RepositoryBasedTransactionQueryService;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
//import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
//import org.springframework.context.annotation.Bean;
//import org.springframework.context.annotation.Configuration;
//import org.springframework.context.annotation.Primary;
//
//import java.util.Map;
//
/// **
// * 事务UI数据配置类
// * 确保UI模块能够正确获取到数据存储实现
// *
// * <AUTHOR>
// * @version 1.0
// * @since 2025-05-27
// */
//@Slf4j
//@Configuration
//public class TransactionUIDataConfiguration {
//
//    /**
//     * 配置基于Repository的事务查询服务
//     * 这是UI模块的核心数据服务实现
//     */
//    @Bean
//    @Primary
//    @ConditionalOnMissingBean(TransactionQueryService.class)
//    public TransactionQueryService repositoryBasedTransactionQueryService(
//            TransactionRepository transactionRepository,
//            TransactionStepRepository transactionStepRepository) {
//
//        log.info("配置基于Repository的事务查询服务");
//        log.info("事务存储实现: {}", transactionRepository.getClass().getSimpleName());
//        log.info("步骤存储实现: {}", transactionStepRepository.getClass().getSimpleName());
//
//        return new RepositoryBasedTransactionQueryService();
//    }
//
//    /**
//     * 内存存储配置
//     * 当没有数据库存储实现时，使用内存存储作为后备方案
//     */
//    @Configuration
//    public static class MemoryStorageConfiguration {
//
//        /**
//         * 检查是否应该使用内存存储
//         */
//        private boolean shouldUseMemoryStorage(org.springframework.core.env.Environment environment) {
//            // 检查多个可能的配置路径
//            String type1 = environment.getProperty("tipray.transaction.storage.type");
//            String type2 = environment.getProperty("tipray.distributed-transaction.storage.type");
//
//            return "MEMORY".equalsIgnoreCase(type1) || "MEMORY".equalsIgnoreCase(type2);
//        }
//
//        @Bean
//        @ConditionalOnMissingBean(TransactionRepository.class)
//        public TransactionRepository inMemoryTransactionRepository() {
//            log.warn("使用内存事务存储 - 仅适用于开发和测试环境");
//            log.warn("生产环境请配置数据库存储实现");
//            return new InMemoryTransactionRepository();
//        }
//
//        @Bean
//        @ConditionalOnMissingBean(TransactionStepRepository.class)
//        public TransactionStepRepository inMemoryTransactionStepRepository() {
//            log.warn("使用内存步骤存储 - 仅适用于开发和测试环境");
//            return new InMemoryTransactionStepRepository();
//        }
//    }
//
//    /**
//     * 数据库存储配置
//     * 默认配置，优先使用数据库存储
//     */
//    @Configuration
//    @ConditionalOnProperty(prefix = "tipray.transaction.storage", name = "type", havingValue = "DATABASE", matchIfMissing = true)
//    public static class DatabaseStorageConfiguration {
//
//        /**
//         * 数据库存储配置检查
//         * 确保数据库存储实现已正确配置
//         */
//        @Bean
//        public DatabaseStorageChecker databaseStorageChecker(
//                TransactionRepository transactionRepository,
//                TransactionStepRepository transactionStepRepository) {
//
//            log.info("=== 数据库存储配置检查 ===");
//            log.info("事务存储实现: {}", transactionRepository.getClass().getName());
//            log.info("步骤存储实现: {}", transactionStepRepository.getClass().getName());
//
//            // 检查是否为内存实现（可能表示配置有问题）
//            if (transactionRepository instanceof InMemoryTransactionRepository) {
//                log.warn("警告: 当前使用内存事务存储，可能数据库存储配置有问题");
//            }
//
//            if (transactionStepRepository instanceof InMemoryTransactionStepRepository) {
//                log.warn("警告: 当前使用内存步骤存储，可能数据库存储配置有问题");
//            }
//
//            return new DatabaseStorageChecker();
//        }
//    }
//
//    /**
//     * 数据库存储检查器
//     * 用于验证数据库存储配置是否正确
//     */
//    public static class DatabaseStorageChecker {
//
//        public void checkDatabaseConnection(TransactionRepository transactionRepository) {
//            try {
//                // 尝试执行一个简单的查询来验证数据库连接
//                long count = transactionRepository.countAll();
//                log.info("数据库连接正常 - 当前事务总数: {}", count);
//            } catch (Exception e) {
//                log.error("数据库连接检查失败", e);
//                throw new RuntimeException("数据库存储配置异常: " + e.getMessage(), e);
//            }
//        }
//    }
//
//    /**
//     * UI数据服务健康检查
//     * 确保UI能够正常获取数据
//     */
//    @Bean
//    public UIDataHealthChecker uiDataHealthChecker(TransactionQueryService transactionQueryService) {
//        log.info("配置UI数据服务健康检查");
//        return new UIDataHealthChecker(transactionQueryService);
//    }
//
//    /**
//     * UI数据健康检查器
//     */
//    public static class UIDataHealthChecker {
//
//        private final TransactionQueryService transactionQueryService;
//
//        public UIDataHealthChecker(TransactionQueryService transactionQueryService) {
//            this.transactionQueryService = transactionQueryService;
//
//            // 启动时进行一次健康检查
//            performHealthCheck();
//        }
//
//        public void performHealthCheck() {
//            try {
//                log.info("执行UI数据服务健康检查...");
//
//                // 测试基本查询功能
//                String healthStatus = transactionQueryService.getSystemHealth();
//                log.info("系统健康状态: {}", healthStatus);
//
//                // 测试统计功能
//                TransactionStatisticsResponse statistics = transactionQueryService.getTransactionStatistics(1);
//                log.info("统计功能正常 - 今日事务数: {}", statistics.getTotal());
//
//                // 测试实时数据功能
//                Map<String, Object> realtimeData = transactionQueryService.getRealtimeData();
//                log.info("实时数据功能正常 - 数据项数: {}", realtimeData.size());
//
//                log.info("UI数据服务健康检查通过");
//
//            } catch (Exception e) {
//                log.error("UI数据服务健康检查失败", e);
//                // 不抛出异常，避免影响应用启动
//                // 但记录错误，便于排查问题
//            }
//        }
//    }
//
//    /**
//     * 数据初始化器
//     * 为演示和测试目的初始化一些示例数据
//     */
//    @Bean
//    @ConditionalOnProperty(prefix = "tipray.transaction.ui", name = "init-sample-data", havingValue = "true", matchIfMissing = false)
//    public SampleDataInitializer sampleDataInitializer(
//            TransactionRepository transactionRepository,
//            TransactionStepRepository transactionStepRepository) {
//
//        log.info("配置示例数据初始化器");
//        return new SampleDataInitializer(transactionRepository, transactionStepRepository);
//    }
//
//    /**
//     * 示例数据初始化器
//     * 用于在开发和演示环境中创建示例数据
//     */
//    public static class SampleDataInitializer {
//
//        private final TransactionRepository transactionRepository;
//        private final TransactionStepRepository transactionStepRepository;
//
//        public SampleDataInitializer(TransactionRepository transactionRepository,
//                                   TransactionStepRepository transactionStepRepository) {
//            this.transactionRepository = transactionRepository;
//            this.transactionStepRepository = transactionStepRepository;
//
//            // 延迟初始化，避免影响应用启动
//            initializeSampleDataAsync();
//        }
//
//        private void initializeSampleDataAsync() {
//            new Thread(() -> {
//                try {
//                    Thread.sleep(5000); // 等待5秒，确保应用完全启动
//                    initializeSampleData();
//                } catch (InterruptedException e) {
//                    Thread.currentThread().interrupt();
//                    log.warn("示例数据初始化被中断");
//                } catch (Exception e) {
//                    log.error("示例数据初始化失败", e);
//                }
//            }).start();
//        }
//
//        private void initializeSampleData() {
//            try {
//                log.info("开始初始化示例数据...");
//
//                // 检查是否已有数据
//                long existingCount = transactionRepository.countAll();
//                if (existingCount > 0) {
//                    log.info("已存在 {} 条事务记录，跳过示例数据初始化", existingCount);
//                    return;
//                }
//
//                // 创建示例事务数据
//                createSampleTransactions();
//
//                log.info("示例数据初始化完成");
//
//            } catch (Exception e) {
//                log.error("初始化示例数据失败", e);
//            }
//        }
//
//        private void createSampleTransactions() {
//            // 这里可以创建一些示例事务数据
//            // 为了简化，暂时只记录日志
//            log.info("创建示例事务数据...");
//
//            // TODO: 实现示例数据创建逻辑
//            // 可以创建不同状态、不同模式的事务数据
//            // 用于UI界面的展示和测试
//        }
//    }
//
//    /**
//     * 配置信息输出
//     */
//    @Bean
//    public ConfigurationReporter configurationReporter(TransactionUIProperties properties) {
//        return new ConfigurationReporter(properties);
//    }
//
//    /**
//     * 配置信息报告器
//     */
//    public static class ConfigurationReporter {
//
//        public ConfigurationReporter(TransactionUIProperties properties) {
//            reportConfiguration(properties);
//        }
//
//        private void reportConfiguration(TransactionUIProperties properties) {
//            log.info("=== Tipray分布式事务UI配置报告 ===");
//            log.info("UI模块启用状态: {}", properties.isEnabled());
//            log.info("UI访问路径: {}", properties.getPathPrefix());
//            log.info("页面标题: {}", properties.getTitle());
//            log.info("自动刷新间隔: {}秒", properties.getRefreshInterval());
//            log.info("每页显示数量: {}", properties.getPageSize());
//            log.info("========================================");
//        }
//    }
//}
