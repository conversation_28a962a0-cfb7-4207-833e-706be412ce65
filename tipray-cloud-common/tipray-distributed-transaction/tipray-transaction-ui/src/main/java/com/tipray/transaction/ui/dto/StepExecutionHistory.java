package com.tipray.transaction.ui.dto;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 步骤执行历史记录
 * 记录每次步骤执行的详细信息
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-05-27
 */
@Data
public class StepExecutionHistory {

    /**
     * 执行序号（第几次执行）
     */
    private Integer attemptNumber;

    /**
     * 执行类型（EXECUTE: 正常执行, RETRY: 重试执行, COMPENSATE: 补偿执行）
     */
    private String executionType;

    /**
     * 执行状态
     */
    private String status;

    /**
     * 开始时间
     */
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    private LocalDateTime endTime;

    /**
     * 执行耗时（毫秒）
     */
    private Long duration;

    /**
     * 执行结果
     */
    private String result;

    /**
     * 错误信息
     */
    private String errorMessage;

    /**
     * 错误类型
     */
    private String errorType;

    /**
     * 错误堆栈
     */
    private String errorStack;

    /**
     * 请求参数
     */
    private String requestParams;

    /**
     * 响应数据
     */
    private String responseData;

    /**
     * 执行节点信息
     */
    private String executionNode;

    /**
     * 网络延迟（毫秒）
     */
    private Long networkLatency;

    /**
     * 是否超时
     */
    private Boolean timeout;

    /**
     * 重试原因
     */
    private String retryReason;

    /**
     * 扩展信息
     */
    private String extendInfo;
}
