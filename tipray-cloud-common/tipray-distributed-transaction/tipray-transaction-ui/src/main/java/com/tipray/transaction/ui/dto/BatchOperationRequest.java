package com.tipray.transaction.ui.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * 批量操作请求
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-05-20
 */
@Data
@Schema(description = "批量操作请求")
public class BatchOperationRequest {

    @Schema(description = "事务ID列表")
    private List<String> transactionIds;

    @Schema(description = "异常ID列表")
    private List<String> exceptionIds;

    @Schema(description = "操作类型")
    private String operationType;

    @Schema(description = "操作备注")
    private String remark;

    /**
     * 获取事务ID列表（非空验证）
     */
    @NotEmpty(message = "事务ID列表不能为空")
    public List<String> getTransactionIds() {
        return transactionIds;
    }

    /**
     * 获取异常ID列表（非空验证）
     */
    @NotEmpty(message = "异常ID列表不能为空")
    public List<String> getExceptionIds() {
        return exceptionIds;
    }
}
