//package com.tipray.transaction.ui.dto;
//
//import io.swagger.v3.oas.annotations.media.Schema;
//import lombok.Data;
//
//import java.time.LocalDateTime;
//import java.util.List;
//
/// **
// * 事务统计响应DTO
// *
// * <AUTHOR>
// * @version 1.0
// * @since 2025-05-20
// */
//@Data
//@Schema(description = "事务统计响应")
//public class TransactionStatisticsResponse {
//
//    @Schema(description = "总事务数")
//    private Integer total;
//
//    @Schema(description = "成功事务数")
//    private Integer success;
//
//    @Schema(description = "失败事务数")
//    private Integer failed;
//
//    @Schema(description = "回滚中事务数")
//    private Integer undoing;
//
//    @Schema(description = "执行中事务数")
//    private Integer running;
//
//    @Schema(description = "平均持续时间(毫秒)")
//    private Long avgDuration;
//
//    @Schema(description = "峰值TPS")
//    private Integer peakTps;
//
//    // 为前端仪表板添加的字段
//    @Schema(description = "总事务数（兼容字段）")
//    private Integer totalCount;
//
//    @Schema(description = "成功事务数（兼容字段）")
//    private Integer successCount;
//
//    @Schema(description = "失败事务数（兼容字段）")
//    private Integer failedCount;
//
//    @Schema(description = "执行中事务数（兼容字段）")
//    private Integer executingCount;
//
//    @Schema(description = "超时事务数")
//    private Integer timeoutCount;
//
//    @Schema(description = "当前TPS")
//    private Integer currentTps;
//
//    @Schema(description = "成功率（数值形式）")
//    private Double successRate;
//
//    @Schema(description = "趋势数据")
//    private List<Object> trendData;
//
//    @Schema(description = "模式分布数据")
//    private List<Object> modeData;
//
//    @Schema(description = "趋势数据")
//    private List<Object> trends;
//
//    @Schema(description = "步骤执行统计")
//    private List<StepExecutionStatistics> stepStatistics;
//
//    @Schema(description = "统计天数")
//    private Integer statisticsDays;
//
//    @Schema(description = "统计开始时间")
//    private LocalDateTime startTime;
//
//    @Schema(description = "统计结束时间")
//    private LocalDateTime endTime;
//
//    @Schema(description = "成功率百分比")
//    public String getSuccessRatePercent() {
//        if (getTotalCountValue() == 0) {
//            return "0%";
//        }
//        double rate = (double) getSuccessCountValue() / getTotalCountValue() * 100;
//        return String.format("%.1f%%", rate);
//    }
//
//    // 兼容性方法
//    public String getSuccessRate() {
//        return getSuccessRatePercent();
//    }
//
//    // 获取数值形式的成功率
//    public Double getSuccessRateValue() {
//        if (getTotalCountValue() == 0) {
//            return 0.0;
//        }
//        return (double) getSuccessCountValue() / getTotalCountValue() * 100;
//    }
//
//    @Schema(description = "失败率百分比")
//    public String getFailureRate() {
//        if (getTotalCountValue() == 0) {
//            return "0.0%";
//        }
//        double rate = (double) getFailedCountValue() / getTotalCountValue() * 100;
//        return String.format("%.1f%%", rate);
//    }
//
//    @Schema(description = "处理中事务数")
//    public Integer getProcessingCount() {
//        int totalVal = getTotalCountValue();
//        int successVal = getSuccessCountValue();
//        int failedVal = getFailedCountValue();
//        return totalVal - successVal - failedVal;
//    }
//
//    // 辅助方法：获取统一的总数值
//    private int getTotalCountValue() {
//        if (totalCount != null) return totalCount;
//        if (total != null) return total;
//        return 0;
//    }
//
//    // 辅助方法：获取统一的成功数值
//    private int getSuccessCountValue() {
//        if (successCount != null) return successCount;
//        if (success != null) return success;
//        return 0;
//    }
//
//    // 辅助方法：获取统一的失败数值
//    private int getFailedCountValue() {
//        if (failedCount != null) return failedCount;
//        if (failed != null) return failed;
//        return 0;
//    }
//
//    // 辅助方法：获取统一的执行中数值
//    private int getExecutingCountValue() {
//        if (executingCount != null) return executingCount;
//        if (running != null) return running;
//        return 0;
//    }
//}
