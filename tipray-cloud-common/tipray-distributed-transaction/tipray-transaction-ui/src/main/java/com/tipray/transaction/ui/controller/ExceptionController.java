//package com.tipray.transaction.ui.controller;
//
//import com.tipray.transaction.ui.dto.ApiResponse;
//import com.tipray.transaction.ui.dto.TransactionDetailResponse;
//import com.tipray.transaction.ui.dto.TransactionQueryRequest;
//import com.tipray.transaction.ui.dto.TransactionListResponse;
//import com.tipray.transaction.ui.service.TransactionUIService;
//import io.swagger.v3.oas.annotations.Operation;
//import io.swagger.v3.oas.annotations.Parameter;
//import io.swagger.v3.oas.annotations.tags.Tag;
//import lombok.RequiredArgsConstructor;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.validation.annotation.Validated;
//import org.springframework.web.bind.annotation.*;
//
//import javax.validation.Valid;
//import java.util.Map;
//
/// **
// * 异常管理控制器
// *
// * <AUTHOR>
// * @version 1.0
// * @since 2024-07-03
// */
//@Slf4j
//@RestController
//@RequestMapping("/api/exceptions")
//@RequiredArgsConstructor
//@Validated
//@Tag(name = "异常管理", description = "异常信息查询和管理接口")
//public class ExceptionController {
//
//    private final TransactionUIService transactionUIService;
//
//    /**
//     * 获取异常列表
//     *
//     * @param request 查询请求
//     * @return 异常列表响应
//     */
//    @Operation(summary = "获取异常列表", description = "根据查询条件获取异常信息列表")
//    @PostMapping("/list")
//    public ApiResponse<TransactionListResponse> getExceptionList(
//            @Valid @RequestBody(required = false) TransactionQueryRequest request) {
//        try {
//            // 如果没有传入请求参数，使用默认值
//            if (request == null) {
//                request = new TransactionQueryRequest();
//            }
//
//            // 设置查询条件：只查询失败的事务
//            request.setStatus("FAILED,ERROR,TIMEOUT");
//
/// /            TransactionListResponse response = transactionUIService.getTransactionList(request);
/// /            return ApiResponse.success("获取异常列表成功", response);
//            return null;
//        } catch (Exception e) {
//            log.error("获取异常列表失败", e);
//            return ApiResponse.error("EXCEPTION_LIST_FAILED", "获取异常列表失败: " + e.getMessage());
//        }
//    }
//
//    /**
//     * 获取异常详情
//     *
//     * @param exceptionId 异常ID（实际为事务ID）
//     * @return 异常详情响应
//     */
//    @Operation(summary = "获取异常详情", description = "根据异常ID获取详细的异常信息")
//    @GetMapping("/{exceptionId}")
//    public ApiResponse<Object> getExceptionDetail(
//            @Parameter(description = "异常ID", required = true)
//            @PathVariable String exceptionId) {
//        try {
//            // 异常详情实际就是事务详情
//            TransactionDetailResponse response = transactionUIService.getTransactionDetail(exceptionId);
//            return ApiResponse.success("获取异常详情成功", response);
//        } catch (Exception e) {
//            log.error("获取异常详情失败: {}", exceptionId, e);
//            return ApiResponse.error("EXCEPTION_DETAIL_FAILED", "获取异常详情失败: " + e.getMessage());
//        }
//    }
//
//    /**
//     * 标记异常为已处理
//     *
//     * @param exceptionId 异常ID
//     * @return 操作结果
//     */
//    @Operation(summary = "标记异常已处理", description = "将指定异常标记为已处理状态")
//    @PostMapping("/{exceptionId}/handle")
//    public ApiResponse<Void> markExceptionHandled(
//            @Parameter(description = "异常ID", required = true)
//            @PathVariable String exceptionId) {
//        try {
//            // 这里可以添加异常处理标记的逻辑
//            // 目前暂时返回成功
//            log.info("标记异常已处理: {}", exceptionId);
//            return ApiResponse.success("异常已标记为已处理");
//        } catch (Exception e) {
//            log.error("标记异常处理失败: {}", exceptionId, e);
//            return ApiResponse.error("MARK_HANDLED_FAILED", "标记异常处理失败: " + e.getMessage());
//        }
//    }
//
//    /**
//     * 批量标记异常为已处理
//     *
//     * @param exceptionIds 异常ID列表
//     * @return 操作结果
//     */
//    @Operation(summary = "批量标记异常已处理", description = "批量将异常标记为已处理状态")
//    @PostMapping("/batch/handle")
//    public ApiResponse<Void> batchMarkExceptionsHandled(
//            @RequestBody java.util.List<String> exceptionIds) {
//        try {
//            log.info("批量标记异常已处理: {}", exceptionIds);
//            // 这里可以添加批量异常处理标记的逻辑
//            return ApiResponse.success("批量标记异常处理成功");
//        } catch (Exception e) {
//            log.error("批量标记异常处理失败", e);
//            return ApiResponse.error("BATCH_MARK_HANDLED_FAILED", "批量标记异常处理失败: " + e.getMessage());
//        }
//    }
//
//    /**
//     * 获取异常统计信息
//     *
//     * @param days 统计天数
//     * @return 异常统计信息
//     */
//    @Operation(summary = "获取异常统计信息", description = "获取指定天数内的异常统计数据")
//    @GetMapping("/statistics")
//    public ApiResponse<Object> getExceptionStatistics(
//            @Parameter(description = "统计天数", example = "7")
//            @RequestParam(defaultValue = "7") Integer days) {
//        try {
////            // 这里可以添加异常统计的逻辑
////            Map stats = java.util.Map.of(
////                "total", 0,
////                "unhandled", 0,
////                "handled", 0,
////                "today", 0
////            );
//            return ApiResponse.success("获取异常统计成功", null);
//        } catch (Exception e) {
//            log.error("获取异常统计失败", e);
//            return ApiResponse.error("EXCEPTION_STATS_FAILED", "获取异常统计失败: " + e.getMessage());
//        }
//    }
//}
