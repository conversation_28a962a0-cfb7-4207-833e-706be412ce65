//package com.tipray.transaction.ui.controller;
//
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//import org.springframework.web.bind.annotation.*;
//
//import java.util.List;
//import java.util.Map;
//
/// **
// * 分布式事务UI控制器
// * 提供轻量级的REST API，为前端界面提供数据
// *
// * <AUTHOR>
// * @version 1.0
// * @since 2025-01-25
// */
//@RestController
//@RequestMapping("${tipray.transaction.ui.api-path:/api/transaction-ui}")
//@CrossOrigin(origins = "*")
//public class TransactionUIController {
//
//    private static final Logger log = LoggerFactory.getLogger(TransactionUIController.class);
//
//    private final TransactionUIProvider uiProvider;
//
//    public TransactionUIController(TransactionUIProvider uiProvider) {
//        this.uiProvider = uiProvider;
//    }
//
//    /**
//     * 获取事务概览信息
//     */
//    @GetMapping("/overview")
//    public ApiResponse<TransactionUIProvider.TransactionOverview> getOverview() {
//        try {
//            TransactionUIProvider.TransactionOverview overview = uiProvider.getTransactionOverview();
//            return ApiResponse.success(overview);
//        } catch (Exception e) {
//            log.error("获取事务概览失败: {}", e.getMessage(), e);
//            return ApiResponse.error("获取概览信息失败: " + e.getMessage());
//        }
//    }
//
//    /**
//     * 获取事务列表
//     */
//    @GetMapping("/transactions")
//    public ApiResponse<TransactionUIProvider.PageResult<TransactionUIProvider.TransactionInfo>> getTransactions(
//            @RequestParam(required = false) String transactionId,
//            @RequestParam(required = false) String groupId,
//            @RequestParam(required = false) String status,
//            @RequestParam(required = false) String mode,
//            @RequestParam(required = false) String startTime,
//            @RequestParam(required = false) String endTime,
//            @RequestParam(defaultValue = "1") int page,
//            @RequestParam(defaultValue = "20") int size) {
//        try {
//            TransactionUIProvider.TransactionQuery query = new TransactionUIProvider.TransactionQuery();
//            query.setTransactionId(transactionId);
//            query.setGroupId(groupId);
//            query.setStatus(status);
//            query.setMode(mode);
//            query.setStartTime(startTime);
//            query.setEndTime(endTime);
//            query.setPage(page);
//            query.setSize(size);
//
//            TransactionUIProvider.PageResult<TransactionUIProvider.TransactionInfo> result =
//                    uiProvider.getTransactionList(query);
//            return ApiResponse.success(result);
//        } catch (Exception e) {
//            log.error("获取事务列表失败: {}", e.getMessage(), e);
//            return ApiResponse.error("获取事务列表失败: " + e.getMessage());
//        }
//    }
//
//    /**
//     * 获取事务详情
//     */
//    @GetMapping("/transactions/{transactionId}")
//    public ApiResponse<Object> getTransactionDetail(@PathVariable String transactionId) {
//        try {
//            Object detail = uiProvider.getTransactionDetail(transactionId);
//            return ApiResponse.success(detail);
//        } catch (Exception e) {
//            log.error("获取事务详情失败: 事务[{}] - {}", transactionId, e.getMessage(), e);
//            return ApiResponse.error("获取事务详情失败: " + e.getMessage());
//        }
//    }
//
//    /**
//     * 获取分支事务列表
//     */
//    @GetMapping("/branches")
//    public ApiResponse<TransactionUIProvider.PageResult<TransactionUIProvider.BranchTransactionInfo>> getBranches(
//            @RequestParam(required = false) String transactionId,
//            @RequestParam(required = false) Long branchId,
//            @RequestParam(required = false) String status,
//            @RequestParam(defaultValue = "1") int page,
//            @RequestParam(defaultValue = "20") int size) {
//        try {
//            TransactionUIProvider.BranchTransactionQuery query = new TransactionUIProvider.BranchTransactionQuery();
//            // 设置查询参数...
//
//            TransactionUIProvider.PageResult<TransactionUIProvider.BranchTransactionInfo> result =
//                    uiProvider.getBranchTransactionList(query);
//            return ApiResponse.success(result);
//        } catch (Exception e) {
//            log.error("获取分支事务列表失败: {}", e.getMessage(), e);
//            return ApiResponse.error("获取分支事务列表失败: " + e.getMessage());
//        }
//    }
//
//    /**
//     * 获取执行历史
//     */
//    @GetMapping("/execution-history")
//    public ApiResponse<TransactionUIProvider.PageResult<TransactionUIProvider.ExecutionRecord>> getExecutionHistory(
//            @RequestParam(required = false) String transactionId,
//            @RequestParam(required = false) Long branchId,
//            @RequestParam(required = false) String status,
//            @RequestParam(defaultValue = "1") int page,
//            @RequestParam(defaultValue = "20") int size) {
//        try {
//            TransactionUIProvider.ExecutionHistoryQuery query = new TransactionUIProvider.ExecutionHistoryQuery();
//            // 设置查询参数...
//
//            TransactionUIProvider.PageResult<TransactionUIProvider.ExecutionRecord> result =
//                    uiProvider.getExecutionHistory(query);
//            return ApiResponse.success(result);
//        } catch (Exception e) {
//            log.error("获取执行历史失败: {}", e.getMessage(), e);
//            return ApiResponse.error("获取执行历史失败: " + e.getMessage());
//        }
//    }
//
//    /**
//     * 获取统计信息
//     */
//    @GetMapping("/statistics")
//    public ApiResponse<Object> getStatistics() {
//        try {
//            TransactionUIProvider.StatisticsQuery query = new TransactionUIProvider.StatisticsQuery();
//            Object statistics = uiProvider.getStatistics(query);
//            return ApiResponse.success(statistics);
//        } catch (Exception e) {
//            log.error("获取统计信息失败: {}", e.getMessage(), e);
//            return ApiResponse.error("获取统计信息失败: " + e.getMessage());
//        }
//    }
//
//    /**
//     * 获取监控指标
//     */
//    @GetMapping("/metrics")
//    public ApiResponse<List<TransactionUIProvider.MetricData>> getMetrics(
//            @RequestParam(required = false) String type,
//            @RequestParam(required = false) String timeRange) {
//        try {
//            TransactionUIProvider.MetricsQuery query = new TransactionUIProvider.MetricsQuery();
//            // 设置查询参数...
//
//            List<TransactionUIProvider.MetricData> metrics = uiProvider.getMetrics(query);
//            return ApiResponse.success(metrics);
//        } catch (Exception e) {
//            log.error("获取监控指标失败: {}", e.getMessage(), e);
//            return ApiResponse.error("获取监控指标失败: " + e.getMessage());
//        }
//    }
//
//    /**
//     * 获取配置信息
//     */
//    @GetMapping("/configuration")
//    public ApiResponse<Map<String, Object>> getConfiguration() {
//        try {
//            Map<String, Object> config = uiProvider.getConfiguration();
//            return ApiResponse.success(config);
//        } catch (Exception e) {
//            log.error("获取配置信息失败: {}", e.getMessage(), e);
//            return ApiResponse.error("获取配置信息失败: " + e.getMessage());
//        }
//    }
//
//    /**
//     * 执行手动操作
//     */
//    @PostMapping("/manual-operation")
//    public ApiResponse<TransactionUIProvider.OperationResult> executeManualOperation(
//            @RequestBody TransactionUIProvider.ManualOperation operation) {
//        try {
//            TransactionUIProvider.OperationResult result = uiProvider.executeManualOperation(operation);
//            return ApiResponse.success(result);
//        } catch (Exception e) {
//            log.error("执行手动操作失败: {} - {}", operation.getType(), e.getMessage(), e);
//            return ApiResponse.error("执行手动操作失败: " + e.getMessage());
//        }
//    }
//
//    /**
//     * 获取操作历史
//     */
//    @GetMapping("/operation-history")
//    public ApiResponse<TransactionUIProvider.PageResult<TransactionUIProvider.OperationRecord>> getOperationHistory(
//            @RequestParam(required = false) String transactionId,
//            @RequestParam(required = false) String operatorId,
//            @RequestParam(defaultValue = "1") int page,
//            @RequestParam(defaultValue = "20") int size) {
//        try {
//            TransactionUIProvider.OperationHistoryQuery query = new TransactionUIProvider.OperationHistoryQuery();
//            // 设置查询参数...
//
//            TransactionUIProvider.PageResult<TransactionUIProvider.OperationRecord> result =
//                    uiProvider.getOperationHistory(query);
//            return ApiResponse.success(result);
//        } catch (Exception e) {
//            log.error("获取操作历史失败: {}", e.getMessage(), e);
//            return ApiResponse.error("获取操作历史失败: " + e.getMessage());
//        }
//    }
//
//    /**
//     * API响应包装类
//     */
//    public static class ApiResponse<T> {
//        private boolean success;
//        private String message;
//        private T data;
//        private long timestamp;
//
//        private ApiResponse(boolean success, String message, T data) {
//            this.success = success;
//            this.message = message;
//            this.data = data;
//            this.timestamp = System.currentTimeMillis();
//        }
//
//        public static <T> ApiResponse<T> success(T data) {
//            return new ApiResponse<>(true, "操作成功", data);
//        }
//
//        public static <T> ApiResponse<T> error(String message) {
//            return new ApiResponse<>(false, message, null);
//        }
//
//        // getter方法
//        public boolean isSuccess() { return success; }
//        public String getMessage() { return message; }
//        public T getData() { return data; }
//        public long getTimestamp() { return timestamp; }
//    }
//}
