package com.tipray.transaction.ui.controller;

import com.tipray.transaction.ui.dto.ApiResponse;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.lang.management.ManagementFactory;
import java.lang.management.MemoryMXBean;
import java.lang.management.OperatingSystemMXBean;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;

/**
 * 系统管理控制器
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-07-03
 */
@Slf4j
@RestController
@RequestMapping("/api/system")
@RequiredArgsConstructor
@Validated
@Tag(name = "系统管理", description = "系统配置和信息管理接口")
public class SystemController {

    @Value("${spring.application.name:tipray-transaction-ui}")
    private String applicationName;

    @Value("${server.port:8080}")
    private String serverPort;

    /**
     * 获取系统配置
     *
     * @return 系统配置信息
     */
    @Operation(summary = "获取系统配置", description = "获取当前系统的配置信息")
    @GetMapping("/config")
    public ApiResponse<Map<String, Object>> getSystemConfig() {
        try {
            Map<String, Object> config = new HashMap<>();

            // 基本配置
            config.put("systemName", "Tipray分布式事务监控中心");
            config.put("version", "1.0.0");
            config.put("adminEmail", "<EMAIL>");
            config.put("timezone", "Asia/Shanghai");
            config.put("language", "zh-CN");

            // 监控配置
            config.put("refreshInterval", 5);
            config.put("dataRetentionDays", 30);
            config.put("maxDisplayRecords", 1000);
            config.put("enableRealtime", true);
            config.put("enableNotifications", true);

            // 事务配置
            config.put("defaultTimeout", 30);
            config.put("maxRetryCount", 3);
            config.put("retryInterval", 1000);
            config.put("enableAutoRollback", true);
            config.put("enableTransactionLog", true);

            // 安全配置
            config.put("sessionTimeout", 30);
            config.put("apiSecretKey", "***");
            config.put("enableIpWhitelist", false);
            config.put("ipWhitelist", "");
            config.put("enableAuditLog", true);

            return ApiResponse.success("获取系统配置成功", config);
        } catch (Exception e) {
            log.error("获取系统配置失败", e);
            return ApiResponse.error("GET_CONFIG_FAILED", "获取系统配置失败: " + e.getMessage());
        }
    }

    /**
     * 保存系统配置
     *
     * @param config 配置数据
     * @return 保存结果
     */
    @Operation(summary = "保存系统配置", description = "保存系统配置信息")
    @PostMapping("/config")
    public ApiResponse<Void> saveSystemConfig(@RequestBody Map<String, Object> config) {
        try {
            log.info("保存系统配置: {}", config);
            // 这里可以添加配置保存的逻辑
            return ApiResponse.success("系统配置保存成功");
        } catch (Exception e) {
            log.error("保存系统配置失败", e);
            return ApiResponse.error("SAVE_CONFIG_FAILED", "保存系统配置失败: " + e.getMessage());
        }
    }

    /**
     * 获取系统信息
     *
     * @return 系统信息
     */
    @Operation(summary = "获取系统信息", description = "获取系统运行状态和资源使用情况")
    @GetMapping("/info")
    public ApiResponse<Map<String, Object>> getSystemInfo() {
        try {
            Map<String, Object> info = new HashMap<>();

            // 基本信息
            info.put("version", "1.0.0");
            info.put("javaVersion", System.getProperty("java.version"));
            info.put("springBootVersion", getSpringBootVersion());
            info.put("startTime", getApplicationStartTime());
            info.put("uptime", getUptime());

            // 系统资源信息
            OperatingSystemMXBean osBean = ManagementFactory.getOperatingSystemMXBean();
            MemoryMXBean memoryBean = ManagementFactory.getMemoryMXBean();


            // 内存信息
            long totalMemory = memoryBean.getHeapMemoryUsage().getMax();
            long usedMemory = memoryBean.getHeapMemoryUsage().getUsed();
            double memoryUsage = (double) usedMemory / totalMemory * 100;

            info.put("memoryUsage", Math.round(memoryUsage));
            info.put("memoryTotal", formatBytes(totalMemory));
            info.put("memoryUsed", formatBytes(usedMemory));

            // 磁盘信息（简化版）
            info.put("diskUsage", 40);
            info.put("diskTotal", "50GB");
            info.put("diskUsed", "20GB");

            // 数据库状态（模拟）
            info.put("dbStatus", "正常");
            info.put("dbConnections", "5/10");

            return ApiResponse.success("获取系统信息成功", info);
        } catch (Exception e) {
            log.error("获取系统信息失败", e);
            return ApiResponse.error("GET_INFO_FAILED", "获取系统信息失败: " + e.getMessage());
        }
    }

    /**
     * 清理系统缓存
     *
     * @return 清理结果
     */
    @Operation(summary = "清理系统缓存", description = "清理系统缓存数据")
    @PostMapping("/cache/clear")
    public ApiResponse<Void> clearSystemCache() {
        try {
            log.info("清理系统缓存");
            // 这里可以添加缓存清理的逻辑
            return ApiResponse.success("系统缓存清理成功");
        } catch (Exception e) {
            log.error("清理系统缓存失败", e);
            return ApiResponse.error("CLEAR_CACHE_FAILED", "清理系统缓存失败: " + e.getMessage());
        }
    }

    /**
     * 重置系统配置
     *
     * @return 重置结果
     */
    @Operation(summary = "重置系统配置", description = "将系统配置重置为默认值")
    @PostMapping("/config/reset")
    public ApiResponse<Void> resetSystemConfig() {
        try {
            log.info("重置系统配置");
            // 这里可以添加配置重置的逻辑
            return ApiResponse.success("系统配置重置成功");
        } catch (Exception e) {
            log.error("重置系统配置失败", e);
            return ApiResponse.error("RESET_CONFIG_FAILED", "重置系统配置失败: " + e.getMessage());
        }
    }

    /**
     * 导出系统配置
     *
     * @return 配置数据
     */
    @Operation(summary = "导出系统配置", description = "导出当前系统配置")
    @GetMapping("/config/export")
    public ApiResponse<Map<String, Object>> exportSystemConfig() {
        try {
            // 获取当前配置
            ApiResponse<Map<String, Object>> configResponse = getSystemConfig();
            return ApiResponse.success("导出系统配置成功", configResponse.getData());
        } catch (Exception e) {
            log.error("导出系统配置失败", e);
            return ApiResponse.error("EXPORT_CONFIG_FAILED", "导出系统配置失败: " + e.getMessage());
        }
    }

    /**
     * 导入系统配置
     *
     * @param config 配置数据
     * @return 导入结果
     */
    @Operation(summary = "导入系统配置", description = "导入系统配置数据")
    @PostMapping("/config/import")
    public ApiResponse<Void> importSystemConfig(@RequestBody Map<String, Object> config) {
        try {
            log.info("导入系统配置: {}", config);
            // 这里可以添加配置导入的逻辑
            return ApiResponse.success("系统配置导入成功");
        } catch (Exception e) {
            log.error("导入系统配置失败", e);
            return ApiResponse.error("IMPORT_CONFIG_FAILED", "导入系统配置失败: " + e.getMessage());
        }
    }

    // 辅助方法

    private String getSpringBootVersion() {
        try {
            return org.springframework.boot.SpringBootVersion.getVersion();
        } catch (Exception e) {
            return "Unknown";
        }
    }

    private String getApplicationStartTime() {
        try {
            long startTime = ManagementFactory.getRuntimeMXBean().getStartTime();
            return LocalDateTime.ofInstant(
                    java.time.Instant.ofEpochMilli(startTime),
                    java.time.ZoneId.systemDefault()
            ).format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        } catch (Exception e) {
            return "Unknown";
        }
    }

    private String getUptime() {
        try {
            long uptime = ManagementFactory.getRuntimeMXBean().getUptime();
            long hours = uptime / (1000 * 60 * 60);
            long minutes = (uptime % (1000 * 60 * 60)) / (1000 * 60);
            return hours + "小时" + minutes + "分钟";
        } catch (Exception e) {
            return "Unknown";
        }
    }

    private String formatBytes(long bytes) {
        if (bytes < 1024) return bytes + "B";
        if (bytes < 1024 * 1024) return String.format("%.1fKB", bytes / 1024.0);
        if (bytes < 1024 * 1024 * 1024) return String.format("%.1fMB", bytes / (1024.0 * 1024));
        return String.format("%.1fGB", bytes / (1024.0 * 1024 * 1024));
    }
}
