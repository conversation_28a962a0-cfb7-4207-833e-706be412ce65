<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta content="width=device-width, initial-scale=1.0" name="viewport">
    <title>Tipray分布式事务监控 - 增强版仪表板</title>

    <!-- 样式文件 -->
    <link href="css/enhanced-dashboard.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">

    <!-- 图表库 -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/Chart.js/3.9.1/chart.min.js"></script>

    <style>
        /* 图标样式映射 */
        .icon-database::before {
            content: '\f1c0';
            font-family: 'Font Awesome 6 Free';
            font-weight: 900;
        }

        .icon-check-circle::before {
            content: '\f058';
            font-family: 'Font Awesome 6 Free';
            font-weight: 900;
        }

        .icon-clock::before {
            content: '\f017';
            font-family: 'Font Awesome 6 Free';
            font-weight: 900;
        }

        .icon-activity::before {
            content: '\f201';
            font-family: 'Font Awesome 6 Free';
            font-weight: 900;
        }

        .icon-refresh::before {
            content: '\f021';
            font-family: 'Font Awesome 6 Free';
            font-weight: 900;
        }

        .icon-play::before {
            content: '\f04b';
            font-family: 'Font Awesome 6 Free';
            font-weight: 900;
        }

        .icon-pause::before {
            content: '\f04c';
            font-family: 'Font Awesome 6 Free';
            font-weight: 900;
        }

        .icon-alert-circle::before {
            content: '\f06a';
            font-family: 'Font Awesome 6 Free';
            font-weight: 900;
        }

        .icon-info::before {
            content: '\f129';
            font-family: 'Font Awesome 6 Free';
            font-weight: 900;
        }

        /* 加载动画 */
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #007bff;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% {
                transform: rotate(0deg);
            }
            100% {
                transform: rotate(360deg);
            }
        }

        /* 页面头部 */
        .page-header {
            background: white;
            padding: 20px 24px;
            margin: -20px -20px 30px -20px;
            border-bottom: 1px solid #e9ecef;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .page-header h1 {
            margin: 0;
            font-size: 24px;
            font-weight: 600;
            color: #212529;
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .page-header .subtitle {
            font-size: 14px;
            color: #6c757d;
            margin-top: 4px;
        }

        .status-indicator {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background-color: #28a745;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% {
                opacity: 1;
            }
            50% {
                opacity: 0.5;
            }
            100% {
                opacity: 1;
            }
        }

        /* 快速操作栏 */
        .quick-actions {
            background: white;
            padding: 16px 24px;
            margin-bottom: 20px;
            border-radius: 12px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            border: 1px solid #e9ecef;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .quick-actions .actions-left {
            display: flex;
            gap: 12px;
            align-items: center;
        }

        .quick-actions .actions-right {
            display: flex;
            gap: 12px;
            align-items: center;
        }

        .last-update {
            font-size: 12px;
            color: #6c757d;
        }

        /* 空状态样式 */
        .empty-state {
            text-align: center;
            padding: 40px 20px;
            color: #6c757d;
        }

        .empty-state i {
            font-size: 48px;
            margin-bottom: 16px;
            opacity: 0.5;
        }

        /* 工具提示 */
        .tooltip {
            position: relative;
            cursor: help;
        }

        .tooltip::after {
            content: attr(data-tooltip);
            position: absolute;
            bottom: 100%;
            left: 50%;
            transform: translateX(-50%);
            background: #333;
            color: white;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            white-space: nowrap;
            opacity: 0;
            pointer-events: none;
            transition: opacity 0.3s;
            z-index: 1000;
        }

        .tooltip:hover::after {
            opacity: 1;
        }
    </style>
</head>
<body>
<!-- 页面头部 -->
<div class="page-header">
    <h1>
        <i class="fas fa-tachometer-alt"></i>
        分布式事务监控中心
        <div class="status-indicator" title="系统运行正常"></div>
    </h1>
    <div class="subtitle">实时监控分布式事务的执行状态和性能指标</div>
</div>

<!-- 快速操作栏 -->
<div class="quick-actions">
    <div class="actions-left">
        <button class="btn btn-primary" onclick="window.location.href='index.html'">
            <i class="fas fa-list"></i> 事务列表
        </button>
        <button class="btn btn-secondary" onclick="window.location.href='statistics.html'">
            <i class="fas fa-chart-bar"></i> 统计分析
        </button>
        <button class="btn btn-secondary" onclick="window.location.href='exceptions.html'">
            <i class="fas fa-exclamation-triangle"></i> 异常管理
        </button>
    </div>
    <div class="actions-right">
        <div class="last-update" id="last-update">
            最后更新: <span id="update-time">-</span>
        </div>
        <button class="btn btn-secondary" id="settings-btn" title="设置">
            <i class="fas fa-cog"></i>
        </button>
    </div>
</div>

<!-- 仪表板容器 -->
<div id="dashboard-container">
    <!-- 加载中状态 -->
    <div class="empty-state" id="loading-state">
        <div class="loading"></div>
        <p>正在加载仪表板数据...</p>
    </div>
</div>

<!-- JavaScript文件 -->
<script src="js/services/api-service.js"></script>
<script src="js/pages/enhanced-dashboard.js"></script>

<script>
    // 页面级别的功能
    document.addEventListener('DOMContentLoaded', function () {
        // 更新时间显示
        function updateTime() {
            const now = new Date();
            const timeString = now.toLocaleString('zh-CN', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
            });

            const updateTimeElement = document.getElementById('update-time');
            if (updateTimeElement) {
                updateTimeElement.textContent = timeString;
            }
        }

        // 初始更新时间
        updateTime();

        // 每秒更新时间
        setInterval(updateTime, 1000);

        // 设置按钮点击事件
        const settingsBtn = document.getElementById('settings-btn');
        if (settingsBtn) {
            settingsBtn.addEventListener('click', function () {
                showSettingsModal();
            });
        }

        // 键盘快捷键
        document.addEventListener('keydown', function (e) {
            // Ctrl+R 或 F5 刷新数据
            if ((e.ctrlKey && e.key === 'r') || e.key === 'F5') {
                e.preventDefault();
                if (window.enhancedDashboard) {
                    window.enhancedDashboard.loadDashboardData();
                }
            }

            // Esc 键停止自动刷新
            if (e.key === 'Escape') {
                if (window.enhancedDashboard) {
                    window.enhancedDashboard.stopAutoRefresh();
                }
            }
        });
    });

    // 显示设置模态框
    function showSettingsModal() {
        const modal = document.createElement('div');
        modal.className = 'modal-overlay';
        modal.innerHTML = `
                <div class="modal-content">
                    <div class="modal-header">
                        <h3>仪表板设置</h3>
                        <button class="modal-close">&times;</button>
                    </div>
                    <div class="modal-body">
                        <div class="setting-item">
                            <label>自动刷新间隔（秒）</label>
                            <input type="number" id="refresh-interval" value="5" min="1" max="60">
                        </div>
                        <div class="setting-item">
                            <label>显示实时事务数量</label>
                            <input type="number" id="realtime-count" value="10" min="5" max="50">
                        </div>
                        <div class="setting-item">
                            <label>启用声音提醒</label>
                            <input type="checkbox" id="sound-alerts">
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button class="btn btn-primary" onclick="saveSettings()">保存</button>
                        <button class="btn btn-secondary" onclick="closeModal()">取消</button>
                    </div>
                </div>
            `;

        // 添加模态框样式
        const style = document.createElement('style');
        style.textContent = `
                .modal-overlay {
                    position: fixed;
                    top: 0;
                    left: 0;
                    right: 0;
                    bottom: 0;
                    background: rgba(0, 0, 0, 0.5);
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    z-index: 2000;
                }
                .modal-content {
                    background: white;
                    border-radius: 12px;
                    width: 90%;
                    max-width: 400px;
                    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
                }
                .modal-header {
                    padding: 20px 24px 16px;
                    border-bottom: 1px solid #e9ecef;
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                }
                .modal-header h3 {
                    margin: 0;
                    font-size: 18px;
                    font-weight: 600;
                }
                .modal-close {
                    background: none;
                    border: none;
                    font-size: 24px;
                    cursor: pointer;
                    color: #6c757d;
                }
                .modal-body {
                    padding: 20px 24px;
                }
                .setting-item {
                    margin-bottom: 16px;
                }
                .setting-item label {
                    display: block;
                    margin-bottom: 6px;
                    font-weight: 500;
                    color: #495057;
                }
                .setting-item input {
                    width: 100%;
                    padding: 8px 12px;
                    border: 1px solid #ced4da;
                    border-radius: 6px;
                    font-size: 14px;
                }
                .modal-footer {
                    padding: 16px 24px 20px;
                    display: flex;
                    gap: 12px;
                    justify-content: flex-end;
                }
            `;
        document.head.appendChild(style);

        document.body.appendChild(modal);

        // 绑定关闭事件
        modal.querySelector('.modal-close').addEventListener('click', closeModal);
        modal.addEventListener('click', function (e) {
            if (e.target === modal) {
                closeModal();
            }
        });

        // 全局函数
        window.closeModal = function () {
            document.body.removeChild(modal);
            document.head.removeChild(style);
        };

        window.saveSettings = function () {
            const refreshInterval = document.getElementById('refresh-interval').value;
            const realtimeCount = document.getElementById('realtime-count').value;
            const soundAlerts = document.getElementById('sound-alerts').checked;

            // 保存设置到localStorage
            localStorage.setItem('dashboard-settings', JSON.stringify({
                refreshInterval: parseInt(refreshInterval) * 1000,
                realtimeCount: parseInt(realtimeCount),
                soundAlerts: soundAlerts
            }));

            // 应用设置
            if (window.enhancedDashboard) {
                window.enhancedDashboard.refreshInterval = parseInt(refreshInterval) * 1000;
                window.enhancedDashboard.startAutoRefresh();
            }

            closeModal();

            // 显示保存成功提示
            const toast = document.createElement('div');
            toast.className = 'toast-message';
            toast.textContent = '设置已保存';
            toast.style.cssText = `
                    position: fixed;
                    top: 20px;
                    right: 20px;
                    background: #28a745;
                    color: white;
                    padding: 12px 20px;
                    border-radius: 6px;
                    z-index: 3000;
                    animation: slideIn 0.3s ease;
                `;
            document.body.appendChild(toast);

            setTimeout(() => {
                if (toast.parentNode) {
                    toast.parentNode.removeChild(toast);
                }
            }, 3000);
        };
    }

    // 错误处理
    window.addEventListener('error', function (e) {
        console.error('页面错误:', e.error);
    });

    // 页面可见性变化处理
    document.addEventListener('visibilitychange', function () {
        if (window.enhancedDashboard) {
            if (document.hidden) {
                // 页面隐藏时停止自动刷新
                window.enhancedDashboard.stopAutoRefresh();
            } else {
                // 页面显示时恢复自动刷新
                window.enhancedDashboard.startAutoRefresh();
                // 立即刷新一次数据
                window.enhancedDashboard.loadDashboardData();
            }
        }
    });
</script>
</body>
</html>
