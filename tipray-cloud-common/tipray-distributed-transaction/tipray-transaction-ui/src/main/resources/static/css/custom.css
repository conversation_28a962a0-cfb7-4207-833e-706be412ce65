/* 自定义样式 - 分布式事务监控UI */

/* ConfirmDialog 样式修复 */
.modal-content .btn-close.position-absolute {
    background: none;
    border: none;
    opacity: 0.5;
    transition: opacity 0.3s ease;
    width: 1.5rem;
    height: 1.5rem;
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-content .btn-close.position-absolute:hover {
    opacity: 1;
}

.modal-content .btn-close.position-absolute:focus {
    box-shadow: none;
    outline: none;
}

/* 确保模态框内容紧凑 */
.modal-sm .modal-body {
    padding: 1.5rem;
}

.modal-sm .modal-footer {
    padding: 1rem 1.5rem 1.5rem;
    border-top: none;
}

/* 时间轴样式优化 */
.timeline {
    position: relative;
    padding-left: 2rem;
}

.timeline::before {
    content: '';
    position: absolute;
    left: 1.25rem;
    top: 0;
    bottom: 0;
    width: 2px;
    background: linear-gradient(to bottom, #e9ecef, #dee2e6, #e9ecef);
    z-index: 1;
}

.timeline-item {
    position: relative;
    margin-bottom: 2rem;
}

.timeline-point {
    position: absolute;
    left: -2rem;
    top: 1rem;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 2.5rem;
    height: 2.5rem;
    border-radius: 50%;
    border: 3px solid;
    background: white;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    z-index: 2;
    transition: all 0.3s ease;
}

.timeline-point:hover {
    transform: scale(1.1);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
}

.timeline-content {
    margin-left: 1rem;
}

.timeline-point-icon {
    font-size: 1rem;
    line-height: 1;
}

.timeline-point-success {
    border-color: #2fb344;
    background: linear-gradient(135deg, #2fb344, #51cf66);
    color: white;
}

.timeline-point-danger {
    border-color: #d63384;
    background: linear-gradient(135deg, #d63384, #e74c3c);
    color: white;
}

.timeline-point-warning {
    border-color: #fd7e14;
    background: linear-gradient(135deg, #fd7e14, #ffa726);
    color: white;
}

.timeline-point-secondary {
    border-color: #6c757d;
    background: linear-gradient(135deg, #6c757d, #95a5a6);
    color: white;
}

.timeline-point-primary {
    border-color: #206bc4;
    background: linear-gradient(135deg, #206bc4, #3498db);
    color: white;
}

/* 时间轴内容卡片样式 */
.timeline-content .card {
    border: none;
    transition: all 0.3s ease;
    margin-bottom: 1rem;
}

.timeline-content .card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.timeline-content .card-header {
    border-bottom: 1px solid #e9ecef;
    background: linear-gradient(135deg, #f8f9fa, #ffffff);
}

/* 代码块样式优化 */
.timeline-content code {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 0.375rem;
    padding: 0.75rem;
    display: block;
    white-space: pre-wrap;
    word-break: break-all;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 0.875rem;
    line-height: 1.5;
    max-height: 300px;
    overflow-y: auto;
    position: relative;
}

/* JSON容器样式 */
.json-container {
    position: relative;
}

.json-container code {
    background: linear-gradient(135deg, #f8f9fa, #ffffff);
    border: 1px solid #dee2e6;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.json-container .btn-link {
    color: #206bc4;
    text-decoration: none;
    font-size: 0.875rem;
}

.json-container .btn-link:hover {
    color: #1a5490;
    text-decoration: underline;
}

/* 统计信息卡片样式 */
.stats-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 0.75rem;
    padding: 1.5rem;
    margin-bottom: 1rem;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.stats-card .stats-number {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.stats-card .stats-label {
    font-size: 0.875rem;
    opacity: 0.9;
}

/* 空状态样式优化 */
.empty {
    text-align: center;
    padding: 3rem 1rem;
}

.empty-img img {
    opacity: 0.6;
    margin-bottom: 1rem;
}

.empty-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: #495057;
    margin-bottom: 0.5rem;
}

.empty-subtitle {
    color: #6c757d;
    font-size: 0.875rem;
}

/* 状态徽章样式优化 */
.badge {
    font-weight: 500;
    letter-spacing: 0.025em;
    padding: 0.375rem 0.75rem;
    border-radius: 0.5rem;
}

/* 加载动画优化 */
.spinner-border {
    animation: spinner-border 0.75s linear infinite;
}

@keyframes spinner-border {
    to {
        transform: rotate(360deg);
    }
}

/* 模态框样式优化 */
.modal-content {
    border: none;
    border-radius: 0.75rem;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.15);
}

.modal-header {
    border-bottom: 1px solid #e9ecef;
    background: linear-gradient(135deg, #f8f9fa, #ffffff);
    border-radius: 0.75rem 0.75rem 0 0;
}

.modal-header.bg-gradient-primary {
    background: linear-gradient(135deg, #206bc4, #3498db);
    border-bottom: none;
}

.modal-title {
    font-weight: 600;
    color: #495057;
}

.modal-header.bg-gradient-primary .modal-title {
    color: white;
}

/* 响应式优化 */
@media (max-width: 768px) {
    .timeline-content .card-body .row {
        flex-direction: column;
    }

    .timeline-content .card-body .col-md-3,
    .timeline-content .card-body .col-md-6 {
        margin-bottom: 1rem;
    }

    .timeline-point {
        width: 2rem;
        height: 2rem;
    }

    .timeline-point-icon {
        font-size: 0.875rem;
    }
}

/* 滚动条样式 */
::-webkit-scrollbar {
    width: 6px;
    height: 6px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* 动画效果 */
.timeline-item {
    animation: fadeInUp 0.5s ease-out;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 工具提示样式 */
.tooltip {
    font-size: 0.875rem;
}

.tooltip-inner {
    background: #343a40;
    border-radius: 0.375rem;
    padding: 0.5rem 0.75rem;
}

/* 表格样式优化 */
.table {
    border-radius: 0.5rem;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.table thead th {
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    border-bottom: 2px solid #dee2e6;
    font-weight: 600;
    color: #495057;
}

/* 按钮样式优化 */
.btn {
    border-radius: 0.5rem;
    font-weight: 500;
    transition: all 0.2s ease;
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* 卡片阴影效果 */
.card {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    transition: box-shadow 0.3s ease;
}

.card:hover {
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12);
}
