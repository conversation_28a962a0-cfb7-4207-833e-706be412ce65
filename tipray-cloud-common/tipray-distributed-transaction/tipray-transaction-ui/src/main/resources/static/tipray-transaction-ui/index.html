<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta content="width=device-width, initial-scale=1.0" name="viewport">
    <title>Tipray分布式事务管理</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #f5f5f5;
            color: #333;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 1rem 2rem;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .header h1 {
            font-size: 1.5rem;
            font-weight: 600;
        }

        .container {
            max-width: 1200px;
            margin: 2rem auto;
            padding: 0 1rem;
        }

        .overview-cards {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }

        .card {
            background: white;
            border-radius: 8px;
            padding: 1.5rem;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            transition: transform 0.2s;
        }

        .card:hover {
            transform: translateY(-2px);
        }

        .card-title {
            font-size: 0.9rem;
            color: #666;
            margin-bottom: 0.5rem;
        }

        .card-value {
            font-size: 2rem;
            font-weight: bold;
            color: #333;
        }

        .card-success .card-value {
            color: #52c41a;
        }

        .card-failed .card-value {
            color: #f5222d;
        }

        .card-running .card-value {
            color: #1890ff;
        }

        .card-timeout .card-value {
            color: #faad14;
        }

        .main-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 2rem;
        }

        .section {
            background: white;
            border-radius: 8px;
            padding: 1.5rem;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .section-title {
            font-size: 1.2rem;
            font-weight: 600;
            margin-bottom: 1rem;
            color: #333;
        }

        .transaction-list {
            max-height: 400px;
            overflow-y: auto;
        }

        .transaction-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.75rem 0;
            border-bottom: 1px solid #f0f0f0;
        }

        .transaction-item:last-child {
            border-bottom: none;
        }

        .transaction-id {
            font-family: monospace;
            font-size: 0.9rem;
            color: #666;
        }

        .status-badge {
            padding: 0.25rem 0.5rem;
            border-radius: 4px;
            font-size: 0.8rem;
            font-weight: 500;
        }

        .status-success {
            background: #f6ffed;
            color: #52c41a;
        }

        .status-failed {
            background: #fff2f0;
            color: #f5222d;
        }

        .status-running {
            background: #e6f7ff;
            color: #1890ff;
        }

        .status-timeout {
            background: #fffbe6;
            color: #faad14;
        }

        .loading {
            text-align: center;
            padding: 2rem;
            color: #666;
        }

        .refresh-btn {
            background: #1890ff;
            color: white;
            border: none;
            padding: 0.5rem 1rem;
            border-radius: 4px;
            cursor: pointer;
            font-size: 0.9rem;
            margin-bottom: 1rem;
        }

        .refresh-btn:hover {
            background: #40a9ff;
        }

        @media (max-width: 768px) {
            .main-content {
                grid-template-columns: 1fr;
            }

            .overview-cards {
                grid-template-columns: repeat(2, 1fr);
            }
        }
    </style>
</head>
<body>
<div class="header">
    <h1>Tipray分布式事务管理</h1>
</div>

<div class="container">
    <!-- 概览卡片 -->
    <div class="overview-cards">
        <div class="card">
            <div class="card-title">总事务数</div>
            <div class="card-value" id="totalTransactions">-</div>
        </div>
        <div class="card card-running">
            <div class="card-title">执行中</div>
            <div class="card-value" id="runningTransactions">-</div>
        </div>
        <div class="card card-success">
            <div class="card-title">成功</div>
            <div class="card-value" id="successTransactions">-</div>
        </div>
        <div class="card card-failed">
            <div class="card-title">失败</div>
            <div class="card-value" id="failedTransactions">-</div>
        </div>
    </div>

    <!-- 主要内容 -->
    <div class="main-content">
        <!-- 最近事务 -->
        <div class="section">
            <div class="section-title">最近事务</div>
            <button class="refresh-btn" onclick="loadTransactions()">刷新</button>
            <div class="transaction-list" id="transactionList">
                <div class="loading">加载中...</div>
            </div>
        </div>

        <!-- 统计信息 -->
        <div class="section">
            <div class="section-title">统计信息</div>
            <div id="statisticsContent">
                <div class="loading">加载中...</div>
            </div>
        </div>
    </div>
</div>

<script>
    // API基础路径
    const API_BASE = '/api/transaction-ui';

    // 加载概览数据
    async function loadOverview() {
        try {
            const response = await fetch(`${API_BASE}/overview`);
            const result = await response.json();

            if (result.success) {
                const data = result.data;
                document.getElementById('totalTransactions').textContent = data.totalTransactions || 0;
                document.getElementById('runningTransactions').textContent = data.runningTransactions || 0;
                document.getElementById('successTransactions').textContent = data.successTransactions || 0;
                document.getElementById('failedTransactions').textContent = data.failedTransactions || 0;
            }
        } catch (error) {
            console.error('加载概览数据失败:', error);
        }
    }

    // 加载事务列表
    async function loadTransactions() {
        try {
            const response = await fetch(`${API_BASE}/transactions?page=1&size=10`);
            const result = await response.json();

            const listElement = document.getElementById('transactionList');

            if (result.success && result.data.data.length > 0) {
                listElement.innerHTML = result.data.data.map(transaction => `
                        <div class="transaction-item">
                            <div>
                                <div class="transaction-id">${transaction.transactionId}</div>
                                <div style="font-size: 0.8rem; color: #999;">${transaction.createTime}</div>
                            </div>
                            <div class="status-badge status-${getStatusClass(transaction.status)}">
                                ${transaction.status}
                            </div>
                        </div>
                    `).join('');
            } else {
                listElement.innerHTML = '<div class="loading">暂无数据</div>';
            }
        } catch (error) {
            console.error('加载事务列表失败:', error);
            document.getElementById('transactionList').innerHTML = '<div class="loading">加载失败</div>';
        }
    }

    // 加载统计信息
    async function loadStatistics() {
        try {
            const response = await fetch(`${API_BASE}/statistics`);
            const result = await response.json();

            const contentElement = document.getElementById('statisticsContent');

            if (result.success) {
                const stats = result.data;
                contentElement.innerHTML = `
                        <div style="margin-bottom: 1rem;">
                            <strong>成功率:</strong> ${(stats.overview?.successRate || 0).toFixed(2)}%
                        </div>
                        <div style="margin-bottom: 1rem;">
                            <strong>平均耗时:</strong> ${(stats.overview?.avgDuration || 0).toFixed(0)}ms
                        </div>
                        <div>
                            <strong>今日事务:</strong> ${stats.overview?.todayTransactions || 0}
                        </div>
                    `;
            } else {
                contentElement.innerHTML = '<div class="loading">暂无统计数据</div>';
            }
        } catch (error) {
            console.error('加载统计信息失败:', error);
            document.getElementById('statisticsContent').innerHTML = '<div class="loading">加载失败</div>';
        }
    }

    // 获取状态样式类
    function getStatusClass(status) {
        switch (status?.toLowerCase()) {
            case 'success':
                return 'success';
            case 'failed':
                return 'failed';
            case 'executing':
                return 'running';
            case 'timeout':
                return 'timeout';
            default:
                return 'running';
        }
    }

    // 初始化页面
    function init() {
        loadOverview();
        loadTransactions();
        loadStatistics();

        // 设置自动刷新
        setInterval(() => {
            loadOverview();
            loadTransactions();
        }, 5000);
    }

    // 页面加载完成后初始化
    document.addEventListener('DOMContentLoaded', init);
</script>
</body>
</html>
