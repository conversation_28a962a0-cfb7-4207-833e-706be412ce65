<!-- Saga模式页面模板 -->
<div class="row row-deck row-cards">
    <!-- Saga模式概览 -->
    <div class="col-12">
        <div class="row row-cards">
            <div class="col-sm-6 col-lg-3">
                <div class="card card-sm">
                    <div class="card-body">
                        <div class="row align-items-center">
                            <div class="col-auto">
                                <span class="bg-purple text-white avatar">
                                    <!-- Saga模式图标 -->
                                    <svg class="icon" fill="none" height="24" stroke="currentColor"
                                         stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                         viewBox="0 0 24 24"
                                         width="24" xmlns="http://www.w3.org/2000/svg">
                                        <path d="M0 0h24v24H0z" fill="none" stroke="none"/>
                                        <path d="M5 7l5 5l-5 5"/>
                                        <path d="M13 7l5 5l-5 5"/>
                                    </svg>
                                </span>
                            </div>
                            <div class="col">
                                <div class="font-weight-medium" id="sagaTotalTransactions">
                                    0
                                </div>
                                <div class="text-muted">
                                    Saga事务总数
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-sm-6 col-lg-3">
                <div class="card card-sm">
                    <div class="card-body">
                        <div class="row align-items-center">
                            <div class="col-auto">
                                <span class="bg-success text-white avatar">
                                    <!-- 成功图标 -->
                                    <svg class="icon" fill="none" height="24" stroke="currentColor"
                                         stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                         viewBox="0 0 24 24"
                                         width="24" xmlns="http://www.w3.org/2000/svg">
                                        <path d="M0 0h24v24H0z" fill="none" stroke="none"/>
                                        <path d="M5 12l5 5l10 -10"/>
                                    </svg>
                                </span>
                            </div>
                            <div class="col">
                                <div class="font-weight-medium" id="sagaSuccessTransactions">
                                    0
                                </div>
                                <div class="text-muted">
                                    成功完成
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-sm-6 col-lg-3">
                <div class="card card-sm">
                    <div class="card-body">
                        <div class="row align-items-center">
                            <div class="col-auto">
                                <span class="bg-danger text-white avatar">
                                    <!-- 补偿图标 -->
                                    <svg class="icon" fill="none" height="24" stroke="currentColor"
                                         stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                         viewBox="0 0 24 24"
                                         width="24" xmlns="http://www.w3.org/2000/svg">
                                        <path d="M0 0h24v24H0z" fill="none" stroke="none"/>
                                        <path d="M9 14l-4 -4l4 -4"/>
                                        <path d="M5 10h11a4 4 0 1 1 0 8h-1"/>
                                    </svg>
                                </span>
                            </div>
                            <div class="col">
                                <div class="font-weight-medium" id="sagaCompensatedTransactions">
                                    0
                                </div>
                                <div class="text-muted">
                                    补偿事务
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-sm-6 col-lg-3">
                <div class="card card-sm">
                    <div class="card-body">
                        <div class="row align-items-center">
                            <div class="col-auto">
                                <span class="bg-warning text-white avatar">
                                    <!-- 步骤图标 -->
                                    <svg class="icon" fill="none" height="24" stroke="currentColor"
                                         stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                         viewBox="0 0 24 24"
                                         width="24" xmlns="http://www.w3.org/2000/svg">
                                        <path d="M0 0h24v24H0z" fill="none" stroke="none"/>
                                        <circle cx="12" cy="12" r="9"/>
                                        <path d="M8 12l2 2l4 -4"/>
                                    </svg>
                                </span>
                            </div>
                            <div class="col">
                                <div class="font-weight-medium" id="sagaTotalSteps">
                                    0
                                </div>
                                <div class="text-muted">
                                    总步骤数
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Saga事务列表 -->
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">Saga事务列表</h3>
                <div class="card-actions">
                    <div class="btn-list">
                        <select class="form-select form-select-sm" id="sagaStatusFilter">
                            <option value="">全部状态</option>
                            <option value="INIT">初始化</option>
                            <option value="PENDING">等待中</option>
                            <option value="EXECUTING">执行中</option>
                            <option value="SUCCESS">成功</option>
                            <option value="FAILED">失败</option>
                            <option value="COMPENSATING">补偿中</option>
                            <option value="COMPENSATED">已补偿</option>
                        </select>
                        <a class="btn btn-outline-primary btn-sm" href="#" id="refreshSagaList">
                            <!-- 刷新图标 -->
                            <svg class="icon" fill="none" height="24" stroke="currentColor"
                                 stroke-linecap="round" stroke-linejoin="round" stroke-width="2" viewBox="0 0 24 24"
                                 width="24" xmlns="http://www.w3.org/2000/svg">
                                <path d="M0 0h24v24H0z" fill="none" stroke="none"/>
                                <path d="M20 11a8.1 8.1 0 0 0 -15.5 -2m-.5 -4v4h4"/>
                                <path d="M4 13a8.1 8.1 0 0 0 15.5 2m.5 4v-4h-4"/>
                            </svg>
                            刷新
                        </a>
                    </div>
                </div>
            </div>
            <div class="table-responsive">
                <table class="table table-vcenter card-table" id="sagaTransactionTable">
                    <thead>
                    <tr>
                        <th>事务ID</th>
                        <th>状态</th>
                        <th>服务名称</th>
                        <th>开始时间</th>
                        <th>持续时间</th>
                        <th>步骤进度</th>
                        <th>执行历史</th>
                        <th class="w-1">操作</th>
                    </tr>
                    </thead>
                    <tbody id="sagaTransactionTableBody">
                    <!-- 示例数据 -->
                    <tr>
                        <td>
                            <div class="d-flex py-1 align-items-center">
                                <span class="avatar me-2 bg-purple text-white">SG</span>
                                <div class="flex-fill">
                                    <div class="font-weight-medium">TXN-SAGA-20240701-001</div>
                                    <div class="text-muted">Saga事务</div>
                                </div>
                            </div>
                        </td>
                        <td>
                            <span class="badge bg-success">已完成</span>
                        </td>
                        <td class="text-muted">
                            支付服务
                        </td>
                        <td class="text-muted">
                            2024-07-01 10:32:20
                        </td>
                        <td class="text-muted">
                            2.5s
                        </td>
                        <td>
                            <div class="progress progress-sm">
                                <div aria-valuemax="100" aria-valuemin="0" aria-valuenow="100"
                                     class="progress-bar bg-success" role="progressbar" style="width: 100%">
                                    <span class="visually-hidden">100% Complete</span>
                                </div>
                            </div>
                            <small class="text-muted">5/5 步骤</small>
                        </td>
                        <td>
                            <a class="btn btn-sm btn-outline-info" data-bs-target="#sagaStepHistoryModal"
                               data-bs-toggle="modal"
                               data-transaction-id="TXN-SAGA-20240701-001" href="#">
                                查看
                            </a>
                        </td>
                        <td>
                            <div class="btn-list flex-nowrap">
                                <a class="btn btn-sm btn-outline-primary" data-bs-target="#sagaTransactionDetailModal"
                                   data-bs-toggle="modal"
                                   data-transaction-id="TXN-SAGA-20240701-001"
                                   href="#">
                                    详情
                                </a>
                                <div class="dropdown">
                                    <button class="btn btn-sm btn-outline-secondary dropdown-toggle align-text-top"
                                            data-bs-toggle="dropdown">
                                        操作
                                    </button>
                                    <div class="dropdown-menu dropdown-menu-end">
                                        <a class="dropdown-item" data-action="retry" href="#">
                                            <!-- 重试图标 -->
                                            <svg class="icon dropdown-item-icon" fill="none"
                                                 height="24" stroke="currentColor" stroke-linecap="round"
                                                 stroke-linejoin="round"
                                                 stroke-width="2" viewBox="0 0 24 24" width="24"
                                                 xmlns="http://www.w3.org/2000/svg">
                                                <path d="M0 0h24v24H0z" fill="none" stroke="none"/>
                                                <path d="M20 11a8.1 8.1 0 0 0 -15.5 -2m-.5 -4v4h4"/>
                                                <path d="M4 13a8.1 8.1 0 0 0 15.5 2m.5 4v-4h-4"/>
                                            </svg>
                                            重试
                                        </a>
                                        <a class="dropdown-item" data-action="compensate" href="#">
                                            <!-- 补偿图标 -->
                                            <svg class="icon dropdown-item-icon" fill="none"
                                                 height="24" stroke="currentColor" stroke-linecap="round"
                                                 stroke-linejoin="round"
                                                 stroke-width="2" viewBox="0 0 24 24" width="24"
                                                 xmlns="http://www.w3.org/2000/svg">
                                                <path d="M0 0h24v24H0z" fill="none" stroke="none"/>
                                                <path d="M9 14l-4 -4l4 -4"/>
                                                <path d="M5 10h11a4 4 0 1 1 0 8h-1"/>
                                            </svg>
                                            手动补偿
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </td>
                    </tr>

                    <tr>
                        <td>
                            <div class="d-flex py-1 align-items-center">
                                <span class="avatar me-2 bg-purple text-white">SG</span>
                                <div class="flex-fill">
                                    <div class="font-weight-medium">TXN-SAGA-20240701-002</div>
                                    <div class="text-muted">Saga事务</div>
                                </div>
                            </div>
                        </td>
                        <td>
                            <span class="badge bg-warning">执行中</span>
                        </td>
                        <td class="text-muted">
                            订单服务
                        </td>
                        <td class="text-muted">
                            2024-07-01 10:35:10
                        </td>
                        <td class="text-muted">
                            45s
                        </td>
                        <td>
                            <div class="progress progress-sm">
                                <div aria-valuemax="100" aria-valuemin="0" aria-valuenow="60"
                                     class="progress-bar bg-warning" role="progressbar" style="width: 60%">
                                    <span class="visually-hidden">60% Complete</span>
                                </div>
                            </div>
                            <small class="text-muted">3/5 步骤</small>
                        </td>
                        <td>
                            <a class="btn btn-sm btn-outline-info" data-bs-target="#sagaStepHistoryModal"
                               data-bs-toggle="modal"
                               data-transaction-id="TXN-SAGA-20240701-002" href="#">
                                查看
                            </a>
                        </td>
                        <td>
                            <div class="btn-list flex-nowrap">
                                <a class="btn btn-sm btn-outline-primary" data-bs-target="#sagaTransactionDetailModal"
                                   data-bs-toggle="modal"
                                   data-transaction-id="TXN-SAGA-20240701-002"
                                   href="#">
                                    详情
                                </a>
                                <div class="dropdown">
                                    <button class="btn btn-sm btn-outline-secondary dropdown-toggle align-text-top"
                                            data-bs-toggle="dropdown">
                                        操作
                                    </button>
                                    <div class="dropdown-menu dropdown-menu-end">
                                        <a class="dropdown-item" data-action="cancel" href="#">
                                            <!-- 取消图标 -->
                                            <svg class="icon dropdown-item-icon" fill="none"
                                                 height="24" stroke="currentColor" stroke-linecap="round"
                                                 stroke-linejoin="round"
                                                 stroke-width="2" viewBox="0 0 24 24" width="24"
                                                 xmlns="http://www.w3.org/2000/svg">
                                                <path d="M0 0h24v24H0z" fill="none" stroke="none"/>
                                                <circle cx="12" cy="12" r="9"/>
                                                <path d="M9 9l6 6"/>
                                                <path d="M15 9l-6 6"/>
                                            </svg>
                                            取消执行
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </td>
                    </tr>
                    </tbody>
                </table>
            </div>
            <div class="card-footer d-flex align-items-center">
                <p class="m-0 text-muted" id="sagaPaginationInfo">显示第 1-20 条，共 2 条记录</p>
                <ul class="pagination m-0 ms-auto" id="sagaTransactionPagination">
                    <li class="page-item disabled">
                        <a class="page-link" href="#" tabindex="-1">
                            <!-- 上一页图标 -->
                            <svg class="icon" fill="none" height="24" stroke="currentColor"
                                 stroke-linecap="round" stroke-linejoin="round" stroke-width="2" viewBox="0 0 24 24"
                                 width="24" xmlns="http://www.w3.org/2000/svg">
                                <polyline points="15,18 9,12 15,6"/>
                            </svg>
                            上一页
                        </a>
                    </li>
                    <li class="page-item active"><a class="page-link" href="#">1</a></li>
                    <li class="page-item disabled">
                        <a class="page-link" href="#">
                            下一页
                            <!-- 下一页图标 -->
                            <svg class="icon" fill="none" height="24" stroke="currentColor"
                                 stroke-linecap="round" stroke-linejoin="round" stroke-width="2" viewBox="0 0 24 24"
                                 width="24" xmlns="http://www.w3.org/2000/svg">
                                <polyline points="9,18 15,12 9,6"/>
                            </svg>
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </div>
</div>

<!-- Saga步骤执行历史模态框 -->
<div aria-hidden="true" class="modal modal-blur fade" id="sagaStepHistoryModal" role="dialog" tabindex="-1">
    <div class="modal-dialog modal-lg modal-dialog-centered" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Saga步骤执行历史</h5>
                <button aria-label="Close" class="btn-close" data-bs-dismiss="modal" type="button"></button>
            </div>
            <div class="modal-body">
                <div id="sagaStepHistoryContent">
                    <!-- Saga步骤历史内容将在这里动态加载 -->
                    <div class="text-center py-4">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">加载中...</span>
                        </div>
                        <div class="mt-2">正在加载步骤执行历史...</div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <a class="btn btn-link link-secondary" data-bs-dismiss="modal" href="#">
                    关闭
                </a>
                <a class="btn btn-primary ms-auto" href="#" id="downloadStepHistory">
                    <!-- 下载图标 -->
                    <svg class="icon" fill="none" height="24" stroke="currentColor" stroke-linecap="round"
                         stroke-linejoin="round" stroke-width="2" viewBox="0 0 24 24" width="24"
                         xmlns="http://www.w3.org/2000/svg">
                        <path d="M0 0h24v24H0z" fill="none" stroke="none"/>
                        <path d="M4 17v2a2 2 0 0 0 2 2h12a2 2 0 0 0 2 -2v-2"/>
                        <polyline points="7,11 12,16 17,11"/>
                        <line x1="12" x2="12" y1="4" y2="16"/>
                    </svg>
                    导出
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Saga事务详情模态框 -->
<div aria-hidden="true" class="modal modal-blur fade" id="sagaTransactionDetailModal" role="dialog" tabindex="-1">
    <div class="modal-dialog modal-xl modal-dialog-centered" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Saga事务详情</h5>
                <button aria-label="Close" class="btn-close" data-bs-dismiss="modal" type="button"></button>
            </div>
            <div class="modal-body">
                <div id="sagaTransactionDetailContent">
                    <!-- Saga事务详情内容将在这里动态加载 -->
                </div>
            </div>
            <div class="modal-footer">
                <a class="btn btn-link link-secondary" data-bs-dismiss="modal" href="#">
                    关闭
                </a>
                <a class="btn btn-primary ms-auto" href="#">
                    <!-- 刷新图标 -->
                    <svg class="icon" fill="none" height="24" stroke="currentColor" stroke-linecap="round"
                         stroke-linejoin="round" stroke-width="2" viewBox="0 0 24 24" width="24"
                         xmlns="http://www.w3.org/2000/svg">
                        <path d="M0 0h24v24H0z" fill="none" stroke="none"/>
                        <path d="M20 11a8.1 8.1 0 0 0 -15.5 -2m-.5 -4v4h4"/>
                        <path d="M4 13a8.1 8.1 0 0 0 15.5 2m.5 4v-4h-4"/>
                    </svg>
                    刷新
                </a>
            </div>
        </div>
    </div>
</div>
