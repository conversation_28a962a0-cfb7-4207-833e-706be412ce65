<!-- 系统设置页面模板 -->
<div class="row row-deck row-cards">
    <!-- 基本配置 -->
    <div class="col-lg-6">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">基本配置</h3>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <label class="form-label">系统名称</label>
                    <input class="form-control" id="systemName" type="text" value="Tipray分布式事务监控中心">
                </div>
                <div class="mb-3">
                    <label class="form-label">系统版本</label>
                    <input class="form-control" id="systemVersion" readonly type="text" value="1.0.0">
                </div>
                <div class="mb-3">
                    <label class="form-label">管理员邮箱</label>
                    <input class="form-control" id="adminEmail" placeholder="<EMAIL>" type="email">
                </div>
                <div class="mb-3">
                    <label class="form-label">时区设置</label>
                    <select class="form-select" id="timezone">
                        <option selected value="Asia/Shanghai">Asia/Shanghai (UTC+8)</option>
                        <option value="UTC">UTC (UTC+0)</option>
                        <option value="America/New_York">America/New_York (UTC-5)</option>
                        <option value="Europe/London">Europe/London (UTC+0)</option>
                    </select>
                </div>
                <div class="mb-3">
                    <label class="form-label">语言设置</label>
                    <select class="form-select" id="language">
                        <option selected value="zh-CN">简体中文</option>
                        <option value="en-US">English</option>
                        <option value="ja-JP">日本語</option>
                    </select>
                </div>
            </div>
            <div class="card-footer">
                <button class="btn btn-primary" id="saveBasicSettings">
                    <!-- 保存图标 -->
                    <svg class="icon" fill="none" height="24" stroke="currentColor" stroke-linecap="round"
                         stroke-linejoin="round" stroke-width="2" viewBox="0 0 24 24" width="24"
                         xmlns="http://www.w3.org/2000/svg">
                        <path d="M0 0h24v24H0z" fill="none" stroke="none"/>
                        <path d="M6 4h10l4 4v10a2 2 0 0 1 -2 2h-12a2 2 0 0 1 -2 -2v-12a2 2 0 0 1 2 -2"/>
                        <circle cx="12" cy="14" r="2"/>
                        <polyline points="14,4 14,8 8,8 8,4"/>
                    </svg>
                    保存基本配置
                </button>
            </div>
        </div>
    </div>

    <!-- 监控配置 -->
    <div class="col-lg-6">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">监控配置</h3>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <label class="form-label">数据刷新间隔 (秒)</label>
                    <input class="form-control" id="refreshInterval" max="300" min="1" type="number" value="5">
                    <small class="form-hint">设置页面数据自动刷新的时间间隔</small>
                </div>
                <div class="mb-3">
                    <label class="form-label">数据保留天数</label>
                    <input class="form-control" id="dataRetentionDays" max="365" min="1" type="number" value="30">
                    <small class="form-hint">超过此天数的历史数据将被自动清理</small>
                </div>
                <div class="mb-3">
                    <label class="form-label">最大显示记录数</label>
                    <input class="form-control" id="maxDisplayRecords" max="10000" min="100" type="number" value="1000">
                    <small class="form-hint">单页最多显示的记录数量</small>
                </div>
                <div class="mb-3">
                    <div class="form-check form-switch">
                        <input checked class="form-check-input" id="enableRealtime" type="checkbox">
                        <label class="form-check-label" for="enableRealtime">
                            启用实时监控
                        </label>
                    </div>
                    <small class="form-hint">是否启用实时事务流监控</small>
                </div>
                <div class="mb-3">
                    <div class="form-check form-switch">
                        <input checked class="form-check-input" id="enableNotifications" type="checkbox">
                        <label class="form-check-label" for="enableNotifications">
                            启用异常通知
                        </label>
                    </div>
                    <small class="form-hint">发生异常时是否发送通知</small>
                </div>
            </div>
            <div class="card-footer">
                <button class="btn btn-primary" id="saveMonitoringSettings">
                    <!-- 保存图标 -->
                    <svg class="icon" fill="none" height="24" stroke="currentColor" stroke-linecap="round"
                         stroke-linejoin="round" stroke-width="2" viewBox="0 0 24 24" width="24"
                         xmlns="http://www.w3.org/2000/svg">
                        <path d="M0 0h24v24H0z" fill="none" stroke="none"/>
                        <path d="M6 4h10l4 4v10a2 2 0 0 1 -2 2h-12a2 2 0 0 1 -2 -2v-12a2 2 0 0 1 2 -2"/>
                        <circle cx="12" cy="14" r="2"/>
                        <polyline points="14,4 14,8 8,8 8,4"/>
                    </svg>
                    保存监控配置
                </button>
            </div>
        </div>
    </div>

    <!-- 事务配置 -->
    <div class="col-lg-6">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">事务配置</h3>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <label class="form-label">默认事务超时时间 (秒)</label>
                    <input class="form-control" id="defaultTransactionTimeout" max="3600" min="1" type="number"
                           value="30">
                    <small class="form-hint">新创建事务的默认超时时间</small>
                </div>
                <div class="mb-3">
                    <label class="form-label">最大重试次数</label>
                    <input class="form-control" id="maxRetryCount" max="10" min="0" type="number" value="3">
                    <small class="form-hint">事务失败时的最大重试次数</small>
                </div>
                <div class="mb-3">
                    <label class="form-label">重试间隔 (毫秒)</label>
                    <input class="form-control" id="retryInterval" max="60000" min="100" type="number" value="1000">
                    <small class="form-hint">重试之间的等待时间</small>
                </div>
                <div class="mb-3">
                    <div class="form-check form-switch">
                        <input checked class="form-check-input" id="enableAutoRollback" type="checkbox">
                        <label class="form-check-label" for="enableAutoRollback">
                            启用自动回滚
                        </label>
                    </div>
                    <small class="form-hint">事务失败时是否自动执行回滚</small>
                </div>
                <div class="mb-3">
                    <div class="form-check form-switch">
                        <input checked class="form-check-input" id="enableTransactionLog" type="checkbox">
                        <label class="form-check-label" for="enableTransactionLog">
                            启用事务日志
                        </label>
                    </div>
                    <small class="form-hint">是否记录详细的事务执行日志</small>
                </div>
            </div>
            <div class="card-footer">
                <button class="btn btn-primary" id="saveTransactionSettings">
                    <!-- 保存图标 -->
                    <svg class="icon" fill="none" height="24" stroke="currentColor" stroke-linecap="round"
                         stroke-linejoin="round" stroke-width="2" viewBox="0 0 24 24" width="24"
                         xmlns="http://www.w3.org/2000/svg">
                        <path d="M0 0h24v24H0z" fill="none" stroke="none"/>
                        <path d="M6 4h10l4 4v10a2 2 0 0 1 -2 2h-12a2 2 0 0 1 -2 -2v-12a2 2 0 0 1 2 -2"/>
                        <circle cx="12" cy="14" r="2"/>
                        <polyline points="14,4 14,8 8,8 8,4"/>
                    </svg>
                    保存事务配置
                </button>
            </div>
        </div>
    </div>

    <!-- 安全配置 -->
    <div class="col-lg-6">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">安全配置</h3>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <label class="form-label">会话超时时间 (分钟)</label>
                    <input class="form-control" id="sessionTimeout" max="480" min="5" type="number" value="30">
                    <small class="form-hint">用户会话的超时时间</small>
                </div>
                <div class="mb-3">
                    <label class="form-label">API访问密钥</label>
                    <div class="input-group">
                        <input class="form-control" id="apiSecretKey" type="password" value="tipray-secret-key-2024">
                        <button class="btn btn-outline-secondary" id="toggleApiKey" type="button">
                            <!-- 显示图标 -->
                            <svg class="icon" fill="none" height="24" stroke="currentColor"
                                 stroke-linecap="round" stroke-linejoin="round" stroke-width="2" viewBox="0 0 24 24"
                                 width="24" xmlns="http://www.w3.org/2000/svg">
                                <path d="M0 0h24v24H0z" fill="none" stroke="none"/>
                                <circle cx="12" cy="12" r="2"/>
                                <path d="M12 1v6m0 6v6"/>
                                <path d="M21 12h-6m-6 0h-6"/>
                            </svg>
                        </button>
                        <button class="btn btn-outline-primary" id="generateApiKey" type="button">
                            重新生成
                        </button>
                    </div>
                    <small class="form-hint">用于API访问验证的密钥</small>
                </div>
                <div class="mb-3">
                    <div class="form-check form-switch">
                        <input class="form-check-input" id="enableIpWhitelist" type="checkbox">
                        <label class="form-check-label" for="enableIpWhitelist">
                            启用IP白名单
                        </label>
                    </div>
                    <small class="form-hint">是否启用IP访问白名单限制</small>
                </div>
                <div class="mb-3" id="ipWhitelistContainer" style="display: none;">
                    <label class="form-label">IP白名单</label>
                    <textarea class="form-control" id="ipWhitelist"
                              placeholder="每行一个IP地址或IP段，例如：&#10;*************&#10;***********/24"
                              rows="3"></textarea>
                    <small class="form-hint">只有白名单中的IP地址才能访问系统</small>
                </div>
                <div class="mb-3">
                    <div class="form-check form-switch">
                        <input checked class="form-check-input" id="enableAuditLog" type="checkbox">
                        <label class="form-check-label" for="enableAuditLog">
                            启用审计日志
                        </label>
                    </div>
                    <small class="form-hint">是否记录用户操作的审计日志</small>
                </div>
            </div>
            <div class="card-footer">
                <button class="btn btn-primary" id="saveSecuritySettings">
                    <!-- 保存图标 -->
                    <svg class="icon" fill="none" height="24" stroke="currentColor" stroke-linecap="round"
                         stroke-linejoin="round" stroke-width="2" viewBox="0 0 24 24" width="24"
                         xmlns="http://www.w3.org/2000/svg">
                        <path d="M0 0h24v24H0z" fill="none" stroke="none"/>
                        <path d="M6 4h10l4 4v10a2 2 0 0 1 -2 2h-12a2 2 0 0 1 -2 -2v-12a2 2 0 0 1 2 -2"/>
                        <circle cx="12" cy="14" r="2"/>
                        <polyline points="14,4 14,8 8,8 8,4"/>
                    </svg>
                    保存安全配置
                </button>
            </div>
        </div>
    </div>

    <!-- 系统信息 -->
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">系统信息</h3>
                <div class="card-actions">
                    <a class="btn btn-outline-primary btn-sm" href="#" id="refreshSystemInfo">
                        <!-- 刷新图标 -->
                        <svg class="icon" fill="none" height="24" stroke="currentColor" stroke-linecap="round"
                             stroke-linejoin="round" stroke-width="2" viewBox="0 0 24 24" width="24"
                             xmlns="http://www.w3.org/2000/svg">
                            <path d="M0 0h24v24H0z" fill="none" stroke="none"/>
                            <path d="M20 11a8.1 8.1 0 0 0 -15.5 -2m-.5 -4v4h4"/>
                            <path d="M4 13a8.1 8.1 0 0 0 15.5 2m.5 4v-4h-4"/>
                        </svg>
                        刷新
                    </a>
                </div>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <dl class="row">
                            <dt class="col-5">系统版本:</dt>
                            <dd class="col-7" id="infoSystemVersion">1.0.0</dd>
                            <dt class="col-5">Java版本:</dt>
                            <dd class="col-7" id="infoJavaVersion">1.8.0_301</dd>
                            <dt class="col-5">Spring Boot版本:</dt>
                            <dd class="col-7" id="infoSpringBootVersion">2.7.0</dd>
                            <dt class="col-5">启动时间:</dt>
                            <dd class="col-7" id="infoStartTime">2024-07-01 09:00:00</dd>
                            <dt class="col-5">运行时长:</dt>
                            <dd class="col-7" id="infoUptime">2小时30分钟</dd>
                        </dl>
                    </div>
                    <div class="col-md-6">
                        <dl class="row">
                            <dt class="col-5">CPU使用率:</dt>
                            <dd class="col-7">
                                <div class="progress progress-sm">
                                    <div aria-valuemax="100" aria-valuemin="0" aria-valuenow="25" class="progress-bar"
                                         role="progressbar" style="width: 25%">
                                        <span class="visually-hidden">25% Complete</span>
                                    </div>
                                </div>
                                <small class="text-muted">25%</small>
                            </dd>
                            <dt class="col-5">内存使用:</dt>
                            <dd class="col-7">
                                <div class="progress progress-sm">
                                    <div aria-valuemax="100" aria-valuemin="0" aria-valuenow="60"
                                         class="progress-bar bg-warning" role="progressbar" style="width: 60%">
                                        <span class="visually-hidden">60% Complete</span>
                                    </div>
                                </div>
                                <small class="text-muted">1.2GB / 2GB</small>
                            </dd>
                            <dt class="col-5">磁盘使用:</dt>
                            <dd class="col-7">
                                <div class="progress progress-sm">
                                    <div aria-valuemax="100" aria-valuemin="0" aria-valuenow="40"
                                         class="progress-bar bg-success" role="progressbar" style="width: 40%">
                                        <span class="visually-hidden">40% Complete</span>
                                    </div>
                                </div>
                                <small class="text-muted">20GB / 50GB</small>
                            </dd>
                            <dt class="col-5">数据库连接:</dt>
                            <dd class="col-7">
                                <span class="badge bg-success">正常</span>
                                <small class="text-muted">5/10 活跃连接</small>
                            </dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 操作按钮 -->
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">系统操作</h3>
            </div>
            <div class="card-body">
                <div class="btn-list">
                    <button class="btn btn-outline-primary" id="exportSettings">
                        <!-- 导出图标 -->
                        <svg class="icon" fill="none" height="24" stroke="currentColor" stroke-linecap="round"
                             stroke-linejoin="round" stroke-width="2" viewBox="0 0 24 24" width="24"
                             xmlns="http://www.w3.org/2000/svg">
                            <path d="M0 0h24v24H0z" fill="none" stroke="none"/>
                            <path d="M14 3v4a1 1 0 0 0 1 1h4"/>
                            <path d="M17 21h-10a2 2 0 0 1 -2 -2v-14a2 2 0 0 1 2 -2h7l5 5v11a2 2 0 0 1 -2 2z"/>
                            <line x1="9" x2="10" y1="9" y2="9"/>
                            <line x1="9" x2="15" y1="13" y2="13"/>
                            <line x1="9" x2="15" y1="17" y2="17"/>
                        </svg>
                        导出配置
                    </button>
                    <button class="btn btn-outline-secondary" id="importSettings">
                        <!-- 导入图标 -->
                        <svg class="icon" fill="none" height="24" stroke="currentColor" stroke-linecap="round"
                             stroke-linejoin="round" stroke-width="2" viewBox="0 0 24 24" width="24"
                             xmlns="http://www.w3.org/2000/svg">
                            <path d="M0 0h24v24H0z" fill="none" stroke="none"/>
                            <path d="M14 3v4a1 1 0 0 0 1 1h4"/>
                            <path d="M5 13v-8a2 2 0 0 1 2 -2h7l5 5v11a2 2 0 0 1 -2 2h-5.5"/>
                            <circle cx="6" cy="20" r="2"/>
                            <path d="M8 20l-2 -2l-2 2"/>
                            <path d="M6 18v2"/>
                        </svg>
                        导入配置
                    </button>
                    <button class="btn btn-outline-warning" id="resetSettings">
                        <!-- 重置图标 -->
                        <svg class="icon" fill="none" height="24" stroke="currentColor" stroke-linecap="round"
                             stroke-linejoin="round" stroke-width="2" viewBox="0 0 24 24" width="24"
                             xmlns="http://www.w3.org/2000/svg">
                            <path d="M0 0h24v24H0z" fill="none" stroke="none"/>
                            <path d="M20 11a8.1 8.1 0 0 0 -15.5 -2m-.5 -4v4h4"/>
                            <path d="M4 13a8.1 8.1 0 0 0 15.5 2m.5 4v-4h-4"/>
                        </svg>
                        重置为默认
                    </button>
                    <button class="btn btn-outline-info" id="clearCache">
                        <!-- 清理图标 -->
                        <svg class="icon" fill="none" height="24" stroke="currentColor" stroke-linecap="round"
                             stroke-linejoin="round" stroke-width="2" viewBox="0 0 24 24" width="24"
                             xmlns="http://www.w3.org/2000/svg">
                            <path d="M0 0h24v24H0z" fill="none" stroke="none"/>
                            <line x1="4" x2="20" y1="7" y2="7"/>
                            <line x1="10" x2="10" y1="11" y2="17"/>
                            <line x1="14" x2="14" y1="11" y2="17"/>
                            <path d="M5 7l1 12a2 2 0 0 0 2 2h8a2 2 0 0 0 2 -2l1 -12"/>
                            <path d="M9 7v-3a1 1 0 0 1 1 -1h4a1 1 0 0 1 1 1v3"/>
                        </svg>
                        清理缓存
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 文件导入模态框 -->
<div aria-hidden="true" class="modal modal-blur fade" id="importModal" role="dialog" tabindex="-1">
    <div class="modal-dialog modal-sm modal-dialog-centered" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">导入配置文件</h5>
                <button aria-label="Close" class="btn-close" data-bs-dismiss="modal" type="button"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <label class="form-label">选择配置文件</label>
                    <input accept=".json,.xml,.properties" class="form-control" id="configFile" type="file">
                    <small class="form-hint">支持JSON、XML、Properties格式</small>
                </div>
            </div>
            <div class="modal-footer">
                <a class="btn btn-link link-secondary" data-bs-dismiss="modal" href="#">
                    取消
                </a>
                <a class="btn btn-primary ms-auto" href="#" id="confirmImport">
                    导入
                </a>
            </div>
        </div>
    </div>
</div>
