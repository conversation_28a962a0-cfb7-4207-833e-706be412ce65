<!-- 大气的统计分析页面模板 -->
<div class="row g-4 mb-4">
    <!-- 时间范围选择 -->
    <div class="col-12">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-transparent border-0">
                <div class="row align-items-center">
                    <div class="col">
                        <h3 class="card-title mb-0 fw-bold">统计分析</h3>
                        <p class="text-muted mb-0">深度分析事务执行情况和性能指标</p>
                    </div>
                    <div class="col-auto">
                        <div class="btn-list">
                            <div class="btn-group" role="group">
                                <input checked class="btn-check" id="rangeToday" name="timeRange" type="radio">
                                <label class="btn btn-outline-primary" for="rangeToday">今日</label>

                                <input class="btn-check" id="rangeWeek" name="timeRange" type="radio">
                                <label class="btn btn-outline-primary" for="rangeWeek">本周</label>

                                <input class="btn-check" id="rangeMonth" name="timeRange" type="radio">
                                <label class="btn btn-outline-primary" for="rangeMonth">本月</label>

                                <input class="btn-check" id="rangeCustom" name="timeRange" type="radio">
                                <label class="btn btn-outline-primary" for="rangeCustom">自定义</label>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="card-body" id="customRangeInputs" style="display: none;">
                <div class="row g-3">
                    <div class="col-md-4">
                        <label class="form-label fw-medium">开始时间</label>
                        <input class="form-control" id="customStartTime" type="datetime-local">
                    </div>
                    <div class="col-md-4">
                        <label class="form-label fw-medium">结束时间</label>
                        <input class="form-control" id="customEndTime" type="datetime-local">
                    </div>
                    <div class="col-md-4">
                        <label class="form-label">&nbsp;</label>
                        <button class="btn btn-primary w-100" id="applyCustomRange">
                            <svg class="icon me-1" fill="none" height="16" stroke="currentColor"
                                 stroke-linecap="round" stroke-linejoin="round" stroke-width="2" viewBox="0 0 24 24"
                                 width="16" xmlns="http://www.w3.org/2000/svg">
                                <path d="M0 0h24v24H0z" fill="none" stroke="none"/>
                                <path d="M5 12l5 5l10 -10"/>
                            </svg>
                            应用
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 核心统计指标 -->
<div class="row g-4 mb-4">
    <div class="col-sm-6 col-xl-3">
        <div class="card border-0 shadow-sm">
            <div class="card-body p-4">
                <div class="row align-items-center">
                    <div class="col-auto">
                        <div class="avatar avatar-lg bg-gradient-primary text-white rounded-3">
                            <svg class="icon icon-lg" fill="none" height="32" stroke="currentColor"
                                 stroke-linecap="round" stroke-linejoin="round" stroke-width="2" viewBox="0 0 24 24"
                                 width="32" xmlns="http://www.w3.org/2000/svg">
                                <path d="M0 0h24v24H0z" fill="none" stroke="none"/>
                                <ellipse cx="12" cy="6" rx="8" ry="3"/>
                                <path d="M4 6v6a8 3 0 0 0 16 0v-6"/>
                                <path d="M4 12v6a8 3 0 0 0 16 0v-6"/>
                            </svg>
                        </div>
                    </div>
                    <div class="col">
                        <div class="h2 mb-1 text-dark fw-bold" id="statsTotalCount">
                            0
                        </div>
                        <div class="text-muted fw-medium mb-2">
                            总事务数
                        </div>
                        <div class="progress progress-sm">
                            <div class="progress-bar bg-primary" role="progressbar" style="width: 100%"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-sm-6 col-xl-3">
        <div class="card border-0 shadow-sm">
            <div class="card-body p-4">
                <div class="row align-items-center">
                    <div class="col-auto">
                        <div class="avatar avatar-lg bg-gradient-success text-white rounded-3">
                            <svg class="icon icon-lg" fill="none" height="32" stroke="currentColor"
                                 stroke-linecap="round" stroke-linejoin="round" stroke-width="2" viewBox="0 0 24 24"
                                 width="32" xmlns="http://www.w3.org/2000/svg">
                                <path d="M0 0h24v24H0z" fill="none" stroke="none"/>
                                <circle cx="12" cy="12" r="10"/>
                                <path d="M8 12l2 2l4 -4"/>
                            </svg>
                        </div>
                    </div>
                    <div class="col">
                        <div class="h2 mb-1 text-dark fw-bold" id="statsSuccessRate">
                            0%
                        </div>
                        <div class="text-muted fw-medium mb-2">
                            成功率
                        </div>
                        <div class="progress progress-sm">
                            <div class="progress-bar bg-success" id="successRateProgress" role="progressbar"
                                 style="width: 0%"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-sm-6 col-xl-3">
        <div class="card border-0 shadow-sm">
            <div class="card-body p-4">
                <div class="row align-items-center">
                    <div class="col-auto">
                        <div class="avatar avatar-lg bg-gradient-warning text-white rounded-3">
                            <svg class="icon icon-lg" fill="none" height="32" stroke="currentColor"
                                 stroke-linecap="round" stroke-linejoin="round" stroke-width="2" viewBox="0 0 24 24"
                                 width="32" xmlns="http://www.w3.org/2000/svg">
                                <path d="M0 0h24v24H0z" fill="none" stroke="none"/>
                                <circle cx="12" cy="12" r="9"/>
                                <polyline points="12,7 12,12 16,14"/>
                            </svg>
                        </div>
                    </div>
                    <div class="col">
                        <div class="h2 mb-1 text-dark fw-bold" id="statsAvgDuration">
                            0ms
                        </div>
                        <div class="text-muted fw-medium mb-2">
                            平均耗时
                        </div>
                        <div class="progress progress-sm">
                            <div class="progress-bar bg-warning" id="avgDurationProgress" role="progressbar"
                                 style="width: 0%"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-sm-6 col-xl-3">
        <div class="card border-0 shadow-sm">
            <div class="card-body p-4">
                <div class="row align-items-center">
                    <div class="col-auto">
                        <div class="avatar avatar-lg bg-gradient-info text-white rounded-3">
                            <svg class="icon icon-lg" fill="none" height="32" stroke="currentColor"
                                 stroke-linecap="round" stroke-linejoin="round" stroke-width="2" viewBox="0 0 24 24"
                                 width="32" xmlns="http://www.w3.org/2000/svg">
                                <path d="M0 0h24v24H0z" fill="none" stroke="none"/>
                                <circle cx="12" cy="13" r="2"/>
                                <path d="M13.45 11.55l2.05 -2.05"/>
                                <path d="M6.4 20a9 9 0 1 1 11.2 0z"/>
                            </svg>
                        </div>
                    </div>
                    <div class="col">
                        <div class="h2 mb-1 text-dark fw-bold" id="statsPeakTps">
                            0
                        </div>
                        <div class="text-muted fw-medium mb-2">
                            峰值TPS
                        </div>
                        <div class="progress progress-sm">
                            <div class="progress-bar bg-info" id="peakTpsProgress" role="progressbar"
                                 style="width: 0%"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 图表区域 -->
<div class="row g-4 mb-4">
    <!-- 趋势图表 -->
    <div class="col-lg-8">
        <div class="card border-0 shadow-sm h-100">
            <div class="card-header bg-transparent border-0">
                <div class="row align-items-center">
                    <div class="col">
                        <h3 class="card-title mb-0 fw-bold">事务执行趋势</h3>
                        <p class="text-muted mb-0">实时监控事务执行情况和性能变化</p>
                    </div>
                    <div class="col-auto">
                        <select class="form-select form-select-sm" id="trendChartType">
                            <option value="line">线性图</option>
                            <option value="area">面积图</option>
                            <option value="bar">柱状图</option>
                        </select>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <div class="chart-container" style="height: 400px;">
                    <div class="d-flex align-items-center justify-content-center h-100" id="detailedTrendChart">
                        <div class="text-center text-muted">
                            <svg class="icon icon-lg mb-3 text-primary" fill="none" height="48"
                                 stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5"
                                 viewBox="0 0 24 24"
                                 width="48" xmlns="http://www.w3.org/2000/svg">
                                <path d="M0 0h24v24H0z" fill="none" stroke="none"/>
                                <path d="M3 12h4l3 8l4 -16l3 8h4"/>
                            </svg>
                            <div class="h4">趋势分析图表</div>
                            <div class="text-muted">图表数据正在加载中...</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 状态分布 -->
    <div class="col-lg-4">
        <div class="card border-0 shadow-sm h-100">
            <div class="card-header bg-transparent border-0">
                <h3 class="card-title mb-0 fw-bold">状态分布</h3>
                <p class="text-muted mb-0">事务执行状态统计</p>
            </div>
            <div class="card-body">
                <div class="chart-container" style="height: 300px;">
                    <div class="d-flex align-items-center justify-content-center h-100" id="statusDistributionChart">
                        <div class="text-center text-muted">
                            <svg class="icon icon-lg mb-3 text-primary" fill="none" height="48"
                                 stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5"
                                 viewBox="0 0 24 24"
                                 width="48" xmlns="http://www.w3.org/2000/svg">
                                <path d="M0 0h24v24H0z" fill="none" stroke="none"/>
                                <circle cx="12" cy="12" r="10"/>
                                <path d="M12 2a15.3 15.3 0 0 1 4 10a15.3 15.3 0 0 1 -4 10a15.3 15.3 0 0 1 -4 -10a15.3 15.3 0 0 1 4 -10z"/>
                                <path d="M2 12h20"/>
                            </svg>
                            <div class="h5">饼图分析</div>
                            <div class="text-muted">图表数据正在加载中...</div>
                        </div>
                    </div>
                </div>

                <!-- 状态统计列表 -->
                <div class="mt-4">
                    <div class="row g-2">
                        <div class="col-6">
                            <div class="d-flex align-items-center">
                                <div class="me-2">
                                    <span class="badge bg-success"></span>
                                </div>
                                <div class="flex-fill">
                                    <div class="fw-medium">成功</div>
                                    <div class="text-muted small" id="successCount">0</div>
                                </div>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="d-flex align-items-center">
                                <div class="me-2">
                                    <span class="badge bg-danger"></span>
                                </div>
                                <div class="flex-fill">
                                    <div class="fw-medium">失败</div>
                                    <div class="text-muted small" id="failedCount">0</div>
                                </div>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="d-flex align-items-center">
                                <div class="me-2">
                                    <span class="badge bg-warning"></span>
                                </div>
                                <div class="flex-fill">
                                    <div class="fw-medium">超时</div>
                                    <div class="text-muted small" id="timeoutCount">0</div>
                                </div>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="d-flex align-items-center">
                                <div class="me-2">
                                    <span class="badge bg-info"></span>
                                </div>
                                <div class="flex-fill">
                                    <div class="fw-medium">执行中</div>
                                    <div class="text-muted small" id="executingCount">0</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 详细统计表格 -->
<div class="row g-4">
    <div class="col-12">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-transparent border-0">
                <div class="row align-items-center">
                    <div class="col">
                        <h3 class="card-title mb-0 fw-bold">详细统计</h3>
                        <p class="text-muted mb-0">按时间段分组的详细统计数据</p>
                    </div>
                    <div class="col-auto">
                        <div class="btn-list">
                            <button class="btn btn-outline-primary btn-sm" id="exportStatistics">
                                <svg class="icon me-1" fill="none" height="16" stroke="currentColor"
                                     stroke-linecap="round" stroke-linejoin="round" stroke-width="2" viewBox="0 0 24 24"
                                     width="16" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M0 0h24v24H0z" fill="none" stroke="none"/>
                                    <path d="M14 3v4a1 1 0 0 0 1 1h4"/>
                                    <path d="M17 21h-10a2 2 0 0 1 -2 -2v-14a2 2 0 0 1 2 -2h7l5 5v11a2 2 0 0 1 -2 2z"/>
                                    <line x1="9" x2="10" y1="9" y2="9"/>
                                    <line x1="9" x2="15" y1="13" y2="13"/>
                                    <line x1="9" x2="15" y1="17" y2="17"/>
                                </svg>
                                导出报告
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            <div class="table-responsive">
                <table class="table table-vcenter card-table table-striped">
                    <thead class="table-light">
                    <tr>
                        <th>时间段</th>
                        <th>总数</th>
                        <th>成功</th>
                        <th>失败</th>
                        <th>成功率</th>
                        <th>平均耗时</th>
                        <th>TPS</th>
                        <th>操作</th>
                    </tr>
                    </thead>
                    <tbody id="statisticsTableBody">
                    <!-- 动态加载的统计数据 -->
                    </tbody>
                </table>
            </div>
            <div class="card-footer bg-transparent border-0">
                <div class="row align-items-center">
                    <div class="col">
                        <p class="m-0 text-muted">显示最近24小时的统计数据</p>
                    </div>
                    <div class="col-auto">
                        <button class="btn btn-outline-secondary btn-sm" id="loadMoreStats">
                            <svg class="icon me-1" fill="none" height="16" stroke="currentColor"
                                 stroke-linecap="round" stroke-linejoin="round" stroke-width="2" viewBox="0 0 24 24"
                                 width="16" xmlns="http://www.w3.org/2000/svg">
                                <path d="M0 0h24v24H0z" fill="none" stroke="none"/>
                                <path d="M20 11a8.1 8.1 0 0 0 -15.5 -2m-.5 -4v4h4"/>
                                <path d="M4 13a8.1 8.1 0 0 0 15.5 2m.5 4v-4h-4"/>
                            </svg>
                            加载更多
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
