<!-- 大气的仪表板页面模板 -->
<div class="row g-4 mb-4">
    <!-- 核心指标卡片 -->
    <div class="col-sm-6 col-xl-3">
        <div class="card border-0 shadow-sm h-100">
            <div class="card-body p-4">
                <div class="row align-items-center">
                    <div class="col-auto">
                        <div class="avatar avatar-lg bg-gradient-primary text-white rounded-3">
                            <svg class="icon icon-lg" fill="none" height="32" stroke="currentColor"
                                 stroke-linecap="round" stroke-linejoin="round" stroke-width="2" viewBox="0 0 24 24"
                                 width="32" xmlns="http://www.w3.org/2000/svg">
                                <path d="M0 0h24v24H0z" fill="none" stroke="none"/>
                                <ellipse cx="12" cy="6" rx="8" ry="3"/>
                                <path d="M4 6v6a8 3 0 0 0 16 0v-6"/>
                                <path d="M4 12v6a8 3 0 0 0 16 0v-6"/>
                            </svg>
                        </div>
                    </div>
                    <div class="col">
                        <div class="h2 mb-1 text-dark fw-bold" id="totalTransactions">
                            1,234
                        </div>
                        <div class="text-muted fw-medium mb-2">
                            总事务数
                        </div>
                        <div class="d-flex align-items-center">
                            <span class="badge bg-success-lt text-success px-2 py-1">
                                <svg class="icon icon-sm me-1" fill="none" height="14" stroke="currentColor"
                                     stroke-linecap="round" stroke-linejoin="round" stroke-width="2" viewBox="0 0 24 24"
                                     width="14" xmlns="http://www.w3.org/2000/svg">
                                    <polyline points="22,7 13.5,15.5 8.5,10.5 2,17"/>
                                    <polyline points="16,7 22,7 22,13"/>
                                </svg>
                                +12.5%
                            </span>
                            <span class="text-muted ms-2 small">较昨日</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-sm-6 col-xl-3">
        <div class="card border-0 shadow-sm h-100">
            <div class="card-body p-4">
                <div class="row align-items-center">
                    <div class="col-auto">
                        <div class="avatar avatar-lg bg-gradient-success text-white rounded-3">
                            <svg class="icon icon-lg" fill="none" height="32" stroke="currentColor"
                                 stroke-linecap="round" stroke-linejoin="round" stroke-width="2" viewBox="0 0 24 24"
                                 width="32" xmlns="http://www.w3.org/2000/svg">
                                <path d="M0 0h24v24H0z" fill="none" stroke="none"/>
                                <path d="M5 12l5 5l10 -10"/>
                            </svg>
                        </div>
                    </div>
                    <div class="col">
                        <div class="h2 mb-1 text-dark fw-bold" id="successTransactions">
                            1,156
                        </div>
                        <div class="text-muted fw-medium mb-2">
                            成功事务
                        </div>
                        <div class="d-flex align-items-center">
                            <span class="badge bg-success-lt text-success px-2 py-1">
                                93.7%
                            </span>
                            <span class="text-muted ms-2 small">成功率</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-sm-6 col-xl-3">
        <div class="card border-0 shadow-sm h-100">
            <div class="card-body p-4">
                <div class="row align-items-center">
                    <div class="col-auto">
                        <div class="avatar avatar-lg bg-gradient-danger text-white rounded-3">
                            <svg class="icon icon-lg" fill="none" height="32" stroke="currentColor"
                                 stroke-linecap="round" stroke-linejoin="round" stroke-width="2" viewBox="0 0 24 24"
                                 width="32" xmlns="http://www.w3.org/2000/svg">
                                <path d="M0 0h24v24H0z" fill="none" stroke="none"/>
                                <path d="M12 9v2m0 4v.01"/>
                                <path d="M5 19h14a2 2 0 0 0 1.84 -2.75l-7.1 -12.25a2 2 0 0 0 -3.48 0l-7.1 12.25a2 2 0 0 0 1.74 2.75"/>
                            </svg>
                        </div>
                    </div>
                    <div class="col">
                        <div class="h2 mb-1 text-dark fw-bold" id="failedTransactions">
                            0
                        </div>
                        <div class="text-muted fw-medium mb-2">
                            失败事务
                        </div>
                        <div class="d-flex align-items-center">
                            <span class="badge bg-danger-lt text-danger px-2 py-1" id="failureRate">
                                0.0%
                            </span>
                            <span class="text-muted ms-2 small">失败率</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-sm-6 col-xl-3">
        <div class="card border-0 shadow-sm h-100">
            <div class="card-body p-4">
                <div class="row align-items-center">
                    <div class="col-auto">
                        <div class="avatar avatar-lg bg-gradient-warning text-white rounded-3">
                            <svg class="icon icon-lg" fill="none" height="32" stroke="currentColor"
                                 stroke-linecap="round" stroke-linejoin="round" stroke-width="2" viewBox="0 0 24 24"
                                 width="32" xmlns="http://www.w3.org/2000/svg">
                                <path d="M0 0h24v24H0z" fill="none" stroke="none"/>
                                <circle cx="12" cy="12" r="9"/>
                                <polyline points="12,7 12,12 16,14"/>
                            </svg>
                        </div>
                    </div>
                    <div class="col">
                        <div class="h2 mb-1 text-dark fw-bold" id="runningTransactions">
                            0
                        </div>
                        <div class="text-muted fw-medium mb-2">
                            执行中事务
                        </div>
                        <div class="d-flex align-items-center">
                            <span class="badge bg-warning-lt text-warning px-2 py-1">
                                实时
                            </span>
                            <span class="text-muted ms-2 small">状态</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 图表和监控区域 -->
<div class="row g-4 mb-4">
    <!-- 实时事务流 -->
    <div class="col-12">
        <div class="card border-0 shadow-sm h-100">
            <div class="card-header bg-transparent border-0 pb-0">
                <div class="row align-items-center">
                    <div class="col">
                        <h3 class="card-title mb-0 fw-bold">实时事务流</h3>
                        <p class="text-muted mb-0">最近24小时事务执行趋势</p>
                    </div>
                    <div class="col-auto">
                        <div class="btn-list">
                            <div class="dropdown">
                                <button class="btn btn-outline-secondary btn-sm dropdown-toggle"
                                        data-bs-toggle="dropdown">
                                    最近24小时
                                </button>
                                <div class="dropdown-menu dropdown-menu-end">
                                    <a class="dropdown-item" href="#">最近1小时</a>
                                    <a class="dropdown-item" href="#">最近6小时</a>
                                    <a class="dropdown-item active" href="#">最近24小时</a>
                                    <a class="dropdown-item" href="#">最近7天</a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="card-body pt-2">
                <div class="table-responsive">
                    <table class="table table-vcenter">
                        <thead>
                        <tr>
                            <th>事务ID</th>
                            <th>事务名称</th>
                            <th>模式</th>
                            <th>状态</th>
                            <th>步骤数</th>
                            <th>执行时间</th>
                            <th>持续时间</th>
                            <th>操作</th>
                        </tr>
                        </thead>
                        <tbody id="realtimeTransactionsTable">
                        <!-- 动态加载的实时事务数据 -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>


</div>

<!-- 最近事务和快速操作 -->
<div class="row g-4">
    <!-- 最近事务 -->
    <div class="col-lg-8">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-transparent border-0 pb-0">
                <div class="row align-items-center">
                    <div class="col">
                        <h3 class="card-title mb-0 fw-bold">最近事务</h3>
                        <p class="text-muted mb-0">最新执行的事务记录</p>
                    </div>
                    <div class="col-auto">
                        <a class="btn btn-primary btn-sm" data-page="transactions" href="#">
                            查看全部
                            <svg class="icon ms-1" fill="none" height="16" stroke="currentColor"
                                 stroke-linecap="round" stroke-linejoin="round" stroke-width="2" viewBox="0 0 24 24"
                                 width="16" xmlns="http://www.w3.org/2000/svg">
                                <polyline points="9,18 15,12 9,6"/>
                            </svg>
                        </a>
                    </div>
                </div>
            </div>
            <div class="card-body pt-2">
                <div class="table-responsive">
                    <table class="table table-vcenter">
                        <thead>
                        <tr>
                            <th>事务ID</th>
                            <th>类型</th>
                            <th>状态</th>
                            <th>执行时间</th>
                            <th>持续时间</th>
                        </tr>
                        </thead>
                        <tbody id="recentTransactionsTable">
                        <!-- 动态加载的最近事务数据 -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- 快速操作 -->
    <div class="col-lg-4">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-transparent border-0 pb-0">
                <h3 class="card-title mb-0 fw-bold">快速操作</h3>
                <p class="text-muted mb-0">常用功能入口</p>
            </div>
            <div class="card-body pt-2">
                <div class="list-group list-group-flush">
                    <a class="list-group-item list-group-item-action border-0 px-0" data-page="transactions" href="#">
                        <div class="row align-items-center">
                            <div class="col-auto">
                                <span class="avatar avatar-sm bg-primary-lt text-primary">
                                    <svg class="icon" fill="none" height="20" stroke="currentColor"
                                         stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                         viewBox="0 0 24 24"
                                         width="20" xmlns="http://www.w3.org/2000/svg">
                                        <path d="M0 0h24v24H0z" fill="none" stroke="none"/>
                                        <path d="M9 11h-4a2 2 0 0 1 -2 -2v-3c0 -1.1 .9 -2 2 -2h4m5 0h4a2 2 0 0 1 2 2v3a2 2 0 0 1 -2 2h-4"/>
                                        <path d="M12 15h2a2 2 0 0 1 2 2v2a2 2 0 0 1 -2 2h-2v-6z"/>
                                        <path d="M12 15v-6"/>
                                        <path d="M9 9v6"/>
                                        <path d="M12 9h-3"/>
                                    </svg>
                                </span>
                            </div>
                            <div class="col">
                                <div class="fw-medium">事务管理</div>
                                <div class="text-muted small">查看和管理所有事务</div>
                            </div>
                        </div>
                    </a>
                    <a class="list-group-item list-group-item-action border-0 px-0" data-page="statistics" href="#">
                        <div class="row align-items-center">
                            <div class="col-auto">
                                <span class="avatar avatar-sm bg-success-lt text-success">
                                    <svg class="icon" fill="none" height="20" stroke="currentColor"
                                         stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                         viewBox="0 0 24 24"
                                         width="20" xmlns="http://www.w3.org/2000/svg">
                                        <path d="M0 0h24v24H0z" fill="none" stroke="none"/>
                                        <path d="M3 12h4l3 8l4 -16l3 8h4"/>
                                    </svg>
                                </span>
                            </div>
                            <div class="col">
                                <div class="fw-medium">统计分析</div>
                                <div class="text-muted small">查看详细统计报告</div>
                            </div>
                        </div>
                    </a>
                    <a class="list-group-item list-group-item-action border-0 px-0" data-page="exceptions" href="#">
                        <div class="row align-items-center">
                            <div class="col-auto">
                                <span class="avatar avatar-sm bg-danger-lt text-danger">
                                    <svg class="icon" fill="none" height="20" stroke="currentColor"
                                         stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                         viewBox="0 0 24 24"
                                         width="20" xmlns="http://www.w3.org/2000/svg">
                                        <path d="M0 0h24v24H0z" fill="none" stroke="none"/>
                                        <path d="M12 9v2m0 4v.01"/>
                                        <path d="M5 19h14a2 2 0 0 0 1.84 -2.75l-7.1 -12.25a2 2 0 0 0 -3.48 0l-7.1 12.25a2 2 0 0 0 1.74 2.75"/>
                                    </svg>
                                </span>
                            </div>
                            <div class="col">
                                <div class="fw-medium">异常管理</div>
                                <div class="text-muted small">处理系统异常和错误</div>
                            </div>
                        </div>
                    </a>
                    <a class="list-group-item list-group-item-action border-0 px-0" data-page="settings" href="#">
                        <div class="row align-items-center">
                            <div class="col-auto">
                                <span class="avatar avatar-sm bg-info-lt text-info">
                                    <svg class="icon" fill="none" height="20" stroke="currentColor"
                                         stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                         viewBox="0 0 24 24"
                                         width="20" xmlns="http://www.w3.org/2000/svg">
                                        <path d="M0 0h24v24H0z" fill="none" stroke="none"/>
                                        <path d="M10.325 4.317c.426 -1.756 2.924 -1.756 3.35 0a1.724 1.724 0 0 0 2.573 1.066c1.543 -.94 3.31 .826 2.37 2.37a1.724 1.724 0 0 0 1.065 2.572c1.756 .426 1.756 2.924 0 3.35a1.724 1.724 0 0 0 -1.066 2.573c.94 1.543 -.826 3.31 -2.37 2.37a1.724 1.724 0 0 0 -2.572 1.065c-.426 1.756 -2.924 1.756 -3.35 0a1.724 1.724 0 0 0 -2.573 -1.066c-1.543 .94 -3.31 -.826 -2.37 -2.37a1.724 1.724 0 0 0 -1.065 -2.572c-1.756 -.426 -1.756 -2.924 0 -3.35a1.724 1.724 0 0 0 1.066 -2.573c-.94 -1.543 .826 -3.31 2.37 -2.37c1 .608 2.296 .07 2.572 -1.065z"/>
                                        <circle cx="12" cy="12" r="3"/>
                                    </svg>
                                </span>
                            </div>
                            <div class="col">
                                <div class="fw-medium">系统设置</div>
                                <div class="text-muted small">配置系统参数</div>
                            </div>
                        </div>
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
