/**
 * 模板加载器
 * 负责动态加载HTML模板和组件
 */
class TemplateLoader {
    constructor() {
        this.templateCache = new Map();
        this.componentCache = new Map();
        this.loadingPromises = new Map();
    }

    /**
     * 加载页面模板
     * @param {string} templateName 模板名称
     * @returns {Promise<string>} 模板HTML内容
     */
    async loadTemplate(templateName) {
        // 检查缓存
        if (this.templateCache.has(templateName)) {
            return this.templateCache.get(templateName);
        }

        // 检查是否正在加载
        if (this.loadingPromises.has(templateName)) {
            return this.loadingPromises.get(templateName);
        }

        // 创建加载Promise
        const loadPromise = this.fetchTemplate(templateName);
        this.loadingPromises.set(templateName, loadPromise);

        try {
            const template = await loadPromise;
            this.templateCache.set(templateName, template);
            this.loadingPromises.delete(templateName);
            return template;
        } catch (error) {
            this.loadingPromises.delete(templateName);
            throw error;
        }
    }

    /**
     * 加载组件模板
     * @param {string} componentName 组件名称
     * @returns {Promise<string>} 组件HTML内容
     */
    async loadComponent(componentName) {
        // 检查缓存
        if (this.componentCache.has(componentName)) {
            return this.componentCache.get(componentName);
        }

        // 检查是否正在加载
        const cacheKey = `component_${componentName}`;
        if (this.loadingPromises.has(cacheKey)) {
            return this.loadingPromises.get(cacheKey);
        }

        // 创建加载Promise
        const loadPromise = this.fetchComponent(componentName);
        this.loadingPromises.set(cacheKey, loadPromise);

        try {
            const component = await loadPromise;
            this.componentCache.set(componentName, component);
            this.loadingPromises.delete(cacheKey);
            return component;
        } catch (error) {
            this.loadingPromises.delete(cacheKey);
            throw error;
        }
    }

    /**
     * 获取模板内容
     * @param {string} templateName 模板名称
     * @returns {Promise<string>} 模板HTML内容
     */
    async fetchTemplate(templateName) {
        try {
            const response = await fetch(`templates/${templateName}.html`);
            if (!response.ok) {
                throw new Error(`模板加载失败: ${templateName} (${response.status})`);
            }
            return await response.text();
        } catch (error) {
            console.error(`Failed to load template: ${templateName}`, error);
            throw new Error(`模板 ${templateName} 加载失败: ${error.message}`);
        }
    }

    /**
     * 获取组件内容
     * @param {string} componentName 组件名称
     * @returns {Promise<string>} 组件HTML内容
     */
    async fetchComponent(componentName) {
        try {
            const response = await fetch(`components/${componentName}.html`);
            if (!response.ok) {
                throw new Error(`组件加载失败: ${componentName} (${response.status})`);
            }
            return await response.text();
        } catch (error) {
            console.error(`Failed to load component: ${componentName}`, error);
            throw new Error(`组件 ${componentName} 加载失败: ${error.message}`);
        }
    }

    /**
     * 预加载模板
     * @param {string[]} templateNames 模板名称数组
     */
    async preloadTemplates(templateNames) {
        const promises = templateNames.map(name => this.loadTemplate(name));
        try {
            await Promise.all(promises);
            console.log('模板预加载完成:', templateNames);
        } catch (error) {
            console.warn('模板预加载部分失败:', error);
        }
    }

    /**
     * 预加载组件
     * @param {string[]} componentNames 组件名称数组
     */
    async preloadComponents(componentNames) {
        const promises = componentNames.map(name => this.loadComponent(name));
        try {
            await Promise.all(promises);
            console.log('组件预加载完成:', componentNames);
        } catch (error) {
            console.warn('组件预加载部分失败:', error);
        }
    }

    /**
     * 清除缓存
     * @param {string} type 缓存类型: 'template', 'component', 'all'
     */
    clearCache(type = 'all') {
        switch (type) {
            case 'template':
                this.templateCache.clear();
                break;
            case 'component':
                this.componentCache.clear();
                break;
            case 'all':
                this.templateCache.clear();
                this.componentCache.clear();
                this.loadingPromises.clear();
                break;
        }
        console.log(`缓存已清除: ${type}`);
    }

    /**
     * 获取缓存状态
     * @returns {Object} 缓存状态信息
     */
    getCacheStatus() {
        return {
            templates: {
                count: this.templateCache.size,
                keys: Array.from(this.templateCache.keys())
            },
            components: {
                count: this.componentCache.size,
                keys: Array.from(this.componentCache.keys())
            },
            loading: {
                count: this.loadingPromises.size,
                keys: Array.from(this.loadingPromises.keys())
            }
        };
    }
}

// 创建全局实例
window.TemplateLoader = new TemplateLoader();
