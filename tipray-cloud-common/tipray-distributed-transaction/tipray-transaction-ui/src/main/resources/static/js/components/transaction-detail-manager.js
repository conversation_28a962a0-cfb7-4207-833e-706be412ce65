/**
 * 事务详情管理器
 * 负责事务详情模态框的显示和交互
 */
class TransactionDetailManager {
    constructor() {
        this.currentTransactionId = null;
        this.currentTransactionData = null;
        this.modal = null;
        this.stepHistoryModal = null;
        this.refreshInterval = null;
        this.isInitialized = false; // 防止重复初始化
    }

    /**
     * 获取当前操作用户ID
     * @returns {string} 用户ID
     */
    getCurrentOperatorId() {
        // 这里可以从session、localStorage或其他地方获取当前用户信息
        // 暂时使用默认值
        return localStorage.getItem('currentUserId') || 'admin';
    }

    /**
     * 初始化
     */
    async init() {
        if (this.isInitialized) {
            console.log('事务详情管理器已经初始化，跳过重复初始化');
            return;
        }

        try {
            // 加载事务详情模态框模板
            await this.loadModalTemplate();

            // 初始化模态框实例
            this.modal = new bootstrap.Modal(document.getElementById('transactionDetailModal'));
            this.stepHistoryModal = new bootstrap.Modal(document.getElementById('stepHistoryModal'));

            // 初始化事件监听器
            this.initEventListeners();

            this.isInitialized = true;
            console.log('事务详情管理器初始化完成');
        } catch (error) {
            console.error('事务详情管理器初始化失败:', error);
        }
    }

    /**
     * 加载模态框模板
     */
    async loadModalTemplate() {
        try {
            // 检查模板是否已经加载
            if (document.getElementById('transactionDetailModal')) {
                console.log('事务详情模板已存在，跳过加载');
                return;
            }

            const response = await fetch('templates/transaction-detail-modal.html');
            if (!response.ok) {
                throw new Error(`加载模板失败: ${response.status}`);
            }

            const template = await response.text();

            // 将模板添加到页面
            const container = document.createElement('div');
            container.innerHTML = template;
            document.body.appendChild(container);

        } catch (error) {
            console.error('加载事务详情模板失败:', error);
            throw error;
        }
    }

    /**
     * 初始化事件监听器
     */
    initEventListeners() {
        // 刷新详情按钮
        const refreshBtn = document.getElementById('refreshTransactionDetailBtn');
        if (refreshBtn) {
            refreshBtn.addEventListener('click', () => {
                this.refreshTransactionDetail();
            });
        }

        // 刷新步骤按钮
        const refreshStepsBtn = document.getElementById('refreshStepsBtn');
        if (refreshStepsBtn) {
            refreshStepsBtn.addEventListener('click', () => {
                this.refreshSteps();
            });
        }

        // 步骤状态过滤
        const stepFilter = document.getElementById('stepFilterStatus');
        if (stepFilter) {
            stepFilter.addEventListener('change', () => {
                this.filterSteps();
            });
        }

        // 事务操作按钮
        this.initTransactionOperationListeners();

        // 模态框关闭事件
        const modalElement = document.getElementById('transactionDetailModal');
        if (modalElement) {
            modalElement.addEventListener('hidden.bs.modal', () => {
                this.onModalClosed();
            });
        }
    }

    /**
     * 初始化事务操作监听器
     */
    initTransactionOperationListeners() {
        // 移除之前的事件监听器（如果存在）
        const buttons = [
            {id: 'retryTransactionBtn', handler: () => this.retryTransaction()},
            {id: 'rollbackTransactionBtn', handler: () => this.rollbackTransaction()},
            {id: 'commitTransactionBtn', handler: () => this.commitTransaction()},
            {id: 'cancelTransactionBtn', handler: () => this.cancelTransaction()}
        ];

        buttons.forEach(({id, handler}) => {
            const btn = document.getElementById(id);
            if (btn) {
                // 移除之前的事件监听器
                if (btn._transactionHandler) {
                    btn.removeEventListener('click', btn._transactionHandler);
                }

                // 添加新的事件监听器
                btn.addEventListener('click', handler);
                btn._transactionHandler = handler;
            }
        });
    }

    /**
     * 显示事务详情
     * @param {string} transactionId 事务ID
     */
    async showTransactionDetail(transactionId) {
        try {
            this.currentTransactionId = transactionId;

            // 显示模态框
            this.modal.show();

            // 显示加载状态
            this.showLoadingState();

            // 加载事务详情
            await this.loadTransactionDetail(transactionId);

            // 开始自动刷新
            this.startAutoRefresh();

        } catch (error) {
            console.error('显示事务详情失败:', error);
            this.showErrorState(error.message);
        }
    }

    /**
     * 加载事务详情
     * @param {string} transactionId 事务ID
     */
    async loadTransactionDetail(transactionId) {
        try {
            console.log(`加载事务详情: ${transactionId}`);

            const response = await window.apiService.getTransactionDetail(transactionId);

            console.log('事务详情响应:', response);

            // 适配ApiResponse<TransactionDetailResponse>格式
            if (response.success !== false) {
                const data = response.data || response;
                this.currentTransactionData = data;

                // 更新事务基本信息
                this.updateTransactionInfo(data);

                // 更新事务步骤 - 适配TransactionDetailResponse结构
                const steps = data.steps || data.transactionSteps || [];
                this.updateTransactionSteps(steps);
            } else {
                throw new Error(response.message || '获取事务详情失败');
            }

        } catch (error) {
            console.error('加载事务详情失败:', error);
            throw error;
        }
    }

    /**
     * 更新事务基本信息
     * @param {Object} transactionData 事务数据
     */
    updateTransactionInfo(transactionData) {
        // 更新状态
        this.updateTransactionStatus(transactionData);

        // 更新基本信息
        this.setElementText('detailTransactionId', transactionData.transactionId || '-');
        this.setElementText('detailGroupId', transactionData.groupId || '-');
        this.setElementText('detailTransactionMode', transactionData.transactionMode || '-');
        this.setElementText('detailStepCount', transactionData.stepCount || '0');
        this.setElementText('detailServiceName', transactionData.serviceName || '-');
        this.setElementText('detailBusinessKey', transactionData.businessKey || '-');

        // 更新时间信息
        this.setElementText('detailStartTime', this.formatDateTime(transactionData.startTime));
        this.setElementText('detailEndTime', this.formatDateTime(transactionData.endTime));
        this.setElementText('detailDuration', this.formatDuration(transactionData.duration));
        this.setElementText('detailTimeout', this.formatTimeout(transactionData.timeoutSeconds));

        // 更新重试信息
        this.updateRetryInfo(transactionData);

        // 更新错误信息
        this.updateTransactionErrorInfo(transactionData);

        // 更新执行上下文信息
        this.updateExecutionContext(transactionData);
    }

    /**
     * 更新事务错误信息
     * @param {Object} transactionData 事务数据
     */
    updateTransactionErrorInfo(transactionData) {
        // 查找错误信息容器
        const errorContainer = document.getElementById('transactionErrorContainer');

        if (errorContainer) {
            // 检查是否有错误信息需要显示
            const hasError = transactionData.errorMessage ||
                transactionData.detailedErrorMessage ||
                transactionData.originalException ||
                (transactionData.status && (transactionData.status.includes('FAILED') ||
                    transactionData.status.includes('ERROR') ||
                    transactionData.status.includes('TIMEOUT')));

            if (hasError) {
                // 确定错误类型
                let errorType = '未知异常';
                if (transactionData.errorType) {
                    errorType = transactionData.errorType;
                } else if (transactionData.status && transactionData.status.includes('TIMEOUT')) {
                    errorType = '超时异常';
                } else if (transactionData.status && transactionData.status.includes('FAILED')) {
                    errorType = '执行失败';
                }

                // 获取主要错误信息
                const mainErrorMessage = transactionData.errorMessage ||
                    transactionData.detailedErrorMessage ||
                    transactionData.originalException ||
                    '未知错误';

                errorContainer.innerHTML = `
                    <div class="card border-danger shadow-sm mb-3">
                        <div class="card-header bg-danger-lt border-danger">
                            <div class="d-flex align-items-center">
                                <svg xmlns="http://www.w3.org/2000/svg" class="icon icon-lg me-2 text-danger" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                    <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
                                    <circle cx="12" cy="12" r="9"/>
                                    <line x1="12" y1="8" x2="12" y2="12"/>
                                    <line x1="12" y1="16" x2="12.01" y2="16"/>
                                </svg>
                                <h6 class="card-title mb-0 fw-bold text-danger">事务执行异常</h6>
                            </div>
                        </div>
                        <div class="card-body">
                            <div class="row g-3">
                                <div class="col-md-6">
                                    <label class="form-label small text-muted mb-1">错误类型</label>
                                    <div class="fw-medium text-danger">${this.escapeHtml(errorType)}</div>
                                </div>
                                <div class="col-md-6">
                                    <label class="form-label small text-muted mb-1">发生时间</label>
                                    <div class="fw-medium">${this.formatDateTime(transactionData.endTime || transactionData.updateTime)}</div>
                                </div>
                                <div class="col-12">
                                    <label class="form-label small text-muted mb-1">错误信息</label>
                                    <div class="p-2 bg-light border rounded">
                                        <code class="small text-danger">${this.escapeHtml(mainErrorMessage)}</code>
                                    </div>
                                </div>
                                ${transactionData.failureReason ? `
                                <div class="col-12">
                                    <label class="form-label small text-muted mb-1">失败原因</label>
                                    <div class="p-2 bg-light border rounded">
                                        <code class="small text-dark">${this.escapeHtml(transactionData.failureReason)}</code>
                                    </div>
                                </div>
                                ` : ''}
                                ${transactionData.rollbackReason ? `
                                <div class="col-12">
                                    <label class="form-label small text-muted mb-1">回滚原因</label>
                                    <div class="p-2 bg-light border rounded">
                                        <code class="small text-dark">${this.escapeHtml(transactionData.rollbackReason)}</code>
                                    </div>
                                </div>
                                ` : ''}
                                ${transactionData.errorStack ? `
                                <div class="col-12">
                                    <details>
                                        <summary class="text-muted small" style="cursor: pointer;">查看错误堆栈</summary>
                                        <pre class="mt-2 p-2 bg-light border rounded small text-dark" style="max-height: 200px; overflow-y: auto; white-space: pre-wrap; word-break: break-all; color: #333 !important;">${this.escapeHtml(transactionData.errorStack)}</pre>
                                    </details>
                                </div>
                                ` : ''}
                                ${transactionData.extendProperties ? `
                                <div class="col-12">
                                    <details>
                                        <summary class="text-muted small" style="cursor: pointer;">查看扩展属性</summary>
                                        <pre class="mt-2 p-2 bg-light border rounded small text-dark" style="color: #333 !important;">${this.escapeHtml(transactionData.extendProperties)}</pre>
                                    </details>
                                </div>
                                ` : ''}
                            </div>
                        </div>
                    </div>
                `;
            } else {
                // 清空错误信息
                errorContainer.innerHTML = '';
            }
        }
    }

    /**
     * HTML转义
     * @param {string} text 文本
     * @returns {string} 转义后的文本
     */
    escapeHtml(text) {
        if (!text) return '';
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    /**
     * 更新事务状态
     * @param {Object} transactionData 事务数据
     */
    updateTransactionStatus(transactionData) {
        const statusAvatar = document.getElementById('transactionStatusAvatar');
        const statusBadge = document.getElementById('transactionStatusBadge');
        const modeText = document.getElementById('transactionModeText');
        const progress = document.getElementById('transactionProgress');

        if (statusAvatar && statusBadge && modeText && progress) {
            // 设置模式文本
            modeText.textContent = transactionData.transactionMode || 'AT';

            // 设置状态样式
            const statusInfo = this.getStatusInfo(transactionData.status);
            statusAvatar.className = `avatar avatar-lg ${statusInfo.avatarClass}`;
            statusBadge.innerHTML = `<span class="badge ${statusInfo.badgeClass}">${statusInfo.text}</span>`;
            progress.textContent = statusInfo.progress;
        }
    }

    /**
     * 获取状态信息
     * @param {string} status 状态
     * @returns {Object} 状态信息
     */
    getStatusInfo(status) {
        const statusMap = {
            // 基本状态
            'INIT': {
                text: '初始化',
                badgeClass: 'bg-secondary',
                avatarClass: 'bg-secondary text-white',
                progress: '初始化中'
            },
            'PENDING': {
                text: '待执行',
                badgeClass: 'bg-warning',
                avatarClass: 'bg-warning text-white',
                progress: '等待执行'
            },
            'EXECUTING': {
                text: '执行中',
                badgeClass: 'bg-info',
                avatarClass: 'bg-info text-white',
                progress: '正在执行'
            },
            'PREPARING': {
                text: '准备提交',
                badgeClass: 'bg-info',
                avatarClass: 'bg-info text-white',
                progress: '准备提交'
            },
            'WAITING': {
                text: '等待中',
                badgeClass: 'bg-warning',
                avatarClass: 'bg-warning text-white',
                progress: '等待中'
            },
            'PAUSED': {
                text: '暂停',
                badgeClass: 'bg-warning',
                avatarClass: 'bg-warning text-white',
                progress: '已暂停'
            },

            // 成功状态
            'SUCCESS': {
                text: '成功',
                badgeClass: 'bg-success',
                avatarClass: 'bg-success text-white',
                progress: '执行完成'
            },

            // 失败状态
            'FAILED': {
                text: '失败',
                badgeClass: 'bg-danger',
                avatarClass: 'bg-danger text-white',
                progress: '执行失败'
            },

            // 撤销相关状态
            'UNDOING': {
                text: '撤销中',
                badgeClass: 'bg-warning',
                avatarClass: 'bg-warning text-white',
                progress: '正在撤销'
            },
            'UNDONE': {
                text: '撤销成功',
                badgeClass: 'bg-success',
                avatarClass: 'bg-success text-white',
                progress: '撤销完成'
            },
            'UNDO_FAILED': {
                text: '撤销失败',
                badgeClass: 'bg-danger',
                avatarClass: 'bg-danger text-white',
                progress: '撤销失败'
            },

            // 其他状态
            'TIMEOUT': {
                text: '超时',
                badgeClass: 'bg-warning',
                avatarClass: 'bg-warning text-white',
                progress: '执行超时'
            },
            'CANCELLED': {
                text: '已取消',
                badgeClass: 'bg-secondary',
                avatarClass: 'bg-secondary text-white',
                progress: '已取消'
            },
            'MANUAL_INTERVENTION_REQUIRED': {
                text: '需要人工干预',
                badgeClass: 'bg-danger',
                avatarClass: 'bg-danger text-white',
                progress: '需要人工干预'
            },

            // 兼容旧状态名称
            'COMMITTED': {
                text: '已提交',
                badgeClass: 'bg-success',
                avatarClass: 'bg-success text-white',
                progress: '已提交'
            },
            'ROLLBACK': {
                text: '已回滚',
                badgeClass: 'bg-warning',
                avatarClass: 'bg-warning text-white',
                progress: '已回滚'
            },
            'COMPENSATING': {
                text: '补偿中',
                badgeClass: 'bg-warning',
                avatarClass: 'bg-warning text-white',
                progress: '正在补偿'
            },
            'COMPENSATED': {
                text: '已补偿',
                badgeClass: 'bg-success',
                avatarClass: 'bg-success text-white',
                progress: '补偿完成'
            },
            'COMPENSATE_FAILED': {
                text: '补偿失败',
                badgeClass: 'bg-danger',
                avatarClass: 'bg-danger text-white',
                progress: '补偿失败'
            },
            'ROLLBACKED': {
                text: '回滚完成',
                badgeClass: 'bg-success',
                avatarClass: 'bg-success text-white',
                progress: '回滚完成'
            },
            'PARTIAL_FAILED': {
                text: '部分失败',
                badgeClass: 'bg-warning',
                avatarClass: 'bg-warning text-white',
                progress: '部分失败'
            },
            'ROLLBACK_FAILED': {
                text: '回滚失败',
                badgeClass: 'bg-danger',
                avatarClass: 'bg-danger text-white',
                progress: '回滚失败'
            }
        };

        const result = statusMap[status];
        if (!result) {
            console.warn(`发现未知事务状态: ${status}，请检查状态映射配置`);
            return {
                text: `未知状态(${status})`,
                badgeClass: 'bg-secondary',
                avatarClass: 'bg-secondary text-white',
                progress: '状态未知'
            };
        }
        return result;
    }

    /**
     * 更新重试信息
     * @param {Object} transactionData 事务数据
     */
    updateRetryInfo(transactionData) {
        const retryCount = transactionData.retryCount || 0;
        const currentRetry = transactionData.currentRetryCount || 0;

        this.setElementText('detailRetryCount', retryCount.toString());
        this.setElementText('detailCurrentRetry', currentRetry.toString());

        // 更新重试进度条
        const progressBar = document.getElementById('detailRetryProgress');
        if (progressBar && retryCount > 0) {
            const percentage = Math.min((currentRetry / retryCount) * 100, 100);
            progressBar.style.width = `${percentage}%`;
            progressBar.className = `progress-bar ${percentage >= 100 ? 'bg-danger' : 'bg-warning'}`;
        }
    }

    /**
     * 更新事务步骤
     * @param {Array} steps 步骤列表
     */
    updateTransactionSteps(steps) {
        const container = document.getElementById('transactionStepsContainer');
        if (!container) return;

        if (!steps || steps.length === 0) {
            container.innerHTML = `
                <div class="text-center py-5">
                    <div class="text-muted">
                        <svg xmlns="http://www.w3.org/2000/svg" class="icon icon-lg mb-3" width="48" height="48" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                            <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
                            <circle cx="12" cy="12" r="9"/>
                            <line x1="9" y1="9" x2="15" y2="15"/>
                            <line x1="15" y1="9" x2="9" y2="15"/>
                        </svg>
                        <div class="h5">暂无步骤信息</div>
                        <div>该事务没有执行步骤记录</div>
                    </div>
                </div>
            `;
            return;
        }

        // 更新整体进度
        this.updateOverallProgress(steps);

        // 生成步骤卡片
        const stepsHtml = steps.map((step, index) => this.generateStepCard(step, index)).join('');
        container.innerHTML = stepsHtml;

        // 绑定步骤操作事件
        this.bindStepEvents();
    }

    /**
     * 更新整体进度
     * @param {Array} steps 步骤列表
     */
    updateOverallProgress(steps) {
        const successSteps = steps.filter(step => step.status === 'SUCCESS').length;
        const totalSteps = steps.length;
        const percentage = totalSteps > 0 ? (successSteps / totalSteps) * 100 : 0;

        this.setElementText('overallProgress', `${successSteps}/${totalSteps}`);

        const progressBar = document.getElementById('overallProgressBar');
        if (progressBar) {
            progressBar.style.width = `${percentage}%`;
            progressBar.className = `progress-bar ${percentage === 100 ? 'bg-success' : 'bg-primary'}`;
        }
    }

    /**
     * 生成步骤卡片HTML
     * @param {Object} step 步骤数据
     * @param {number} index 步骤索引
     * @returns {string} HTML字符串
     */
    generateStepCard(step, index) {
        const statusInfo = this.getStepStatusInfo(step.status);
        const duration = this.formatDuration(step.duration);
        const retryInfo = step.currentRetryCount > 0 ? `(重试${step.currentRetryCount}次)` : '';

        return `
            <div class="card border-0 shadow-sm mb-3 step-card" data-step-id="${step.stepId}">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-auto">
                            <div class="avatar ${statusInfo.avatarClass}">
                                <span class="fw-bold">${index + 1}</span>
                            </div>
                        </div>
                        <div class="col">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="fw-bold text-dark mb-1">${step.stepName || '未命名步骤'}</div>
                                    <div class="text-muted small mb-2">
                                        <span class="badge ${statusInfo.badgeClass} me-2">${statusInfo.text}</span>
                                        ${step.critical ? '<span class="badge bg-warning-lt text-warning">关键步骤</span>' : ''}
                                        ${retryInfo ? `<span class="badge bg-info-lt text-info">${retryInfo}</span>` : ''}
                                    </div>
                                    <div class="text-muted small">
                                        <strong>服务:</strong> ${step.targetService || '-'} <br>
                                        <strong>方法:</strong> ${step.targetMethod || '-'}
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="text-muted small">
                                        <strong>开始时间:</strong><br>
                                        ${this.formatDateTime(step.startTime)}<br>
                                        <strong>持续时间:</strong> ${duration}
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="btn-list">
                                        <button class="btn btn-sm btn-outline-info view-step-history" data-step-id="${step.stepId}" data-step-name="${step.stepName}">
                                            <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="16" height="16" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                                <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
                                                <circle cx="12" cy="12" r="9"/>
                                                <polyline points="12,7 12,12 16,14"/>
                                            </svg>
                                            执行历史
                                        </button>
                                        ${this.generateStepActionButtons(step)}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    ${step.errorMessage ? `
                        <div class="mt-3 p-3 bg-danger-lt border border-danger rounded">
                            <div class="fw-bold text-danger mb-1">错误信息:</div>
                            <div class="text-danger small">${step.errorMessage}</div>
                        </div>
                    ` : ''}
                </div>
            </div>
        `;
    }

    /**
     * 获取步骤状态信息
     * @param {string} status 状态
     * @returns {Object} 状态信息
     */
    getStepStatusInfo(status) {
        const statusMap = {
            // 基本执行状态
            'PENDING': {
                text: '待执行',
                badgeClass: 'bg-warning',
                avatarClass: 'bg-warning text-white'
            },
            'EXECUTING': {
                text: '执行中',
                badgeClass: 'bg-info',
                avatarClass: 'bg-info text-white'
            },
            'SUCCESS': {
                text: '执行成功',
                badgeClass: 'bg-success',
                avatarClass: 'bg-success text-white'
            },
            'FAILED': {
                text: '执行失败',
                badgeClass: 'bg-danger',
                avatarClass: 'bg-danger text-white'
            },

            // 撤销相关状态
            'UNDOING': {
                text: '撤销中',
                badgeClass: 'bg-warning',
                avatarClass: 'bg-warning text-white'
            },
            'UNDONE': {
                text: '撤销成功',
                badgeClass: 'bg-success',
                avatarClass: 'bg-success text-white'
            },
            'UNDO_FAILED': {
                text: '撤销失败',
                badgeClass: 'bg-danger',
                avatarClass: 'bg-danger text-white'
            },

            // 其他状态
            'SKIPPED': {
                text: '已跳过',
                badgeClass: 'bg-secondary',
                avatarClass: 'bg-secondary text-white'
            },
            'TIMEOUT': {
                text: '超时',
                badgeClass: 'bg-warning',
                avatarClass: 'bg-warning text-white'
            },

            // 兼容旧状态名称
            'COMMITTED': {
                text: '已提交',
                badgeClass: 'bg-success',
                avatarClass: 'bg-success text-white'
            },
            'ROLLBACK': {
                text: '已回滚',
                badgeClass: 'bg-warning',
                avatarClass: 'bg-warning text-white'
            },
            'COMPENSATING': {
                text: '补偿中',
                badgeClass: 'bg-warning',
                avatarClass: 'bg-warning text-white'
            },
            'COMPENSATED': {
                text: '已补偿',
                badgeClass: 'bg-success',
                avatarClass: 'bg-success text-white'
            },
            'COMPENSATE_FAILED': {
                text: '补偿失败',
                badgeClass: 'bg-danger',
                avatarClass: 'bg-danger text-white'
            },
            'ROLLBACKED': {
                text: '回滚完成',
                badgeClass: 'bg-success',
                avatarClass: 'bg-success text-white'
            },
            'CANCELLED': {
                text: '已取消',
                badgeClass: 'bg-secondary',
                avatarClass: 'bg-secondary text-white'
            }
        };

        const result = statusMap[status];
        if (!result) {
            console.warn(`发现未知步骤状态: ${status}，请检查状态映射配置`);
            return {
                text: `未知状态(${status})`,
                badgeClass: 'bg-secondary',
                avatarClass: 'bg-secondary text-white'
            };
        }
        return result;
    }

    /**
     * 生成步骤操作按钮
     * @param {Object} step 步骤数据
     * @returns {string} 按钮HTML
     */
    generateStepActionButtons(step) {
        const buttons = [];

        if (step.status === 'FAILED') {
            buttons.push(`
                <button class="btn btn-sm btn-outline-primary retry-step" data-step-id="${step.stepId}">
                    <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="16" height="16" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                        <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
                        <path d="M20 11a8.1 8.1 0 0 0 -15.5 -2m-.5 -4v4h4"/>
                        <path d="M4 13a8.1 8.1 0 0 0 15.5 2m.5 4v-4h-4"/>
                    </svg>
                    重试
                </button>
            `);
        }

        if (step.status === 'PENDING' || step.status === 'FAILED') {
            buttons.push(`
                <button class="btn btn-sm btn-outline-warning skip-step" data-step-id="${step.stepId}">
                    <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="16" height="16" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                        <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
                        <path d="M4 4l11.733 16h4.267l-11.733 -16z"/>
                        <path d="M4 20l6.768 -6.768m2.46 -2.46l6.772 -6.772"/>
                    </svg>
                    跳过
                </button>
            `);
        }

        return buttons.join('');
    }

    /**
     * 绑定步骤事件
     */
    bindStepEvents() {
        // 移除之前的事件监听器（通过重新克隆元素来清除所有事件监听器）
        const container = document.getElementById('transactionStepsContainer');
        if (!container) return;

        // 使用事件委托来避免重复绑定问题
        // 移除之前的委托事件监听器
        const oldHandler = container._stepEventHandler;
        if (oldHandler) {
            container.removeEventListener('click', oldHandler);
        }

        // 创建新的事件处理器
        const newHandler = (e) => {
            e.preventDefault();
            e.stopPropagation();

            const target = e.target.closest('.view-step-history, .retry-step, .skip-step');
            if (!target) return;

            const stepId = target.getAttribute('data-step-id');

            if (target.classList.contains('view-step-history')) {
                const stepName = target.getAttribute('data-step-name');
                this.showStepHistory(stepId, stepName);
            } else if (target.classList.contains('retry-step')) {
                this.retryStep(stepId);
            } else if (target.classList.contains('skip-step')) {
                this.skipStep(stepId);
            }
        };

        // 绑定新的事件处理器
        container.addEventListener('click', newHandler);
        container._stepEventHandler = newHandler;
    }

    /**
     * 显示步骤执行历史
     * @param {string} stepId 步骤ID
     * @param {string} stepName 步骤名称
     */
    async showStepHistory(stepId, stepName) {
        try {
            console.log(`查看步骤执行历史: ${stepId}`);

            // 显示步骤历史模态框
            this.stepHistoryModal.show();

            // 更新模态框标题
            const modalTitle = document.querySelector('#stepHistoryModal .modal-title');
            if (modalTitle) {
                modalTitle.textContent = `步骤执行历史 - ${stepName}`;
            }

            // 显示加载状态
            const content = document.getElementById('stepHistoryContent');
            if (content) {
                content.innerHTML = `
                    <div class="text-center py-5">
                        <div class="spinner-border text-primary mb-3" role="status">
                            <span class="visually-hidden">加载中...</span>
                        </div>
                        <div class="h5 text-muted">正在加载执行历史...</div>
                    </div>
                `;
            }

            // 加载步骤执行历史
            const response = await window.apiService.getStepExecutionHistory(this.currentTransactionId, stepId);

            // 调试：打印完整的响应数据
            console.log('步骤执行历史原始响应:', JSON.stringify(response, null, 2));

            if (response.success !== false && (response.data || response.code === 200)) {
                const responseData = response.data || response;

                // 调试：打印响应数据结构
                console.log('响应数据类型:', typeof responseData);
                console.log('响应数据内容:', responseData);

                // 处理后端返回的数据结构
                let historyData;
                if (Array.isArray(responseData)) {
                    // 如果直接返回数组
                    historyData = responseData;
                    console.log('检测到数组格式，直接使用');
                } else if (responseData && responseData.executionHistory) {
                    // 如果返回包含executionHistory的对象
                    historyData = responseData.executionHistory;
                    console.log('检测到对象格式，使用executionHistory字段');
                } else if (responseData && typeof responseData === 'object') {
                    // 如果是对象但没有executionHistory字段，尝试查找其他可能的字段
                    const possibleFields = ['histories', 'data', 'list', 'records'];
                    for (const field of possibleFields) {
                        if (responseData[field] && Array.isArray(responseData[field])) {
                            historyData = responseData[field];
                            console.log(`检测到对象格式，使用${field}字段`);
                            break;
                        }
                    }
                    if (!historyData) {
                        // 如果都没找到，将对象转换为数组
                        historyData = [responseData];
                        console.log('未找到数组字段，将对象包装为数组');
                    }
                } else {
                    // 其他情况，返回空数组
                    historyData = [];
                    console.log('无法识别数据格式，使用空数组');
                }

                console.log('最终处理的历史数据:', historyData);
                this.renderStepHistory(historyData);
            } else {
                throw new Error(response.message || '获取步骤执行历史失败');
            }

        } catch (error) {
            console.error('显示步骤执行历史失败:', error);
            const content = document.getElementById('stepHistoryContent');
            if (content) {
                content.innerHTML = `
                    <div class="alert alert-danger" role="alert">
                        <h4 class="alert-title">加载失败</h4>
                        <div class="text-muted">${error.message}</div>
                    </div>
                `;
            }
        }
    }

    /**
     * 渲染步骤执行历史
     * @param {Array} historyData 历史数据
     */
    renderStepHistory(historyData) {
        const content = document.getElementById('stepHistoryContent');
        if (!content) return;

        console.log('渲染步骤执行历史，数据类型:', typeof historyData, '数据:', historyData);

        // 确保historyData是数组
        if (!Array.isArray(historyData)) {
            console.warn('historyData不是数组，尝试转换:', historyData);
            historyData = [];
        }

        if (historyData.length === 0) {
            content.innerHTML = `
                <div class="empty">
                    <div class="empty-img">
                        <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTI4IiBoZWlnaHQ9IjEyOCIgdmlld0JveD0iMCAwIDEyOCAxMjgiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxwYXRoIGQ9Ik02NCA5NkM4MC41Njg1IDk2IDk0IDgyLjU2ODUgOTQgNjZDOTQgNDkuNDMxNSA4MC41Njg1IDM2IDY0IDM2QzQ3LjQzMTUgMzYgMzQgNDkuNDMxNSAzNCA2NkMzNCA4Mi41Njg1IDQ3LjQzMTUgOTYgNjQgOTZaIiBzdHJva2U9IiNEQURERTIiIHN0cm9rZS13aWR0aD0iMiIvPgo8cGF0aCBkPSJNNTQgNTZMNjQgNjZMNzQgNTYiIHN0cm9rZT0iI0RBRERFMiIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiLz4KPC9zdmc+" alt="暂无数据" style="height: 5rem;">
                    </div>
                    <p class="empty-title">暂无执行历史</p>
                    <p class="empty-subtitle text-muted">该步骤还没有执行历史记录</p>
                </div>
            `;
            return;
        }

        try {
            // 更新统计信息
            this.updateHistoryStats(historyData);

            const historyHtml = historyData.map((history, index) => this.generateHistoryCard(history, index)).join('');
            content.innerHTML = `
                <div class="timeline">
                    ${historyHtml}
                </div>
            `;
        } catch (error) {
            console.error('渲染步骤执行历史失败:', error);
            content.innerHTML = `
                <div class="alert alert-danger" role="alert">
                    <h4 class="alert-title">渲染失败</h4>
                    <div class="text-muted">渲染执行历史时发生错误: ${error.message}</div>
                </div>
            `;
        }
    }

    /**
     * 生成历史记录卡片
     * @param {Object} history 历史记录
     * @param {number} index 索引
     * @returns {string} HTML字符串
     */
    generateHistoryCard(history, index) {
        const statusInfo = this.getStepStatusInfo(history.status);
        const executionTypeText = this.getExecutionTypeText(history.executionType);
        const isSuccess = history.status === 'SUCCESS' || history.status === 'COMMITTED';
        const isFailure = history.status === 'FAILED' || history.status === 'ROLLBACK';
        const isExecuting = history.status === 'EXECUTING' || history.status === 'PENDING';

        // 获取执行类型图标
        const getExecutionIcon = (type) => {
            switch (type) {
                case 'EXECUTE':
                    return '▶️';
                case 'RETRY':
                    return '🔄';
                case 'COMPENSATE':
                    return '↩️';
                default:
                    return '⚡';
            }
        };

        // 获取状态图标
        const getStatusIcon = (status) => {
            if (isSuccess) return '✅';
            if (isFailure) return '❌';
            if (isExecuting) return '⏳';
            return '❓';
        };

        return `
            <div class="timeline-item">
                <div class="timeline-point ${this.getTimelinePointClass(history.status)}">
                    <span class="timeline-point-icon">${getStatusIcon(history.status)}</span>
                </div>
                <div class="timeline-content">
                    <div class="card shadow-sm">
                        <div class="card-header bg-light">
                            <div class="d-flex justify-content-between align-items-center">
                                <div class="d-flex align-items-center">
                                    <span class="me-2" style="font-size: 1.2em;">${getExecutionIcon(history.executionType)}</span>
                                    <h6 class="mb-0">第${history.attemptNumber}次${executionTypeText}</h6>
                                    <span class="badge ${statusInfo.badgeClass} ms-2">${statusInfo.text}</span>
                                </div>
                                <small class="text-muted">${this.formatDateTime(history.startTime)}</small>
                            </div>
                        </div>
                        <div class="card-body">
                            <!-- 基本信息 -->
                            <div class="row g-3 mb-3">
                                <div class="col-md-3">
                                    <div class="d-flex align-items-center">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="icon icon-sm me-2 text-muted" width="16" height="16" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                            <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
                                            <circle cx="12" cy="12" r="9"/>
                                            <polyline points="12,7 12,12 15,15"/>
                                        </svg>
                                        <div>
                                            <div class="text-muted small">执行时间</div>
                                            <div class="fw-bold">${this.formatDuration(history.duration)}</div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="d-flex align-items-center">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="icon icon-sm me-2 text-muted" width="16" height="16" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                            <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
                                            <circle cx="12" cy="12" r="3"/>
                                            <path d="M12 1v6m0 6v6"/>
                                            <path d="M21 12h-6m-6 0H3"/>
                                        </svg>
                                        <div>
                                            <div class="text-muted small">执行节点</div>
                                            <div class="fw-bold">${history.executionNode || '未知'}</div>
                                        </div>
                                    </div>
                                </div>
                                ${history.networkLatency ? `
                                <div class="col-md-3">
                                    <div class="d-flex align-items-center">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="icon icon-sm me-2 text-muted" width="16" height="16" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                            <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
                                            <path d="M3 12a9 9 0 1 0 18 0a9 9 0 0 0 -18 0"/>
                                            <path d="M12 7v5l3 3"/>
                                        </svg>
                                        <div>
                                            <div class="text-muted small">网络延迟</div>
                                            <div class="fw-bold">${history.networkLatency}ms</div>
                                        </div>
                                    </div>
                                </div>
                                ` : ''}
                                ${history.httpStatusCode ? `
                                <div class="col-md-3">
                                    <div class="d-flex align-items-center">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="icon icon-sm me-2 text-muted" width="16" height="16" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                            <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
                                            <path d="M3 7v10a2 2 0 0 0 2 2h14a2 2 0 0 0 2 -2V7a2 2 0 0 0 -2 -2H5a2 2 0 0 0 -2 2z"/>
                                            <path d="M8 11h8"/>
                                            <path d="M8 15h6"/>
                                        </svg>
                                        <div>
                                            <div class="text-muted small">HTTP状态</div>
                                            <div class="fw-bold">${history.httpStatusCode}</div>
                                        </div>
                                    </div>
                                </div>
                                ` : ''}
                            </div>

                            <!-- 请求响应数据 -->
                            ${(history.requestParams || history.responseData) ? `
                            <div class="row g-3">
                                ${history.requestParams ? `
                                <div class="col-md-6">
                                    <div class="mb-2">
                                        <label class="form-label small fw-bold text-muted">
                                            <svg xmlns="http://www.w3.org/2000/svg" class="icon icon-sm me-1" width="16" height="16" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                                <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
                                                <path d="M7 8l-4 4l4 4"/>
                                                <path d="M17 8l4 4l-4 4"/>
                                                <path d="M14 4l-4 16"/>
                                            </svg>
                                            请求参数
                                        </label>
                                    </div>
                                    <div class="bg-light p-2 rounded border">
                                        <code class="small text-dark">${this.formatJsonData(history.requestParams)}</code>
                                    </div>
                                </div>
                                ` : ''}
                                ${history.responseData ? `
                                <div class="col-md-6">
                                    <div class="mb-2">
                                        <label class="form-label small fw-bold text-muted">
                                            <svg xmlns="http://www.w3.org/2000/svg" class="icon icon-sm me-1" width="16" height="16" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                                <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
                                                <path d="M12 3c7.2 0 9 1.8 9 9s-1.8 9-9 9s-9-1.8-9-9s1.8-9 9-9z"/>
                                                <path d="M15 9l-6 6"/>
                                                <path d="M9 9l6 6"/>
                                            </svg>
                                            响应数据
                                        </label>
                                    </div>
                                    <div class="bg-light p-2 rounded border">
                                        <code class="small text-dark">${this.formatJsonData(history.responseData)}</code>
                                    </div>
                                </div>
                                ` : ''}
                            </div>
                            ` : ''}

                            <!-- 错误信息 -->
                            ${history.errorMessage ? `
                            <div class="mt-3">
                                <div class="alert alert-danger mb-0">
                                    <div class="d-flex align-items-start">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="icon icon-sm me-2 mt-1 text-danger" width="16" height="16" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                            <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
                                            <circle cx="12" cy="12" r="9"/>
                                            <line x1="12" y1="8" x2="12" y2="12"/>
                                            <line x1="12" y1="16" x2="12.01" y2="16"/>
                                        </svg>
                                        <div class="flex-fill">
                                            <div class="fw-bold">错误信息</div>
                                            <div class="small">${history.errorMessage}</div>
                                            ${history.retryReason ? `
                                            <div class="mt-2 p-2 bg-warning-lt border border-warning rounded">
                                                <div class="fw-bold text-warning">重试原因</div>
                                                <div class="small text-warning">${history.retryReason}</div>
                                            </div>
                                            ` : ''}
                                        </div>
                                    </div>
                                </div>
                            </div>
                            ` : ''}
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * 更新执行历史统计信息
     * @param {Array} historyData 历史数据
     */
    updateHistoryStats(historyData) {
        if (!historyData || historyData.length === 0) {
            // 重置统计信息
            this.setStatValue('totalAttempts', '0');
            this.setStatValue('successAttempts', '0');
            this.setStatValue('failedAttempts', '0');
            this.setStatValue('avgDuration', '0ms');
            return;
        }

        const totalAttempts = historyData.length;
        const successAttempts = historyData.filter(h => h.status === 'SUCCESS' || h.status === 'COMMITTED').length;
        const failedAttempts = historyData.filter(h => h.status === 'FAILED' || h.status === 'ROLLBACK').length;

        // 计算平均耗时
        const validDurations = historyData.filter(h => h.duration && h.duration > 0).map(h => h.duration);
        const avgDuration = validDurations.length > 0
            ? Math.round(validDurations.reduce((sum, d) => sum + d, 0) / validDurations.length)
            : 0;

        // 更新统计显示
        this.setStatValue('totalAttempts', totalAttempts.toString());
        this.setStatValue('successAttempts', successAttempts.toString());
        this.setStatValue('failedAttempts', failedAttempts.toString());
        this.setStatValue('avgDuration', this.formatDuration(avgDuration));
    }

    /**
     * 设置统计值
     * @param {string} elementId 元素ID
     * @param {string} value 值
     */
    setStatValue(elementId, value) {
        const element = document.getElementById(elementId);
        if (element) {
            element.textContent = value;
        }
    }

    /**
     * 获取时间轴点的样式类
     * @param {string} status 状态
     * @returns {string} CSS类名
     */
    getTimelinePointClass(status) {
        switch (status) {
            case 'SUCCESS':
            case 'COMMITTED':
                return 'timeline-point-success';
            case 'FAILED':
            case 'ROLLBACK':
                return 'timeline-point-danger';
            case 'EXECUTING':
            case 'PENDING':
                return 'timeline-point-warning';
            case 'TIMEOUT':
                return 'timeline-point-secondary';
            default:
                return 'timeline-point-primary';
        }
    }

    /**
     * 格式化JSON数据显示
     * @param {string} jsonStr JSON字符串
     * @returns {string} 格式化后的字符串
     */
    formatJsonData(jsonStr) {
        if (!jsonStr) return '';

        try {
            // 尝试解析并格式化JSON
            const parsed = JSON.parse(jsonStr);
            const formatted = JSON.stringify(parsed, null, 2);

            // 如果格式化后的JSON太长，提供展开/收起功能
            if (formatted.length > 500) {
                const truncated = this.truncateText(formatted, 300);
                const id = 'json-' + Math.random().toString(36).substr(2, 9);
                return `
                    <div class="json-container">
                        <div id="${id}-short">${this.escapeHtml(truncated)}
                            <br><button class="btn btn-sm btn-link p-0 mt-1" onclick="this.parentElement.style.display='none'; document.getElementById('${id}-full').style.display='block';">
                                <svg xmlns="http://www.w3.org/2000/svg" class="icon icon-sm" width="16" height="16" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                    <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
                                    <polyline points="6,9 12,15 18,9"/>
                                </svg>
                                展开完整内容
                            </button>
                        </div>
                        <div id="${id}-full" style="display: none;">${this.escapeHtml(formatted)}
                            <br><button class="btn btn-sm btn-link p-0 mt-1" onclick="this.parentElement.style.display='none'; document.getElementById('${id}-short').style.display='block';">
                                <svg xmlns="http://www.w3.org/2000/svg" class="icon icon-sm" width="16" height="16" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                    <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
                                    <polyline points="18,15 12,9 6,15"/>
                                </svg>
                                收起
                            </button>
                        </div>
                    </div>
                `;
            } else {
                return this.escapeHtml(formatted);
            }
        } catch (e) {
            // 如果不是有效JSON，直接返回原字符串（完整显示）
            return this.escapeHtml(jsonStr);
        }
    }


    /**
     * 获取执行类型文本
     * @param {string} executionType 执行类型
     * @returns {string} 执行类型文本
     */
    getExecutionTypeText(executionType) {
        const typeMap = {
            'EXECUTE': '执行',
            'RETRY': '重试',
            'COMPENSATE': '补偿'
        };
        return typeMap[executionType] || '执行';
    }

    /**
     * 重试步骤
     * @param {string} stepId 步骤ID
     */
    async retryStep(stepId) {
        try {
            const result = await ConfirmDialog.confirm('确定要重试这个步骤吗？');
            if (!result.confirmed) {
                return;
            }

            console.log(`重试步骤: ${stepId}`);

            const response = await window.apiService.retryStep(this.currentTransactionId, stepId);

            if (response.success !== false && (response.code === 200 || response.code === '200')) {
                this.showSuccessMessage('步骤重试请求已提交');
                // 刷新步骤信息
                setTimeout(() => {
                    this.refreshSteps();
                }, 1000);
            } else {
                throw new Error(response.message || '步骤重试失败');
            }

        } catch (error) {
            console.error('重试步骤失败:', error);
            this.showErrorMessage('步骤重试失败: ' + error.message);
        }
    }

    /**
     * 跳过步骤
     * @param {string} stepId 步骤ID
     */
    async skipStep(stepId) {
        try {
            const reason = prompt('请输入跳过原因:');
            if (!reason) {
                return;
            }

            console.log(`跳过步骤: ${stepId}, 原因: ${reason}`);

            const response = await window.apiService.skipStep(this.currentTransactionId, stepId, reason);

            if (response.success !== false && (response.code === 200 || response.code === '200')) {
                this.showSuccessMessage('步骤已跳过');
                // 刷新步骤信息
                setTimeout(() => {
                    this.refreshSteps();
                }, 1000);
            } else {
                throw new Error(response.message || '跳过步骤失败');
            }

        } catch (error) {
            console.error('跳过步骤失败:', error);
            this.showErrorMessage('跳过步骤失败: ' + error.message);
        }
    }

    /**
     * 重试事务
     */
    async retryTransaction() {
        try {
            console.log('retryTransaction方法被调用，调用栈:', new Error().stack);

            // 检查是否已经在处理中
            if (this._isProcessing) {
                console.log('已经在处理中，忽略重复调用');
                return;
            }
            this._isProcessing = true;

            const result = await ConfirmDialog.confirm('确定要重试整个事务吗？');
            console.log('ConfirmDialog结果:', result);
            if (!result.confirmed) {
                this._isProcessing = false;
                return;
            }

            console.log(`重试事务: ${this.currentTransactionId}`);

            const operatorId = this.getCurrentOperatorId();
            const reason = '手动重试事务';
            const response = await window.apiService.retryTransaction(this.currentTransactionId, operatorId, reason);

            console.log('重试事务响应:', response);

            // 适配ApiResponse格式
            if (response.success !== false) {
                this.showSuccessMessage('事务重试请求已提交');
                // 刷新事务详情
                setTimeout(() => {
                    this.refreshTransactionDetail();
                }, 1000);
            } else {
                throw new Error(response.message || '事务重试失败');
            }

        } catch (error) {
            console.error('重试事务失败:', error);
            this.showErrorMessage('事务重试失败: ' + error.message);
        } finally {
            this._isProcessing = false;
        }
    }

    /**
     * 回滚事务
     */
    async rollbackTransaction() {
        try {
            if (this._isProcessing) {
                console.log('已经在处理中，忽略重复调用');
                return;
            }
            this._isProcessing = true;

            const result = await ConfirmDialog.danger('确定要手动回滚这个事务吗？此操作不可撤销！', '危险操作');
            if (!result.confirmed) {
                this._isProcessing = false;
                return;
            }

            console.log(`回滚事务: ${this.currentTransactionId}`);

            const operatorId = this.getCurrentOperatorId();
            const reason = '手动回滚事务';
            const response = await window.apiService.rollbackTransaction(this.currentTransactionId, operatorId, reason);

            if (response.success !== false && (response.code === 200 || response.code === '200')) {
                this.showSuccessMessage('事务回滚请求已提交');
                // 刷新事务详情
                setTimeout(() => {
                    this.refreshTransactionDetail();
                }, 1000);
            } else {
                throw new Error(response.message || '事务回滚失败');
            }

        } catch (error) {
            console.error('回滚事务失败:', error);
            this.showErrorMessage('事务回滚失败: ' + error.message);
        } finally {
            this._isProcessing = false;
        }
    }

    /**
     * 提交事务
     */
    async commitTransaction() {
        try {
            if (this._isProcessing) {
                console.log('已经在处理中，忽略重复调用');
                return;
            }
            this._isProcessing = true;

            const result = await ConfirmDialog.confirm('确定要手动提交这个事务吗？');
            if (!result.confirmed) {
                this._isProcessing = false;
                return;
            }

            console.log(`提交事务: ${this.currentTransactionId}`);

            const operatorId = this.getCurrentOperatorId();
            const reason = '手动提交事务';
            const response = await window.apiService.commitTransaction(this.currentTransactionId, operatorId, reason);

            if (response.success !== false && (response.code === 200 || response.code === '200')) {
                this.showSuccessMessage('事务提交请求已提交');
                // 刷新事务详情
                setTimeout(() => {
                    this.refreshTransactionDetail();
                }, 1000);
            } else {
                throw new Error(response.message || '事务提交失败');
            }

        } catch (error) {
            console.error('提交事务失败:', error);
            this.showErrorMessage('事务提交失败: ' + error.message);
        } finally {
            this._isProcessing = false;
        }
    }

    /**
     * 取消事务
     */
    async cancelTransaction() {
        try {
            if (this._isProcessing) {
                console.log('已经在处理中，忽略重复调用');
                return;
            }
            this._isProcessing = true;

            const result = await ConfirmDialog.confirm('确定要取消这个事务吗？');
            if (!result.confirmed) {
                this._isProcessing = false;
                return;
            }

            console.log(`取消事务: ${this.currentTransactionId}`);

            const operatorId = this.getCurrentOperatorId();
            const reason = '手动取消事务';
            const response = await window.apiService.cancelTransaction(this.currentTransactionId, operatorId, reason);

            if (response.success !== false && (response.code === 200 || response.code === '200')) {
                this.showSuccessMessage('事务取消请求已提交');
                // 刷新事务详情
                setTimeout(() => {
                    this.refreshTransactionDetail();
                }, 1000);
            } else {
                throw new Error(response.message || '事务取消失败');
            }

        } catch (error) {
            console.error('取消事务失败:', error);
            this.showErrorMessage('事务取消失败: ' + error.message);
        } finally {
            this._isProcessing = false;
        }
    }

    /**
     * 刷新事务详情
     */
    async refreshTransactionDetail() {
        if (this.currentTransactionId) {
            try {
                await this.loadTransactionDetail(this.currentTransactionId);
                this.showSuccessMessage('事务详情已刷新');
            } catch (error) {
                this.showErrorMessage('刷新失败: ' + error.message);
            }
        }
    }

    /**
     * 刷新步骤信息
     */
    async refreshSteps() {
        if (this.currentTransactionData && this.currentTransactionData.steps) {
            try {
                await this.loadTransactionDetail(this.currentTransactionId);
                this.showSuccessMessage('步骤信息已刷新');
            } catch (error) {
                this.showErrorMessage('刷新失败: ' + error.message);
            }
        }
    }

    /**
     * 过滤步骤
     */
    filterSteps() {
        const filterValue = document.getElementById('stepFilterStatus').value;
        const stepCards = document.querySelectorAll('.step-card');

        stepCards.forEach(card => {
            if (!filterValue) {
                card.style.display = 'block';
            } else {
                const statusBadge = card.querySelector('.badge');
                const stepStatus = statusBadge ? statusBadge.textContent.trim() : '';
                const statusMap = {
                    'SUCCESS': '成功',
                    'FAILED': '失败',
                    'EXECUTING': '执行中',
                    'PENDING': '待执行',
                    'SKIPPED': '已跳过'
                };

                if (statusMap[filterValue] === stepStatus) {
                    card.style.display = 'block';
                } else {
                    card.style.display = 'none';
                }
            }
        });
    }

    /**
     * 开始自动刷新
     */
    startAutoRefresh() {
        this.stopAutoRefresh();
        this.refreshInterval = setInterval(() => {
            if (this.currentTransactionId) {
                this.loadTransactionDetail(this.currentTransactionId).catch(error => {
                    console.warn('自动刷新失败:', error);
                });
            }
        }, 10000); // 每10秒刷新一次
    }

    /**
     * 停止自动刷新
     */
    stopAutoRefresh() {
        if (this.refreshInterval) {
            clearInterval(this.refreshInterval);
            this.refreshInterval = null;
        }
    }

    /**
     * 模态框关闭事件
     */
    onModalClosed() {
        this.stopAutoRefresh();
        this.currentTransactionId = null;
        this.currentTransactionData = null;
    }

    /**
     * 显示加载状态
     */
    showLoadingState() {
        const container = document.getElementById('transactionStepsContainer');
        if (container) {
            container.innerHTML = `
                <div class="text-center py-5">
                    <div class="spinner-border text-primary mb-3" role="status">
                        <span class="visually-hidden">加载中...</span>
                    </div>
                    <div class="h5 text-muted">正在加载事务详情...</div>
                </div>
            `;
        }
    }

    /**
     * 显示错误状态
     * @param {string} message 错误消息
     */
    showErrorState(message) {
        const container = document.getElementById('transactionStepsContainer');
        if (container) {
            container.innerHTML = `
                <div class="alert alert-danger" role="alert">
                    <h4 class="alert-title">加载失败</h4>
                    <div class="text-muted">${message}</div>
                    <div class="mt-3">
                        <button class="btn btn-danger" onclick="window.transactionDetailManager.refreshTransactionDetail()">重新加载</button>
                    </div>
                </div>
            `;
        }
    }

    // 工具方法
    setElementText(id, text) {
        const element = document.getElementById(id);
        if (element) {
            element.textContent = text || '-';
        }
    }

    formatDateTime(dateTime) {
        if (!dateTime) return '-';
        try {
            const date = new Date(dateTime);
            return date.toLocaleString('zh-CN');
        } catch (error) {
            return dateTime;
        }
    }

    formatDuration(duration) {
        if (!duration) return '-';
        if (duration < 1000) {
            return `${duration}ms`;
        } else if (duration < 60000) {
            return `${(duration / 1000).toFixed(1)}s`;
        } else {
            const minutes = Math.floor(duration / 60000);
            const seconds = Math.floor((duration % 60000) / 1000);
            return `${minutes}m ${seconds}s`;
        }
    }

    formatTimeout(timeoutSeconds) {
        if (!timeoutSeconds) return '-';
        if (timeoutSeconds < 60) {
            return `${timeoutSeconds}秒`;
        } else {
            const minutes = Math.floor(timeoutSeconds / 60);
            const seconds = timeoutSeconds % 60;
            return seconds > 0 ? `${minutes}分${seconds}秒` : `${minutes}分钟`;
        }
    }

    truncateText(text, maxLength) {
        if (!text) return '';
        if (text.length <= maxLength) return text;
        return text.substring(0, maxLength) + '...';
    }

    showSuccessMessage(message) {
        console.log('成功:', message);
        if (window.TablerApp && typeof window.TablerApp.showSuccessNotification === 'function') {
            window.TablerApp.showSuccessNotification(message);
        } else {
            // 简单的成功提示
            const toast = document.createElement('div');
            toast.className = 'alert alert-success position-fixed top-0 end-0 m-3';
            toast.style.zIndex = '9999';
            toast.textContent = message;
            document.body.appendChild(toast);
            setTimeout(() => {
                document.body.removeChild(toast);
            }, 3000);
        }
    }

    showErrorMessage(message) {
        console.error('错误:', message);
        if (window.TablerApp && typeof window.TablerApp.showErrorNotification === 'function') {
            window.TablerApp.showErrorNotification(message);
        } else {
            // 简单的错误提示
            const toast = document.createElement('div');
            toast.className = 'alert alert-danger position-fixed top-0 end-0 m-3';
            toast.style.zIndex = '9999';
            toast.textContent = message;
            document.body.appendChild(toast);
            setTimeout(() => {
                document.body.removeChild(toast);
            }, 5000);
        }
    }

    /**
     * 更新执行上下文信息
     * @param {Object} transactionData 事务数据
     */
    updateExecutionContext(transactionData) {
        try {
            // 更新执行环境信息
            this.setElementText('contextExecutionType', transactionData.executionType || '-');
            this.setElementText('contextInitiatorThread', transactionData.initiatorThread || '-');
            this.setElementText('contextSpringProfile', transactionData.springProfileActive || '-');
            this.setElementText('contextJvmInfo', transactionData.jvmInfo || '-');

            // 更新业务方法信息
            this.setElementText('contextBusinessClassName', transactionData.businessClassName || '-');
            this.setElementText('contextBusinessMethodName', transactionData.businessMethodName || '-');
            this.setElementText('contextBusinessMethodSignature', transactionData.businessMethodSignature || '-');
            this.updateMethodArgs(transactionData.businessMethodArgs);

            // 更新调用栈信息
            this.updateCallStackInfo(transactionData.callStackInfo);

            // 更新系统属性
            this.updateSystemPropertiesInfo(transactionData.systemProperties);

        } catch (error) {
            console.error('更新执行上下文信息失败:', error);
        }
    }

    /**
     * 更新调用栈信息
     * @param {string} callStackInfo 调用栈JSON字符串
     */
    updateCallStackInfo(callStackInfo) {
        const container = document.getElementById('contextCallStack');
        if (!container) return;

        if (!callStackInfo) {
            container.innerHTML = '<div class="text-muted">暂无调用栈信息</div>';
            return;
        }

        try {
            const callStack = JSON.parse(callStackInfo);
            if (!Array.isArray(callStack) || callStack.length === 0) {
                container.innerHTML = '<div class="text-muted">暂无调用栈信息</div>';
                return;
            }

            let html = '<div class="list-group list-group-flush">';
            callStack.forEach((stack, index) => {
                const isBusinessMethod = stack.isBusinessMethod ? 'text-primary fw-bold' : '';
                html += `
                    <div class="list-group-item px-0 py-2 border-0">
                        <div class="d-flex align-items-center">
                            <span class="badge bg-secondary me-2">${index + 1}</span>
                            <div class="flex-fill">
                                <div class="${isBusinessMethod}">${stack.className}.${stack.methodName}</div>
                                <div class="text-muted small">${stack.fileName}:${stack.lineNumber}</div>
                            </div>
                        </div>
                    </div>
                `;
            });
            html += '</div>';
            container.innerHTML = html;

        } catch (error) {
            console.error('解析调用栈信息失败:', error);
            container.innerHTML = '<div class="text-danger">调用栈信息格式错误</div>';
        }
    }

    /**
     * 更新方法参数信息
     * @param {string} methodArgsInfo 方法参数JSON字符串
     */
    updateMethodArgs(methodArgsInfo) {
        const container = document.getElementById('contextBusinessMethodArgs');
        if (!container) return;

        if (!methodArgsInfo) {
            container.innerHTML = '<div class="text-muted">无参数</div>';
            return;
        }

        try {
            const methodArgs = JSON.parse(methodArgsInfo);
            if (!Array.isArray(methodArgs) || methodArgs.length === 0) {
                container.innerHTML = '<div class="text-muted">无参数</div>';
                return;
            }

            let html = '<div class="list-group list-group-flush">';
            methodArgs.forEach((arg, index) => {
                const serializableIcon = arg.serializable ?
                    '<span class="badge bg-success-lt me-1">可序列化</span>' :
                    '<span class="badge bg-warning-lt me-1">不可序列化</span>';

                const valueDisplay = arg.serializable && arg.value ?
                    this.truncateText(arg.value, 100) :
                    (arg.value || 'null');

                html += `
                    <div class="list-group-item px-0 py-2 border-0">
                        <div class="d-flex align-items-start">
                            <span class="badge bg-secondary me-2">${index}</span>
                            <div class="flex-fill">
                                <div class="d-flex align-items-center mb-1">
                                    <span class="fw-medium me-2">${arg.simpleType || arg.type}</span>
                                    ${serializableIcon}
                                </div>
                                <div class="text-muted small font-monospace">${valueDisplay}</div>
                                ${arg.type !== arg.simpleType ? `<div class="text-muted small">完整类型: ${arg.type}</div>` : ''}
                            </div>
                        </div>
                    </div>
                `;
            });
            html += '</div>';
            container.innerHTML = html;

        } catch (error) {
            console.error('解析方法参数信息失败:', error);
            // 尝试按旧格式解析（兼容性处理）
            try {
                const oldFormat = JSON.parse(methodArgsInfo);
                if (Array.isArray(oldFormat)) {
                    let html = '<div class="list-group list-group-flush">';
                    oldFormat.forEach((type, index) => {
                        html += `
                            <div class="list-group-item px-0 py-2 border-0">
                                <div class="d-flex align-items-center">
                                    <span class="badge bg-secondary me-2">${index}</span>
                                    <span class="fw-medium">${type}</span>
                                    <span class="badge bg-warning-lt ms-2">旧格式</span>
                                </div>
                            </div>
                        `;
                    });
                    html += '</div>';
                    container.innerHTML = html;
                } else {
                    container.innerHTML = '<div class="text-danger">参数信息格式错误</div>';
                }
            } catch (e) {
                container.innerHTML = '<div class="text-danger">参数信息格式错误</div>';
            }
        }
    }

    /**
     * 更新系统属性信息
     * @param {string} systemPropertiesInfo 系统属性JSON字符串
     */
    updateSystemPropertiesInfo(systemPropertiesInfo) {
        const container = document.getElementById('contextSystemProperties');
        if (!container) return;

        if (!systemPropertiesInfo) {
            container.innerHTML = '<div class="text-muted">暂无系统属性信息</div>';
            return;
        }

        try {
            const systemProperties = JSON.parse(systemPropertiesInfo);
            if (!systemProperties || Object.keys(systemProperties).length === 0) {
                container.innerHTML = '<div class="text-muted">暂无系统属性信息</div>';
                return;
            }

            let html = '<div class="row g-2">';
            Object.entries(systemProperties).forEach(([key, value]) => {
                html += `
                    <div class="col-12">
                        <div class="d-flex">
                            <span class="text-muted me-2">${key}:</span>
                            <span class="flex-fill">${value}</span>
                        </div>
                    </div>
                `;
            });
            html += '</div>';
            container.innerHTML = html;

        } catch (error) {
            console.error('解析系统属性信息失败:', error);
            container.innerHTML = '<div class="text-danger">系统属性信息格式错误</div>';
        }
    }
}

// 创建全局实例
window.transactionDetailManager = new TransactionDetailManager();
