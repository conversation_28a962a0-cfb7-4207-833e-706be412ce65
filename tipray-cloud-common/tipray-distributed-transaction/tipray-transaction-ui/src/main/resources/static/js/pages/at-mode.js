/**
 * AT模式页面类
 * 管理AT模式页面的数据加载和交互
 */
class AtModePage {
    constructor() {
        this.currentPage = 1;
        this.pageSize = 20;
        this.totalCount = 0;
        this.statusFilter = '';
    }

    /**
     * 初始化页面
     */
    async init() {
        try {
            console.log('初始化AT模式页面...');

            // 初始化事件监听器
            this.initEventListeners();

            // 加载初始数据
            await this.loadData();

            console.log('AT模式页面初始化完成');
        } catch (error) {
            console.error('AT模式页面初始化失败:', error);
        }
    }

    /**
     * 销毁页面
     */
    async destroy() {
        try {
            console.log('AT模式页面已销毁');
        } catch (error) {
            console.error('AT模式页面销毁失败:', error);
        }
    }

    /**
     * 刷新数据
     */
    async refreshData() {
        try {
            await this.loadData();
            console.log('AT模式数据刷新完成');
        } catch (error) {
            console.error('AT模式数据刷新失败:', error);
        }
    }

    /**
     * 初始化事件监听器
     */
    initEventListeners() {
        // 状态过滤器
        const statusFilter = document.getElementById('atStatusFilter');
        if (statusFilter) {
            statusFilter.addEventListener('change', (e) => {
                this.statusFilter = e.target.value;
                this.currentPage = 1;
                this.loadData();
            });
        }

        // 刷新按钮
        const refreshBtn = document.getElementById('refreshAtList');
        if (refreshBtn) {
            refreshBtn.addEventListener('click', () => {
                this.refreshData();
            });
        }

        // 表格操作事件
        this.initTableEventListeners();

        // 模态框事件
        this.initModalEventListeners();
    }

    /**
     * 初始化表格事件监听器
     */
    initTableEventListeners() {
        const tableBody = document.getElementById('atTransactionTableBody');
        if (tableBody) {
            tableBody.addEventListener('click', (e) => {
                const target = e.target.closest('[data-action]');
                if (target) {
                    e.preventDefault();
                    const action = target.getAttribute('data-action');
                    const transactionId = this.getTransactionIdFromRow(target);
                    this.handleTableAction(action, transactionId);
                }

                // 处理UndoLog查看
                const undoLogBtn = e.target.closest('[data-bs-target="#undoLogModal"]');
                if (undoLogBtn) {
                    e.preventDefault();
                    const transactionId = undoLogBtn.getAttribute('data-transaction-id');
                    this.showUndoLog(transactionId);
                }

                // 处理详情查看
                const detailBtn = e.target.closest('[data-bs-target="#atTransactionDetailModal"]');
                if (detailBtn) {
                    e.preventDefault();
                    const transactionId = detailBtn.getAttribute('data-transaction-id');
                    this.showTransactionDetail(transactionId);
                }
            });
        }
    }

    /**
     * 初始化模态框事件监听器
     */
    initModalEventListeners() {
        // UndoLog模态框
        const undoLogModal = document.getElementById('undoLogModal');
        if (undoLogModal) {
            undoLogModal.addEventListener('show.bs.modal', (e) => {
                const transactionId = e.relatedTarget?.getAttribute('data-transaction-id');
                if (transactionId) {
                    this.loadUndoLog(transactionId);
                }
            });
        }

        // 事务详情模态框
        const detailModal = document.getElementById('atTransactionDetailModal');
        if (detailModal) {
            detailModal.addEventListener('show.bs.modal', (e) => {
                const transactionId = e.relatedTarget?.getAttribute('data-transaction-id');
                if (transactionId) {
                    this.loadTransactionDetail(transactionId);
                }
            });
        }
    }

    /**
     * 加载数据
     */
    async loadData() {
        try {
            // 显示加载状态
            this.showLoading();

            // 模拟API调用
            const data = await this.fetchAtTransactions();

            // 更新统计卡片
            this.updateStatCards(data.stats);

            // 更新表格
            this.updateTable(data.transactions);

            // 更新分页
            this.updatePagination(data.totalCount);

        } catch (error) {
            console.error('AT模式数据加载失败:', error);
            this.showError('数据加载失败: ' + error.message);
        }
    }

    /**
     * 获取AT事务数据
     */
    async fetchAtTransactions() {
        // 模拟API调用
        return new Promise((resolve) => {
            setTimeout(() => {
                const transactions = this.generateMockAtTransactions();
                resolve({
                    transactions: transactions,
                    totalCount: transactions.length,
                    stats: {
                        total: Math.floor(Math.random() * 1000),
                        success: Math.floor(Math.random() * 800),
                        rollback: Math.floor(Math.random() * 100),
                        undoLog: Math.floor(Math.random() * 500)
                    }
                });
            }, 500);
        });
    }

    /**
     * 生成模拟AT事务数据
     */
    generateMockAtTransactions() {
        const transactions = [];
        const statuses = ['已提交', '已回滚', '执行中', '失败'];
        const statusClasses = ['success', 'danger', 'warning', 'orange'];
        const services = ['订单服务', '支付服务', '库存服务', '用户服务'];

        for (let i = 1; i <= 10; i++) {
            const statusIndex = Math.floor(Math.random() * statuses.length);
            const serviceIndex = Math.floor(Math.random() * services.length);

            transactions.push({
                id: `TXN-AT-20240701-${String(i).padStart(3, '0')}`,
                status: statuses[statusIndex],
                statusClass: statusClasses[statusIndex],
                service: services[serviceIndex],
                startTime: new Date(Date.now() - Math.random() * 24 * 60 * 60 * 1000).toLocaleString('zh-CN'),
                duration: (Math.random() * 10).toFixed(1) + 's',
                branchCount: Math.floor(Math.random() * 5) + 1
            });
        }

        return transactions;
    }

    /**
     * 更新统计卡片
     */
    updateStatCards(stats) {
        const elements = {
            atTotalTransactions: document.getElementById('atTotalTransactions'),
            atSuccessTransactions: document.getElementById('atSuccessTransactions'),
            atRollbackTransactions: document.getElementById('atRollbackTransactions'),
            atUndoLogCount: document.getElementById('atUndoLogCount')
        };

        if (elements.atTotalTransactions) {
            elements.atTotalTransactions.textContent = stats.total.toLocaleString();
        }
        if (elements.atSuccessTransactions) {
            elements.atSuccessTransactions.textContent = stats.success.toLocaleString();
        }
        if (elements.atRollbackTransactions) {
            elements.atRollbackTransactions.textContent = stats.rollback.toLocaleString();
        }
        if (elements.atUndoLogCount) {
            elements.atUndoLogCount.textContent = stats.undoLog.toLocaleString();
        }
    }

    /**
     * 更新表格
     */
    updateTable(transactions) {
        const tableBody = document.getElementById('atTransactionTableBody');
        if (!tableBody) return;

        const html = transactions.map(transaction => `
            <tr>
                <td>
                    <input class="form-check-input" type="checkbox" value="${transaction.id}">
                </td>
                <td>
                    <div class="d-flex align-items-center">
                        <span class="avatar avatar-sm me-3 bg-primary text-white fw-bold">AT</span>
                        <div>
                            <div class="fw-medium text-dark">${transaction.id}</div>
                            <div class="text-muted small">AT事务</div>
                        </div>
                    </div>
                </td>
                <td>
                    <span class="badge bg-${transaction.statusClass}">${transaction.status}</span>
                </td>
                <td class="text-muted">
                    ${transaction.service}
                </td>
                <td class="text-muted">
                    ${transaction.startTime}
                </td>
                <td class="text-muted">
                    ${transaction.duration}
                </td>
                <td class="text-center">
                    <span class="badge bg-secondary-lt">${transaction.branchCount}</span>
                </td>
                <td>
                    <a href="#" class="btn btn-sm btn-outline-info" data-bs-toggle="modal" data-bs-target="#undoLogModal" data-transaction-id="${transaction.id}">
                        查看
                    </a>
                </td>
                <td>
                    <div class="btn-list flex-nowrap">
                        <a href="#" class="btn btn-sm btn-outline-primary" data-bs-toggle="modal" data-bs-target="#atTransactionDetailModal" data-transaction-id="${transaction.id}">
                            详情
                        </a>
                        <div class="dropdown">
                            <button class="btn btn-sm btn-outline-secondary dropdown-toggle align-text-top" data-bs-toggle="dropdown">
                                操作
                            </button>
                            <div class="dropdown-menu dropdown-menu-end">
                                ${this.generateActionMenu(transaction)}
                            </div>
                        </div>
                    </div>
                </td>
            </tr>
        `).join('');

        tableBody.innerHTML = html;
    }

    /**
     * 生成操作菜单
     */
    generateActionMenu(transaction) {
        let menu = '';

        if (transaction.status === '失败') {
            menu += `
                <a class="dropdown-item" href="#" data-action="retry">
                    <svg xmlns="http://www.w3.org/2000/svg" class="icon dropdown-item-icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                        <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
                        <path d="M20 11a8.1 8.1 0 0 0 -15.5 -2m-.5 -4v4h4"/>
                        <path d="M4 13a8.1 8.1 0 0 0 15.5 2m.5 4v-4h-4"/>
                    </svg>
                    重试
                </a>
            `;
        }

        if (transaction.status === '已提交') {
            menu += `
                <a class="dropdown-item" href="#" data-action="rollback">
                    <svg xmlns="http://www.w3.org/2000/svg" class="icon dropdown-item-icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                        <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
                        <path d="M9 14l-4 -4l4 -4"/>
                        <path d="M5 10h11a4 4 0 1 1 0 8h-1"/>
                    </svg>
                    手动回滚
                </a>
            `;
        }

        return menu;
    }

    /**
     * 更新分页
     */
    updatePagination(totalCount) {
        this.totalCount = totalCount;

        const paginationInfo = document.getElementById('atPaginationInfo');
        if (paginationInfo) {
            const start = (this.currentPage - 1) * this.pageSize + 1;
            const end = Math.min(this.currentPage * this.pageSize, totalCount);
            paginationInfo.textContent = `显示第 ${start}-${end} 条，共 ${totalCount} 条记录`;
        }
    }

    /**
     * 处理表格操作
     */
    handleTableAction(action, transactionId) {
        console.log(`执行AT操作: ${action}, 事务ID: ${transactionId}`);

        switch (action) {
            case 'retry':
                this.retryTransaction(transactionId);
                break;
            case 'rollback':
                this.rollbackTransaction(transactionId);
                break;
            default:
                console.warn('未知操作:', action);
        }
    }

    /**
     * 重试事务
     */
    async retryTransaction(transactionId) {
        const result = await ConfirmDialog.confirm(`确定要重试AT事务 ${transactionId} 吗？`);
        if (result.confirmed) {
            try {
                console.log('重试AT事务:', transactionId);
                const operatorId = localStorage.getItem('currentUserId') || 'admin';
                const reason = 'AT模式页面手动重试';
                const response = await window.apiService.retryTransaction(transactionId, operatorId, reason);

                if (response.success !== false) {
                    this.showSuccessMessage(`AT事务 ${transactionId} 重试请求已提交`);
                    // 刷新数据
                    this.refreshData();
                } else {
                    throw new Error(response.message || '重试操作失败');
                }
            } catch (error) {
                console.error('重试AT事务失败:', error);
                this.showErrorMessage('重试操作失败: ' + error.message);
            }
        }
    }

    /**
     * 回滚事务
     */
    async rollbackTransaction(transactionId) {
        const result = await ConfirmDialog.danger(`确定要手动回滚AT事务 ${transactionId} 吗？此操作不可撤销！`, '危险操作');
        if (result.confirmed) {
            try {
                console.log('回滚AT事务:', transactionId);
                const operatorId = localStorage.getItem('currentUserId') || 'admin';
                const reason = 'AT模式页面手动回滚';
                const response = await window.apiService.rollbackTransaction(transactionId, operatorId, reason);

                if (response.success !== false) {
                    this.showSuccessMessage(`AT事务 ${transactionId} 回滚请求已提交`);
                    // 刷新数据
                    this.refreshData();
                } else {
                    throw new Error(response.message || '回滚操作失败');
                }
            } catch (error) {
                console.error('回滚AT事务失败:', error);
                this.showErrorMessage('回滚操作失败: ' + error.message);
            }
        }
    }

    /**
     * 显示UndoLog
     */
    showUndoLog(transactionId) {
        console.log('显示UndoLog:', transactionId);
        this.loadUndoLog(transactionId);
    }

    /**
     * 加载UndoLog
     */
    async loadUndoLog(transactionId) {
        const content = document.getElementById('undoLogContent');
        if (!content) return;

        content.innerHTML = `
            <div class="text-center py-4">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">加载中...</span>
                </div>
                <div class="mt-2">正在加载UndoLog数据...</div>
            </div>
        `;

        try {
            await new Promise(resolve => setTimeout(resolve, 1000));

            content.innerHTML = `
                <div class="row">
                    <div class="col-12">
                        <h5>UndoLog记录</h5>
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>分支ID</th>
                                        <th>表名</th>
                                        <th>操作类型</th>
                                        <th>记录时间</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>BR-001</td>
                                        <td>t_order</td>
                                        <td>INSERT</td>
                                        <td>2024-07-01 10:30:15</td>
                                    </tr>
                                    <tr>
                                        <td>BR-002</td>
                                        <td>t_inventory</td>
                                        <td>UPDATE</td>
                                        <td>2024-07-01 10:30:16</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        <div class="mt-3">
                            <h6>Before Image</h6>
                            <pre class="bg-light p-2"><code>{"id": 1001, "status": "PENDING", "amount": 100.00}</code></pre>
                            <h6>After Image</h6>
                            <pre class="bg-light p-2"><code>{"id": 1001, "status": "SUCCESS", "amount": 100.00}</code></pre>
                        </div>
                    </div>
                </div>
            `;
        } catch (error) {
            content.innerHTML = `
                <div class="alert alert-danger" role="alert">
                    <h4 class="alert-title">加载失败</h4>
                    <div class="text-muted">${error.message}</div>
                </div>
            `;
        }
    }

    /**
     * 显示事务详情
     */
    showTransactionDetail(transactionId) {
        console.log('显示AT事务详情:', transactionId);
        this.loadTransactionDetail(transactionId);
    }

    /**
     * 加载事务详情
     */
    async loadTransactionDetail(transactionId) {
        const content = document.getElementById('atTransactionDetailContent');
        if (!content) return;

        content.innerHTML = `
            <div class="text-center py-4">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">加载中...</span>
                </div>
                <div class="mt-2">正在加载AT事务详情...</div>
            </div>
        `;

        try {
            await new Promise(resolve => setTimeout(resolve, 1000));

            content.innerHTML = `
                <div class="row">
                    <div class="col-md-6">
                        <h4>基本信息</h4>
                        <dl class="row">
                            <dt class="col-4">事务ID:</dt>
                            <dd class="col-8">${transactionId}</dd>
                            <dt class="col-4">事务模式:</dt>
                            <dd class="col-8"><span class="badge bg-blue">AT</span></dd>
                            <dt class="col-4">状态:</dt>
                            <dd class="col-8"><span class="badge bg-success">已提交</span></dd>
                            <dt class="col-4">服务名称:</dt>
                            <dd class="col-8">订单服务</dd>
                            <dt class="col-4">开始时间:</dt>
                            <dd class="col-8">2024-07-01 10:30:15</dd>
                            <dt class="col-4">结束时间:</dt>
                            <dd class="col-8">2024-07-01 10:30:16</dd>
                            <dt class="col-4">持续时间:</dt>
                            <dd class="col-8">1.2秒</dd>
                        </dl>
                    </div>
                    <div class="col-md-6">
                        <h4>分支事务</h4>
                        <div class="list-group list-group-flush">
                            <div class="list-group-item">
                                <div class="row align-items-center">
                                    <div class="col-auto">
                                        <span class="badge bg-success">1</span>
                                    </div>
                                    <div class="col">
                                        <div class="text-truncate">
                                            <strong>订单创建分支</strong>
                                        </div>
                                        <div class="text-muted">订单服务 • 已提交</div>
                                    </div>
                                </div>
                            </div>
                            <div class="list-group-item">
                                <div class="row align-items-center">
                                    <div class="col-auto">
                                        <span class="badge bg-success">2</span>
                                    </div>
                                    <div class="col">
                                        <div class="text-truncate">
                                            <strong>库存扣减分支</strong>
                                        </div>
                                        <div class="text-muted">库存服务 • 已提交</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        } catch (error) {
            content.innerHTML = `
                <div class="alert alert-danger" role="alert">
                    <h4 class="alert-title">加载失败</h4>
                    <div class="text-muted">${error.message}</div>
                </div>
            `;
        }
    }

    /**
     * 从表格行获取事务ID
     */
    getTransactionIdFromRow(element) {
        const row = element.closest('tr');
        if (row) {
            const idCell = row.querySelector('td:first-child .font-weight-medium');
            return idCell ? idCell.textContent.trim() : null;
        }
        return null;
    }

    /**
     * 显示加载状态
     */
    showLoading() {
        const tableBody = document.getElementById('atTransactionTableBody');
        if (tableBody) {
            tableBody.innerHTML = `
                <tr>
                    <td colspan="8" class="text-center py-4">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">加载中...</span>
                        </div>
                        <div class="mt-2">正在加载AT事务数据...</div>
                    </td>
                </tr>
            `;
        }
    }

    /**
     * 显示错误信息
     */
    showError(message) {
        const tableBody = document.getElementById('atTransactionTableBody');
        if (tableBody) {
            tableBody.innerHTML = `
                <tr>
                    <td colspan="8" class="text-center py-4">
                        <div class="alert alert-danger" role="alert">
                            <h4 class="alert-title">加载失败</h4>
                            <div class="text-muted">${message}</div>
                        </div>
                    </td>
                </tr>
            `;
        }
    }

    /**
     * 显示成功消息
     */
    showSuccessMessage(message) {
        console.log('成功:', message);
        if (window.TablerApp && typeof window.TablerApp.showSuccessNotification === 'function') {
            window.TablerApp.showSuccessNotification(message);
        } else {
            ConfirmDialog.success(message);
        }
    }
}

// 注册页面类到全局
window.AtModePage = AtModePage;
