/**
 * 系统设置页面类
 * 管理系统设置页面的数据加载和交互
 */
class SettingsPage {
    constructor() {
        this.settings = {};
        this.systemInfo = {};
    }

    /**
     * 初始化页面
     */
    async init() {
        try {
            console.log('初始化系统设置页面...');

            // 初始化事件监听器
            this.initEventListeners();

            // 加载初始数据
            await this.loadData();

            console.log('系统设置页面初始化完成');
        } catch (error) {
            console.error('系统设置页面初始化失败:', error);
        }
    }

    /**
     * 销毁页面
     */
    async destroy() {
        try {
            console.log('系统设置页面已销毁');
        } catch (error) {
            console.error('系统设置页面销毁失败:', error);
        }
    }

    /**
     * 刷新数据
     */
    async refreshData() {
        try {
            await this.loadData();
            console.log('系统设置数据刷新完成');
        } catch (error) {
            console.error('系统设置数据刷新失败:', error);
        }
    }

    /**
     * 初始化事件监听器
     */
    initEventListeners() {
        // 基本配置保存
        const saveBasicBtn = document.getElementById('saveBasicSettings');
        if (saveBasicBtn) {
            saveBasicBtn.addEventListener('click', () => {
                this.saveBasicSettings();
            });
        }

        // 监控配置保存
        const saveMonitoringBtn = document.getElementById('saveMonitoringSettings');
        if (saveMonitoringBtn) {
            saveMonitoringBtn.addEventListener('click', () => {
                this.saveMonitoringSettings();
            });
        }

        // 事务配置保存
        const saveTransactionBtn = document.getElementById('saveTransactionSettings');
        if (saveTransactionBtn) {
            saveTransactionBtn.addEventListener('click', () => {
                this.saveTransactionSettings();
            });
        }

        // 安全配置保存
        const saveSecurityBtn = document.getElementById('saveSecuritySettings');
        if (saveSecurityBtn) {
            saveSecurityBtn.addEventListener('click', () => {
                this.saveSecuritySettings();
            });
        }

        // API密钥显示/隐藏
        const toggleApiKeyBtn = document.getElementById('toggleApiKey');
        if (toggleApiKeyBtn) {
            toggleApiKeyBtn.addEventListener('click', () => {
                this.toggleApiKeyVisibility();
            });
        }

        // 生成新API密钥
        const generateApiKeyBtn = document.getElementById('generateApiKey');
        if (generateApiKeyBtn) {
            generateApiKeyBtn.addEventListener('click', () => {
                this.generateApiKey();
            });
        }

        // IP白名单开关
        const ipWhitelistToggle = document.getElementById('enableIpWhitelist');
        if (ipWhitelistToggle) {
            ipWhitelistToggle.addEventListener('change', (e) => {
                this.toggleIpWhitelist(e.target.checked);
            });
        }

        // 系统操作按钮
        this.initSystemOperationListeners();

        // 刷新系统信息
        const refreshSystemInfoBtn = document.getElementById('refreshSystemInfo');
        if (refreshSystemInfoBtn) {
            refreshSystemInfoBtn.addEventListener('click', () => {
                this.refreshSystemInfo();
            });
        }
    }

    /**
     * 初始化系统操作监听器
     */
    initSystemOperationListeners() {
        // 导出配置
        const exportBtn = document.getElementById('exportSettings');
        if (exportBtn) {
            exportBtn.addEventListener('click', () => {
                this.exportSettings();
            });
        }

        // 导入配置
        const importBtn = document.getElementById('importSettings');
        if (importBtn) {
            importBtn.addEventListener('click', () => {
                this.showImportModal();
            });
        }

        // 重置配置
        const resetBtn = document.getElementById('resetSettings');
        if (resetBtn) {
            resetBtn.addEventListener('click', () => {
                this.resetSettings();
            });
        }

        // 清理缓存
        const clearCacheBtn = document.getElementById('clearCache');
        if (clearCacheBtn) {
            clearCacheBtn.addEventListener('click', () => {
                this.clearCache();
            });
        }

        // 确认导入
        const confirmImportBtn = document.getElementById('confirmImport');
        if (confirmImportBtn) {
            confirmImportBtn.addEventListener('click', () => {
                this.confirmImport();
            });
        }
    }

    /**
     * 加载数据
     */
    async loadData() {
        try {
            // 模拟API调用
            const data = await this.fetchSettings();

            // 更新设置表单
            this.updateSettingsForms(data.settings);

            // 更新系统信息
            this.updateSystemInfo(data.systemInfo);

        } catch (error) {
            console.error('设置数据加载失败:', error);
        }
    }

    /**
     * 获取设置数据
     */
    async fetchSettings() {
        try {
            console.log('获取系统设置数据...');

            // 并行获取系统配置和系统信息
            const [configResponse, infoResponse] = await Promise.all([
                window.apiService.getSystemConfig(),
                window.apiService.getSystemInfo()
            ]);

            console.log('系统配置响应:', configResponse);
            console.log('系统信息响应:', infoResponse);

            // 处理配置数据
            let settings = {};
            if (configResponse.success !== false) {
                const configData = configResponse.data || configResponse;
                settings = this.transformConfigData(configData);
            }

            // 处理系统信息
            let systemInfo = {};
            if (infoResponse.success !== false) {
                const infoData = infoResponse.data || infoResponse;
                systemInfo = this.transformSystemInfo(infoData);
            }

            return {settings, systemInfo};

        } catch (error) {
            console.error('获取设置数据失败:', error);

            // API调用失败，返回默认数据
            console.warn('API调用失败，返回默认设置数据');
            this.showError('无法获取系统设置，请检查后端服务是否正常运行');
            return this.getDefaultSettings();
        }
    }

    /**
     * 转换配置数据
     */
    transformConfigData(configData) {
        return {
            basic: {
                systemName: configData.systemName || 'Tipray分布式事务监控中心',
                systemVersion: configData.version || '1.0.0',
                adminEmail: configData.adminEmail || '<EMAIL>',
                timezone: configData.timezone || 'Asia/Shanghai',
                language: configData.language || 'zh-CN'
            },
            monitoring: {
                refreshInterval: configData.refreshInterval || 5,
                dataRetentionDays: configData.dataRetentionDays || 30,
                maxDisplayRecords: configData.maxDisplayRecords || 1000,
                enableRealtime: configData.enableRealtime !== false,
                enableNotifications: configData.enableNotifications !== false
            },
            transaction: {
                defaultTimeout: configData.defaultTimeout || 30,
                maxRetryCount: configData.maxRetryCount || 3,
                retryInterval: configData.retryInterval || 1000,
                enableAutoRollback: configData.enableAutoRollback !== false,
                enableTransactionLog: configData.enableTransactionLog !== false
            },
            security: {
                sessionTimeout: configData.sessionTimeout || 30,
                apiSecretKey: configData.apiSecretKey || '***',
                enableIpWhitelist: configData.enableIpWhitelist || false,
                ipWhitelist: configData.ipWhitelist || '',
                enableAuditLog: configData.enableAuditLog !== false
            }
        };
    }

    /**
     * 转换系统信息
     */
    transformSystemInfo(infoData) {
        return {
            systemVersion: infoData.version || '1.0.0',
            javaVersion: infoData.javaVersion || 'Unknown',
            springBootVersion: infoData.springBootVersion || 'Unknown',
            startTime: this.formatDateTime(infoData.startTime) || 'Unknown',
            uptime: infoData.uptime || 'Unknown',
            cpuUsage: infoData.cpuUsage || 0,
            memoryUsage: infoData.memoryUsage || 0,
            memoryTotal: infoData.memoryTotal || 'Unknown',
            memoryUsed: infoData.memoryUsed || 'Unknown',
            diskUsage: infoData.diskUsage || 0,
            diskTotal: infoData.diskTotal || 'Unknown',
            diskUsed: infoData.diskUsed || 'Unknown',
            dbStatus: infoData.dbStatus || 'Unknown',
            dbConnections: infoData.dbConnections || 'Unknown'
        };
    }

    /**
     * 获取默认设置
     */
    getDefaultSettings() {
        return {
            settings: {
                basic: {
                    systemName: 'Tipray分布式事务监控中心',
                    systemVersion: '1.0.0',
                    adminEmail: '<EMAIL>',
                    timezone: 'Asia/Shanghai',
                    language: 'zh-CN'
                },
                monitoring: {
                    refreshInterval: 5,
                    dataRetentionDays: 30,
                    maxDisplayRecords: 1000,
                    enableRealtime: true,
                    enableNotifications: true
                },
                transaction: {
                    defaultTimeout: 30,
                    maxRetryCount: 3,
                    retryInterval: 1000,
                    enableAutoRollback: true,
                    enableTransactionLog: true
                },
                security: {
                    sessionTimeout: 30,
                    apiSecretKey: '***',
                    enableIpWhitelist: false,
                    ipWhitelist: '',
                    enableAuditLog: true
                }
            },
            systemInfo: {
                systemVersion: '1.0.0',
                javaVersion: 'Unknown',
                springBootVersion: 'Unknown',
                startTime: 'Unknown',
                uptime: 'Unknown',
                cpuUsage: 0,
                memoryUsage: 0,
                memoryTotal: 'Unknown',
                memoryUsed: 'Unknown',
                diskUsage: 0,
                diskTotal: 'Unknown',
                diskUsed: 'Unknown',
                dbStatus: 'Unknown',
                dbConnections: 'Unknown'
            }
        };
    }

    /**
     * 更新设置表单
     */
    updateSettingsForms(settings) {
        this.settings = settings;

        // 基本配置
        if (settings.basic) {
            this.setFormValue('systemName', settings.basic.systemName);
            this.setFormValue('systemVersion', settings.basic.systemVersion);
            this.setFormValue('adminEmail', settings.basic.adminEmail);
            this.setFormValue('timezone', settings.basic.timezone);
            this.setFormValue('language', settings.basic.language);
        }

        // 监控配置
        if (settings.monitoring) {
            this.setFormValue('refreshInterval', settings.monitoring.refreshInterval);
            this.setFormValue('dataRetentionDays', settings.monitoring.dataRetentionDays);
            this.setFormValue('maxDisplayRecords', settings.monitoring.maxDisplayRecords);
            this.setFormValue('enableRealtime', settings.monitoring.enableRealtime);
            this.setFormValue('enableNotifications', settings.monitoring.enableNotifications);
        }

        // 事务配置
        if (settings.transaction) {
            this.setFormValue('defaultTransactionTimeout', settings.transaction.defaultTimeout);
            this.setFormValue('maxRetryCount', settings.transaction.maxRetryCount);
            this.setFormValue('retryInterval', settings.transaction.retryInterval);
            this.setFormValue('enableAutoRollback', settings.transaction.enableAutoRollback);
            this.setFormValue('enableTransactionLog', settings.transaction.enableTransactionLog);
        }

        // 安全配置
        if (settings.security) {
            this.setFormValue('sessionTimeout', settings.security.sessionTimeout);
            this.setFormValue('apiSecretKey', settings.security.apiSecretKey);
            this.setFormValue('enableIpWhitelist', settings.security.enableIpWhitelist);
            this.setFormValue('ipWhitelist', settings.security.ipWhitelist);
            this.setFormValue('enableAuditLog', settings.security.enableAuditLog);

            // 更新IP白名单容器显示状态
            this.toggleIpWhitelist(settings.security.enableIpWhitelist);
        }
    }

    /**
     * 更新系统信息
     */
    updateSystemInfo(systemInfo) {
        this.systemInfo = systemInfo;

        // 更新系统信息显示
        this.setTextContent('infoSystemVersion', systemInfo.systemVersion);
        this.setTextContent('infoJavaVersion', systemInfo.javaVersion);
        this.setTextContent('infoSpringBootVersion', systemInfo.springBootVersion);
        this.setTextContent('infoStartTime', systemInfo.startTime);
        this.setTextContent('infoUptime', systemInfo.uptime);

        // 更新进度条
        this.updateProgressBar('cpu', systemInfo.cpuUsage);
        this.updateProgressBar('memory', systemInfo.memoryUsage, `${systemInfo.memoryUsed} / ${systemInfo.memoryTotal}`);
        this.updateProgressBar('disk', systemInfo.diskUsage, `${systemInfo.diskUsed} / ${systemInfo.diskTotal}`);
    }

    /**
     * 设置表单值
     */
    setFormValue(id, value) {
        const element = document.getElementById(id);
        if (element) {
            if (element.type === 'checkbox') {
                element.checked = value;
            } else {
                element.value = value;
            }
        }
    }

    /**
     * 设置文本内容
     */
    setTextContent(id, text) {
        const element = document.getElementById(id);
        if (element) {
            element.textContent = text;
        }
    }

    /**
     * 更新进度条
     */
    updateProgressBar(type, percentage, text) {
        // 这里可以根据type找到对应的进度条并更新
        console.log(`更新${type}进度条: ${percentage}% ${text || ''}`);
    }

    /**
     * 保存基本配置
     */
    async saveBasicSettings() {
        try {
            const settings = {
                systemName: this.getFormValue('systemName'),
                adminEmail: this.getFormValue('adminEmail'),
                timezone: this.getFormValue('timezone'),
                language: this.getFormValue('language')
            };

            console.log('保存基本配置:', settings);

            // 模拟API调用
            await new Promise(resolve => setTimeout(resolve, 500));

            this.showSuccessMessage('基本配置保存成功');
        } catch (error) {
            console.error('基本配置保存失败:', error);
            this.showErrorMessage('基本配置保存失败: ' + error.message);
        }
    }

    /**
     * 保存监控配置
     */
    async saveMonitoringSettings() {
        try {
            const settings = {
                refreshInterval: parseInt(this.getFormValue('refreshInterval')),
                dataRetentionDays: parseInt(this.getFormValue('dataRetentionDays')),
                maxDisplayRecords: parseInt(this.getFormValue('maxDisplayRecords')),
                enableRealtime: this.getFormValue('enableRealtime'),
                enableNotifications: this.getFormValue('enableNotifications')
            };

            console.log('保存监控配置:', settings);

            await new Promise(resolve => setTimeout(resolve, 500));

            this.showSuccessMessage('监控配置保存成功');
        } catch (error) {
            console.error('监控配置保存失败:', error);
            this.showErrorMessage('监控配置保存失败: ' + error.message);
        }
    }

    /**
     * 保存事务配置
     */
    async saveTransactionSettings() {
        try {
            const settings = {
                defaultTimeout: parseInt(this.getFormValue('defaultTransactionTimeout')),
                maxRetryCount: parseInt(this.getFormValue('maxRetryCount')),
                retryInterval: parseInt(this.getFormValue('retryInterval')),
                enableAutoRollback: this.getFormValue('enableAutoRollback'),
                enableTransactionLog: this.getFormValue('enableTransactionLog')
            };

            console.log('保存事务配置:', settings);

            await new Promise(resolve => setTimeout(resolve, 500));

            this.showSuccessMessage('事务配置保存成功');
        } catch (error) {
            console.error('事务配置保存失败:', error);
            this.showErrorMessage('事务配置保存失败: ' + error.message);
        }
    }

    /**
     * 保存安全配置
     */
    async saveSecuritySettings() {
        try {
            const settings = {
                sessionTimeout: parseInt(this.getFormValue('sessionTimeout')),
                apiSecretKey: this.getFormValue('apiSecretKey'),
                enableIpWhitelist: this.getFormValue('enableIpWhitelist'),
                ipWhitelist: this.getFormValue('ipWhitelist'),
                enableAuditLog: this.getFormValue('enableAuditLog')
            };

            console.log('保存安全配置:', settings);

            await new Promise(resolve => setTimeout(resolve, 500));

            this.showSuccessMessage('安全配置保存成功');
        } catch (error) {
            console.error('安全配置保存失败:', error);
            this.showErrorMessage('安全配置保存失败: ' + error.message);
        }
    }

    /**
     * 获取表单值
     */
    getFormValue(id) {
        const element = document.getElementById(id);
        if (element) {
            if (element.type === 'checkbox') {
                return element.checked;
            } else {
                return element.value;
            }
        }
        return null;
    }

    /**
     * 切换API密钥可见性
     */
    toggleApiKeyVisibility() {
        const apiKeyInput = document.getElementById('apiSecretKey');
        if (apiKeyInput) {
            if (apiKeyInput.type === 'password') {
                apiKeyInput.type = 'text';
            } else {
                apiKeyInput.type = 'password';
            }
        }
    }

    /**
     * 生成新API密钥
     */
    async generateApiKey() {
        const result = await ConfirmDialog.danger('确定要生成新的API密钥吗？这将使现有密钥失效！', '危险操作');
        if (result.confirmed) {
            const newKey = 'tipray-' + Date.now() + '-' + Math.random().toString(36).substr(2, 9);
            this.setFormValue('apiSecretKey', newKey);
            this.showSuccessMessage('新API密钥已生成');
        }
    }

    /**
     * 切换IP白名单
     */
    toggleIpWhitelist(enabled) {
        const container = document.getElementById('ipWhitelistContainer');
        if (container) {
            container.style.display = enabled ? 'block' : 'none';
        }
    }

    /**
     * 导出设置
     */
    exportSettings() {
        console.log('导出系统配置...');

        // 模拟导出功能
        const settings = {
            basic: this.settings.basic,
            monitoring: this.settings.monitoring,
            transaction: this.settings.transaction,
            security: {...this.settings.security, apiSecretKey: '***'} // 隐藏敏感信息
        };

        const dataStr = JSON.stringify(settings, null, 2);
        const dataBlob = new Blob([dataStr], {type: 'application/json'});

        const link = document.createElement('a');
        link.href = URL.createObjectURL(dataBlob);
        link.download = 'tipray-settings-' + new Date().toISOString().split('T')[0] + '.json';
        link.click();

        this.showSuccessMessage('配置文件导出成功');
    }

    /**
     * 显示导入模态框
     */
    showImportModal() {
        const modal = document.getElementById('importModal');
        if (modal && typeof bootstrap !== 'undefined') {
            const bsModal = new bootstrap.Modal(modal);
            bsModal.show();
        }
    }

    /**
     * 确认导入
     */
    confirmImport() {
        const fileInput = document.getElementById('configFile');
        if (fileInput && fileInput.files.length > 0) {
            const file = fileInput.files[0];
            const reader = new FileReader();

            reader.onload = (e) => {
                try {
                    const settings = JSON.parse(e.target.result);
                    this.updateSettingsForms(settings);
                    this.showSuccessMessage('配置导入成功');

                    // 关闭模态框
                    const modal = document.getElementById('importModal');
                    if (modal && typeof bootstrap !== 'undefined') {
                        const bsModal = bootstrap.Modal.getInstance(modal);
                        if (bsModal) {
                            bsModal.hide();
                        }
                    }
                } catch (error) {
                    this.showErrorMessage('配置文件格式错误: ' + error.message);
                }
            };

            reader.readAsText(file);
        } else {
            this.showErrorMessage('请选择配置文件');
        }
    }

    /**
     * 重置设置
     */
    async resetSettings() {
        const result = await ConfirmDialog.danger('确定要重置所有设置为默认值吗？此操作不可撤销！', '危险操作');
        if (result.confirmed) {
            console.log('重置系统设置...');
            this.showSuccessMessage('设置已重置为默认值');
            this.refreshData();
        }
    }

    /**
     * 清理缓存
     */
    async clearCache() {
        const result = await ConfirmDialog.confirm('确定要清理系统缓存吗？');
        if (result.confirmed) {
            console.log('清理系统缓存...');
            this.showSuccessMessage('系统缓存清理完成');
        }
    }

    /**
     * 刷新系统信息
     */
    async refreshSystemInfo() {
        try {
            console.log('刷新系统信息...');

            // 模拟API调用
            const data = await this.fetchSettings();
            this.updateSystemInfo(data.systemInfo);

            this.showSuccessMessage('系统信息刷新完成');
        } catch (error) {
            console.error('系统信息刷新失败:', error);
            this.showErrorMessage('系统信息刷新失败: ' + error.message);
        }
    }

    /**
     * 显示成功消息
     */
    showSuccessMessage(message) {
        console.log('成功:', message);
        if (window.TablerApp && typeof window.TablerApp.showSuccessNotification === 'function') {
            window.TablerApp.showSuccessNotification(message);
        } else {
            ConfirmDialog.success(message);
        }
    }

    /**
     * 显示错误消息
     */
    showErrorMessage(message) {
        console.error('错误:', message);
        if (window.TablerApp && typeof window.TablerApp.showErrorNotification === 'function') {
            window.TablerApp.showErrorNotification(message);
        } else {
            ConfirmDialog.error(message);
        }
    }
}

// 注册页面类到全局
window.SettingsPage = SettingsPage;
