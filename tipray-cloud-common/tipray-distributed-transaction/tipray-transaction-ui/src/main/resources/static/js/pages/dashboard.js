/**
 * 仪表板页面类
 * 管理仪表板页面的数据加载和交互
 */
class DashboardPage {
    constructor() {
        this.charts = {};
        this.refreshInterval = null;
        this.realtimeData = [];
        this.maxRealtimeItems = 50;
    }

    /**
     * 初始化页面
     */
    async init() {
        try {
            console.log('初始化仪表板页面...');

            // 初始化事件监听器
            this.initEventListeners();

            // 加载初始数据
            await this.loadData();

            // 加载最近事务
            await this.loadRecentTransactions();

            // 启动实时数据更新
            this.startRealtimeUpdates();

            console.log('仪表板页面初始化完成');
        } catch (error) {
            console.error('仪表板页面初始化失败:', error);
        }
    }

    /**
     * 销毁页面
     */
    async destroy() {
        try {
            // 停止实时更新
            this.stopRealtimeUpdates();

            // 销毁图表
            this.destroyCharts();

            // 清理事件监听器
            this.removeEventListeners();

            console.log('仪表板页面已销毁');
        } catch (error) {
            console.error('仪表板页面销毁失败:', error);
        }
    }

    /**
     * 刷新数据
     */
    async refreshData() {
        try {
            await this.loadData();
            this.updateCharts();
            console.log('仪表板数据刷新完成');
        } catch (error) {
            console.error('仪表板数据刷新失败:', error);
        }
    }

    /**
     * 初始化事件监听器
     */
    initEventListeners() {
        // 暂停/恢复实时更新
        const pauseBtn = document.getElementById('pauseRealtime');
        if (pauseBtn) {
            pauseBtn.addEventListener('click', (e) => {
                e.preventDefault();
                this.toggleRealtimeUpdates();
            });
        }

        // 清空实时列表
        const clearBtn = document.getElementById('clearRealtime');
        if (clearBtn) {
            clearBtn.addEventListener('click', (e) => {
                e.preventDefault();
                this.clearRealtimeList();
            });
        }

        // 时间范围选择
        document.querySelectorAll('[data-range]').forEach(item => {
            item.addEventListener('click', (e) => {
                e.preventDefault();
                const range = e.target.getAttribute('data-range');
                this.changeTimeRange(range);
            });
        });
    }

    /**
     * 移除事件监听器
     */
    removeEventListeners() {
        // 这里可以移除特定的事件监听器
        // 由于使用了addEventListener，页面销毁时会自动清理
    }

    /**
     * 加载数据
     */
    async loadData() {
        try {
            // 模拟API调用
            const data = await this.fetchDashboardData();

            // 更新统计卡片
            this.updateStatCards(data.stats);

            // 更新图表数据
            this.updateChartsData(data.charts);

        } catch (error) {
            console.error('数据加载失败:', error);
            throw error;
        }
    }

    /**
     * 获取仪表板数据
     */
    async fetchDashboardData() {
        try {
            console.log('获取仪表板数据...');

            // 调用后端仪表板概览接口
            const response = await fetch('/api/dashboard/overview');
            const result = await response.json();

            console.log('仪表板数据响应:', result);

            // 适配ApiResponse格式
            if (result.success !== false) {
                const data = result.data || result;

                // 转换数据格式以适配前端组件
                return {
                    stats: {
                        total: data.total || 0,
                        success: data.success || 0,
                        running: data.running || 0,
                        failed: data.failed || 0,
                        successRate: data.successRatePercent || data.successRate || "0.0%",
                        failureRate: data.failureRate || "0.0%",
                        trends: data.trends || []
                    }
                };
            } else {
                throw new Error(result.message || '获取仪表板数据失败');
            }
        } catch (error) {
            console.error('获取仪表板数据失败:', error);

            // API调用失败，返回空数据
            console.warn('API调用失败，返回空统计数据');
            this.showError('无法获取统计数据，请检查后端服务是否正常运行');
            return {
                stats: {
                    total: 0,
                    success: 0,
                    running: 0,
                    failed: 0,
                    successRate: "0.0%",
                    failureRate: "0.0%",
                    trends: []
                }
            };
        }
    }

    /**
     * 生成趋势数据
     */
    generateTrendData() {
        const data = [];
        const now = new Date();
        for (let i = 23; i >= 0; i--) {
            const time = new Date(now.getTime() - i * 60 * 60 * 1000);
            data.push({
                time: time.toISOString(),
                success: Math.floor(Math.random() * 100),
                failed: Math.floor(Math.random() * 20),
                running: Math.floor(Math.random() * 10)
            });
        }
        return data;
    }

    /**
     * 生成模式分布数据
     */
    generateModeData() {
        return [
            {name: 'AT模式', value: Math.floor(Math.random() * 1000)},
            {name: 'Saga模式', value: Math.floor(Math.random() * 800)},
            {name: 'TCC模式', value: Math.floor(Math.random() * 200)}
        ];
    }

    /**
     * 更新统计卡片
     */
    updateStatCards(stats) {
        const elements = {
            totalTransactions: document.getElementById('totalTransactions'),
            successTransactions: document.getElementById('successTransactions'),
            runningTransactions: document.getElementById('runningTransactions'),
            failedTransactions: document.getElementById('failedTransactions'),
            failureRate: document.getElementById('failureRate')
        };

        if (elements.totalTransactions) {
            elements.totalTransactions.textContent = stats.total.toLocaleString();
        }
        if (elements.successTransactions) {
            elements.successTransactions.textContent = stats.success.toLocaleString();
            // 更新成功率
            const successRateElement = elements.successTransactions.closest('.card-body').querySelector('.badge');
            if (successRateElement) {
                successRateElement.textContent = stats.successRate;
            }
        }
        if (elements.runningTransactions) {
            elements.runningTransactions.textContent = stats.running.toLocaleString();
        }
        if (elements.failedTransactions) {
            elements.failedTransactions.textContent = stats.failed.toLocaleString();
        }
        if (elements.failureRate) {
            elements.failureRate.textContent = stats.failureRate;
        }
    }

    /**
     * 初始化图表
     */
    initCharts() {
        // 这里可以使用Chart.js、ECharts或其他图表库
        // 为了简化，这里只是占位符
        console.log('初始化图表...');

        // 初始化趋势图表
        this.initTrendChart();

        // 初始化模式分布图表
        this.initModeChart();
    }

    /**
     * 初始化趋势图表
     */
    initTrendChart() {
        const container = document.getElementById('chart-transaction-trend');
        if (container) {
            // 这里可以初始化具体的图表
            container.innerHTML = '<div class="text-center text-muted">趋势图表占位符</div>';
        }
    }

    /**
     * 初始化模式分布图表
     */
    initModeChart() {
        const container = document.getElementById('chart-transaction-mode');
        if (container) {
            // 这里可以初始化具体的图表
            container.innerHTML = '<div class="text-center text-muted">模式分布图表占位符</div>';
        }
    }

    /**
     * 更新图表数据
     */
    updateChartsData(data) {
        // 更新图表数据
        console.log('更新图表数据:', data);
    }

    /**
     * 更新图表
     */
    updateCharts() {
        // 刷新所有图表
        Object.values(this.charts).forEach(chart => {
            if (chart && typeof chart.update === 'function') {
                chart.update();
            }
        });
    }

    /**
     * 销毁图表
     */
    destroyCharts() {
        Object.values(this.charts).forEach(chart => {
            if (chart && typeof chart.destroy === 'function') {
                chart.destroy();
            }
        });
        this.charts = {};
    }

    /**
     * 启动实时更新
     */
    startRealtimeUpdates() {
        this.refreshInterval = setInterval(async () => {
            try {
                await this.loadRealtimeTransactions();
                await this.loadRecentTransactions();
                await this.loadData(); // 更新统计数据
            } catch (error) {
                console.error('实时更新失败:', error);
            }
        }, 5000); // 每5秒获取一次实时数据
    }

    /**
     * 停止实时更新
     */
    stopRealtimeUpdates() {
        if (this.refreshInterval) {
            clearInterval(this.refreshInterval);
            this.refreshInterval = null;
        }
    }

    /**
     * 切换实时更新
     */
    toggleRealtimeUpdates() {
        const pauseBtn = document.getElementById('pauseRealtime');
        if (this.refreshInterval) {
            this.stopRealtimeUpdates();
            if (pauseBtn) {
                pauseBtn.innerHTML = '<svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round"><polygon points="5,3 19,12 5,21"/></svg> 继续';
            }
        } else {
            this.startRealtimeUpdates();
            if (pauseBtn) {
                pauseBtn.innerHTML = '<svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round"><rect x="6" y="4" width="4" height="16"/><rect x="14" y="4" width="4" height="16"/></svg> 暂停';
            }
        }
    }

    /**
     * 加载实时事务数据
     */
    async loadRealtimeTransactions() {
        try {
            const response = await fetch('/api/dashboard/recent-transactions?limit=10');
            const result = await response.json();

            if (result.success !== false) {
                const data = result.data || result;
                const transactions = data.list || [];

                // 转换数据格式
                const realtimeTransactions = transactions.map(transaction => ({
                    transactionId: transaction.transactionId,
                    transactionName: transaction.transactionName || transaction.groupId,
                    mode: transaction.mode || 'AT',
                    status: transaction.status,
                    statusText: this.getStatusText(transaction.status),
                    stepCount: transaction.stepCount || (transaction.steps ? transaction.steps.length : 0),
                    startTime: transaction.startTime,
                    duration: transaction.duration,
                    errorMessage: transaction.errorMessage
                }));

                this.updateRealtimeTransactionsTable(realtimeTransactions);
            }
        } catch (error) {
            console.error('加载实时事务数据失败:', error);
            throw error;
        }
    }

    /**
     * 映射事务状态
     */
    mapTransactionStatus(status) {
        if (!status) return 'warning';

        const statusUpper = status.toUpperCase();
        if (statusUpper.includes('SUCCESS') || statusUpper.includes('COMMITTED')) {
            return 'success';
        } else if (statusUpper.includes('FAILED') || statusUpper.includes('ERROR')) {
            return 'danger';
        } else {
            return 'warning';
        }
    }

    /**
     * 获取状态文本
     */
    getStatusText(status) {
        const statusMap = {
            // 基本状态
            'INIT': '初始化',
            'PENDING': '待执行',
            'EXECUTING': '执行中',
            'PREPARING': '准备提交',
            'WAITING': '等待中',
            'PAUSED': '暂停',

            // 成功状态
            'SUCCESS': '成功',
            'COMMITTED': '已提交',
            'UNDONE': '撤销成功',
            'COMPENSATED': '已补偿',
            'ROLLBACKED': '回滚完成',

            // 失败状态
            'FAILED': '失败',
            'UNDO_FAILED': '撤销失败',
            'COMPENSATE_FAILED': '补偿失败',
            'ROLLBACK_FAILED': '回滚失败',
            'MANUAL_INTERVENTION_REQUIRED': '需要人工干预',

            // 进行中状态
            'UNDOING': '撤销中',
            'COMPENSATING': '补偿中',
            'ROLLBACK': '已回滚',

            // 其他状态
            'TIMEOUT': '超时',
            'CANCELLED': '已取消',
            'PARTIAL_FAILED': '部分失败'
        };
        return statusMap[status] || `未知状态(${status})`;
    }

    /**
     * 更新实时事务表格
     */
    updateRealtimeTransactionsTable(transactions) {
        const tableBody = document.getElementById('realtimeTransactionsTable');
        if (!tableBody) return;

        const html = transactions.map(transaction => {
            const statusClass = this.getStatusClass(transaction.status);
            const timeFormatted = this.formatDateTime(transaction.startTime);
            const durationFormatted = this.formatDuration(transaction.duration);

            return `
                <tr>
                    <td>
                        <span class="text-truncate" style="max-width: 200px; display: inline-block;" title="${transaction.transactionId}">
                            ${transaction.transactionId}
                        </span>
                    </td>
                    <td>
                        <span class="text-truncate" style="max-width: 150px; display: inline-block;" title="${transaction.transactionName}">
                            ${transaction.transactionName}
                        </span>
                    </td>
                    <td>
                        <span class="badge bg-info-lt">${transaction.mode}</span>
                    </td>
                    <td>
                        <span class="badge bg-${statusClass}">${transaction.statusText}</span>
                    </td>
                    <td>${transaction.stepCount}</td>
                    <td>${timeFormatted}</td>
                    <td>${durationFormatted}</td>
                    <td>
                        <div class="btn-group btn-group-sm">
                            <button class="btn btn-outline-primary btn-sm" onclick="viewTransactionDetail('${transaction.transactionId}')" title="查看详情">
                                <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="16" height="16" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                    <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
                                    <circle cx="12" cy="12" r="2"/>
                                    <path d="M22 12c-2.667 4.667 -6 7 -10 7s-7.333 -2.333 -10 -7c2.667 -4.667 6 -7 10 -7s7.333 2.333 10 7"/>
                                </svg>
                            </button>
                            ${transaction.status === 'FAILED' || transaction.status === 'UNDONE' ? `
                            <button class="btn btn-outline-warning btn-sm" onclick="retryTransaction('${transaction.transactionId}')" title="重试">
                                <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="16" height="16" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                    <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
                                    <path d="M4.05 11a8 8 0 1 1 .5 4m-.5 5v-5h5"/>
                                </svg>
                            </button>
                            ` : ''}
                        </div>
                    </td>
                </tr>
            `;
        }).join('');

        tableBody.innerHTML = html;
    }

    /**
     * 获取状态样式类
     */
    getStatusClass(status) {
        const statusMap = {
            // 基本状态
            'INIT': 'secondary',
            'PENDING': 'info',
            'EXECUTING': 'warning',
            'PREPARING': 'info',
            'WAITING': 'warning',
            'PAUSED': 'warning',

            // 成功状态
            'SUCCESS': 'success',
            'COMMITTED': 'success',
            'UNDONE': 'success',
            'COMPENSATED': 'success',
            'ROLLBACKED': 'success',

            // 失败状态
            'FAILED': 'danger',
            'UNDO_FAILED': 'danger',
            'COMPENSATE_FAILED': 'danger',
            'ROLLBACK_FAILED': 'danger',
            'MANUAL_INTERVENTION_REQUIRED': 'danger',

            // 进行中状态
            'UNDOING': 'warning',
            'COMPENSATING': 'warning',
            'ROLLBACK': 'warning',

            // 其他状态
            'TIMEOUT': 'orange',
            'CANCELLED': 'secondary',
            'PARTIAL_FAILED': 'warning'
        };
        return statusMap[status] || 'secondary';
    }

    /**
     * 格式化日期时间
     */
    formatDateTime(dateTimeStr) {
        if (!dateTimeStr) return '-';

        try {
            const date = new Date(dateTimeStr);
            return date.toLocaleString('zh-CN', {
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
            });
        } catch (e) {
            return dateTimeStr;
        }
    }

    /**
     * 格式化持续时间
     */
    formatDuration(duration) {
        if (!duration) return '-';

        if (duration < 1000) {
            return duration + 'ms';
        } else if (duration < 60000) {
            return (duration / 1000).toFixed(1) + 's';
        } else {
            const minutes = Math.floor(duration / 60000);
            const seconds = Math.floor((duration % 60000) / 1000);
            return `${minutes}m${seconds}s`;
        }
    }

    /**
     * 添加实时事务
     */
    addRealtimeTransaction() {
        const transaction = this.generateRandomTransaction();
        this.realtimeData.unshift(transaction);

        // 限制最大数量
        if (this.realtimeData.length > this.maxRealtimeItems) {
            this.realtimeData = this.realtimeData.slice(0, this.maxRealtimeItems);
        }

        this.updateRealtimeList();
    }

    /**
     * 生成随机事务
     */
    generateRandomTransaction() {
        const statuses = ['success', 'warning', 'danger'];
        const statusTexts = ['成功', '执行中', '失败'];
        const services = ['订单服务', '支付服务', '库存服务', '用户服务'];
        const modes = ['AT', 'Saga'];

        const statusIndex = Math.floor(Math.random() * statuses.length);
        const status = statuses[statusIndex];
        const statusText = statusTexts[statusIndex];

        return {
            id: `TXN-${Date.now()}-${Math.floor(Math.random() * 1000)}`,
            mode: modes[Math.floor(Math.random() * modes.length)],
            status: status,
            statusText: statusText,
            service: services[Math.floor(Math.random() * services.length)],
            time: new Date()
        };
    }

    /**
     * 更新实时列表
     */
    updateRealtimeList() {
        const container = document.getElementById('realtimeTransactionList');
        if (!container) return;

        const html = this.realtimeData.map(transaction => {
            const timeAgo = this.getTimeAgo(transaction.time);
            return `
                <div class="row">
                    <div class="col-auto">
                        <span class="status-indicator status-${transaction.status}"></span>
                    </div>
                    <div class="col">
                        <div class="text-truncate">
                            <strong>${transaction.id}</strong> - ${transaction.mode}模式事务${transaction.statusText}
                        </div>
                        <div class="text-muted">${transaction.service} • ${timeAgo}</div>
                    </div>
                    <div class="col-auto">
                        <span class="badge bg-${transaction.status === 'success' ? 'success' : transaction.status === 'warning' ? 'warning' : 'danger'}">${transaction.statusText}</span>
                    </div>
                </div>
            `;
        }).join('');

        container.innerHTML = html;
    }

    /**
     * 清空实时列表
     */
    clearRealtimeList() {
        this.realtimeData = [];
        this.updateRealtimeList();
    }

    /**
     * 获取时间差描述
     */
    getTimeAgo(time) {
        const now = new Date();
        const diff = Math.floor((now - time) / 1000);

        if (diff < 60) {
            return `${diff}秒前`;
        } else if (diff < 3600) {
            return `${Math.floor(diff / 60)}分钟前`;
        } else {
            return `${Math.floor(diff / 3600)}小时前`;
        }
    }

    /**
     * 改变时间范围
     */
    changeTimeRange(range) {
        console.log('改变时间范围:', range);
        // 这里可以根据时间范围重新加载数据
        this.refreshData();
    }

    /**
     * 加载最近事务
     */
    async loadRecentTransactions() {
        try {
            const response = await fetch('/api/dashboard/recent-transactions?limit=5');
            const result = await response.json();

            if (result.success !== false) {
                const data = result.data || result;
                const transactions = data.list || [];

                this.updateRecentTransactionsTable(transactions);
            }
        } catch (error) {
            console.error('加载最近事务失败:', error);
        }
    }

    /**
     * 更新最近事务表格
     */
    updateRecentTransactionsTable(transactions) {
        const tableBody = document.getElementById('recentTransactionsTable');
        if (!tableBody) return;

        const html = transactions.map(transaction => {
            const statusClass = this.getStatusClass(transaction.status);
            const timeFormatted = this.formatDateTime(transaction.startTime);
            const durationFormatted = this.formatDuration(transaction.duration);

            return `
                <tr>
                    <td>
                        <span class="text-truncate" style="max-width: 150px; display: inline-block;" title="${transaction.transactionId}">
                            ${transaction.transactionId}
                        </span>
                    </td>
                    <td>
                        <span class="badge bg-info-lt">${transaction.mode}</span>
                    </td>
                    <td>
                        <span class="badge bg-${statusClass}">${this.getStatusText(transaction.status)}</span>
                    </td>
                    <td>${timeFormatted}</td>
                    <td>${durationFormatted}</td>
                </tr>
            `;
        }).join('');

        tableBody.innerHTML = html;
    }

    /**
     * 显示错误信息
     */
    showError(message) {
        console.error('Dashboard Error:', message);
        // 这里可以显示用户友好的错误提示
        // 例如使用toast或者alert
    }
}

// 注册页面类到全局
window.DashboardPage = DashboardPage;

// 全局函数：查看事务详情
window.viewTransactionDetail = function (transactionId) {
    console.log('查看事务详情:', transactionId);

    // 检查是否有事务详情管理器
    if (window.transactionDetailManager && typeof window.transactionDetailManager.showTransactionDetail === 'function') {
        window.transactionDetailManager.showTransactionDetail(transactionId);
    } else if (window.pageManager) {
        // 如果没有详情管理器，跳转到事务列表页面
        window.pageManager.loadPage('transactions', {transactionId: transactionId});
    } else {
        // 最后的备选方案：直接调用API获取详情
        showTransactionDetailModal(transactionId);
    }
};

// 显示事务详情模态框
async function showTransactionDetailModal(transactionId) {
    try {
        // 调用API获取事务详情
        const response = await fetch(`/api/transaction/detail/${transactionId}`);
        const result = await response.json();

        if (result.success !== false) {
            const transaction = result.data || result;

            // 创建模态框HTML
            const modalHtml = `
                <div class="modal fade" id="transactionDetailModal" tabindex="-1">
                    <div class="modal-dialog modal-lg">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title">事务详情</h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                            </div>
                            <div class="modal-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <h6>基本信息</h6>
                                        <dl class="row">
                                            <dt class="col-4">事务ID:</dt>
                                            <dd class="col-8">${transaction.transactionId}</dd>
                                            <dt class="col-4">事务名称:</dt>
                                            <dd class="col-8">${transaction.transactionName || '-'}</dd>
                                            <dt class="col-4">事务模式:</dt>
                                            <dd class="col-8"><span class="badge bg-info">${transaction.mode}</span></dd>
                                            <dt class="col-4">状态:</dt>
                                            <dd class="col-8"><span class="badge bg-${getStatusClassForModal(transaction.status)}">${getStatusTextForModal(transaction.status)}</span></dd>
                                            <dt class="col-4">开始时间:</dt>
                                            <dd class="col-8">${formatDateTimeForModal(transaction.startTime)}</dd>
                                            <dt class="col-4">结束时间:</dt>
                                            <dd class="col-8">${formatDateTimeForModal(transaction.endTime)}</dd>
                                            <dt class="col-4">持续时间:</dt>
                                            <dd class="col-8">${formatDurationForModal(transaction.duration)}</dd>
                                        </dl>
                                    </div>
                                    <div class="col-md-6">
                                        <h6>执行步骤</h6>
                                        <div class="list-group list-group-flush">
                                            ${(transaction.steps || []).map((step, index) => `
                                                <div class="list-group-item">
                                                    <div class="row align-items-center">
                                                        <div class="col-auto">
                                                            <span class="badge bg-${getStatusClassForModal(step.status)}">${index + 1}</span>
                                                        </div>
                                                        <div class="col">
                                                            <div class="text-truncate">
                                                                <strong>${step.stepName}</strong>
                                                            </div>
                                                            <div class="text-muted">${step.targetService} • ${getStatusTextForModal(step.status)}</div>
                                                        </div>
                                                    </div>
                                                </div>
                                            `).join('')}
                                        </div>
                                    </div>
                                </div>
                                ${transaction.errorMessage ? `
                                    <div class="mt-3">
                                        <h6>错误信息</h6>
                                        <div class="alert alert-danger">
                                            <pre class="mb-0">${transaction.errorMessage}</pre>
                                        </div>
                                    </div>
                                ` : ''}
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            // 移除已存在的模态框
            const existingModal = document.getElementById('transactionDetailModal');
            if (existingModal) {
                existingModal.remove();
            }

            // 添加新的模态框到页面
            document.body.insertAdjacentHTML('beforeend', modalHtml);

            // 显示模态框
            const modal = new bootstrap.Modal(document.getElementById('transactionDetailModal'));
            modal.show();

        } else {
            ConfirmDialog.error('获取事务详情失败: ' + (result.message || '未知错误'));
        }
    } catch (error) {
        console.error('获取事务详情失败:', error);
        ConfirmDialog.error('获取事务详情失败: ' + error.message);
    }
}

// 模态框中使用的辅助函数
function getStatusClassForModal(status) {
    const statusMap = {
        'INIT': 'secondary', 'PENDING': 'info', 'EXECUTING': 'warning', 'PREPARING': 'info',
        'WAITING': 'warning', 'PAUSED': 'warning', 'SUCCESS': 'success', 'COMMITTED': 'success',
        'UNDONE': 'success', 'COMPENSATED': 'success', 'ROLLBACKED': 'success', 'FAILED': 'danger',
        'UNDO_FAILED': 'danger', 'COMPENSATE_FAILED': 'danger', 'ROLLBACK_FAILED': 'danger',
        'MANUAL_INTERVENTION_REQUIRED': 'danger', 'UNDOING': 'warning', 'COMPENSATING': 'warning',
        'ROLLBACK': 'warning', 'TIMEOUT': 'warning', 'CANCELLED': 'secondary', 'PARTIAL_FAILED': 'warning'
    };
    return statusMap[status] || 'secondary';
}

function getStatusTextForModal(status) {
    const statusMap = {
        'INIT': '初始化', 'PENDING': '待执行', 'EXECUTING': '执行中', 'PREPARING': '准备提交',
        'WAITING': '等待中', 'PAUSED': '暂停', 'SUCCESS': '成功', 'COMMITTED': '已提交',
        'UNDONE': '撤销成功', 'COMPENSATED': '已补偿', 'ROLLBACKED': '回滚完成', 'FAILED': '失败',
        'UNDO_FAILED': '撤销失败', 'COMPENSATE_FAILED': '补偿失败', 'ROLLBACK_FAILED': '回滚失败',
        'MANUAL_INTERVENTION_REQUIRED': '需要人工干预', 'UNDOING': '撤销中', 'COMPENSATING': '补偿中',
        'ROLLBACK': '已回滚', 'TIMEOUT': '超时', 'CANCELLED': '已取消', 'PARTIAL_FAILED': '部分失败'
    };
    return statusMap[status] || `未知状态(${status})`;
}

function formatDateTimeForModal(dateTimeStr) {
    if (!dateTimeStr) return '-';
    try {
        const date = new Date(dateTimeStr);
        return date.toLocaleString('zh-CN');
    } catch (e) {
        return dateTimeStr;
    }
}

function formatDurationForModal(duration) {
    if (!duration) return '-';
    if (duration < 1000) {
        return duration + 'ms';
    } else if (duration < 60000) {
        return (duration / 1000).toFixed(1) + 's';
    } else {
        const minutes = Math.floor(duration / 60000);
        const seconds = Math.floor((duration % 60000) / 1000);
        return `${minutes}m${seconds}s`;
    }
}

// 全局函数：重试事务
window.retryTransaction = async function (transactionId) {
    console.log('重试事务:', transactionId);
    const result = await ConfirmDialog.confirm('确定要重试这个事务吗？');
    if (result.confirmed) {
        try {
            const operatorId = localStorage.getItem('currentUserId') || 'admin';
            const reason = '仪表板手动重试';
            const response = await window.apiService.retryTransaction(transactionId, operatorId, reason);

            if (response.success !== false) {
                ConfirmDialog.success('重试操作已提交');
                // 刷新数据
                if (window.currentPage && window.currentPage.refreshData) {
                    window.currentPage.refreshData();
                }
            } else {
                throw new Error(response.message || '重试操作失败');
            }
        } catch (error) {
            console.error('重试失败:', error);
            ConfirmDialog.error('重试操作失败: ' + error.message);
        }
    }
};
