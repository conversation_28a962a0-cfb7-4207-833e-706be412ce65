/**
 * API服务类
 * 统一管理所有后端接口调用
 */
class ApiService {
    constructor() {
        this.baseUrl = '/api/transaction';
        this.timeout = 30000; // 30秒超时
    }

    /**
     * 通用请求方法
     * @param {string} url 请求URL
     * @param {Object} options 请求选项
     * @returns {Promise<Object>} 响应数据
     */
    async request(url, options = {}) {
        const defaultOptions = {
            headers: {
                'Content-Type': 'application/json',
                'Accept': 'application/json'
            },
            timeout: this.timeout
        };

        const finalOptions = {...defaultOptions, ...options};

        try {
            console.log(`API请求: ${finalOptions.method || 'GET'} ${url}`);
            console.log('请求参数:', finalOptions);

            const response = await fetch(url, finalOptions);

            console.log(`响应状态: ${response.status} ${response.statusText}`);

            if (!response.ok) {
                const errorText = await response.text();
                console.error('响应错误内容:', errorText);
                throw new Error(`HTTP ${response.status}: ${response.statusText} - ${errorText}`);
            }

            const data = await response.json();

            // 检查业务状态码 - 适配ApiResponse格式
            if (data.success === false) {
                throw new Error(data.message || '请求失败');
            }

            console.log(`API响应: ${url}`, data);
            return data;
        } catch (error) {
            console.error(`API请求失败: ${url}`, error);

            // 如果是网络错误，提供更友好的错误信息
            if (error.name === 'TypeError' && error.message.includes('fetch')) {
                throw new Error('网络连接失败，请检查服务器是否正常运行');
            }

            throw error;
        }
    }

    /**
     * GET请求
     */
    async get(url, params = {}) {
        const queryString = new URLSearchParams(params).toString();
        const fullUrl = queryString ? `${url}?${queryString}` : url;

        return this.request(fullUrl, {
            method: 'GET'
        });
    }

    /**
     * POST请求
     */
    async post(url, data = {}) {
        return this.request(url, {
            method: 'POST',
            body: JSON.stringify(data)
        });
    }

    /**
     * PUT请求
     */
    async put(url, data = {}) {
        return this.request(url, {
            method: 'PUT',
            body: JSON.stringify(data)
        });
    }

    /**
     * DELETE请求
     */
    async delete(url) {
        return this.request(url, {
            method: 'DELETE'
        });
    }

    // ==================== 事务相关接口 ====================

    /**
     * 查询事务列表
     * @param {Object} queryParams 查询参数
     * @returns {Promise<Object>} 事务列表响应
     */
    async getTransactionList(queryParams = {}) {
        // 对接真实的后端接口 POST /api/transaction/list
        console.log('调用事务列表接口:', queryParams);
        return this.post(`${this.baseUrl}/list`, queryParams);
    }

    /**
     * 获取事务详情
     * @param {string} transactionId 事务ID
     * @returns {Promise<Object>} 事务详情响应
     */
    async getTransactionDetail(transactionId) {
        // 对接真实的后端接口 GET /api/transaction/detail/{transactionId}
        console.log('调用事务详情接口:', transactionId);
        return this.get(`${this.baseUrl}/detail/${transactionId}`);
    }

    /**
     * 获取实时事务数据
     * @param {Object} params 查询参数
     * @returns {Promise<Object>} 实时事务数据
     */
    async getRealtimeTransactions(params = {}) {
        // 对接真实的实时数据接口
        console.log('调用实时事务数据接口:', params);
        return this.get(`${this.baseUrl}/realtime`, params);
    }

    // ==================== 仪表板相关接口 ====================

    /**
     * 获取仪表板概览数据
     * @returns {Promise<Object>} 仪表板概览数据
     */
    async getDashboardOverview() {
        console.log('调用仪表板概览接口');
        return this.get('/api/dashboard/overview');
    }

    /**
     * 获取实时监控数据
     * @returns {Promise<Object>} 实时监控数据
     */
    async getRealtimeMonitorData() {
        console.log('调用实时监控数据接口');
        return this.get('/api/dashboard/realtime');
    }

    /**
     * 获取事务模式统计
     * @param {number} days 统计天数
     * @returns {Promise<Object>} 事务模式统计数据
     */
    async getModeStatistics(days = 7) {
        console.log('调用事务模式统计接口:', days);
        return this.get('/api/dashboard/mode-statistics', {days});
    }

    /**
     * 获取性能趋势数据
     * @param {number} hours 统计小时数
     * @returns {Promise<Object>} 性能趋势数据
     */
    async getPerformanceTrends(hours = 24) {
        console.log('调用性能趋势接口:', hours);
        return this.get('/api/dashboard/performance-trends', {hours});
    }

    /**
     * 获取异常统计分析
     * @param {number} days 统计天数
     * @returns {Promise<Object>} 异常统计数据
     */
    async getExceptionStatistics(days = 7) {
        console.log('调用异常统计接口:', days);
        return this.get('/api/dashboard/exception-statistics', {days});
    }

    /**
     * 获取系统健康检查
     * @returns {Promise<Object>} 系统健康状态
     */
    async getHealthCheck() {
        console.log('调用系统健康检查接口');
        return this.get('/api/dashboard/health-check');
    }

    /**
     * 获取事务统计信息
     * @param {number} days 统计天数
     * @returns {Promise<Object>} 统计信息响应
     */
    async getTransactionStatistics(days = 7) {
        return this.get(`${this.baseUrl}/statistics`, {days});
    }

    /**
     * 获取事务统计信息（POST方式，支持复杂查询）
     * @param {Object} queryParams 查询参数
     * @returns {Promise<Object>} 统计信息响应
     */
    async getTransactionStatisticsPost(queryParams = {}) {
        // 对接真实的后端接口 POST /api/transaction/statistics
        console.log('调用事务统计接口(POST):', queryParams);
        return this.post(`${this.baseUrl}/statistics`, queryParams);
    }

    // ==================== 统计分析相关接口 ====================

    /**
     * 获取事务模式统计分析
     * @param {string} startTime 开始时间
     * @param {string} endTime 结束时间
     * @returns {Promise<Object>} 事务模式统计数据
     */
    async getTransactionModeStatistics(startTime, endTime) {
        console.log('调用事务模式统计分析接口:', startTime, endTime);
        const params = {};
        if (startTime) params.startTime = startTime;
        if (endTime) params.endTime = endTime;
        return this.get('/api/statistics/transaction-modes', params);
    }

    /**
     * 获取时间维度统计
     * @param {string} dimension 时间维度：hour/day/week/month
     * @param {string} startTime 开始时间
     * @param {string} endTime 结束时间
     * @returns {Promise<Object>} 时间维度统计数据
     */
    async getTimeDimensionStatistics(dimension = 'day', startTime, endTime) {
        console.log('调用时间维度统计接口:', dimension, startTime, endTime);
        const params = {dimension};
        if (startTime) params.startTime = startTime;
        if (endTime) params.endTime = endTime;
        return this.get('/api/statistics/time-dimension', params);
    }

    /**
     * 获取性能统计分析
     * @param {number} days 统计天数
     * @returns {Promise<Object>} 性能统计数据
     */
    async getPerformanceStatistics(days = 7) {
        console.log('调用性能统计分析接口:', days);
        return this.get('/api/statistics/performance', {days});
    }

    /**
     * 获取异常分析统计
     * @param {number} days 统计天数
     * @returns {Promise<Object>} 异常分析数据
     */
    async getExceptionAnalysis(days = 7) {
        console.log('调用异常分析统计接口:', days);
        return this.get('/api/statistics/exceptions', {days});
    }

    /**
     * 批量重试事务
     * @param {Array<string>} transactionIds 事务ID列表
     * @returns {Promise<Object>} 操作结果
     */
    async batchRetryTransactions(transactionIds) {
        return this.post(`${this.baseUrl}/batch/retry`, {transactionIds});
    }

    /**
     * 批量取消事务
     * @param {Array<string>} transactionIds 事务ID列表
     * @returns {Promise<Object>} 操作结果
     */
    async batchCancelTransactions(transactionIds) {
        return this.post(`${this.baseUrl}/batch/cancel`, {transactionIds});
    }

    /**
     * 重试单个事务
     * @param {string} transactionId 事务ID
     * @param {string} operatorId 操作人ID，默认为'admin'
     * @param {string} reason 操作原因
     * @returns {Promise<Object>} 操作结果
     */
    async retryTransaction(transactionId, operatorId = 'admin', reason = '手动重试') {
        // 对接手动操作接口 POST /api/transaction/manual/retry/{transactionId}
        console.log('调用重试事务接口:', transactionId, operatorId, reason);
        const params = new URLSearchParams();
        params.append('operatorId', operatorId);
        if (reason) {
            params.append('reason', reason);
        }
        return this.post(`${this.baseUrl}/manual/retry/${transactionId}?${params.toString()}`);
    }

    /**
     * 取消单个事务
     * @param {string} transactionId 事务ID
     * @param {string} operatorId 操作人ID，默认为'admin'
     * @param {string} reason 操作原因
     * @returns {Promise<Object>} 操作结果
     */
    async cancelTransaction(transactionId, operatorId = 'admin', reason = '手动取消') {
        // 对接手动操作接口 POST /api/transaction/manual/cancel/{transactionId}
        console.log('调用取消事务接口:', transactionId, operatorId, reason);
        const params = new URLSearchParams();
        params.append('operatorId', operatorId);
        if (reason) {
            params.append('reason', reason);
        }
        return this.post(`${this.baseUrl}/manual/cancel/${transactionId}?${params.toString()}`);
    }

    /**
     * 手动回滚事务
     * @param {string} transactionId 事务ID
     * @param {string} operatorId 操作人ID，默认为'admin'
     * @param {string} reason 操作原因
     * @returns {Promise<Object>} 操作结果
     */
    async rollbackTransaction(transactionId, operatorId = 'admin', reason = '手动回滚') {
        // 对接手动操作接口 POST /api/transaction/manual/rollback/{transactionId}
        console.log('调用回滚事务接口:', transactionId, operatorId, reason);
        const params = new URLSearchParams();
        params.append('operatorId', operatorId);
        if (reason) {
            params.append('reason', reason);
        }
        return this.post(`${this.baseUrl}/manual/rollback/${transactionId}?${params.toString()}`);
    }

    /**
     * 手动提交事务
     * @param {string} transactionId 事务ID
     * @param {string} operatorId 操作人ID，默认为'admin'
     * @param {string} reason 操作原因
     * @returns {Promise<Object>} 操作结果
     */
    async commitTransaction(transactionId, operatorId = 'admin', reason = '手动提交') {
        // 对接手动操作接口 POST /api/transaction/manual/commit/{transactionId}
        console.log('调用提交事务接口:', transactionId, operatorId, reason);
        const params = new URLSearchParams();
        params.append('operatorId', operatorId);
        if (reason) {
            params.append('reason', reason);
        }
        return this.post(`${this.baseUrl}/manual/commit/${transactionId}?${params.toString()}`);
    }

    /**
     * 获取事务手动操作日志
     * @param {string} transactionId 事务ID
     * @returns {Promise<Object>} 日志数据
     */
    async getTransactionManualLogs(transactionId) {
        // 对接手动操作接口 GET /api/transaction/manual/logs/{transactionId}
        console.log('调用获取事务手动操作日志接口:', transactionId);
        return this.get(`${this.baseUrl}/manual/logs/${transactionId}`);
    }

    /**
     * 获取手动操作历史
     * @param {string} transactionId 事务ID
     * @returns {Promise<Object>} 操作历史数据
     */
    async getManualOperationHistory(transactionId) {
        // 对接手动操作接口 GET /api/transaction/manual/history/{transactionId}
        console.log('调用获取手动操作历史接口:', transactionId);
        return this.get(`${this.baseUrl}/manual/history/${transactionId}`);
    }

    /**
     * 检查操作可用性
     * @param {string} transactionId 事务ID
     * @param {string} operationType 操作类型 (retry|rollback|commit|cancel)
     * @returns {Promise<Object>} 检查结果
     */
    async checkOperationAvailable(transactionId, operationType) {
        // 对接手动操作接口 GET /api/transaction/manual/check/{transactionId}/{operationType}
        console.log('调用检查操作可用性接口:', transactionId, operationType);
        return this.get(`${this.baseUrl}/manual/check/${transactionId}/${operationType}`);
    }

    // ==================== 异常相关接口 ====================

    /**
     * 获取异常信息列表
     * @param {Object} queryParams 查询参数
     * @returns {Promise<Object>} 异常信息列表
     */
    async getExceptionList(queryParams = {}) {
        return this.post(`${this.baseUrl}/exceptions`, queryParams);
    }

    /**
     * 获取异常详情
     * @param {string} exceptionId 异常ID
     * @returns {Promise<Object>} 异常详情
     */
    async getExceptionDetail(exceptionId) {
        return this.get(`${this.baseUrl}/exceptions/${exceptionId}`);
    }

    /**
     * 标记异常为已处理
     * @param {string} exceptionId 异常ID
     * @returns {Promise<Object>} 操作结果
     */
    async markExceptionHandled(exceptionId) {
        return this.post(`${this.baseUrl}/exceptions/${exceptionId}/handle`);
    }

    /**
     * 忽略异常
     * @param {string} exceptionId 异常ID
     * @returns {Promise<Object>} 操作结果
     */
    async ignoreException(exceptionId) {
        return this.post(`${this.baseUrl}/exceptions/${exceptionId}/ignore`);
    }

    // ==================== 系统配置接口 ====================

    /**
     * 获取系统配置
     * @returns {Promise<Object>} 系统配置
     */
    async getSystemConfig() {
        return this.get(`${this.baseUrl}/config`);
    }

    /**
     * 更新系统配置
     * @param {Object} config 配置信息
     * @returns {Promise<Object>} 更新结果
     */
    async updateSystemConfig(config) {
        return this.put(`${this.baseUrl}/config`, config);
    }

    // ==================== 步骤相关接口 ====================

    /**
     * 获取步骤执行历史
     * @param {string} transactionId 事务ID
     * @param {number} stepId 步骤ID
     * @returns {Promise<Object>} 步骤执行历史
     */
    async getStepExecutionHistory(transactionId, stepId) {
        return this.get(`${this.baseUrl}/steps/${stepId}/history`, {transactionId});
    }

    /**
     * 重试步骤
     * @param {string} transactionId 事务ID
     * @param {number} stepId 步骤ID
     * @returns {Promise<Object>} 操作结果
     */
    async retryStep(transactionId, stepId) {
        return this.post(`${this.baseUrl}/steps/${stepId}/retry`, {transactionId});
    }

    /**
     * 跳过步骤
     * @param {string} transactionId 事务ID
     * @param {number} stepId 步骤ID
     * @param {string} reason 跳过原因
     * @returns {Promise<Object>} 操作结果
     */
    async skipStep(transactionId, stepId, reason) {
        return this.post(`${this.baseUrl}/steps/${stepId}/skip`, {transactionId, reason});
    }

    // ==================== 健康检查接口 ====================

    /**
     * 健康检查
     * @returns {Promise<Object>} 健康状态
     */
    async healthCheck() {
        return this.get(`${this.baseUrl}/health`);
    }

    /**
     * 获取系统信息
     * @returns {Promise<Object>} 系统信息
     */
    async getSystemInfo() {
        return this.get(`${this.baseUrl}/info`);
    }

    // ==================== 异常相关接口 ====================

    /**
     * 获取异常列表
     * @param {Object} params 查询参数
     * @returns {Promise<Object>} 异常列表
     */
    async getExceptionList(params = {}) {
        return this.post('/api/exceptions/list', params);
    }

    /**
     * 获取异常详情
     * @param {string} exceptionId 异常ID
     * @returns {Promise<Object>} 异常详情
     */
    async getExceptionDetail(exceptionId) {
        return this.get(`/api/exceptions/${exceptionId}`);
    }

    /**
     * 标记异常为已处理
     * @param {string} exceptionId 异常ID
     * @returns {Promise<Object>} 操作结果
     */
    async markExceptionHandled(exceptionId) {
        return this.post(`/api/exceptions/${exceptionId}/handle`);
    }

    // ==================== 系统配置相关接口 ====================

    /**
     * 获取系统配置
     * @returns {Promise<Object>} 系统配置
     */
    async getSystemConfig() {
        return this.get('/api/system/config');
    }

    /**
     * 保存系统配置
     * @param {Object} config 配置数据
     * @returns {Promise<Object>} 保存结果
     */
    async saveSystemConfig(config) {
        return this.post('/api/system/config', config);
    }

    /**
     * 重置系统配置
     * @returns {Promise<Object>} 重置结果
     */
    async resetSystemConfig() {
        return this.post('/api/system/config/reset');
    }

    /**
     * 导出系统配置
     * @returns {Promise<Object>} 配置数据
     */
    async exportSystemConfig() {
        return this.get('/api/system/config/export');
    }

    /**
     * 导入系统配置
     * @param {Object} config 配置数据
     * @returns {Promise<Object>} 导入结果
     */
    async importSystemConfig(config) {
        return this.post('/api/system/config/import', config);
    }

    /**
     * 清理系统缓存
     * @returns {Promise<Object>} 清理结果
     */
    async clearSystemCache() {
        return this.post('/api/system/cache/clear');
    }
}

// 创建全局API服务实例
window.apiService = new ApiService();

// 导出API服务类
window.ApiService = ApiService;
