/**
 * 事务列表页面类
 * 管理事务列表页面的数据加载和交互
 */
class TransactionsPage {
    constructor() {
        this.currentPage = 1;
        this.pageSize = 20;
        this.totalCount = 0;
        this.filters = {};
        this.sortField = 'startTime';
        this.sortOrder = 'desc';
    }

    /**
     * 初始化页面
     */
    async init() {
        try {
            console.log('初始化事务列表页面...');

            // 初始化事件监听器
            this.initEventListeners();

            // 加载初始数据
            await this.loadData();

            console.log('事务列表页面初始化完成');
        } catch (error) {
            console.error('事务列表页面初始化失败:', error);
        }
    }

    /**
     * 销毁页面
     */
    async destroy() {
        try {
            // 清理事件监听器
            this.removeEventListeners();

            console.log('事务列表页面已销毁');
        } catch (error) {
            console.error('事务列表页面销毁失败:', error);
        }
    }

    /**
     * 刷新数据
     */
    async refreshData() {
        try {
            await this.loadData();
            console.log('事务列表数据刷新完成');
        } catch (error) {
            console.error('事务列表数据刷新失败:', error);
        }
    }

    /**
     * 初始化事件监听器
     */
    initEventListeners() {
        // 搜索按钮
        const searchBtn = document.getElementById('searchTransactions');
        if (searchBtn) {
            searchBtn.addEventListener('click', () => {
                this.handleSearch();
            });
        }

        // 重置过滤器按钮
        const resetBtn = document.getElementById('resetFilters');
        if (resetBtn) {
            resetBtn.addEventListener('click', () => {
                this.resetFilters();
            });
        }

        // 导出按钮
        const exportBtn = document.getElementById('exportTransactions');
        if (exportBtn) {
            exportBtn.addEventListener('click', () => {
                this.exportTransactions();
            });
        }

        // 页面大小选择
        const pageSizeSelect = document.getElementById('pageSizeSelect');
        if (pageSizeSelect) {
            pageSizeSelect.addEventListener('change', (e) => {
                this.pageSize = parseInt(e.target.value);
                this.currentPage = 1;
                this.loadData();
            });
        }

        // 表格操作按钮
        this.initTableEventListeners();

        // 模态框事件
        this.initModalEventListeners();
    }

    /**
     * 初始化表格事件监听器
     */
    initTableEventListeners() {
        // 使用事件委托处理表格内的按钮点击
        const tableBody = document.getElementById('transactionTableBody');
        if (tableBody) {
            tableBody.addEventListener('click', (e) => {
                // 处理操作菜单项点击
                const actionTarget = e.target.closest('[data-action]');
                if (actionTarget) {
                    e.preventDefault();
                    e.stopPropagation();
                    const action = actionTarget.getAttribute('data-action');
                    const transactionId = actionTarget.getAttribute('data-transaction-id');
                    if (transactionId) {
                        this.handleTableAction(action, transactionId);
                    }
                    return;
                }

                // 处理详情按钮点击
                const detailBtn = e.target.closest('.view-transaction-detail');
                if (detailBtn) {
                    e.preventDefault();
                    e.stopPropagation();
                    const transactionId = detailBtn.getAttribute('data-transaction-id');
                    if (transactionId) {
                        this.showTransactionDetail(transactionId);
                    }
                    return;
                }
            });
        }
    }

    /**
     * 初始化模态框事件监听器
     */
    initModalEventListeners() {
        // 事务详情模态框
        const detailModal = document.getElementById('transactionDetailModal');
        if (detailModal) {
            detailModal.addEventListener('show.bs.modal', (e) => {
                const transactionId = e.relatedTarget?.getAttribute('data-transaction-id');
                if (transactionId) {
                    this.loadTransactionDetail(transactionId);
                }
            });
        }
    }

    /**
     * 移除事件监听器
     */
    removeEventListeners() {
        // 由于使用了addEventListener，页面销毁时会自动清理
    }

    /**
     * 加载数据
     */
    async loadData() {
        try {
            // 显示加载状态
            this.showLoading();

            // 模拟API调用
            const data = await this.fetchTransactions();

            // 更新表格
            this.updateTable(data.transactions);

            // 更新分页信息
            this.updatePagination(data.totalCount);

            // 更新统计信息
            this.updateStatistics(data.totalCount);

        } catch (error) {
            console.error('数据加载失败:', error);
            this.showError('数据加载失败: ' + error.message);
        }
    }

    /**
     * 获取事务数据
     */
    async fetchTransactions() {
        try {
            console.log('获取事务列表...');

            // 检查API服务是否可用
            if (!window.apiService) {
                throw new Error('API服务未初始化');
            }

            // 构建查询参数
            const queryParams = this.buildQueryParams();
            console.log('查询参数:', queryParams);

            // 调用后端API
            const response = await window.apiService.getTransactionList(queryParams);

            console.log('后端响应:', response);

            // 适配ApiResponse<TransactionListResponse>格式
            if (response.success !== false) {
                const data = response.data || response;

                // 转换数据格式 - 适配TransactionListResponse结构
                const transactionList = data.list || data.transactions || data.records || [];
                const transactions = transactionList.map(item => this.transformTransactionData(item));

                // 更新分页信息 - 适配PageInfo结构
                const pageInfo = data.pageInfo || data;
                this.updatePaginationInfo(pageInfo);

                console.log(`获取到 ${transactions.length} 条事务记录`);
                return {
                    transactions: transactions,
                    totalCount: pageInfo.total || transactions.length,
                    currentPage: pageInfo.pageNum || this.currentPage,
                    pageSize: pageInfo.pageSize || this.pageSize
                };
            } else {
                throw new Error(response.message || '获取事务列表失败');
            }
        } catch (error) {
            console.error('获取事务数据失败:', error);

            // 如果API调用失败，返回空数据而不是模拟数据
            console.warn('API调用失败，返回空数据列表');
            this.showError('无法获取事务数据，请检查后端服务是否正常运行');
            return {
                transactions: [],
                totalCount: 0,
                currentPage: this.currentPage,
                pageSize: this.pageSize
            };
        }
    }

    /**
     * 构建查询参数 - 适配TransactionQueryRequest格式
     */
    buildQueryParams() {
        const params = {
            // 分页参数
            pageNum: this.currentPage,
            pageSize: this.pageSize,

            // 查询条件 - 匹配TransactionQueryRequest字段
            transactionId: this.getFilterValue('filterTransactionId'),
            mode: this.getFilterValue('filterMode'),
            status: this.getFilterValue('filterStatus'),
            serviceName: this.getFilterValue('filterService'),

            // 时间范围
            startTime: this.getFilterValue('filterStartTime'),
            endTime: this.getFilterValue('filterEndTime'),

            // 排序参数 - 匹配TransactionQueryRequest字段
            orderBy: this.sortField || 'startTime',
            orderDirection: this.sortOrder || 'DESC'
        };

        // 移除空值参数
        Object.keys(params).forEach(key => {
            if (params[key] === '' || params[key] === null || params[key] === undefined) {
                delete params[key];
            }
        });

        return params;
    }

    /**
     * 转换事务数据格式 - 适配TransactionInfo结构
     */
    transformTransactionData(item) {
        return {
            id: item.transactionId || item.id,
            mode: item.mode || item.transactionMode || 'AT',
            modeClass: this.getModeClass(item.mode || item.transactionMode),
            status: item.status,
            statusClass: this.getStatusClass(item.status),
            statusText: this.getStatusText(item.status),
            service: item.serviceName || '-',
            startTime: this.formatDateTime(item.startTime),
            duration: this.formatDuration(item.duration),
            stepCount: item.stepCount || 0,
            businessKey: item.businessKey,
            groupId: item.groupId,
            endTime: item.endTime,
            retryCount: item.retryCount || 0,
            currentRetryCount: item.currentRetryCount || 0,
            timeoutSeconds: item.timeoutSeconds,
            errorMessage: item.errorMessage,
            // 新增字段
            createTime: item.createTime,
            updateTime: item.updateTime
        };
    }

    /**
     * 获取模式样式类
     */
    getModeClass(mode) {
        const modeMap = {
            'AT': 'blue',
            'SAGA': 'purple',
            'TCC': 'green'
        };
        return modeMap[mode] || 'secondary';
    }

    /**
     * 获取状态样式类
     */
    getStatusClass(status) {
        const statusMap = {
            // 基本状态
            'INIT': 'secondary',
            'PENDING': 'info',
            'EXECUTING': 'warning',
            'PREPARING': 'info',
            'WAITING': 'warning',
            'PAUSED': 'warning',

            // 成功状态
            'SUCCESS': 'success',
            'COMMITTED': 'success',
            'UNDONE': 'success',
            'COMPENSATED': 'success',
            'ROLLBACKED': 'success',

            // 失败状态
            'FAILED': 'danger',
            'UNDO_FAILED': 'danger',
            'COMPENSATE_FAILED': 'danger',
            'ROLLBACK_FAILED': 'danger',
            'MANUAL_INTERVENTION_REQUIRED': 'danger',

            // 进行中状态
            'UNDOING': 'warning',
            'COMPENSATING': 'warning',
            'ROLLBACK': 'warning',

            // 其他状态
            'TIMEOUT': 'orange',
            'CANCELLED': 'secondary',
            'PARTIAL_FAILED': 'warning'
        };
        return statusMap[status] || 'secondary';
    }

    /**
     * 获取状态文本
     */
    getStatusText(status) {
        const statusMap = {
            // 基本状态
            'INIT': '初始化',
            'PENDING': '待执行',
            'EXECUTING': '执行中',
            'PREPARING': '准备提交',
            'WAITING': '等待中',
            'PAUSED': '暂停',

            // 成功状态
            'SUCCESS': '成功',
            'COMMITTED': '已提交',
            'UNDONE': '撤销成功',
            'COMPENSATED': '已补偿',
            'ROLLBACKED': '回滚完成',

            // 失败状态
            'FAILED': '失败',
            'UNDO_FAILED': '撤销失败',
            'COMPENSATE_FAILED': '补偿失败',
            'ROLLBACK_FAILED': '回滚失败',
            'MANUAL_INTERVENTION_REQUIRED': '需要人工干预',

            // 进行中状态
            'UNDOING': '撤销中',
            'COMPENSATING': '补偿中',
            'ROLLBACK': '已回滚',

            // 其他状态
            'TIMEOUT': '超时',
            'CANCELLED': '已取消',
            'PARTIAL_FAILED': '部分失败'
        };
        return statusMap[status] || `未知状态(${status})`;
    }

    /**
     * 获取过滤器值
     */
    getFilterValue(filterId) {
        const element = document.getElementById(filterId);
        return element ? element.value.trim() : '';
    }

    /**
     * 更新分页信息
     */
    updatePaginationInfo(data) {
        if (data.total !== undefined) {
            this.totalRecords = data.total;
        }
        if (data.pages !== undefined) {
            this.totalPages = data.pages;
        }
        if (data.current !== undefined) {
            this.currentPage = data.current;
        }
    }

    /**
     * 格式化日期时间
     */
    formatDateTime(dateTime) {
        if (!dateTime) return '-';
        try {
            const date = new Date(dateTime);
            return date.toLocaleString('zh-CN');
        } catch (error) {
            return dateTime;
        }
    }

    /**
     * 格式化持续时间
     */
    formatDuration(duration) {
        if (!duration) return '-';
        if (duration < 1000) {
            return `${duration}ms`;
        } else if (duration < 60000) {
            return `${(duration / 1000).toFixed(1)}s`;
        } else {
            const minutes = Math.floor(duration / 60000);
            const seconds = Math.floor((duration % 60000) / 1000);
            return `${minutes}m ${seconds}s`;
        }
    }

    /**
     * 生成模拟事务数据
     */
    generateMockTransactions() {
        const transactions = [];
        const statuses = ['SUCCESS', 'FAILED', 'EXECUTING', 'TIMEOUT'];
        const statusTexts = ['成功', '失败', '执行中', '超时'];
        const statusClasses = ['success', 'danger', 'warning', 'orange'];
        const modes = ['AT', 'SAGA', 'TCC'];
        const modeClasses = ['blue', 'purple', 'green'];
        const services = ['订单服务', '支付服务', '库存服务', '用户服务', '通知服务'];

        for (let i = 1; i <= 20; i++) {
            const statusIndex = Math.floor(Math.random() * statuses.length);
            const modeIndex = Math.floor(Math.random() * modes.length);
            const serviceIndex = Math.floor(Math.random() * services.length);

            transactions.push({
                id: `TXN-20240701-${String(i).padStart(3, '0')}`,
                mode: modes[modeIndex],
                modeClass: modeClasses[modeIndex],
                status: statuses[statusIndex],
                statusText: statusTexts[statusIndex],
                statusClass: statusClasses[statusIndex],
                service: services[serviceIndex],
                startTime: new Date(Date.now() - Math.random() * 24 * 60 * 60 * 1000).toLocaleString('zh-CN'),
                duration: (Math.random() * 10).toFixed(1) + 's',
                stepCount: Math.floor(Math.random() * 8) + 1
            });
        }

        return transactions;
    }

    /**
     * 更新表格
     */
    updateTable(transactions) {
        const tableBody = document.getElementById('transactionTableBody');
        if (!tableBody) return;

        const html = transactions.map(transaction => `
            <tr>
                <td>
                    <input class="form-check-input" type="checkbox" value="${transaction.id}">
                </td>
                <td>
                    <div class="d-flex align-items-center">
                        <span class="avatar avatar-sm me-3 bg-${transaction.modeClass} text-white">${transaction.mode}</span>
                        <div>
                            <div class="fw-medium text-dark">${transaction.id}</div>
                            <div class="text-muted small">全局事务</div>
                        </div>
                    </div>
                </td>
                <td>
                    <span class="badge bg-${transaction.modeClass}">${transaction.mode}</span>
                </td>
                <td>
                    <span class="badge bg-${transaction.statusClass}">${transaction.statusText}</span>
                </td>
                <td class="text-muted">
                    ${transaction.service}
                </td>
                <td class="text-muted">
                    ${transaction.startTime}
                </td>
                <td class="text-muted">
                    ${transaction.duration}
                </td>
                <td class="text-center">
                    <span class="badge bg-secondary-lt">${transaction.stepCount}</span>
                </td>
                <td>
                    <div class="btn-group" role="group">
                        <button type="button" class="btn btn-sm btn-outline-primary view-transaction-detail" data-transaction-id="${transaction.id}">
                            详情
                        </button>
                        <div class="dropdown">
                            <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown" aria-expanded="false" style="z-index: 1050;">
                                操作
                            </button>
                            <ul class="dropdown-menu dropdown-menu-end" style="z-index: 1051;">
                                ${this.generateActionMenu(transaction)}
                            </ul>
                        </div>
                    </div>
                </td>
            </tr>
        `).join('');

        tableBody.innerHTML = html;

        // 绑定事务详情查看事件
        this.bindTransactionDetailEvents();
    }

    /**
     * 绑定事务详情查看事件（使用事件委托避免重复绑定）
     */
    bindTransactionDetailEvents() {
        const tableBody = document.getElementById('transactionTableBody');
        if (!tableBody) return;

        // 移除之前的事件监听器
        if (tableBody._detailEventHandler) {
            tableBody.removeEventListener('click', tableBody._detailEventHandler);
        }

        // 使用事件委托
        const handler = (e) => {
            const detailBtn = e.target.closest('.view-transaction-detail');
            if (detailBtn) {
                e.preventDefault();
                const transactionId = detailBtn.getAttribute('data-transaction-id');
                if (transactionId && window.transactionDetailManager) {
                    window.transactionDetailManager.showTransactionDetail(transactionId);
                } else {
                    console.error('事务详情管理器未找到或事务ID为空');
                }
            }
        };

        tableBody.addEventListener('click', handler);
        tableBody._detailEventHandler = handler;
    }

    /**
     * 生成操作菜单
     */
    generateActionMenu(transaction) {
        let menu = '';

        if (transaction.status === 'FAILED' || transaction.status === 'TIMEOUT' || transaction.status === 'UNDO_FAILED') {
            menu += `
                <li><a class="dropdown-item" href="#" data-action="retry" data-transaction-id="${transaction.id}">
                    <svg xmlns="http://www.w3.org/2000/svg" class="icon dropdown-item-icon me-2" width="16" height="16" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                        <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
                        <path d="M20 11a8.1 8.1 0 0 0 -15.5 -2m-.5 -4v4h4"/>
                        <path d="M4 13a8.1 8.1 0 0 0 15.5 2m.5 4v-4h-4"/>
                    </svg>
                    重试事务
                </a></li>
            `;
        }

        if (transaction.status === 'EXECUTING' || transaction.status === 'PENDING' || transaction.status === 'WAITING') {
            menu += `
                <li><a class="dropdown-item" href="#" data-action="cancel" data-transaction-id="${transaction.id}">
                    <svg xmlns="http://www.w3.org/2000/svg" class="icon dropdown-item-icon me-2" width="16" height="16" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                        <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
                        <circle cx="12" cy="12" r="9"/>
                        <path d="M9 9l6 6"/>
                        <path d="M15 9l-6 6"/>
                    </svg>
                    取消事务
                </a></li>
            `;
        }

        if (transaction.status === 'SUCCESS' || transaction.status === 'COMMITTED') {
            menu += `
                <li><a class="dropdown-item" href="#" data-action="rollback" data-transaction-id="${transaction.id}">
                    <svg xmlns="http://www.w3.org/2000/svg" class="icon dropdown-item-icon me-2" width="16" height="16" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                        <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
                        <path d="M9 14l-4 -4l4 -4"/>
                        <path d="M5 10h11a4 4 0 1 1 0 8h-1"/>
                    </svg>
                    手动回滚
                </a></li>
            `;
        }

        // 分隔线
        if (menu) {
            menu += `<li><hr class="dropdown-divider"></li>`;
        }

        // 查看日志
        menu += `
            <li><a class="dropdown-item" href="#" data-action="logs" data-transaction-id="${transaction.id}">
                <svg xmlns="http://www.w3.org/2000/svg" class="icon dropdown-item-icon me-2" width="16" height="16" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                    <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
                    <path d="M9 7h-3a2 2 0 0 0 -2 2v9a2 2 0 0 0 2 2h9a2 2 0 0 0 2 -2v-3"/>
                    <path d="M9 15h3l8.5 -8.5a1.5 1.5 0 0 0 -3 -3l-8.5 8.5v3"/>
                    <line x1="16" y1="5" x2="19" y2="8"/>
                </svg>
                查看日志
            </a></li>
        `;

        // 删除操作（仅对失败或已完成的事务）
        if (transaction.status !== 'EXECUTING' && transaction.status !== 'PENDING' && transaction.status !== 'WAITING') {
            menu += `
                <li><a class="dropdown-item text-danger" href="#" data-action="delete" data-transaction-id="${transaction.id}">
                    <svg xmlns="http://www.w3.org/2000/svg" class="icon dropdown-item-icon me-2" width="16" height="16" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                        <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
                        <line x1="4" y1="7" x2="20" y2="7"/>
                        <line x1="10" y1="11" x2="10" y2="17"/>
                        <line x1="14" y1="11" x2="14" y2="17"/>
                        <path d="M5 7l1 12a2 2 0 0 0 2 2h8a2 2 0 0 0 2 -2l1 -12"/>
                        <path d="M9 7v-3a1 1 0 0 1 1 -1h4a1 1 0 0 1 1 1v3"/>
                    </svg>
                    删除记录
                </a></li>
            `;
        }

        return menu;
    }

    /**
     * 更新分页信息
     */
    updatePagination(totalCount) {
        this.totalCount = totalCount;

        // 更新分页信息文本
        const paginationInfo = document.getElementById('paginationInfo');
        if (paginationInfo) {
            const start = (this.currentPage - 1) * this.pageSize + 1;
            const end = Math.min(this.currentPage * this.pageSize, totalCount);
            paginationInfo.textContent = `显示第 ${start}-${end} 条，共 ${totalCount} 条记录`;
        }

        // 更新分页按钮
        this.updatePaginationButtons();
    }

    /**
     * 更新分页按钮
     */
    updatePaginationButtons() {
        const pagination = document.getElementById('transactionPagination');
        if (!pagination) return;

        const totalPages = Math.ceil(this.totalCount / this.pageSize);
        let html = '';

        // 上一页按钮
        html += `
            <li class="page-item ${this.currentPage <= 1 ? 'disabled' : ''}">
                <a class="page-link" href="#" data-page="${this.currentPage - 1}" tabindex="-1">
                    <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                        <polyline points="15,18 9,12 15,6"/>
                    </svg>
                    上一页
                </a>
            </li>
        `;

        // 页码按钮
        for (let i = 1; i <= totalPages; i++) {
            if (i === this.currentPage) {
                html += `<li class="page-item active"><a class="page-link" href="#">${i}</a></li>`;
            } else if (Math.abs(i - this.currentPage) <= 2 || i === 1 || i === totalPages) {
                html += `<li class="page-item"><a class="page-link" href="#" data-page="${i}">${i}</a></li>`;
            } else if (Math.abs(i - this.currentPage) === 3) {
                html += `<li class="page-item disabled"><a class="page-link" href="#">...</a></li>`;
            }
        }

        // 下一页按钮
        html += `
            <li class="page-item ${this.currentPage >= totalPages ? 'disabled' : ''}">
                <a class="page-link" href="#" data-page="${this.currentPage + 1}">
                    下一页
                    <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                        <polyline points="9,18 15,12 9,6"/>
                    </svg>
                </a>
            </li>
        `;

        pagination.innerHTML = html;

        // 添加分页点击事件
        pagination.addEventListener('click', (e) => {
            e.preventDefault();
            const target = e.target.closest('[data-page]');
            if (target) {
                const page = parseInt(target.getAttribute('data-page'));
                if (page > 0 && page <= totalPages && page !== this.currentPage) {
                    this.currentPage = page;
                    this.loadData();
                }
            }
        });
    }

    /**
     * 更新统计信息
     */
    updateStatistics(totalCount) {
        const countElement = document.getElementById('transactionCount');
        if (countElement) {
            countElement.textContent = `共 ${totalCount} 条记录`;
        }
    }

    /**
     * 处理搜索
     */
    handleSearch() {
        // 收集过滤条件
        this.filters = {
            transactionId: document.getElementById('filterTransactionId')?.value || '',
            mode: document.getElementById('filterMode')?.value || '',
            status: document.getElementById('filterStatus')?.value || '',
            service: document.getElementById('filterService')?.value || '',
            startTime: document.getElementById('filterStartTime')?.value || '',
            endTime: document.getElementById('filterEndTime')?.value || ''
        };

        // 重置到第一页
        this.currentPage = 1;

        // 重新加载数据
        this.loadData();
    }

    /**
     * 重置过滤器
     */
    resetFilters() {
        // 清空所有过滤器输入
        const filterInputs = [
            'filterTransactionId',
            'filterMode',
            'filterStatus',
            'filterService',
            'filterStartTime',
            'filterEndTime'
        ];

        filterInputs.forEach(id => {
            const element = document.getElementById(id);
            if (element) {
                element.value = '';
            }
        });

        // 清空过滤条件
        this.filters = {};
        this.currentPage = 1;

        // 重新加载数据
        this.loadData();
    }

    /**
     * 导出事务数据
     */
    exportTransactions() {
        // 模拟导出功能
        console.log('导出事务数据...');

        // 这里可以实现实际的导出逻辑
        // 例如调用后端API生成Excel文件

        // 显示成功消息
        this.showSuccessMessage('导出请求已提交，请稍后下载');
    }

    /**
     * 处理表格操作
     */
    handleTableAction(action, transactionId) {
        console.log(`执行操作: ${action}, 事务ID: ${transactionId}`);

        switch (action) {
            case 'retry':
                this.retryTransaction(transactionId);
                break;
            case 'cancel':
                this.cancelTransaction(transactionId);
                break;
            case 'rollback':
                this.rollbackTransaction(transactionId);
                break;
            case 'logs':
                this.viewTransactionLogs(transactionId);
                break;
            case 'delete':
                this.deleteTransaction(transactionId);
                break;
            default:
                console.warn('未知操作:', action);
        }
    }

    /**
     * 重试事务
     */
    async retryTransaction(transactionId) {
        try {
            // 使用美观的输入对话框
            const reasonResult = await ConfirmDialog.prompt(
                `请输入重试事务的原因：`,
                '重试事务',
                '例如：网络异常恢复，重新执行...'
            );

            if (!reasonResult.confirmed) {
                return; // 用户取消
            }

            const reason = reasonResult.inputValue || '手动重试';

            // 使用美观的确认对话框
            const confirmResult = await ConfirmDialog.show({
                title: '确认重试',
                message: `确定要重试事务 <code>${transactionId}</code> 吗？<br><small class="text-muted">原因：${reason}</small>`,
                type: 'warning',
                confirmText: '确定重试',
                cancelText: '取消'
            });

            if (!confirmResult.confirmed) {
                return;
            }

            console.log('重试事务:', transactionId);

            const operatorId = localStorage.getItem('currentUserId') || 'admin';
            const result = await window.apiService.retryTransaction(transactionId, operatorId, reason);

            if (result.success !== false) {
                this.showToast('success', `事务 ${transactionId} 重试成功`);
                // 刷新数据
                this.loadData();
            } else {
                this.showToast('error', `事务重试失败: ${result.message || '未知错误'}`);
            }

        } catch (error) {
            console.error('重试事务失败:', error);
            this.showToast('error', `重试事务失败: ${error.message}`);
        }
    }

    /**
     * 取消事务
     */
    async cancelTransaction(transactionId) {
        try {
            // 使用美观的输入对话框
            const reasonResult = await ConfirmDialog.prompt(
                `请输入取消事务的原因：`,
                '取消事务',
                '例如：业务需求变更，停止执行...'
            );

            if (!reasonResult.confirmed) {
                return; // 用户取消
            }

            const reason = reasonResult.inputValue || '手动取消';

            // 使用美观的确认对话框
            const confirmResult = await ConfirmDialog.show({
                title: '确认取消',
                message: `确定要取消事务 <code>${transactionId}</code> 吗？<br><small class="text-muted">原因：${reason}</small>`,
                type: 'warning',
                confirmText: '确定取消',
                cancelText: '不取消'
            });

            if (!confirmResult.confirmed) {
                return;
            }

            console.log('取消事务:', transactionId);

            const response = await fetch(`/api/transaction/manual/cancel/${transactionId}?operatorId=admin&reason=${encodeURIComponent(reason)}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                }
            });

            const result = await response.json();

            if (result.success !== false) {
                this.showToast('success', `事务 ${transactionId} 取消成功`);
                // 刷新数据
                this.loadData();
            } else {
                this.showToast('error', `事务取消失败: ${result.message || '未知错误'}`);
            }

        } catch (error) {
            console.error('取消事务失败:', error);
            this.showToast('error', `取消事务失败: ${error.message}`);
        }
    }

    /**
     * 回滚事务
     */
    async rollbackTransaction(transactionId) {
        try {
            // 使用美观的输入对话框
            const reasonResult = await ConfirmDialog.prompt(
                `请输入回滚事务的原因：`,
                '回滚事务',
                '例如：数据异常，需要撤销操作...'
            );

            if (!reasonResult.confirmed) {
                return; // 用户取消
            }

            const reason = reasonResult.inputValue || '手动回滚';

            // 使用危险操作确认对话框
            const confirmResult = await ConfirmDialog.show({
                title: '危险操作确认',
                message: `确定要回滚事务 <code>${transactionId}</code> 吗？<br><small class="text-danger">⚠️ 此操作不可撤销！</small><br><small class="text-muted">原因：${reason}</small>`,
                type: 'danger',
                confirmText: '确定回滚',
                cancelText: '取消'
            });

            if (!confirmResult.confirmed) {
                return;
            }

            console.log('回滚事务:', transactionId);

            const response = await fetch(`/api/transaction/manual/rollback/${transactionId}?operatorId=admin&reason=${encodeURIComponent(reason)}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                }
            });

            const result = await response.json();

            if (result.success !== false) {
                this.showToast('success', `事务 ${transactionId} 回滚成功`);
                // 刷新数据
                this.loadData();
            } else {
                this.showToast('error', `事务回滚失败: ${result.message || '未知错误'}`);
            }

        } catch (error) {
            console.error('回滚事务失败:', error);
            this.showToast('error', `回滚事务失败: ${error.message}`);
        }
    }

    /**
     * 删除事务
     */
    async deleteTransaction(transactionId) {
        try {
            // 使用危险操作确认对话框
            const confirmResult = await ConfirmDialog.show({
                title: '危险操作确认',
                message: `确定要删除事务 <code>${transactionId}</code> 吗？<br><small class="text-danger">⚠️ 此操作不可撤销！</small>`,
                type: 'danger',
                confirmText: '确定删除',
                cancelText: '取消'
            });

            if (!confirmResult.confirmed) {
                return;
            }

            console.log('删除事务:', transactionId);

            // TODO: 实现删除事务的API调用
            // 目前先显示提示信息
            this.showToast('info', `事务 ${transactionId} 删除功能待实现`);

        } catch (error) {
            console.error('删除事务失败:', error);
            this.showToast('error', `删除事务失败: ${error.message}`);
        }
    }

    /**
     * 查看事务日志
     */
    async viewTransactionLogs(transactionId) {
        try {
            console.log('查看事务日志:', transactionId);

            const response = await fetch(`/api/transaction/manual/logs/${transactionId}`);
            const result = await response.json();

            if (result.success !== false) {
                // 显示日志模态框
                this.showLogsModal(transactionId, result.data || []);
            } else {
                this.showToast('error', `获取事务日志失败: ${result.message || '未知错误'}`);
            }

        } catch (error) {
            console.error('获取事务日志失败:', error);
            this.showToast('error', `获取事务日志失败: ${error.message}`);
        }
    }

    /**
     * 显示事务详情
     */
    showTransactionDetail(transactionId) {
        console.log('显示事务详情:', transactionId);
        this.loadTransactionDetail(transactionId);
    }

    /**
     * 加载事务详情
     */
    async loadTransactionDetail(transactionId) {
        const content = document.getElementById('transactionDetailContent');
        if (!content) return;

        // 显示加载状态
        content.innerHTML = `
            <div class="text-center py-4">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">加载中...</span>
                </div>
                <div class="mt-2">正在加载事务详情...</div>
            </div>
        `;

        try {
            // 调用真实的API获取事务详情
            const response = await fetch(`/api/transaction/detail/${transactionId}`);
            const result = await response.json();

            if (result.success === false) {
                throw new Error(result.message || '获取事务详情失败');
            }

            const transaction = result.data || result;

            // 显示详情内容
            content.innerHTML = this.generateTransactionDetailHtml(transaction);

        } catch (error) {
            console.error('加载事务详情失败:', error);
            content.innerHTML = `
                <div class="text-center py-4">
                    <div class="text-danger mb-3">
                        <svg xmlns="http://www.w3.org/2000/svg" class="icon icon-lg" width="48" height="48" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                            <circle cx="12" cy="12" r="9"/>
                            <path d="M9 9l6 6"/>
                            <path d="M15 9l-6 6"/>
                        </svg>
                    </div>
                    <h4 class="text-muted">加载失败</h4>
                    <p class="text-muted">${error.message}</p>
                    <button class="btn btn-outline-primary" onclick="window.currentPage.loadTransactionDetail('${transactionId}')">
                        重新加载
                    </button>
                </div>
            `;
        }
    }

    /**
     * 生成事务详情HTML
     */
    generateTransactionDetailHtml(transaction) {
        const statusClass = this.getStatusClass(transaction.status);
        const statusText = this.getStatusText(transaction.status);

        return `
            <div class="row">
                <div class="col-md-6">
                    <h4>基本信息</h4>
                    <dl class="row">
                        <dt class="col-4">事务ID:</dt>
                        <dd class="col-8">
                            <code>${transaction.transactionId || transaction.id}</code>
                            <button class="btn btn-sm btn-outline-secondary ms-2" onclick="navigator.clipboard.writeText('${transaction.transactionId || transaction.id}')">
                                <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="16" height="16" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                    <rect x="9" y="9" width="13" height="13" rx="2" ry="2"/>
                                    <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"/>
                                </svg>
                            </button>
                        </dd>
                        <dt class="col-4">事务名称:</dt>
                        <dd class="col-8">${transaction.transactionName || transaction.groupId || '-'}</dd>
                        <dt class="col-4">事务模式:</dt>
                        <dd class="col-8"><span class="badge bg-info">${transaction.mode || 'AT'}</span></dd>
                        <dt class="col-4">状态:</dt>
                        <dd class="col-8"><span class="badge bg-${statusClass}">${statusText}</span></dd>
                        <dt class="col-4">业务键:</dt>
                        <dd class="col-8">${transaction.businessKey || '-'}</dd>
                        <dt class="col-4">开始时间:</dt>
                        <dd class="col-8">${this.formatDateTime(transaction.startTime)}</dd>
                        <dt class="col-4">结束时间:</dt>
                        <dd class="col-8">${this.formatDateTime(transaction.endTime)}</dd>
                        <dt class="col-4">持续时间:</dt>
                        <dd class="col-8">${this.formatDuration(transaction.duration)}</dd>
                        <dt class="col-4">重试次数:</dt>
                        <dd class="col-8">${transaction.currentRetryCount || 0}/${transaction.retryCount || 0}</dd>
                    </dl>

                    ${transaction.errorMessage ? `
                        <h4 class="mt-4">错误信息</h4>
                        <div class="alert alert-danger">
                            <pre class="mb-0">${transaction.errorMessage}</pre>
                        </div>
                    ` : ''}
                </div>
                <div class="col-md-6">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h4 class="mb-0">执行步骤</h4>
                        <span class="badge bg-secondary">${(transaction.steps || []).length} 个步骤</span>
                    </div>
                    <div class="list-group list-group-flush">
                        ${this.generateStepsHtml(transaction.steps || [])}
                    </div>

                    <div class="mt-4">
                        <h4>操作</h4>
                        <div class="btn-group" role="group">
                            ${this.generateDetailOperationButtons(transaction)}
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * 生成步骤HTML
     */
    generateStepsHtml(steps) {
        if (!steps || steps.length === 0) {
            return '<div class="text-center text-muted py-3">暂无执行步骤</div>';
        }

        return steps.map((step, index) => {
            const stepStatusClass = this.getStepStatusClass(step.status);
            const stepStatusText = this.getStepStatusText(step.status);

            return `
                <div class="list-group-item">
                    <div class="row align-items-center">
                        <div class="col-auto">
                            <span class="badge bg-${stepStatusClass}">${index + 1}</span>
                        </div>
                        <div class="col">
                            <div class="text-truncate">
                                <strong>${step.stepName || step.targetMethod || '未知步骤'}</strong>
                            </div>
                            <div class="text-muted small">
                                ${step.targetService || '未知服务'} • ${stepStatusText}
                                ${step.duration ? ` • ${this.formatDuration(step.duration)}` : ''}
                            </div>
                            ${step.errorMessage ? `
                                <div class="text-danger small mt-1">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="icon icon-sm me-1" width="16" height="16" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                        <circle cx="12" cy="12" r="9"/>
                                        <path d="M12 8v4"/>
                                        <path d="M12 16h.01"/>
                                    </svg>
                                    ${step.errorMessage}
                                </div>
                            ` : ''}
                        </div>
                    </div>
                </div>
            `;
        }).join('');
    }

    /**
     * 生成详情页操作按钮
     */
    generateDetailOperationButtons(transaction) {
        const buttons = [];
        const status = transaction.status;

        // 重试按钮
        if (['FAILED', 'UNDO_FAILED', 'TIMEOUT', 'CANCELLED'].includes(status)) {
            buttons.push(`
                <button class="btn btn-outline-warning btn-sm" onclick="window.currentPage.retryTransaction('${transaction.transactionId || transaction.id}')">
                    <svg xmlns="http://www.w3.org/2000/svg" class="icon me-1" width="16" height="16" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                        <path d="M20 11a8.1 8.1 0 0 0 -15.5 -2m-.5 -4v4h4"/>
                        <path d="M4 13a8.1 8.1 0 0 0 15.5 2m.5 4v-4h-4"/>
                    </svg>
                    重试
                </button>
            `);
        }

        // 回滚按钮
        if (['SUCCESS', 'COMMITTED'].includes(status)) {
            buttons.push(`
                <button class="btn btn-outline-danger btn-sm" onclick="window.currentPage.rollbackTransaction('${transaction.transactionId || transaction.id}')">
                    <svg xmlns="http://www.w3.org/2000/svg" class="icon me-1" width="16" height="16" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                        <path d="M9 14l-4 -4l4 -4"/>
                        <path d="M5 10h11a4 4 0 1 1 0 8h-1"/>
                    </svg>
                    回滚
                </button>
            `);
        }

        // 取消按钮
        if (['EXECUTING', 'PENDING', 'WAITING', 'PAUSED'].includes(status)) {
            buttons.push(`
                <button class="btn btn-outline-secondary btn-sm" onclick="window.currentPage.cancelTransaction('${transaction.transactionId || transaction.id}')">
                    <svg xmlns="http://www.w3.org/2000/svg" class="icon me-1" width="16" height="16" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                        <circle cx="12" cy="12" r="9"/>
                        <path d="M9 9l6 6"/>
                        <path d="M15 9l-6 6"/>
                    </svg>
                    取消
                </button>
            `);
        }

        // 查看日志按钮
        buttons.push(`
            <button class="btn btn-outline-info btn-sm" onclick="window.currentPage.viewTransactionLogs('${transaction.transactionId || transaction.id}')">
                <svg xmlns="http://www.w3.org/2000/svg" class="icon me-1" width="16" height="16" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                    <path d="M9 7h-3a2 2 0 0 0 -2 2v9a2 2 0 0 0 2 2h9a2 2 0 0 0 2 -2v-3"/>
                    <path d="M9 15h3l8.5 -8.5a1.5 1.5 0 0 0 -3 -3l-8.5 8.5v3"/>
                    <line x1="16" y1="5" x2="19" y2="8"/>
                </svg>
                查看日志
            </button>
        `);

        // 刷新按钮
        buttons.push(`
            <button class="btn btn-outline-primary btn-sm" onclick="window.currentPage.loadTransactionDetail('${transaction.transactionId || transaction.id}')">
                <svg xmlns="http://www.w3.org/2000/svg" class="icon me-1" width="16" height="16" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                    <path d="M20 11a8.1 8.1 0 0 0 -15.5 -2m-.5 -4v4h4"/>
                    <path d="M4 13a8.1 8.1 0 0 0 15.5 2m.5 4v-4h-4"/>
                </svg>
                刷新
            </button>
        `);

        return buttons.join('');
    }

    /**
     * 获取步骤状态样式类
     */
    getStepStatusClass(status) {
        const statusMap = {
            'PENDING': 'secondary',
            'EXECUTING': 'warning',
            'SUCCESS': 'success',
            'FAILED': 'danger',
            'SKIPPED': 'info',
            'COMPENSATING': 'warning',
            'COMPENSATED': 'success',
            'COMPENSATION_FAILED': 'danger'
        };
        return statusMap[status] || 'secondary';
    }

    /**
     * 获取步骤状态文本
     */
    getStepStatusText(status) {
        const statusMap = {
            'PENDING': '待执行',
            'EXECUTING': '执行中',
            'SUCCESS': '成功',
            'FAILED': '失败',
            'SKIPPED': '跳过',
            'COMPENSATING': '补偿中',
            'COMPENSATED': '已补偿',
            'COMPENSATION_FAILED': '补偿失败'
        };
        return statusMap[status] || status;
    }

    /**
     * 从表格行获取事务ID
     */
    getTransactionIdFromRow(element) {
        const row = element.closest('tr');
        if (row) {
            const idCell = row.querySelector('td:first-child .font-weight-medium');
            return idCell ? idCell.textContent.trim() : null;
        }
        return null;
    }

    /**
     * 显示加载状态
     */
    showLoading() {
        const tableBody = document.getElementById('transactionTableBody');
        if (tableBody) {
            tableBody.innerHTML = `
                <tr>
                    <td colspan="8" class="text-center py-4">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">加载中...</span>
                        </div>
                        <div class="mt-2">正在加载事务数据...</div>
                    </td>
                </tr>
            `;
        }
    }

    /**
     * 显示错误信息
     */
    showError(message) {
        const tableBody = document.getElementById('transactionTableBody');
        if (tableBody) {
            tableBody.innerHTML = `
                <tr>
                    <td colspan="8" class="text-center py-4">
                        <div class="alert alert-danger" role="alert">
                            <h4 class="alert-title">加载失败</h4>
                            <div class="text-muted">${message}</div>
                        </div>
                    </td>
                </tr>
            `;
        }
    }

    /**
     * 显示Toast提示
     */
    showToast(type, message, options = {}) {
        if (window.Toast) {
            return window.Toast.show(type, message, options);
        } else {
            // 降级到console输出
            console.log(`[${type.toUpperCase()}] ${message}`);
        }
    }

    /**
     * 显示成功消息
     */
    showSuccessMessage(message) {
        this.showToast('success', message);
    }

    /**
     * 显示错误消息
     */
    showErrorMessage(message) {
        this.showToast('error', message);
    }

    /**
     * 显示日志模态框
     */
    showLogsModal(transactionId, logs) {
        // 创建日志模态框HTML
        const modalHtml = `
            <div class="modal fade" id="transactionLogsModal" tabindex="-1">
                <div class="modal-dialog modal-xl">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title d-flex align-items-center">
                                <svg xmlns="http://www.w3.org/2000/svg" class="icon me-2 text-info" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                    <path d="M9 7h-3a2 2 0 0 0 -2 2v9a2 2 0 0 0 2 2h9a2 2 0 0 0 2 -2v-3"/>
                                    <path d="M9 15h3l8.5 -8.5a1.5 1.5 0 0 0 -3 -3l-8.5 8.5v3"/>
                                    <line x1="16" y1="5" x2="19" y2="8"/>
                                </svg>
                                事务日志 - ${transactionId}
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            ${logs.length > 0 ? `
                                <div class="mb-3 d-flex justify-content-between align-items-center">
                                    <span class="text-muted">共 ${logs.length} 条日志记录</span>
                                    <div class="btn-group btn-group-sm" role="group">
                                        <input type="radio" class="btn-check" name="logLevel" id="logAll" autocomplete="off" checked>
                                        <label class="btn btn-outline-secondary" for="logAll">全部</label>

                                        <input type="radio" class="btn-check" name="logLevel" id="logError" autocomplete="off">
                                        <label class="btn btn-outline-danger" for="logError">错误</label>

                                        <input type="radio" class="btn-check" name="logLevel" id="logWarn" autocomplete="off">
                                        <label class="btn btn-outline-warning" for="logWarn">警告</label>

                                        <input type="radio" class="btn-check" name="logLevel" id="logInfo" autocomplete="off">
                                        <label class="btn btn-outline-info" for="logInfo">信息</label>
                                    </div>
                                </div>
                                <div class="table-responsive" style="max-height: 500px;">
                                    <table class="table table-sm table-hover">
                                        <thead class="table-light sticky-top">
                                            <tr>
                                                <th style="width: 140px;">时间</th>
                                                <th style="width: 80px;">级别</th>
                                                <th>消息</th>
                                                <th style="width: 200px;">类名</th>
                                                <th style="width: 120px;">方法名</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            ${logs.map(log => `
                                                <tr class="log-row" data-level="${log.logLevel || 'INFO'}">
                                                    <td class="text-nowrap small">${this.formatLogTime(log.logTime)}</td>
                                                    <td><span class="badge bg-${this.getLogLevelClass(log.logLevel)}">${log.logLevel || 'INFO'}</span></td>
                                                    <td class="text-break">${this.escapeHtml(log.logMessage || '-')}</td>
                                                    <td class="text-truncate small" title="${log.className || '-'}">${this.getShortClassName(log.className)}</td>
                                                    <td class="text-truncate small">${log.methodName || '-'}</td>
                                                </tr>
                                            `).join('')}
                                        </tbody>
                                    </table>
                                </div>
                            ` : `
                                <div class="text-center py-5">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="icon icon-lg text-muted mb-3" width="48" height="48" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                        <path d="M14 3v4a1 1 0 0 0 1 1h4"/>
                                        <path d="M17 21h-10a2 2 0 0 1 -2 -2v-14a2 2 0 0 1 2 -2h7l5 5v11a2 2 0 0 1 -2 2z"/>
                                        <line x1="9" y1="9" x2="10" y2="9"/>
                                        <line x1="9" y1="13" x2="15" y2="13"/>
                                        <line x1="9" y1="17" x2="15" y2="17"/>
                                    </svg>
                                    <h4 class="text-muted">暂无日志记录</h4>
                                    <p class="text-muted">该事务还没有生成日志记录</p>
                                </div>
                            `}
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-outline-primary" onclick="window.currentPage.viewTransactionLogs('${transactionId}')">
                                <svg xmlns="http://www.w3.org/2000/svg" class="icon me-1" width="16" height="16" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                    <path d="M20 11a8.1 8.1 0 0 0 -15.5 -2m-.5 -4v4h4"/>
                                    <path d="M4 13a8.1 8.1 0 0 0 15.5 2m.5 4v-4h-4"/>
                                </svg>
                                刷新
                            </button>
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // 移除已存在的模态框
        const existingModal = document.getElementById('transactionLogsModal');
        if (existingModal) {
            existingModal.remove();
        }

        // 添加新的模态框到页面
        document.body.insertAdjacentHTML('beforeend', modalHtml);

        const modalElement = document.getElementById('transactionLogsModal');

        // 添加日志级别过滤功能
        if (logs.length > 0) {
            modalElement.addEventListener('change', (e) => {
                if (e.target.name === 'logLevel') {
                    const level = e.target.id.replace('log', '').toUpperCase();
                    const rows = modalElement.querySelectorAll('.log-row');

                    rows.forEach(row => {
                        if (level === 'ALL' || row.dataset.level === level) {
                            row.style.display = '';
                        } else {
                            row.style.display = 'none';
                        }
                    });
                }
            });
        }

        // 显示模态框
        const modal = new bootstrap.Modal(modalElement);
        modal.show();
    }

    /**
     * 获取日志级别样式类
     */
    getLogLevelClass(level) {
        switch (level) {
            case 'ERROR':
                return 'danger';
            case 'WARN':
                return 'warning';
            case 'INFO':
                return 'info';
            case 'DEBUG':
                return 'secondary';
            default:
                return 'secondary';
        }
    }

    /**
     * 格式化日志时间
     */
    formatLogTime(timeStr) {
        if (!timeStr) return '-';

        try {
            const date = new Date(timeStr);
            return date.toLocaleString('zh-CN', {
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit',
                fractionalSecondDigits: 3
            });
        } catch (e) {
            return timeStr;
        }
    }

    /**
     * 获取短类名
     */
    getShortClassName(className) {
        if (!className) return '-';

        const parts = className.split('.');
        return parts.length > 1 ? parts[parts.length - 1] : className;
    }

    /**
     * HTML转义
     */
    escapeHtml(text) {
        if (!text) return '';

        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }
}

// 注册页面类到全局
window.TransactionsPage = TransactionsPage;
