/**
 * 统计分析页面类
 * 管理统计分析页面的数据加载和交互
 */
class StatisticsPage {
    constructor() {
        this.currentTimeRange = 'today';
        this.charts = {};
        this.isInitialized = false;
    }

    /**
     * 初始化页面
     */
    async init() {
        if (this.isInitialized) {
            console.log('统计分析页面已经初始化，跳过重复初始化');
            return;
        }

        try {
            console.log('初始化统计分析页面...');

            // 初始化事件监听器
            this.initEventListeners();

            // 加载初始数据
            await this.loadData();

            this.isInitialized = true;
            console.log('统计分析页面初始化完成');
        } catch (error) {
            console.error('统计分析页面初始化失败:', error);
        }
    }

    /**
     * 销毁页面
     */
    async destroy() {
        try {
            // 销毁图表
            this.destroyCharts();

            console.log('统计分析页面已销毁');
        } catch (error) {
            console.error('统计分析页面销毁失败:', error);
        }
    }

    /**
     * 刷新数据
     */
    async refreshData() {
        try {
            await this.loadData();
            console.log('统计分析数据刷新完成');
        } catch (error) {
            console.error('统计分析数据刷新失败:', error);
        }
    }

    /**
     * 初始化事件监听器
     */
    initEventListeners() {
        // 时间范围选择
        document.querySelectorAll('input[name="timeRange"]').forEach(radio => {
            radio.addEventListener('change', (e) => {
                this.currentTimeRange = e.target.id.replace('range', '').toLowerCase();
                if (this.currentTimeRange === 'custom') {
                    document.getElementById('customRangeInputs').style.display = 'block';
                } else {
                    document.getElementById('customRangeInputs').style.display = 'none';
                    this.loadData();
                }
            });
        });

        // 自定义时间范围应用
        const applyBtn = document.getElementById('applyCustomRange');
        if (applyBtn) {
            applyBtn.addEventListener('click', () => {
                this.loadData();
            });
        }

        // 图表类型切换
        const chartTypeSelect = document.getElementById('trendChartType');
        if (chartTypeSelect) {
            chartTypeSelect.addEventListener('change', (e) => {
                this.updateChartType(e.target.value);
            });
        }
    }

    /**
     * 加载数据
     */
    async loadData() {
        try {
            // 模拟API调用
            const data = await this.fetchStatisticsData();

            // 更新统计卡片
            this.updateStatCards(data.stats);

            // 更新图表
            this.updateCharts(data.charts);

            // 更新统计表格
            this.updateStatisticsTable(data.tableData);

        } catch (error) {
            console.error('统计数据加载失败:', error);
        }
    }

    /**
     * 获取统计数据
     */
    async fetchStatisticsData() {
        try {
            console.log('获取统计分析数据...');

            // 构建查询参数
            const queryParams = {
                days: this.getStatisticsDays(),
                includeRealtime: false,
                includeCharts: true
            };

            // 调用后端统计接口
            const response = await window.apiService.getTransactionStatisticsPost(queryParams);

            console.log('统计分析数据响应:', response);

            // 适配ApiResponse格式
            if (response.success !== false) {
                const data = response.data || response;

                // 转换数据格式以适配前端组件
                return {
                    stats: {
                        totalCount: data.totalCount || data.total || 0,
                        successRate: data.successRatePercent || data.successRate || '0%',
                        avgDuration: this.formatDuration(data.avgDuration || 0),
                        peakTps: data.peakTps || 0
                    },
                    charts: {
                        trend: data.trendData || data.trends || this.generateTrendData(),
                        distribution: data.modeData || this.generateDistributionData()
                    },
                    tableData: this.generateTableDataFromStats(data)
                };
            } else {
                throw new Error(response.message || '获取统计数据失败');
            }
        } catch (error) {
            console.error('获取统计数据失败:', error);

            // API调用失败，返回空数据
            console.warn('API调用失败，返回空统计数据');
            this.showError('无法获取统计数据，请检查后端服务是否正常运行');
            return {
                stats: {
                    totalCount: 0,
                    successRate: '0%',
                    avgDuration: '0ms',
                    peakTps: 0
                },
                charts: {
                    trend: [],
                    distribution: []
                },
                tableData: []
            };
        }
    }

    /**
     * 生成趋势数据
     */
    generateTrendData() {
        const data = [];
        const now = new Date();
        for (let i = 23; i >= 0; i--) {
            const time = new Date(now.getTime() - i * 60 * 60 * 1000);
            data.push({
                time: time.toISOString(),
                total: Math.floor(Math.random() * 100),
                success: Math.floor(Math.random() * 80),
                failed: Math.floor(Math.random() * 20)
            });
        }
        return data;
    }

    /**
     * 生成分布数据
     */
    generateDistributionData() {
        return [
            {name: '成功', value: Math.floor(Math.random() * 1000)},
            {name: '失败', value: Math.floor(Math.random() * 200)},
            {name: '超时', value: Math.floor(Math.random() * 100)},
            {name: '执行中', value: Math.floor(Math.random() * 50)}
        ];
    }

    /**
     * 获取统计天数
     */
    getStatisticsDays() {
        const timeRangeMap = {
            'today': 1,
            'week': 7,
            'month': 30,
            'quarter': 90
        };
        return timeRangeMap[this.currentTimeRange] || 7;
    }

    /**
     * 格式化持续时间
     */
    formatDuration(duration) {
        if (!duration || duration === 0) return '0ms';
        if (duration < 1000) return duration + 'ms';
        if (duration < 60000) return (duration / 1000).toFixed(1) + 's';
        return (duration / 60000).toFixed(1) + 'm';
    }

    /**
     * 从统计数据生成表格数据
     */
    generateTableDataFromStats(statsData) {
        // 如果后端提供了详细的时间段数据，使用它
        if (statsData.hourlyData && Array.isArray(statsData.hourlyData)) {
            return statsData.hourlyData.map(item => ({
                timeSlot: item.timeSlot || item.hour,
                total: item.total || 0,
                success: item.success || 0,
                failed: item.failed || 0,
                successRate: item.successRate || '0%',
                avgDuration: this.formatDuration(item.avgDuration || 0),
                tps: item.tps || 0
            }));
        }

        // 否则生成模拟数据
        return this.generateTableData();
    }

    /**
     * 生成表格数据
     */
    generateTableData() {
        const data = [];
        const now = new Date();
        for (let i = 0; i < 24; i++) {
            const time = new Date(now.getTime() - i * 60 * 60 * 1000);
            const total = Math.floor(Math.random() * 100);
            const success = Math.floor(total * (Math.random() * 0.3 + 0.7));
            const failed = total - success;
            data.push({
                timeSlot: time.getHours() + ':00-' + (time.getHours() + 1) + ':00',
                total: total,
                success: success,
                failed: failed,
                successRate: total > 0 ? ((success / total) * 100).toFixed(1) + '%' : '0%',
                avgDuration: Math.floor(Math.random() * 1000) + 'ms',
                tps: Math.floor(Math.random() * 50)
            });
        }
        return data;
    }

    /**
     * 更新统计卡片
     */
    updateStatCards(stats) {
        const elements = {
            statsTotalCount: document.getElementById('statsTotalCount'),
            statsSuccessRate: document.getElementById('statsSuccessRate'),
            statsAvgDuration: document.getElementById('statsAvgDuration'),
            statsPeakTps: document.getElementById('statsPeakTps')
        };

        if (elements.statsTotalCount) {
            elements.statsTotalCount.textContent = stats.totalCount.toLocaleString();
        }
        if (elements.statsSuccessRate) {
            elements.statsSuccessRate.textContent = stats.successRate;
        }
        if (elements.statsAvgDuration) {
            elements.statsAvgDuration.textContent = stats.avgDuration;
        }
        if (elements.statsPeakTps) {
            elements.statsPeakTps.textContent = stats.peakTps.toLocaleString();
        }
    }

    /**
     * 更新图表
     */
    updateCharts(data) {
        // 这里可以使用Chart.js、ECharts等图表库
        console.log('更新图表数据:', data);

        // 更新趋势图表占位符
        const trendChart = document.getElementById('detailedTrendChart');
        if (trendChart) {
            // 简单的占位符更新
            const placeholder = trendChart.querySelector('.text-center');
            if (placeholder) {
                placeholder.innerHTML = `
                    <svg xmlns="http://www.w3.org/2000/svg" class="icon icon-lg mb-2" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                        <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
                        <path d="M3 12h4l3 8l4 -16l3 8h4"/>
                    </svg>
                    <div>趋势图表已更新 (${data.trend.length} 个数据点)</div>
                `;
            }
        }

        // 更新分布图表占位符
        const distributionChart = document.getElementById('statusDistributionChart');
        if (distributionChart) {
            const placeholder = distributionChart.querySelector('.text-center');
            if (placeholder) {
                placeholder.innerHTML = `
                    <svg xmlns="http://www.w3.org/2000/svg" class="icon icon-lg mb-2" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                        <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
                        <circle cx="12" cy="12" r="10"/>
                        <path d="M12 2a15.3 15.3 0 0 1 4 10a15.3 15.3 0 0 1 -4 10a15.3 15.3 0 0 1 -4 -10a15.3 15.3 0 0 1 4 -10z"/>
                        <path d="M2 12h20"/>
                    </svg>
                    <div>分布图表已更新 (${data.distribution.length} 个分类)</div>
                `;
            }
        }
    }

    /**
     * 更新统计表格
     */
    updateStatisticsTable(data) {
        const tableBody = document.getElementById('statisticsTableBody');
        if (!tableBody) return;

        const html = data.map(row => `
            <tr>
                <td>${row.timeSlot}</td>
                <td>${row.total}</td>
                <td>${row.success}</td>
                <td>${row.failed}</td>
                <td>
                    <span class="badge bg-${parseFloat(row.successRate) > 90 ? 'success' : parseFloat(row.successRate) > 70 ? 'warning' : 'danger'}">
                        ${row.successRate}
                    </span>
                </td>
                <td>${row.avgDuration}</td>
                <td>${row.tps}</td>
            </tr>
        `).join('');

        tableBody.innerHTML = html;
    }

    /**
     * 更新图表类型
     */
    updateChartType(type) {
        console.log('切换图表类型:', type);
        // 这里可以实现图表类型切换逻辑
    }

    /**
     * 销毁图表
     */
    destroyCharts() {
        Object.values(this.charts).forEach(chart => {
            if (chart && typeof chart.destroy === 'function') {
                chart.destroy();
            }
        });
        this.charts = {};
    }
}

// 注册页面类到全局
window.StatisticsPage = StatisticsPage;
