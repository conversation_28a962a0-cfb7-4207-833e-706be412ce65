package com.tipray.transaction.ui.test;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;

/**
 * UI测试应用启动类
 * 用于单独测试UI功能
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-05-27
 */
@SpringBootApplication(exclude = {DataSourceAutoConfiguration.class})
public class UITestApplication {

    public static void main(String[] args) {
        System.setProperty("server.port", "8080");
        SpringApplication.run(UITestApplication.class, args);

        System.out.println("\n=== Tipray分布式事务UI测试应用已启动 ===");
        System.out.println("UI界面访问地址: http://localhost:8080/ui");
        System.out.println("API接口地址: http://localhost:8080/api/transaction");
        System.out.println("测试步骤执行历史: http://localhost:8080/api/transaction/steps/1751446311554070/history");
        System.out.println("健康检查: http://localhost:8080/api/transaction/health");
        System.out.println("=======================================\n");
    }
}
