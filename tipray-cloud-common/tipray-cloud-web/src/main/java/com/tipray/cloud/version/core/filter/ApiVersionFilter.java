package com.tipray.cloud.version.core.filter;

import com.tipray.cloud.version.core.exception.ApiVersionException;
import org.springframework.web.filter.OncePerRequestFilter;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletRequestWrapper;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * API版本过滤器
 * 处理URL路径中的版本信息
 */
public class ApiVersionFilter extends OncePerRequestFilter {

    // 路径中版本的正则表达式匹配
    private final static Pattern VERSION_PATH_PATTERN = Pattern.compile("/v([0-9]+)(/|$)");
    
    // 版本请求属性名称
    public static final String VERSION_REQUEST_ATTRIBUTE = "API_VERSION";

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain)
            throws ServletException, IOException {
        
        try {
            String requestURI = request.getRequestURI();
            Matcher matcher = VERSION_PATH_PATTERN.matcher(requestURI);
            
            if (matcher.find()) {
                // 找到版本号
                String versionPart = matcher.group(0);
                String versionNumber = matcher.group(1);
                String version = "v" + versionNumber;
                
                // 修改后的URI（去掉版本号部分）
                String newRequestURI = removeVersionFromPath(requestURI, versionPart);
                
                // 创建包装请求，同时存储版本信息
                HttpServletRequest wrappedRequest = new VersionRequestWrapper(request, newRequestURI, version);
                
                filterChain.doFilter(wrappedRequest, response);
            } else {
                // 没有版本信息，直接继续
                filterChain.doFilter(request, response);
            }
        } catch (ApiVersionException e) {
            // 处理API版本异常
            logger.warn("API版本错误: " + e.getMessage() + ", 请求版本: " + e.getRequestedVersion() + ", 路径: " + e.getRequestedPath());
            response.sendError(HttpServletResponse.SC_NOT_FOUND, e.getMessage());
        }
    }
    
    /**
     * 从路径中移除版本号部分
     */
    private String removeVersionFromPath(String path, String versionPart) {
        if (versionPart.endsWith("/")) {
            // 如果版本后面有斜杠，直接移除整个版本部分
            return path.replace(versionPart, "/");
        } else {
            // 如果版本后面没有斜杠，移除整个版本部分
            return path.replace(versionPart, "");
        }
    }
    
    /**
     * 版本请求包装器
     * 重写getRequestURI和getServletPath方法，移除版本号部分
     * 同时在请求属性中保存版本信息
     */
    private static class VersionRequestWrapper extends HttpServletRequestWrapper {
        private final String newRequestURI;
        private final String version;
        
        public VersionRequestWrapper(HttpServletRequest request, String newRequestURI, String version) {
            super(request);
            this.newRequestURI = newRequestURI;
            this.version = version;
            
            // 将版本信息设置到请求属性中
            request.setAttribute(VERSION_REQUEST_ATTRIBUTE, version);
        }
        
        @Override
        public String getRequestURI() {
            return newRequestURI;
        }
        
        @Override
        public String getServletPath() {
            return newRequestURI;
        }
        
        @Override
        public Object getAttribute(String name) {
            if (VERSION_REQUEST_ATTRIBUTE.equals(name)) {
                return version;
            }
            return super.getAttribute(name);
        }
    }
}
