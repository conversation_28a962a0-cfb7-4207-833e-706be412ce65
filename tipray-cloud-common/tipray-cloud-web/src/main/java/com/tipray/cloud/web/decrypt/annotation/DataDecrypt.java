package com.tipray.cloud.web.decrypt.annotation;

import java.lang.annotation.*;

/**
 * 数据解密注解
 * 用于标记需要自动解密DataParam中encryptData字段的方法参数
 *
 * <AUTHOR>
 */
@Target(ElementType.PARAMETER)
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface DataDecrypt {

    /**
     * 解密算法类型，预留扩展
     * @return 算法类型
     */
    String algorithm() default "DEFAULT";

    /**
     * 是否启用解密，默认启用
     * @return 是否启用
     */
    boolean enabled() default true;

}
