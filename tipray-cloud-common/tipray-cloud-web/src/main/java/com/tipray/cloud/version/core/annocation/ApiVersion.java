package com.tipray.cloud.version.core.annocation;

import com.tipray.cloud.version.core.enums.VersionStrategy;

import java.lang.annotation.*;

/**
 * API版本控制注解
 * 用于标识API的版本
 * <AUTHOR>
 */
@Target({ElementType.METHOD, ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface ApiVersion {
    /**
     * API版本号
     * 例如：v1, v2, v3
     * @return API版本号
     */
    String value() default "v1";

    /**
     * 版本识别方式，默认同时支持多种方式
     * @return 版本识别方式
     */
    VersionStrategy[] strategy() default {
        VersionStrategy.URL_PATH,
        VersionStrategy.REQUEST_PARAM,
        VersionStrategy.REQUEST_HEADER,
        VersionStrategy.ACCEPT_HEADER
    };

    /**
     * 版本参数名
     * URL路径：/api/{version}/users
     * 请求参数：/api/users?version=v1
     * 请求头：Version: v1
     * Accept头：Accept: application/vnd.company.app-v1+json
     * @return 参数名
     */
    String paramName() default "version";

    /**
     * Accept头的媒体类型前缀
     * 例如：application/vnd.company.app-
     * 完整例子: Accept: application/vnd.company.app-v1+json
     * @return 媒体类型前缀
     */
    String mediaTypePrefix() default "application/vnd.tipray.api-";
}
