package com.tipray.cloud;

import com.tipray.cloud.exception.GlobalExceptionHandler;
import com.tipray.cloud.version.core.ApiVersionRequestMappingHandlerMapping;
import com.tipray.cloud.version.core.filter.ApiVersionFilter;
import com.tipray.cloud.web.decrypt.advice.DataDecryptRequestBodyAdvice;
import com.tipray.cloud.web.filter.CacheRequestBodyFilter;
import com.tipray.cloud.web.interceptor.DynamicInterceptorRegistrar;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.web.servlet.WebMvcRegistrations;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.core.Ordered;
import org.springframework.web.cors.CorsConfiguration;
import org.springframework.web.cors.UrlBasedCorsConfigurationSource;
import org.springframework.web.filter.CorsFilter;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;
import org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping;

import javax.servlet.Filter;

/**
 * Web MVC配置类
 * 用于注册自定义的请求映射处理器
 * <AUTHOR>
 */
@Configuration
public class WebMvcConfiguration implements WebMvcRegistrations, WebMvcConfigurer {

    private static final Logger logger = LoggerFactory.getLogger(WebMvcConfiguration.class);

    @Autowired
    private ApplicationContext applicationContext;

    /**
     * 自定义接口版本映射 处理请求头 请求参数带版本的情况
     */
    @Override
    @Primary
    public RequestMappingHandlerMapping getRequestMappingHandlerMapping() {
        ApiVersionRequestMappingHandlerMapping mapping = new ApiVersionRequestMappingHandlerMapping();
        mapping.setOrder(Ordered.HIGHEST_PRECEDENCE);
        return mapping;
    }

    /**
     * 注册API版本过滤器 主要为了解决 路径上带版本的情况
     */
    @Bean
    public FilterRegistrationBean<ApiVersionFilter> apiVersionFilter() {
        return createFilterBean(new ApiVersionFilter(), Integer.MIN_VALUE + 100);
    }

    /**
     * 创建 CorsFilter Bean，解决跨域问题
     */
    @Bean
    public FilterRegistrationBean<CorsFilter> corsFilterBean() {
        // 创建 CorsConfiguration 对象
        CorsConfiguration config = new CorsConfiguration();
        config.setAllowCredentials(true);
        config.addAllowedOriginPattern("*"); // 设置访问源地址
        config.addAllowedHeader("*"); // 设置访问源请求头
        config.addAllowedMethod("*"); // 设置访问源请求方法
        // 创建 UrlBasedCorsConfigurationSource 对象
        UrlBasedCorsConfigurationSource source = new UrlBasedCorsConfigurationSource();
        source.registerCorsConfiguration("/**", config); // 对接口配置跨域设置
        return createFilterBean(new CorsFilter(source), Integer.MIN_VALUE);
    }

    /**
     * 创建 RequestBodyCacheFilter Bean，可重复读取请求内容
     */
    @Bean
    public FilterRegistrationBean<CacheRequestBodyFilter> requestBodyCacheFilter() {
        return createFilterBean(new CacheRequestBodyFilter(), Integer.MIN_VALUE + 500);
    }

    public static <T extends Filter> FilterRegistrationBean<T> createFilterBean(T filter, Integer order) {
        FilterRegistrationBean<T> bean = new FilterRegistrationBean<>(filter);
        bean.setOrder(order);
        return bean;
    }

    @Bean
    public GlobalExceptionHandler globalExceptionHandler() {
        return new GlobalExceptionHandler();
    }

    @Bean
    public DataDecryptRequestBodyAdvice dataDecryptRequestBodyAdvice() {
        return new DataDecryptRequestBodyAdvice();
    }

    /**
     * 添加拦截器配置
     * 支持动态添加的全局拦截器
     */
    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        // 扫描并注册所有标记了@DynamicInterceptor注解的拦截器
        DynamicInterceptorRegistrar.registerDynamicInterceptors(registry, applicationContext);
    }

}
