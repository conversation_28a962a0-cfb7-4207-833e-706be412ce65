package com.tipray.cloud.web.decrypt.pojo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 数据参数包装类
 * 用于包装加密的业务数据
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DataParam implements Serializable {

    /**
     * 应用编码
     */
    private String applicationCode;

    /**
     * 公司编码
     */
    private String companyCode;

    /**
     * 加密数据（业务数据的加密字符串）
     */
    private String encryptData;

    /**
     * 时间戳
     */
    private String ts;

    /**
     * 状态
     */
    private String state;

    /**
     * 随机种子
     */
    private String seed;

    private static final long serialVersionUID = 1L;
}
