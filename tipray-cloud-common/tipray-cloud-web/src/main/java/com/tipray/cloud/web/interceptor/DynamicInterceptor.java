package com.tipray.cloud.web.interceptor;

import org.springframework.stereotype.Component;

import java.lang.annotation.*;

/**
 * 动态拦截器注解
 * 标记在HandlerInterceptor实现类上，会被自动注册到全局拦截器配置中
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-05-20
 */
@Target(ElementType.TYPE)
@Retention(RetentionPolicy.RUNTIME)
@Documented
@Component
public @interface DynamicInterceptor {

    /**
     * 拦截路径模式
     * 默认拦截所有路径
     */
    String[] pathPatterns() default {"/**"};

    /**
     * 排除路径模式
     * 默认排除常见的监控和文档路径
     */
    String[] excludePatterns() default {
            "/actuator/**",
            "/swagger-ui/**",
            "/v3/api-docs/**",
            "/error"
    };

    /**
     * 执行顺序
     * 数值越小优先级越高
     */
    int order() default 0;
}
