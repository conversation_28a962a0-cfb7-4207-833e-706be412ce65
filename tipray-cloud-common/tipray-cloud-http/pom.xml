<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.tipray</groupId>
        <artifactId>tipray-cloud-common</artifactId>
        <version>${revision}</version>
    </parent>

    <artifactId>tipray-cloud-http</artifactId>
    <packaging>jar</packaging>

    <name>${project.artifactId}</name>
    <description>tipray-cloud http客户端模块</description>

    <dependencies>
        <!-- Spring Boot 配置所需依赖 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-configuration-processor</artifactId>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-autoconfigure</artifactId>
        </dependency>
        <!-- Forest HTTP客户端 -->
        <dependency>
            <groupId>com.dtflys.forest</groupId>
            <artifactId>forest-spring-boot-starter</artifactId>
        </dependency>
    </dependencies>
</project>
