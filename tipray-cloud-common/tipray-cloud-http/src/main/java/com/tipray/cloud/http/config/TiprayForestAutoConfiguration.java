package com.tipray.cloud.http.config;

import com.dtflys.forest.config.SpringForestProperties;
import com.dtflys.forest.interceptor.SpringInterceptorFactory;
import com.dtflys.forest.logging.DefaultLogHandler;
import com.dtflys.forest.reflection.SpringForestObjectFactory;
import com.dtflys.forest.springboot.ForestAutoConfiguration;
import com.dtflys.forest.springboot.ForestBeanRegister;
import com.dtflys.forest.springboot.properties.ForestConfigurationProperties;
import com.tipray.cloud.http.core.log.HttpLogHandler;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.DependsOn;


/**
 * http客户端自动配置
 * <AUTHOR>
 */
@AutoConfiguration(before = ForestAutoConfiguration.class)
public class TiprayForestAutoConfiguration {

    @Autowired
    private ConfigurableApplicationContext applicationContext;

    @Bean
    public DefaultLogHandler defaultLogHandler() {
        // 自定义日志处理器
        return new HttpLogHandler();
    }

    @Bean
    @DependsOn("forestBeanProcessor")
    public ForestBeanRegister forestBeanRegister(SpringForestProperties properties,
                                                 SpringForestObjectFactory forestObjectFactory,
                                                 SpringInterceptorFactory forestInterceptorFactory,
                                                 ForestConfigurationProperties forestConfigurationProperties) {
        // 设置全局的日志处理器
        forestConfigurationProperties.setLogHandler(HttpLogHandler.class);

        ForestBeanRegister register = new ForestBeanRegister(
                applicationContext,
                forestConfigurationProperties,
                properties,
                forestObjectFactory,
                forestInterceptorFactory);
        register.registerForestConfiguration();
        register.registerScanner();
        return register;
    }
}
