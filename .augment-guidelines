核心执行原则
1. 文件内容准确性（最高优先级）
必须始终使用最新的文件内容，绝对禁止使用任何缓存或过时信息
在进行任何代码修改前，必须先使用 view 工具查看当前文件的实际内容
在使用 codebase-retrieval 工具后，仍需通过 view 工具验证文件的当前状态
2. Java 版本兼容性
强制要求：检查项目根目录的 pom.xml 文件中指定的 Java 版本
如果 pom.xml 中未明确指定 Java 版本，默认使用 Java 1.8
所有代码语法必须与指定的 Java 版本完全兼容
3. 语言和本地化要求
所有日志输出必须使用中文
所有代码注释必须使用中文
所有与用户的交流必须使用中文回答
异常信息和提示信息优先使用中文
4. 代码质量标准
代码结构必须清晰规范，遵循良好的编程实践
功能实现必须完整，不允许任何简化或省略
严禁使用占位符代码（如 // TODO、// 实现具体逻辑 等）
参考优秀开源框架的架构设计，确保扩展性强
5. 实现完整性要求
绝对禁止简化实现，所有功能必须完全实现
如果代码量大，可以分步骤实现，但每一步都必须是完整的功能实现
不允许为了简化而省略关键逻辑，这会导致用户调试困难
6. 文件操作策略
同一文件的多个修改必须在一次操作中完成，不要分多次进行
删除代码操作特别要求一次性完成，避免多次操作导致的不一致
使用 str-replace-editor 工具时，尽可能将相关修改合并到一次调用中
7. 输出控制
除非用户明确要求，否则不输出任何文档
专注于代码实现，避免不必要的文档生成
只在用户明确询问时才提供解释性文档
8. 代码修改范围控制
当用户明确指定要修改某部分代码时，严格限制修改范围
绝对不允许修改用户未要求修改的其他代码部分
保持对现有代码的最小侵入性
执行检查清单
在执行任何任务前，必须确认：
已使用 view 工具查看最新文件内容
已检查 Java 版本兼容性
确认所有输出使用中文
确认功能实现完整性
确认修改范围符合用户要求
违规处理
如果发现自己违反了以上任何一条原则，必须：
立即停止当前操作
重新查看最新文件内容
按照正确的原则重新执行
这些原则是强制性的，不允许任何例外或妥协。
