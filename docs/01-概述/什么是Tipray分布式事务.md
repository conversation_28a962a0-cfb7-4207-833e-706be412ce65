# Tipray 分布式事务是什么？

Tipray 分布式事务是一款开源的分布式事务解决方案，专为微服务架构设计，致力于提供高性能和简单易用的分布式事务服务。Tipray 为用户提供了 AT、Saga 两种事务模式，为用户打造一站式的分布式解决方案。

![Tipray分布式事务架构图](../images/tipray-architecture.png)

## 🏗️ 架构组成

Tipray 分布式事务框架由三个核心模块组成：

### 1. tipray-cloud-transaction（核心框架）
- **分布式事务协调**: 统一的事务管理器，支持事务的开始、提交、回滚
- **Saga模式支持**: 基于补偿机制的分布式事务实现  
- **AT模式协调**: 作为AT模式的事务协调者，管理分支事务
- **事务上下文管理**: 统一的事务上下文，支持跨服务传递
- **事务屏障**: 防重复执行，保证幂等性
- **重试机制**: 支持步骤级别的重试和退避策略

### 2. tipray-cloud-transaction-client（AT模式客户端）
- **数据源代理**: 自动代理数据源，拦截SQL执行
- **UndoLog生成**: 自动生成回滚日志，支持数据恢复
- **SQL解析**: 基于Seata的SQL解析器，支持多种SQL类型
- **全局锁管理**: 防止脏写，保证数据一致性
- **分支事务管理**: 作为分支事务参与者，响应协调者指令

### 3. tipray-cloud-transaction-ui-starter（监控界面）
- **实时监控**: 显示事务执行状态和统计信息
- **事务详情**: 查看事务组和步骤的详细执行信息
- **过滤搜索**: 支持按状态、时间、关键字过滤
- **手动干预**: 对失败的事务进行手动补偿
- **统计报表**: 成功率、失败率、性能指标等

## 🎯 设计原则

- **模块独立性**: 核心框架不依赖客户端模块，支持可选依赖
- **统一架构**: AT模式和Saga模式共用统一的事务管理器和上下文
- **松耦合**: 通过反射机制实现模块间通信
- **优雅降级**: 没有客户端模块时仍可正常工作

## 📊 功能对比表

| 功能特性 | tipray-cloud-transaction | tipray-cloud-transaction-client | tipray-cloud-transaction-ui-starter |
|---------|-------------------------|--------------------------------|-----------------------------------|
| **事务协调** | ✅ 完整支持 | ❌ 不支持 | ❌ 不支持 |
| **Saga模式** | ✅ 完整支持 | ❌ 不支持 | ❌ 不支持 |
| **AT模式协调** | ✅ 协调者角色 | ❌ 不支持 | ❌ 不支持 |
| **AT模式参与** | ❌ 不支持 | ✅ 参与者角色 | ❌ 不支持 |
| **数据源代理** | ❌ 不支持 | ✅ 完整支持 | ❌ 不支持 |
| **UndoLog生成** | ❌ 不支持 | ✅ 完整支持 | ❌ 不支持 |
| **全局锁** | ❌ 不支持 | ✅ 完整支持 | ❌ 不支持 |
| **事务监控** | ✅ 数据收集 | ❌ 不支持 | ✅ 界面展示 |
| **可视化界面** | ❌ 不支持 | ❌ 不支持 | ✅ 完整支持 |
| **手动干预** | ❌ 不支持 | ❌ 不支持 | ✅ 完整支持 |
| **独立部署** | ✅ 可独立使用 | ✅ 可独立使用 | ❌ 需要依赖核心框架 |

## 🔄 模块协作模式

### 1. 纯Saga模式
```
应用服务
├── tipray-cloud-transaction
└── tipray-cloud-transaction-ui-starter (可选)
```

**特点**:
- 只需要核心框架
- 基于补偿机制
- 适用于服务编排场景

### 2. 纯AT模式
```
DLP服务 (协调者)
├── tipray-cloud-transaction
└── tipray-cloud-transaction-ui-starter (可选)

云服务 (参与者)
└── tipray-cloud-transaction-client
```

**特点**:
- DLP服务作为协调者
- 云服务作为参与者
- 强一致性保证

### 3. 混合模式
```
应用服务
├── tipray-cloud-transaction
├── tipray-cloud-transaction-client (可选)
└── tipray-cloud-transaction-ui-starter (可选)
```

**特点**:
- 同时支持Saga和AT模式
- 灵活的事务策略选择
- 适用于复杂业务场景

## 🎯 选择指南

### 只需要Saga模式
```xml
<dependency>
    <groupId>com.tipray</groupId>
    <artifactId>tipray-cloud-transaction</artifactId>
</dependency>
```

**适用场景**:
- 服务编排和流程管理
- 不涉及复杂数据库操作
- 对最终一致性要求不高

### 只需要AT模式
```xml
<!-- DLP服务 -->
<dependency>
    <groupId>com.tipray</groupId>
    <artifactId>tipray-cloud-transaction</artifactId>
</dependency>

<!-- 云服务 -->
<dependency>
    <groupId>com.tipray</groupId>
    <artifactId>tipray-cloud-transaction-client</artifactId>
</dependency>
```

**适用场景**:
- 数据库操作密集
- 需要强一致性保证
- 微服务架构

### 需要监控界面
```xml
<dependency>
    <groupId>com.tipray</groupId>
    <artifactId>tipray-cloud-transaction</artifactId>
</dependency>
<dependency>
    <groupId>com.tipray</groupId>
    <artifactId>tipray-cloud-transaction-ui-starter</artifactId>
</dependency>
```

**适用场景**:
- 开发和测试环境
- 生产环境监控
- 需要手动干预能力

### 完整功能
```xml
<dependency>
    <groupId>com.tipray</groupId>
    <artifactId>tipray-cloud-transaction</artifactId>
</dependency>
<dependency>
    <groupId>com.tipray</groupId>
    <artifactId>tipray-cloud-transaction-client</artifactId>
</dependency>
<dependency>
    <groupId>com.tipray</groupId>
    <artifactId>tipray-cloud-transaction-ui-starter</artifactId>
</dependency>
```

**适用场景**:
- 复杂业务系统
- 需要多种事务模式
- 完整的监控和管理需求
