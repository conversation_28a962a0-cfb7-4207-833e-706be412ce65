# 分布式事务日志规范

## 概述

本文档定义了 tipray-distributed-transaction 框架的统一日志规范，使用标准的 `@Slf4j` 注解，确保所有组件的日志输出格式一致、信息完整、便于监控和排查问题。

## 日志级别定义

### ERROR
- **用途**：记录系统错误、异常情况
- **场景**：事务失败、网络异常、数据库错误、业务异常等
- **要求**：必须包含完整的异常堆栈信息

### WARN  
- **用途**：记录警告信息、潜在问题
- **场景**：配置缺失、性能警告、重试操作、降级处理等
- **要求**：包含警告原因和影响范围

### INFO
- **用途**：记录关键业务流程、状态变更
- **场景**：事务开始/提交/回滚、分支注册/执行、状态转换等
- **要求**：包含核心业务信息，便于业务监控

### DEBUG
- **用途**：记录详细的执行过程、调试信息
- **场景**：方法调用、参数传递、中间状态、性能指标等
- **要求**：包含详细的上下文信息，便于问题排查

## 日志格式规范

### 基础格式
```
[事务ID|分支ID] [操作类型] - 消息内容 [上下文信息]
```

### 格式说明
- **事务ID**：全局事务标识，格式：tx-{timestamp}-{sequence}
- **分支ID**：分支事务标识，数字格式，无分支时显示 "----"
- **操作类型**：BEGIN/COMMIT/ROLLBACK/REGISTER/EXEC/STATE/ERROR/WARN/DEBUG/PERF
- **消息内容**：具体的日志描述
- **上下文信息**：键值对格式的补充信息，格式：[key1=value1, key2=value2]

### 示例
```
[tx-1753790590777-9855|----] [BEGIN] - 事务开始 [mode=AT, method=processData]
[tx-1753790590777-9855|1753790590774255] [REGISTER] - 分支注册 [service=LOCAL, method=processData]
[tx-1753790590777-9855|1753790590774255] [STATE] - UNKNOWN -> REGISTERED [event=REGISTER, cost=0ms]
[tx-1753790590777-9855|----] [COMMIT] - 事务提交成功 [branches=3, total=218ms]
```

## 日志工具使用规范

### 使用 @Slf4j 注解
所有类都使用 Lombok 的 `@Slf4j` 注解，自动生成 `log` 字段：

```java
@Slf4j
@Component
public class TransactionService {
    
    public void processTransaction() {
        log.info("[{}|{}] [BEGIN] - 事务开始 [mode={}, method={}]", 
                getTxId(), getBranchId(), mode, method);
    }
}
```

### 统一的日志工具类

框架提供 `LoggingUtils` 工具类简化日志格式化：

```java
@UtilityClass
public class LoggingUtils {
    
    /**
     * 获取事务ID（无参数版本，从上下文获取）
     */
    public static String getTxId() {
        TransactionContext context = TransactionContextHolder.getCurrentContext();
        return context != null ? context.getTransactionId() : "UNKNOWN";
    }

    /**
     * 获取事务ID（支持多个候选参数，按优先级顺序取值）
     * @param candidates 候选事务ID参数，按优先级从高到低排列
     */
    public static String getTxId(String... candidates) {
        // 按优先级顺序检查候选参数，所有参数都无效时从上下文获取
    }

    /**
     * 获取分支ID（无参数版本，从上下文获取）
     */
    public static String getBranchId() {
        TransactionContext context = TransactionContextHolder.getCurrentContext();
        return context != null && context.getBranchId() != null ?
               context.getBranchId().toString() : "----";
    }

    /**
     * 获取分支ID（支持多个候选参数，按优先级顺序取值）
     * @param candidates 候选分支ID参数，支持Long、String、Number类型
     */
    public static String getBranchId(Object... candidates) {
        // 按优先级顺序检查候选参数，所有参数都无效时从上下文获取
    }
    
    /**
     * 格式化上下文信息
     */
    public static String formatContext(Object... params) {
        if (params == null || params.length == 0) {
            return "";
        }
        
        StringBuilder sb = new StringBuilder("[");
        for (int i = 0; i < params.length; i += 2) {
            if (i > 0) sb.append(", ");
            if (i + 1 < params.length) {
                sb.append(params[i]).append("=").append(params[i + 1]);
            } else {
                sb.append(params[i]);
            }
        }
        sb.append("]");
        return sb.toString();
    }
    
    /**
     * 格式化耗时信息
     */
    public static String formatDuration(long startTime) {
        return (System.currentTimeMillis() - startTime) + "ms";
    }
    
    /**
     * 格式化耗时信息（LocalDateTime）
     */
    public static String formatDuration(LocalDateTime startTime) {
        return Duration.between(startTime, LocalDateTime.now()).toMillis() + "ms";
    }
}
```

### 标准日志方法模板

#### 1. 事务生命周期日志
```java
// 方式1：从上下文自动获取（推荐用于大部分场景）
log.info("[{}|{}] [BEGIN] - 事务开始 {}",
         LoggingUtils.getTxId(), LoggingUtils.getBranchId(),
         LoggingUtils.formatContext("mode", mode, "method", method));

// 方式2：明确传入参数（推荐用于有明确参数的场景）
log.info("[{}|{}] [COMMIT] - 事务提交成功 {}",
         LoggingUtils.getTxId(transactionId), LoggingUtils.getBranchId(branchId),
         LoggingUtils.formatContext("branches", branchCount, "total", LoggingUtils.formatDuration(startTime)));

// 方式3：多个候选参数，按优先级取值（推荐用于复杂场景）
log.info("[{}|{}] [ROLLBACK] - 事务回滚 {}",
         LoggingUtils.getTxId(currentTxId, context.getTransactionId()),
         LoggingUtils.getBranchId(currentBranchId, context.getBranchId()),
         LoggingUtils.formatContext("reason", reason, "cause", cause.getClass().getSimpleName()));
```

#### 2. 分支事务日志
```java
// 分支注册
log.info("[{}|{}] [REGISTER] - 分支注册 {}", 
         LoggingUtils.getTxId(), LoggingUtils.getBranchId(), 
         LoggingUtils.formatContext("service", service, "method", method));

// 分支执行
log.info("[{}|{}] [EXEC] - 分支执行 {}", 
         LoggingUtils.getTxId(), LoggingUtils.getBranchId(), 
         LoggingUtils.formatContext("action", action, "step", step, "timeout", timeout + "s"));

// 分支提交
log.info("[{}|{}] [COMMIT] - 分支提交成功 {}", 
         LoggingUtils.getTxId(), LoggingUtils.getBranchId(), 
         LoggingUtils.formatContext("cost", LoggingUtils.formatDuration(startTime)));
```

#### 3. 状态转换日志
```java
// 状态转换
log.debug("[{}|{}] [STATE] - {} -> {} {}", 
          LoggingUtils.getTxId(), LoggingUtils.getBranchId(), 
          fromStatus, toStatus, 
          LoggingUtils.formatContext("event", event, "cost", LoggingUtils.formatDuration(startTime)));

// 非法转换警告
log.warn("[{}|{}] [WARN] - 非法状态转换 {}", 
         LoggingUtils.getTxId(), LoggingUtils.getBranchId(), 
         LoggingUtils.formatContext("from", fromStatus, "to", toStatus, "event", event));
```

#### 4. 错误日志
```java
// 异常记录
log.error("[{}|{}] [ERROR] - {} {}", 
          LoggingUtils.getTxId(), LoggingUtils.getBranchId(), 
          action + "失败", 
          LoggingUtils.formatContext("error", e.getClass().getSimpleName(), "msg", e.getMessage()), 
          e);

// 警告记录
log.warn("[{}|{}] [WARN] - {} {}", 
         LoggingUtils.getTxId(), LoggingUtils.getBranchId(), 
         action, 
         LoggingUtils.formatContext("reason", reason));
```

#### 5. 性能监控日志
```java
// 性能指标
log.info("[{}|{}] [PERF] - {}性能 {}", 
         LoggingUtils.getTxId(), LoggingUtils.getBranchId(), 
         phase, 
         LoggingUtils.formatContext("cost", LoggingUtils.formatDuration(startTime), 
                                   "branches", branchCount, "success", successCount));
```

#### 6. 调试日志
```java
// 详细调试信息
log.debug("[{}|{}] [DEBUG] - {} {}", 
          LoggingUtils.getTxId(), LoggingUtils.getBranchId(), 
          action, 
          LoggingUtils.formatContext("param1", value1, "param2", value2));
```

## 各组件日志规范

### 事务引擎 (DistributedTransactionEngine)
```java
@Slf4j
@Component
public class DistributedTransactionEngine {
    
    public void beginTransaction() {
        log.info("[{}|{}] [BEGIN] - 事务开始 {}", 
                LoggingUtils.getTxId(), LoggingUtils.getBranchId(), 
                LoggingUtils.formatContext("mode", mode, "method", method));
    }
    
    public void commitTransaction() {
        log.info("[{}|{}] [COMMIT] - 事务提交成功 {}", 
                LoggingUtils.getTxId(), LoggingUtils.getBranchId(), 
                LoggingUtils.formatContext("branches", branchCount, "total", LoggingUtils.formatDuration(startTime)));
    }
}
```

### 分支协调器 (BranchTransactionCoordinator)
```java
@Slf4j
@Component
public class BranchTransactionCoordinator {
    
    public void registerBranch() {
        log.info("[{}|{}] [REGISTER] - 分支注册 {}", 
                LoggingUtils.getTxId(), LoggingUtils.getBranchId(), 
                LoggingUtils.formatContext("service", service, "method", method));
    }
}
```

### 状态机 (TransactionStateMachine)
```java
@Slf4j
@Component
public class TransactionStateMachine {
    
    public void transition() {
        log.debug("[{}|{}] [STATE] - {} -> {} {}", 
                 LoggingUtils.getTxId(), LoggingUtils.getBranchId(), 
                 fromStatus, toStatus, 
                 LoggingUtils.formatContext("event", event, "cost", LoggingUtils.formatDuration(startTime)));
    }
}
```

### HTTP客户端 (AtHttpClient)
```java
@Slf4j
@Component
public class AtHttpClient {
    
    public void sendRequest() {
        log.debug("[{}|{}] [DEBUG] - AT HTTP请求 {}", 
                 LoggingUtils.getTxId(), LoggingUtils.getBranchId(), 
                 LoggingUtils.formatContext("method", method, "url", url));
    }
}
```

## 日志配置建议

### 生产环境配置
```yaml
logging:
  level:
    com.tipray.transaction: INFO
    com.tipray.transaction.core.logging: WARN
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{50} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{50} - %msg%n"
```

### 开发环境配置
```yaml
logging:
  level:
    com.tipray.transaction: DEBUG
    com.tipray.transaction.core: DEBUG
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{50} - %msg%n"
```

## 注意事项

1. **统一使用 @Slf4j**：所有类都使用 Lombok 的 `@Slf4j` 注解
2. **使用 LoggingUtils**：统一使用工具类获取事务上下文和格式化信息
3. **事务ID获取策略**：
   - 优先使用明确的参数值：`LoggingUtils.getTxId(transactionId)`
   - 多个候选参数按优先级：`LoggingUtils.getTxId(param1, param2, param3)`
   - 无参数时从上下文获取：`LoggingUtils.getTxId()`
   - 所有方式都无效时返回 "UNKNOWN"
4. **分支ID处理策略**：
   - 支持多种类型：Long、String、Number
   - 按优先级顺序取值：`LoggingUtils.getBranchId(branchId, context.getBranchId())`
   - 所有方式都无效时返回 "----"
5. **异常记录**：ERROR级别日志必须包含完整的异常堆栈
6. **性能敏感**：DEBUG级别日志在生产环境会被过滤，避免影响性能
7. **上下文传播**：确保异步操作中正确传播事务上下文
8. **日志安全**：避免在日志中输出敏感信息（密码、密钥等）

## 参数优先级说明

### getTxId() 方法优先级
1. **第一优先级**：传入的第一个非空字符串参数
2. **第二优先级**：传入的第二个非空字符串参数
3. **第N优先级**：传入的第N个非空字符串参数
4. **最后优先级**：从 TransactionContextHolder 获取当前上下文
5. **默认值**：所有方式都无效时返回 "UNKNOWN"

### getBranchId() 方法优先级
1. **第一优先级**：传入的第一个非空参数（Long/String/Number）
2. **第二优先级**：传入的第二个非空参数
3. **第N优先级**：传入的第N个非空参数
4. **最后优先级**：从 TransactionContextHolder 获取当前上下文
5. **默认值**：所有方式都无效时返回 "----"

## 完整示例

```java
@Slf4j
@Component
public class ExampleTransactionService {

    @DistributedTransaction(mode = TransactionMode.AT)
    public void processOrder(String transactionId, Long branchId, OrderRequest request) {
        long startTime = System.currentTimeMillis();

        // 方式1：明确传入参数（推荐）
        log.info("[{}|{}] [BEGIN] - 事务开始 {}",
                LoggingUtils.getTxId(transactionId), LoggingUtils.getBranchId(branchId),
                LoggingUtils.formatContext("mode", "AT", "method", "processOrder", "orderId", request.getOrderId()));

        try {
            // 业务处理
            processInventory(transactionId, branchId, request);
            processPayment(transactionId, request);

            // 事务提交日志
            log.info("[{}|{}] [COMMIT] - 事务提交成功 {}",
                    LoggingUtils.getTxId(transactionId), LoggingUtils.getBranchId(branchId),
                    LoggingUtils.formatContext("orderId", request.getOrderId(), "total", LoggingUtils.formatDuration(startTime)));

        } catch (Exception e) {
            // 错误日志
            log.error("[{}|{}] [ERROR] - 订单处理失败 {}",
                     LoggingUtils.getTxId(transactionId), LoggingUtils.getBranchId(branchId),
                     LoggingUtils.formatContext("orderId", request.getOrderId(), "error", e.getClass().getSimpleName(), "msg", e.getMessage()),
                     e);
            throw e;
        }
    }

    private void processInventory(String txId, Long branchId, OrderRequest request) {
        // 方式2：多个候选参数，按优先级取值
        TransactionContext context = TransactionContextHolder.getCurrentContext();
        log.debug("[{}|{}] [DEBUG] - 处理库存 {}",
                 LoggingUtils.getTxId(txId, context != null ? context.getTransactionId() : null),
                 LoggingUtils.getBranchId(branchId, context != null ? context.getBranchId() : null),
                 LoggingUtils.formatContext("orderId", request.getOrderId(), "items", request.getItems().size()));
    }

    private void processPayment(String txId, OrderRequest request) {
        // 方式3：从上下文自动获取（适用于上下文可靠的场景）
        log.debug("[{}|{}] [DEBUG] - 处理支付 {}",
                 LoggingUtils.getTxId(), LoggingUtils.getBranchId(),
                 LoggingUtils.formatContext("orderId", request.getOrderId(), "amount", request.getAmount()));
    }
}
```

## 日志输出示例

使用上述规范后，日志输出效果如下：

```
2025-01-29 20:03:10.777 [http-exec-9] INFO  c.t.t.ExampleTransactionService - [tx-1753790590777-9855|----] [BEGIN] - 事务开始 [mode=AT, method=processOrder, orderId=12345]
2025-01-29 20:03:10.784 [http-exec-9] DEBUG c.t.t.ExampleTransactionService - [tx-1753790590777-9855|----] [DEBUG] - 处理库存 [orderId=12345, items=3]
2025-01-29 20:03:10.785 [http-exec-9] INFO  c.t.t.BranchTransactionCoordinator - [tx-1753790590777-9855|1753790590774255] [REGISTER] - 分支注册 [service=InventoryService, method=reduceStock]
2025-01-29 20:03:10.890 [http-exec-9] DEBUG c.t.t.TransactionStateMachine - [tx-1753790590777-9855|1753790590774255] [STATE] - UNKNOWN -> REGISTERED [event=REGISTER, cost=5ms]
2025-01-29 20:03:10.995 [http-exec-9] INFO  c.t.t.ExampleTransactionService - [tx-1753790590777-9855|----] [COMMIT] - 事务提交成功 [orderId=12345, total=218ms]
```
