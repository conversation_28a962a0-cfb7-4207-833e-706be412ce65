# AT模式

AT模式是Tipray分布式事务基于数据库事务日志的分布式事务模式，提供强一致性保证。AT模式基于支持本地ACID事务的关系型数据库，通过自动生成回滚日志实现分布式事务。

![AT模式示意图](../images/at-mode.png)

## 🎯 前提条件

- **关系型数据库**: 基于支持本地ACID事务的关系型数据库
- **JDBC访问**: Java应用通过JDBC访问数据库
- **undo_log表**: 每个参与AT模式的数据库需要创建undo_log表

## 🏗️ 整体机制

AT模式采用两阶段提交协议的演变：

### 一阶段
- **业务数据更新**: 执行业务SQL，更新业务数据
- **生成回滚日志**: 自动生成前后镜像，记录到undo_log表
- **获取全局锁**: 向TC申请相关记录的全局锁
- **本地事务提交**: 业务数据和回滚日志一并提交，释放本地锁和连接资源

### 二阶段
- **提交**: 异步化，快速完成，删除undo_log记录
- **回滚**: 通过undo_log中的回滚日志进行反向补偿

## 🔒 写隔离

### 全局锁机制
- 一阶段本地事务提交前，需要确保先拿到**全局锁**
- 拿不到**全局锁**，不能提交本地事务
- 拿**全局锁**的尝试被限制在一定范围内，超出范围将放弃，并回滚本地事务

### 示例说明
两个全局事务tx1和tx2，分别对表a的字段m进行更新操作，m的初始值1000。

1. **tx1先开始**:
   - 开启本地事务，拿到本地锁
   - 更新操作: m = 1000 - 100 = 900
   - 本地事务提交前，先拿到该记录的**全局锁**
   - 本地提交释放本地锁

2. **tx2后开始**:
   - 开启本地事务，拿到本地锁
   - 更新操作: m = 900 - 100 = 800
   - 本地事务提交前，尝试拿该记录的**全局锁**
   - tx1全局提交前，该记录的全局锁被tx1持有
   - tx2需要重试等待**全局锁**

3. **tx1二阶段全局提交**:
   - 释放**全局锁**
   - tx2拿到**全局锁**提交本地事务

## 📖 读隔离

在数据库本地事务隔离级别**读已提交（Read Committed）**或以上的基础上，Tipray AT模式的默认全局隔离级别是**读未提交（Read Uncommitted）**。

如果应用在特定场景下，必需要求全局的**读已提交**，目前通过SELECT FOR UPDATE语句的代理实现：

- SELECT FOR UPDATE语句的执行会申请**全局锁**
- 如果**全局锁**被其他事务持有，则释放本地锁并重试
- 直到**全局锁**拿到，即读取的相关数据是**已提交**的，才返回

## 🏗️ 架构说明

### DLP服务（事务协调者）
```
DLP服务
├── 本地数据库操作 (Spring事务管理)
├── 分布式事务协调 (tipray-cloud-transaction)
└── 调用云服务 ──┐
                 ├── 云服务A + undo_log表
                 ├── 云服务B + undo_log表  
                 └── 云服务C + undo_log表
```

### 云服务（分支事务参与者）
```
云服务
├── 接收事务请求
├── 执行本地数据库操作
├── 自动生成UndoLog (tipray-cloud-transaction-client)
└── 响应协调者指令
```

## 📝 注解说明

### @DistributedTransaction（DLP服务使用）
```java
@DistributedTransaction(
    mode = DistributedTransactionMode.AT,  // 指定AT模式
    timeout = 30000,                       // 超时时间
    description = "AT模式分布式事务"
)
```

### @AtService（云服务使用）
```java
@AtService  // 标记AT模式服务方法
public ApiResponse<String> storeData(@RequestBody CloudServiceRequest request) {
    // 业务逻辑
}
```

## 💻 使用示例

### 1. DLP服务实现

#### 依赖配置
```xml
<dependency>
    <groupId>com.tipray</groupId>
    <artifactId>tipray-cloud-transaction</artifactId>
    <version>1.0</version>
</dependency>
```

#### 配置文件
```yaml
tipray:
  transaction:
    enabled: true
    at:
      enabled: true                    # 启用AT模式
      global-timeout: 60000            # 全局事务超时
      branch-timeout: 30000            # 分支事务超时
      cloud-services:                  # 云服务配置
        urls:
          cloud-storage-service: "http://localhost:8081"
          cloud-analysis-service: "http://localhost:8082"
          cloud-notify-service: "http://localhost:8083"
        connect-timeout: 5000          # 连接超时
        read-timeout: 10000            # 读取超时
```

#### 业务代码
```java
@Service
public class DlpDataService {

    @Autowired
    private DlpDataMapper dlpDataMapper;
    
    @Autowired
    private CloudStorageService cloudStorageService;
    
    @Autowired
    private CloudAnalysisService cloudAnalysisService;

    @DistributedTransaction(
        mode = DistributedTransactionMode.AT,
        description = "数据处理分布式事务",
        timeout = 30000
    )
    public void processData(DataRequest request) {
        // 1. 本地数据库操作 - 自动参与本地事务
        DlpDataRecord record = new DlpDataRecord();
        record.setDataId(request.getDataId());
        record.setContent(request.getContent());
        record.setCreateTime(new Date());
        dlpDataMapper.insert(record);

        // 2. 调用云服务 - 自动注册分支事务
        CloudServiceRequest cloudRequest = new CloudServiceRequest();
        cloudRequest.setDataId(request.getDataId());
        cloudRequest.setDataContent(request.getContent());
        cloudRequest.setDataType(request.getDataType());
        
        // 存储服务调用
        cloudStorageService.storeData(cloudRequest);
        
        // 分析服务调用
        cloudAnalysisService.analyzeData(cloudRequest);
        
        // 任何异常都会自动回滚本地事务和所有分支事务
        if (request.isSimulateError()) {
            throw new RuntimeException("模拟业务异常");
        }
    }
}
```

### 2. 云服务实现

#### 依赖配置
```xml
<dependency>
    <groupId>com.tipray</groupId>
    <artifactId>tipray-cloud-transaction-client</artifactId>
    <version>1.0</version>
</dependency>
```

#### 配置文件
```yaml
tipray:
  transaction:
    client:
      enabled: true
      service-name: cloud-storage-service  # 服务名称
      global-lock:
        enabled: true                      # 启用全局锁
        lock-wait-timeout: 30000           # 锁等待超时
        lock-retry-interval: 10            # 锁重试间隔
        lock-retry-times: 30               # 锁重试次数
      data-source-proxy:
        enabled: true                      # 启用数据源代理
```

#### 数据库表结构
```sql
-- undo_log表（必须创建）
CREATE TABLE `undo_log` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `branch_id` bigint(20) NOT NULL,
  `xid` varchar(100) NOT NULL,
  `context` varchar(128) NOT NULL,
  `rollback_info` longblob NOT NULL,
  `log_status` int(11) NOT NULL,
  `log_created` datetime NOT NULL,
  `log_modified` datetime NOT NULL,
  `ext` varchar(100) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `ux_undo_log` (`xid`,`branch_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- 业务表
CREATE TABLE `storage_record` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `data_id` varchar(100) NOT NULL,
  `content` text,
  `data_type` varchar(50),
  `create_time` datetime NOT NULL,
  `update_time` datetime,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_data_id` (`data_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
```

#### 业务代码
```java
@RestController
@RequestMapping("/api/storage")
public class CloudStorageController {

    @Autowired
    private StorageService storageService;

    @PostMapping("/store")
    @AtService  // 标记为AT模式服务
    public ApiResponse<String> storeData(@RequestBody CloudServiceRequest request) {
        try {
            String result = storageService.storeData(request);
            return ApiResponse.success(result);
        } catch (Exception e) {
            log.error("存储数据失败", e);
            return ApiResponse.error("存储失败: " + e.getMessage());
        }
    }
}

@Service
public class StorageService {

    @Autowired
    private StorageRecordMapper storageMapper;

    public String storeData(CloudServiceRequest request) {
        // 数据库操作会自动生成UndoLog
        StorageRecord record = new StorageRecord();
        record.setDataId(request.getDataId());
        record.setContent(request.getDataContent());
        record.setDataType(request.getDataType());
        record.setCreateTime(new Date());
        
        // 模拟业务异常
        if (request.isSimulateError()) {
            throw new RuntimeException("存储服务业务异常");
        }
        
        storageMapper.insert(record);
        
        return "STORAGE-" + record.getId();
    }
}
```

## 🔧 工作机制详解

### 一阶段执行过程

以业务SQL为例：`update product set name = 'GTS' where name = 'TXC';`

1. **解析SQL**: 得到SQL类型（UPDATE）、表（product）、条件（where name = 'TXC'）
2. **查询前镜像**: 
   ```sql
   select id, name, since from product where name = 'TXC';
   ```
   得到前镜像：
   | id | name | since |
   |----|------|-------|
   | 1  | TXC  | 2014  |

3. **执行业务SQL**: 更新这条记录的name为'GTS'
4. **查询后镜像**:
   ```sql
   select id, name, since from product where id = 1;
   ```
   得到后镜像：
   | id | name | since |
   |----|------|-------|
   | 1  | GTS  | 2014  |

5. **插入回滚日志**: 把前后镜像数据以及业务SQL相关信息组成回滚日志记录
6. **注册分支事务**: 向TC申请product表中主键值等于1的记录的**全局锁**
7. **本地事务提交**: 业务数据更新和undo_log一并提交
8. **上报结果**: 将本地事务提交结果上报给TC

### 二阶段-回滚

1. **收到回滚请求**: TC发送分支回滚请求
2. **开启本地事务**: 执行回滚操作
3. **查找undo_log**: 通过XID和Branch ID查找相应的undo_log记录
4. **数据校验**: 拿undo_log中的后镜像与当前数据比较
5. **生成回滚SQL**: 根据undo_log中的前镜像和业务SQL信息生成回滚语句：
   ```sql
   update product set name = 'TXC' where id = 1;
   ```
6. **执行回滚**: 执行回滚SQL
7. **提交本地事务**: 提交回滚操作
8. **上报结果**: 将分支事务回滚结果上报给TC

### 二阶段-提交

1. **收到提交请求**: TC发送分支提交请求
2. **异步处理**: 把请求放入异步任务队列，立即返回成功
3. **删除undo_log**: 异步批量删除相应的undo_log记录

## 🎯 AT模式特性

### 1. 自动UndoLog生成
- 所有数据库操作自动生成回滚日志
- 支持INSERT、UPDATE、DELETE操作
- 基于Seata的SQL解析和UndoLog机制

### 2. 全局锁机制
- 防止脏写问题
- 自动锁管理，无需手动干预
- 支持锁等待和超时配置

### 3. 数据源代理
- 自动代理所有数据源
- 透明的事务参与
- 支持多数据源场景

## 🎯 最佳实践

### 1. 数据库设计
- 每个表都要有主键
- 避免使用外键约束
- 合理设计索引

### 2. 事务设计
- 保持事务简短
- 避免长时间持有锁
- 合理设置超时时间

### 3. 性能优化
- 使用连接池
- 合理配置全局锁参数
- 定期清理undo_log

## 🔍 故障排查

### 常见问题
1. **UndoLog未生成**: 检查数据源代理配置
2. **全局锁获取失败**: 检查锁配置参数
3. **回滚失败**: 检查undo_log表和数据一致性
4. **性能问题**: 检查锁等待时间和数据库性能

### 解决方案
- 查看AT模式相关日志
- 检查undo_log表数据
- 使用监控界面排查
- 优化数据库和锁配置
