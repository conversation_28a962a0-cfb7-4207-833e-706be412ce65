# Saga模式

Saga模式是Tipray分布式事务提供的长事务解决方案，基于补偿机制实现分布式事务。在Saga模式中，业务流程中每个参与者都提交本地事务，当出现某一个参与者失败则补偿前面已经成功的参与者。

![Saga模式示意图](../images/saga-mode.png)

## 🎯 适用场景

- **业务流程长**: 涉及多个步骤的复杂业务流程
- **业务流程多**: 需要编排多个服务的场景
- **跨系统调用**: 参与者包含其它公司或遗留系统服务
- **最终一致性**: 对数据一致性要求相对宽松的场景

## ✨ 优势

- **一阶段提交**: 本地事务立即提交，无锁，高性能
- **事件驱动**: 参与者可异步执行，高吞吐
- **补偿服务**: 易于实现，业务语义清晰
- **灵活性**: 支持复杂的业务流程编排

## ⚠️ 缺点

- **不保证隔离性**: 中间状态对外可见
- **补偿复杂性**: 需要设计合理的补偿逻辑

## 🏗️ 工作机制

### 整体流程
1. **正向执行**: 按顺序执行各个事务步骤
2. **异常处理**: 某步骤失败时，按逆序补偿已成功的步骤
3. **最终状态**: 要么全部成功，要么全部补偿

### 事务状态
- **ACTIVE**: 事务正在执行
- **COMMITTED**: 事务成功提交
- **COMPENSATED**: 事务已补偿
- **FAILED**: 事务失败

## 📝 注解说明

### @DistributedTransaction
标记分布式事务方法，定义事务组。

```java
@DistributedTransaction(
    groupId = "",                    // 事务ID，不指定则自动生成
    mode = DistributedTransactionMode.SAGA,  // 事务模式
    timeout = 300000,                // 超时时间（毫秒）
    autoCompensate = true,           // 是否自动补偿
    description = "",                // 事务描述
    tags = {}                        // 标签
)
```

### @DistributedTransactionStep
标记事务步骤方法。

```java
@DistributedTransactionStep(
    name = "step-name",              // 步骤名称（必须）
    description = "",                // 步骤描述
    compensateMethod = "",           // 补偿方法名
    retryCount = 0,                  // 重试次数
    retryInterval = 1000,            // 重试间隔（毫秒）
    backoffMultiplier = 2.0,         // 退避倍数
    critical = true                  // 是否关键步骤
)
```

## 💻 使用示例

### 1. 注解式使用

#### 定义事务组
```java
@Service
public class OrderService {

    @Autowired
    private PaymentService paymentService;
    
    @Autowired
    private InventoryService inventoryService;

    @DistributedTransaction(
        description = "创建订单事务组",
        timeout = 60000,
        autoCompensate = true
    )
    public String createOrder(OrderRequest request) {
        // 1. 验证用户
        String userValidation = paymentService.validateUser(request.getUserId());
        
        // 2. 验证商品库存
        String productValidation = inventoryService.validateProduct(
            request.getProductId(), request.getQuantity());
        
        // 3. 创建订单
        String orderNo = createOrderRecord(request);
        
        // 4. 处理支付
        String paymentId = paymentService.processPayment(orderNo, request.getAmount());
        
        // 5. 扣减库存
        inventoryService.reduceInventory(request.getProductId(), request.getQuantity());
        
        return orderNo;
    }
}
```

#### 定义事务步骤
```java
@Service
public class PaymentService {

    @DistributedTransactionStep(
        name = "validate-user",
        description = "验证用户信息",
        compensateMethod = "compensateValidateUser",
        retryCount = 2,
        retryInterval = 1000,
        critical = true
    )
    public String validateUser(Long userId) {
        log.info("验证用户: {}", userId);
        
        if (userId == null || userId <= 0) {
            throw new RuntimeException("用户ID无效");
        }
        
        return "USER-VALIDATED-" + userId;
    }

    /**
     * 补偿方法 - 当事务回滚时自动调用
     */
    public void compensateValidateUser(Long userId, String validationResult) {
        log.info("补偿用户验证: userId={}, result={}", userId, validationResult);
        // 用户验证的补偿通常不需要特殊处理
    }

    @DistributedTransactionStep(
        name = "process-payment",
        description = "处理支付",
        compensateMethod = "refundPayment",
        retryCount = 3,
        retryInterval = 2000,
        backoffMultiplier = 2.0,
        critical = true
    )
    public String processPayment(String orderNo, BigDecimal amount) {
        log.info("处理支付: orderNo={}, amount={}", orderNo, amount);
        
        // 模拟支付处理
        String paymentId = "PAY-" + System.currentTimeMillis();
        
        // 调用第三方支付接口
        if (Math.random() < 0.1) { // 模拟10%失败率
            throw new RuntimeException("支付处理失败");
        }
        
        log.info("支付成功: paymentId={}", paymentId);
        return paymentId;
    }

    /**
     * 支付补偿方法 - 退款
     */
    public void refundPayment(String orderNo, BigDecimal amount, String paymentId) {
        log.info("执行退款: orderNo={}, amount={}, paymentId={}", orderNo, amount, paymentId);
        
        // 调用第三方退款接口
        // ...
        
        log.info("退款完成: paymentId={}", paymentId);
    }
}
```

### 2. 编程式使用

```java
@Service
public class OrderService {

    @Autowired
    private DistributedTransactionFactory transactionFactory;

    public String createOrderProgrammatically(OrderRequest request) {
        // 创建分布式事务
        DistributedTransaction transaction = transactionFactory.create(
            DistributedTransactionConfig.builder()
                .description("编程式订单创建事务")
                .timeout(60000)
                .autoCompensate(true)
                .build()
        );

        try {
            // 开始事务
            transaction.begin();

            // 添加事务步骤
            transaction.addStep("validate-user", () -> {
                return validateUser(request.getUserId());
            }, (result) -> {
                compensateValidateUser(request.getUserId(), (String) result);
            });

            transaction.addStep("create-order", () -> {
                return createOrderRecord(request);
            }, (result) -> {
                deleteOrderRecord((String) result);
            });

            // 执行事务
            transaction.execute();

            // 提交事务
            transaction.commit();

            return transaction.getResult("create-order", String.class);

        } catch (Exception e) {
            // 自动补偿
            transaction.compensate();
            throw e;
        }
    }
}
```

### 3. 非关键步骤

```java
@DistributedTransactionStep(
    name = "send-notification",
    description = "发送通知",
    compensateMethod = "cancelNotification",
    critical = false  // 非关键步骤，失败不影响整体事务
)
public void sendNotification(String orderNo) {
    log.info("发送订单通知: {}", orderNo);
    
    // 发送邮件或短信通知
    // 即使失败也不会导致整个事务回滚
    try {
        emailService.sendOrderNotification(orderNo);
        smsService.sendOrderNotification(orderNo);
    } catch (Exception e) {
        log.warn("通知发送失败，但不影响主流程: {}", e.getMessage());
    }
}

public void cancelNotification(String orderNo) {
    log.info("取消订单通知: {}", orderNo);
    // 通知的补偿逻辑
}
```

## 🔧 高级特性

### 1. 重试机制
```java
@DistributedTransactionStep(
    name = "call-external-service",
    description = "调用外部服务",
    retryCount = 3,              // 重试3次
    retryInterval = 2000,        // 初始间隔2秒
    backoffMultiplier = 2.0      // 每次重试间隔翻倍
)
public String callExternalService(String data) {
    // 第1次失败：等待2秒重试
    // 第2次失败：等待4秒重试  
    // 第3次失败：等待8秒重试
    // 第4次失败：执行补偿
    return externalService.process(data);
}
```

### 2. 条件执行
```java
@DistributedTransactionStep(
    name = "conditional-step",
    description = "条件执行步骤"
)
public String conditionalStep(OrderRequest request) {
    // 根据业务条件决定是否执行
    if (request.getAmount().compareTo(BigDecimal.valueOf(1000)) > 0) {
        return performHighValueOperation(request);
    } else {
        return performNormalOperation(request);
    }
}
```

### 3. 异步执行
```java
@DistributedTransactionStep(
    name = "async-step",
    description = "异步执行步骤",
    critical = false
)
@Async
public CompletableFuture<String> asyncStep(String data) {
    return CompletableFuture.supplyAsync(() -> {
        // 异步处理逻辑
        return processAsync(data);
    });
}
```

## 📊 监控和调试

### 1. 日志输出
```
[TXG-20231201-001] - [SAGA] 事务开始: 创建订单事务组
[TXG-20231201-001] - [SAGA] 步骤执行: validate-user
[TXG-20231201-001] - [SAGA] 步骤成功: validate-user
[TXG-20231201-001] - [SAGA] 步骤执行: process-payment
[TXG-20231201-001] - [SAGA] 步骤失败: process-payment
[TXG-20231201-001] - [SAGA] 开始补偿: validate-user
[TXG-20231201-001] - [SAGA] 补偿完成: validate-user
[TXG-20231201-001] - [SAGA] 事务补偿完成
```

### 2. 监控界面
通过监控界面可以查看：
- 事务执行状态
- 步骤执行详情
- 补偿执行情况
- 性能统计信息

## 🎯 最佳实践

### 1. 补偿设计原则
- **幂等性**: 补偿操作可以重复执行
- **可靠性**: 补偿操作必须能够成功
- **业务语义**: 补偿操作符合业务逻辑

### 2. 事务设计建议
- **保持简短**: 避免过长的事务链
- **合理分组**: 相关操作放在同一事务组
- **异常处理**: 充分考虑各种异常情况

### 3. 性能优化
- **异步执行**: 非关键步骤使用异步
- **批量操作**: 合并相似操作
- **缓存使用**: 合理使用缓存减少重复计算

## 🔍 故障排查

### 常见问题
1. **事务未启动**: 检查@DistributedTransaction注解和AOP配置
2. **步骤未执行**: 确保通过Spring Bean调用
3. **补偿未触发**: 检查compensateMethod配置
4. **重试不生效**: 验证重试参数设置

### 解决方案
- 查看详细日志
- 使用监控界面排查
- 检查配置参数
- 验证业务逻辑
