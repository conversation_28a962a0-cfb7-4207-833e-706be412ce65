# Tipray 分布式事务框架文档

欢迎使用 Tipray 分布式事务框架！本文档将帮助您快速了解和使用这个强大的分布式事务解决方案。

## 📚 文档导航

### 🎯 概述
- [什么是Tipray分布式事务](01-概述/什么是Tipray分布式事务.md) - 了解框架的基本概念和架构
- [常见问题](01-概述/常见问题.md) - 快速解决常见使用问题

### 📖 用户文档
- [快速开始](02-用户文档/快速开始.md) - 5分钟快速上手指南
- [参数配置](02-用户文档/参数配置.md) - 详细的配置参数说明
- [监控界面](02-用户文档/监控界面.md) - 可视化监控界面使用指南

### 🔄 事务模式
- [Saga模式](03-事务模式/Saga模式.md) - 基于补偿机制的长事务解决方案
- [AT模式](03-事务模式/AT模式.md) - 基于数据库事务日志的强一致性方案

### 🛠️ 开发者指南
- [API参考](04-开发者指南/API参考.md) - 完整的API接口文档

## 🚀 快速开始

### 1. 选择合适的模块

根据您的需求选择合适的模块组合：

#### 只需要Saga模式
```xml
<dependency>
    <groupId>com.tipray</groupId>
    <artifactId>tipray-cloud-transaction</artifactId>
    <version>1.0</version>
</dependency>
```

#### 需要AT模式
```xml
<!-- DLP服务 -->
<dependency>
    <groupId>com.tipray</groupId>
    <artifactId>tipray-cloud-transaction</artifactId>
    <version>1.0</version>
</dependency>

<!-- 云服务 -->
<dependency>
    <groupId>com.tipray</groupId>
    <artifactId>tipray-cloud-transaction-client</artifactId>
    <version>1.0</version>
</dependency>
```

#### 需要监控界面
```xml
<dependency>
    <groupId>com.tipray</groupId>
    <artifactId>tipray-cloud-transaction-ui-starter</artifactId>
    <version>1.0</version>
</dependency>
```

### 2. 基础配置

```yaml
tipray:
  transaction:
    enabled: true
    default-timeout: 300000
    storage:
      type: MEMORY
    monitor:
      enabled: true
```

### 3. 使用示例

#### Saga模式示例
```java
@Service
public class OrderService {

    @DistributedTransaction(
        description = "创建订单事务组",
        timeout = 60000
    )
    public String createOrder(OrderRequest request) {
        // 1. 验证用户
        paymentService.validateUser(request.getUserId());
        
        // 2. 处理支付
        paymentService.processPayment(request.getOrderNo(), request.getAmount());
        
        // 3. 扣减库存
        inventoryService.reduceInventory(request.getProductId(), request.getQuantity());
        
        return request.getOrderNo();
    }
}

@Service
public class PaymentService {

    @DistributedTransactionStep(
        name = "process-payment",
        description = "处理支付",
        compensateMethod = "refundPayment"
    )
    public String processPayment(String orderNo, BigDecimal amount) {
        // 支付逻辑
        return "PAY-" + System.currentTimeMillis();
    }

    public void refundPayment(String orderNo, BigDecimal amount, String paymentId) {
        // 退款逻辑
    }
}
```

#### AT模式示例
```java
// DLP服务
@DistributedTransaction(
    mode = DistributedTransactionMode.AT,
    description = "数据处理分布式事务"
)
public void processData(DataRequest request) {
    // 本地数据库操作
    dlpDataMapper.insert(record);
    
    // 调用云服务
    cloudStorageService.storeData(request);
}

// 云服务
@PostMapping("/store")
@AtService
public ApiResponse<String> storeData(@RequestBody CloudServiceRequest request) {
    // 数据库操作会自动生成UndoLog
    storageMapper.insert(record);
    return ApiResponse.success("STORAGE-" + record.getId());
}
```

## 🎯 核心特性

### ✨ 多模式支持
- **Saga模式**: 基于补偿机制，适用于长事务和服务编排
- **AT模式**: 基于数据库事务日志，提供强一致性保证

### 🏗️ 模块化设计
- **tipray-cloud-transaction**: 核心框架，支持Saga模式和AT模式协调
- **tipray-cloud-transaction-client**: AT模式客户端，提供数据源代理和UndoLog生成
- **tipray-cloud-transaction-ui-starter**: 可视化监控界面

### 🔧 易于使用
- **注解驱动**: 通过简单的注解即可实现分布式事务
- **编程式API**: 提供灵活的编程式事务API
- **自动配置**: Spring Boot自动配置，开箱即用

### 📊 监控和管理
- **实时监控**: 内嵌式监控界面，实时查看事务状态
- **详细日志**: 完整的事务执行日志和错误信息
- **手动干预**: 支持手动补偿和重试操作

### 🚀 高性能
- **异步处理**: 支持异步执行和批量操作
- **连接池**: 优化的数据库连接池和线程池配置
- **缓存机制**: 智能缓存减少重复计算

## 📊 架构对比

| 特性 | Saga模式 | AT模式 |
|------|---------|--------|
| **一致性** | 最终一致性 | 强一致性 |
| **性能** | 高性能 | 中等性能 |
| **复杂度** | 中等 | 低 |
| **适用场景** | 长事务、服务编排 | 数据库操作密集 |
| **隔离性** | 业务隔离 | 数据库隔离 |
| **回滚机制** | 补偿 | UndoLog |

## 🎯 选择指南

### 选择Saga模式的场景
- 业务流程复杂，涉及多个服务
- 对性能要求较高
- 可以接受最终一致性
- 需要灵活的补偿逻辑

### 选择AT模式的场景
- 主要是数据库操作
- 对数据一致性要求严格
- 希望透明的事务处理
- 现有代码改动较小

## 📖 学习路径

### 初学者
1. 阅读 [什么是Tipray分布式事务](01-概述/什么是Tipray分布式事务.md)
2. 跟随 [快速开始](02-用户文档/快速开始.md) 进行实践
3. 查看 [常见问题](01-概述/常见问题.md) 解决使用问题

### 进阶用户
1. 深入学习 [Saga模式](03-事务模式/Saga模式.md) 和 [AT模式](03-事务模式/AT模式.md)
2. 了解 [参数配置](02-用户文档/参数配置.md) 进行性能调优
3. 使用 [监控界面](02-用户文档/监控界面.md) 进行运维管理

### 开发者
1. 查看 [API参考](04-开发者指南/API参考.md) 了解完整API
2. 参考测试项目进行深度定制
3. 贡献代码和文档

## 🔗 相关资源

- **测试项目**: `tipray-at-transaction-test` - 完整的使用示例
- **源码地址**: 查看项目源码了解实现细节
- **Seata官网**: https://seata.apache.org - 了解AT模式的理论基础

## 📞 技术支持

如有问题或建议，请通过以下方式联系：

1. **查看文档**: 首先查看相关文档和FAQ
2. **参考示例**: 查看测试项目中的完整示例
3. **检查日志**: 开启详细日志进行问题排查
4. **使用监控**: 通过监控界面查看事务执行详情

---

*感谢使用 Tipray 分布式事务框架！希望这个框架能够帮助您构建更加可靠的分布式系统。*
