# tipray-cloud-transaction 配置文档

## 📋 **配置概述**

tipray-cloud-transaction框架提供了丰富的配置选项，支持不同环境和业务场景的需求。配置分为两个主要模块：

- **tipray-cloud-transaction**: 分布式事务管理器配置
- **tipray-cloud-transaction-client**: 分布式事务客户端配置

## 🔧 **tipray-cloud-transaction 配置**

### **完整配置示例**

```yaml
tipray:
  transaction:
    # 基础配置
    enabled: true                           # 是否启用分布式事务
    default-timeout: 300000                 # 默认超时时间（毫秒）
    
    # 存储配置
    storage:
      type: MEMORY                          # 存储类型: MEMORY, DATABASE
      
    # 监控配置
    monitor:
      enabled: true                         # 是否启用监控
      check-interval: 30000                 # 监控检查间隔（毫秒）
      
    # 清理配置
    cleanup:
      enabled: true                         # 是否启用自动清理
      check-interval: 1800000               # 清理检查间隔（毫秒，默认30分钟）
      retention-time: 86400000              # 数据保留时间（毫秒，默认24小时）
      batch-size: 100                       # 批量清理大小
      
    # 线程池配置
    thread-pool:
      core-pool-size: 10                    # 核心线程数
      maximum-pool-size: 50                 # 最大线程数
      keep-alive-time: 60                   # 线程空闲时间（秒）
      queue-capacity: 200                   # 队列容量
      thread-name-prefix: "tipray-tx-"      # 线程名前缀
      
    # AT模式配置
    at:
      enabled: true                         # 是否启用AT模式
      undo-log-table: "undo_log"           # UndoLog表名
      data-source-proxy: true              # 是否启用数据源代理
      global-timeout: 60000                # 全局事务超时时间（毫秒）
      branch-timeout: 30000                # 分支事务超时时间（毫秒）
      
      # 云服务配置
      cloud-services:
        connect-timeout: 5000               # 连接超时时间（毫秒）
        read-timeout: 10000                 # 读取超时时间（毫秒）
        
    # Saga模式配置
    saga:
      enabled: true                         # 是否启用Saga模式
      
    # 异常处理配置
    exception-handling:
      auto-throw-exception: true            # 是否启用自动异常处理
      
      # HTTP客户端异常处理
      http-client:
        auto-throw-exception: true          # 是否自动抛出HTTP异常
        success-status-codes: [200, 201, 202]  # 成功的HTTP状态码
        business-success-field: "success"   # 业务成功字段名
        error-message-fields: ["message", "error", "msg", "errorMessage", "description"]
        retry-on-server-error: true         # 服务器错误时是否重试
        max-retries: 3                      # 最大重试次数
        retry-interval: 1000                # 重试间隔（毫秒）
        connect-timeout: 5000               # 连接超时时间（毫秒）
        read-timeout: 30000                 # 读取超时时间（毫秒）
        
      # UndoLog异常处理
      undo-log:
        enable-detailed-error: true         # 是否启用详细异常信息
        throw-on-serialize-error: true      # 序列化失败时是否抛出异常
        throw-on-deserialize-error: true    # 反序列化失败时是否抛出异常
        throw-on-undo-error: true           # 回滚失败时是否抛出异常
        include-sql-in-error: false         # 是否在异常信息中包含SQL
        max-sql-length: 200                 # SQL语句最大长度
        
      # 全局锁异常处理
      global-lock:
        enable-detailed-error: true         # 是否启用详细异常信息
        throw-on-lock-failure: true         # 锁获取失败时是否抛出异常
        throw-on-lock-timeout: true         # 锁超时时是否抛出异常
        throw-on-lock-conflict: true        # 锁冲突时是否抛出异常
        include-conflict-tx-id: true        # 是否包含冲突事务ID
        include-wait-time: true             # 是否包含等待时间
```

### **配置项详细说明**

#### **基础配置**
| 配置项 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| `enabled` | boolean | true | 是否启用分布式事务 |
| `default-timeout` | long | 300000 | 默认超时时间（毫秒） |

#### **存储配置**
| 配置项 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| `storage.type` | enum | MEMORY | 存储类型：MEMORY, DATABASE |

#### **监控配置**
| 配置项 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| `monitor.enabled` | boolean | true | 是否启用监控 |
| `monitor.check-interval` | long | 30000 | 监控检查间隔（毫秒） |

#### **清理配置**
| 配置项 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| `cleanup.enabled` | boolean | true | 是否启用自动清理 |
| `cleanup.check-interval` | long | 1800000 | 清理检查间隔（毫秒） |
| `cleanup.retention-time` | long | 86400000 | 数据保留时间（毫秒） |
| `cleanup.batch-size` | int | 100 | 批量清理大小 |

#### **线程池配置**
| 配置项 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| `thread-pool.core-pool-size` | int | 10 | 核心线程数 |
| `thread-pool.maximum-pool-size` | int | 50 | 最大线程数 |
| `thread-pool.keep-alive-time` | long | 60 | 线程空闲时间（秒） |
| `thread-pool.queue-capacity` | int | 200 | 队列容量 |
| `thread-pool.thread-name-prefix` | String | "tipray-tx-" | 线程名前缀 |

#### **AT模式配置**
| 配置项 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| `at.enabled` | boolean | true | 是否启用AT模式 |
| `at.undo-log-table` | String | "undo_log" | UndoLog表名 |
| `at.data-source-proxy` | boolean | true | 是否启用数据源代理 |
| `at.global-timeout` | long | 60000 | 全局事务超时时间（毫秒） |
| `at.branch-timeout` | long | 30000 | 分支事务超时时间（毫秒） |
| `at.cloud-services.connect-timeout` | long | 5000 | 连接超时时间（毫秒） |
| `at.cloud-services.read-timeout` | long | 10000 | 读取超时时间（毫秒） |

#### **异常处理配置**
| 配置项 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| `exception-handling.auto-throw-exception` | boolean | true | 是否启用自动异常处理 |
| `exception-handling.http-client.max-retries` | int | 3 | 最大重试次数 |
| `exception-handling.http-client.retry-interval` | long | 1000 | 重试间隔（毫秒） |
| `exception-handling.http-client.connect-timeout` | int | 5000 | 连接超时时间（毫秒） |
| `exception-handling.http-client.read-timeout` | int | 30000 | 读取超时时间（毫秒） |

### **环境配置示例**

#### **开发环境配置**
```yaml
tipray:
  transaction:
    default-timeout: 60000                  # 开发环境缩短超时时间
    cleanup:
      check-interval: 300000                # 5分钟清理一次
      retention-time: 3600000               # 数据保留1小时
    thread-pool:
      core-pool-size: 2                     # 减少线程数
      maximum-pool-size: 10
    exception-handling:
      http-client:
        max-retries: 1                      # 减少重试次数
        retry-interval: 500
        connect-timeout: 3000
        read-timeout: 15000
```

#### **生产环境配置**
```yaml
tipray:
  transaction:
    default-timeout: 600000                 # 生产环境增加超时时间
    cleanup:
      check-interval: 3600000               # 1小时清理一次
      retention-time: 259200000             # 数据保留3天
    thread-pool:
      core-pool-size: 20                    # 增加线程数
      maximum-pool-size: 100
      queue-capacity: 500
    exception-handling:
      http-client:
        max-retries: 5                      # 增加重试次数
        retry-interval: 2000
        connect-timeout: 10000
        read-timeout: 60000
```

## 🔧 **tipray-cloud-transaction-client 配置**

### **完整配置示例**

```yaml
tipray:
  transaction:
    client:
      # 基础配置
      enabled: true                         # 是否启用分布式事务客户端
      service-name: "cloud-storage-service" # 服务名称（用作资源ID）
      branch-timeout: 30000                 # 分支事务超时时间（毫秒）

      # 全局锁配置
      global-lock:
        enabled: true                       # 是否启用全局锁
        lock-wait-timeout: 30000            # 锁等待超时时间（毫秒）
        lock-retry-interval: 10             # 锁重试间隔（毫秒）
        lock-retry-times: 30                # 锁重试次数
        lock-cleanup-interval: 60000        # 锁清理间隔（毫秒）
        lock-expire-time: 300000            # 锁过期时间（毫秒）

      # 数据源代理配置
      data-source-proxy:
        enabled: true                       # 是否启用自动数据源代理
        excludes: []                        # 排除的数据源Bean名称

      # UndoLog配置
      undo-log:
        table-name: "undo_log"             # UndoLog表名
        retention-time: 604800000          # UndoLog数据保留时间（毫秒，默认7天）
```

### **配置项详细说明**

#### **基础配置**
| 配置项 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| `enabled` | boolean | true | 是否启用分布式事务客户端 |
| `service-name` | String | "unknown-service" | 服务名称，用作资源ID |
| `branch-timeout` | long | 30000 | 分支事务超时时间（毫秒） |

#### **全局锁配置**
| 配置项 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| `global-lock.enabled` | boolean | true | 是否启用全局锁 |
| `global-lock.lock-wait-timeout` | long | 30000 | 锁等待超时时间（毫秒） |
| `global-lock.lock-retry-interval` | long | 10 | 锁重试间隔（毫秒） |
| `global-lock.lock-retry-times` | int | 30 | 锁重试次数 |
| `global-lock.lock-cleanup-interval` | long | 60000 | 锁清理间隔（毫秒） |
| `global-lock.lock-expire-time` | long | 300000 | 锁过期时间（毫秒） |

#### **数据源代理配置**
| 配置项 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| `data-source-proxy.enabled` | boolean | true | 是否启用自动数据源代理 |
| `data-source-proxy.excludes` | String[] | [] | 排除的数据源Bean名称 |

#### **UndoLog配置**
| 配置项 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| `undo-log.table-name` | String | "undo_log" | UndoLog表名 |
| `undo-log.retention-time` | long | 604800000 | UndoLog数据保留时间（毫秒） |

### **环境配置示例**

#### **开发环境配置**
```yaml
tipray:
  transaction:
    client:
      service-name: "dev-storage-service"
      branch-timeout: 15000                 # 开发环境缩短超时时间
      global-lock:
        lock-wait-timeout: 10000            # 缩短锁等待时间
        lock-retry-times: 10                # 减少重试次数
        lock-cleanup-interval: 30000        # 增加清理频率
      undo-log:
        retention-time: 86400000            # 开发环境保留1天
```

#### **生产环境配置**
```yaml
tipray:
  transaction:
    client:
      service-name: "prod-storage-service"
      branch-timeout: 60000                 # 生产环境增加超时时间
      global-lock:
        lock-wait-timeout: 60000            # 增加锁等待时间
        lock-retry-times: 50                # 增加重试次数
        lock-cleanup-interval: 300000       # 减少清理频率
        lock-expire-time: 600000            # 增加锁过期时间
      undo-log:
        retention-time: 1209600000          # 生产环境保留14天
```

## 🔧 **tipray-cloud-transaction-ui-starter 配置**

### **完整配置示例**

```yaml
tipray:
  transaction:
    ui:
      # 基础配置
      enabled: true                         # 是否启用可视化界面
      path: "/tipray-transaction-ui"        # 界面访问路径
      title: "Tipray全局事务监控"           # 页面标题

      # 显示配置
      refresh-interval: 5                   # 自动刷新间隔（秒）
      page-size: 20                         # 每页显示数量

      # 功能配置
      enable-web-socket: false              # 是否启用WebSocket（暂未实现）
      data-retention-days: 7                # 数据保留天数
```

### **配置项详细说明**

#### **UI配置**
| 配置项 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| `ui.enabled` | boolean | true | 是否启用可视化界面 |
| `ui.path` | String | "/tipray-transaction-ui" | 界面访问路径 |
| `ui.title` | String | "Tipray全局事务监控" | 页面标题 |
| `ui.refresh-interval` | int | 5 | 自动刷新间隔（秒） |
| `ui.page-size` | int | 20 | 每页显示数量 |
| `ui.enable-web-socket` | boolean | false | 是否启用WebSocket |
| `ui.data-retention-days` | int | 7 | 数据保留天数 |

## 📊 **配置优先级**

配置的优先级从高到低为：

1. **命令行参数** (`--tipray.transaction.enabled=true`)
2. **环境变量** (`TIPRAY_TRANSACTION_ENABLED=true`)
3. **application.yml/application.properties**
4. **默认配置值**

## 🚀 **配置最佳实践**

### **1. 环境隔离**
```yaml
# 使用Spring Profile进行环境隔离
spring:
  profiles:
    active: dev

---
spring:
  profiles: dev
tipray:
  transaction:
    default-timeout: 60000
    cleanup:
      retention-time: 3600000

---
spring:
  profiles: prod
tipray:
  transaction:
    default-timeout: 600000
    cleanup:
      retention-time: 259200000
```

### **2. 配置外部化**
```yaml
# 使用配置中心或外部配置文件
tipray:
  transaction:
    default-timeout: ${TRANSACTION_TIMEOUT:300000}
    thread-pool:
      core-pool-size: ${THREAD_POOL_CORE_SIZE:10}
      maximum-pool-size: ${THREAD_POOL_MAX_SIZE:50}
```

### **3. 监控配置**
```yaml
# 生产环境建议启用监控
tipray:
  transaction:
    monitor:
      enabled: true
      check-interval: 30000
    ui:
      enabled: true
      refresh-interval: 10
```

### **4. 性能调优配置**
```yaml
# 高并发场景配置
tipray:
  transaction:
    thread-pool:
      core-pool-size: 50
      maximum-pool-size: 200
      queue-capacity: 1000
    cleanup:
      batch-size: 500
      check-interval: 1800000
```

## ⚠️ **注意事项**

1. **服务名称配置**：`service-name` 必须在整个分布式系统中唯一
2. **超时时间配置**：确保分支事务超时时间小于全局事务超时时间
3. **线程池配置**：根据实际业务并发量调整线程池参数
4. **清理配置**：生产环境建议适当延长数据保留时间
5. **锁配置**：高并发场景下可能需要调整锁重试参数
6. **异常处理配置**：生产环境建议启用详细异常信息用于问题排查
