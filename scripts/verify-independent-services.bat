@echo off
chcp 65001 >nul
echo ========================================
echo Tipray 独立微服务验证脚本
echo ========================================

set SCRIPT_DIR=%~dp0
set PROJECT_ROOT=%SCRIPT_DIR%..
set SERVICES_DIR=%PROJECT_ROOT%\tipray-cloud-service

echo 项目根目录: %PROJECT_ROOT%
echo 服务目录: %SERVICES_DIR%

echo.
echo ========================================
echo 验证独立微服务配置
echo ========================================

echo 检查独立微服务列表：

set SERVICES=tipray-cloud-sms tipray-at-transaction-test tipray-cloud-offline-strategy-extend
set ALL_VALID=true

for %%s in (%SERVICES%) do (
    echo.
    echo 验证服务: %%s
    echo ----------------------------------------
    
    if not exist "%SERVICES_DIR%\%%s\pom.xml" (
        echo ❌ 错误：找不到 %%s\pom.xml
        set ALL_VALID=false
    ) else (
        echo ✅ pom.xml 存在
        
        REM 检查pom.xml是否包含独立配置
        findstr /C:"<parent>" "%SERVICES_DIR%\%%s\pom.xml" | findstr /C:"tipray-cloud-service" >nul
        if !ERRORLEVEL! equ 0 (
            echo ❌ 警告：%%s 仍然依赖父模块 tipray-cloud-service
            set ALL_VALID=false
        ) else (
            echo ✅ 已独立，不依赖父模块
        )
        
        REM 检查是否引入了tipray-cloud-bom
        findstr /C:"tipray-cloud-bom" "%SERVICES_DIR%\%%s\pom.xml" >nul
        if !ERRORLEVEL! equ 0 (
            echo ✅ 已引入 tipray-cloud-bom
        ) else (
            echo ❌ 警告：未引入 tipray-cloud-bom
            set ALL_VALID=false
        )
    )
)

echo.
echo ========================================
echo 验证构建能力
echo ========================================

echo 选择验证方式：
echo 1. 快速验证（仅检查pom.xml语法）
echo 2. 完整验证（实际构建测试）
echo 3. 跳过构建验证

set /p build_choice=请选择 (1-3): 

if "%build_choice%"=="1" (
    call :quick_validate
) else if "%build_choice%"=="2" (
    call :full_validate  
) else if "%build_choice%"=="3" (
    echo 跳过构建验证
) else (
    echo 无效选择，跳过构建验证
)

echo.
echo ========================================
echo 验证结果
echo ========================================

if "%ALL_VALID%"=="true" (
    echo ✅ 所有微服务配置正确，可以独立部署
    echo.
    echo 下一步操作建议：
    echo 1. 使用 build-independent-services.bat 构建微服务
    echo 2. 参考 docs\SVN-Externals-配置指南.md 配置SVN Externals
    echo 3. 将独立微服务提交到单独的SVN仓库
) else (
    echo ❌ 发现配置问题，请检查上述错误信息
    echo.
    echo 常见问题解决方案：
    echo 1. 确保微服务pom.xml已移除对tipray-cloud-service的parent依赖
    echo 2. 确保已引入tipray-cloud-bom进行版本管理
    echo 3. 确保所有必要的依赖都已正确配置
)

echo.
pause
exit /b 0

:quick_validate
echo.
echo 执行快速验证...
for %%s in (%SERVICES%) do (
    echo 验证 %%s 的 pom.xml 语法...
    cd /d "%SERVICES_DIR%\%%s"
    mvn help:effective-pom -q >nul 2>&1
    if !ERRORLEVEL! equ 0 (
        echo ✅ %%s pom.xml 语法正确
    ) else (
        echo ❌ %%s pom.xml 语法错误
        set ALL_VALID=false
    )
)
goto :eof

:full_validate
echo.
echo 执行完整构建验证...
echo 注意：这将需要较长时间，请确保已构建基础组件

REM 先构建基础组件
echo 构建基础组件...
cd /d "%PROJECT_ROOT%"
call mvn clean install -pl tipray-cloud-bom,tipray-cloud-common -am -DskipTests -q
if %ERRORLEVEL% neq 0 (
    echo ❌ 基础组件构建失败
    set ALL_VALID=false
    goto :eof
)

REM 验证各个微服务
for %%s in (%SERVICES%) do (
    echo 构建验证 %%s...
    cd /d "%SERVICES_DIR%\%%s"
    call mvn clean compile -DskipTests -q
    if !ERRORLEVEL! equ 0 (
        echo ✅ %%s 构建成功
    ) else (
        echo ❌ %%s 构建失败
        set ALL_VALID=false
    )
)
goto :eof
