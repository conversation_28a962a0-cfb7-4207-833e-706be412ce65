#
# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#
org.apache.seata.sqlparser.druid.mysql.MySQLOperateRecognizerHolder
org.apache.seata.sqlparser.druid.mariadb.MariadbOperateRecognizerHolder
org.apache.seata.sqlparser.druid.oracle.OracleOperateRecognizerHolder
org.apache.seata.sqlparser.druid.postgresql.PostgresqlOperateRecognizerHolder
org.apache.seata.sqlparser.druid.sqlserver.SqlServerOperateRecognizerHolder
org.apache.seata.sqlparser.druid.polardbx.PolarDBXOperateRecognizerHolder
org.apache.seata.sqlparser.druid.dm.DmOperateRecognizerHolder
org.apache.seata.sqlparser.druid.oscar.OscarOperateRecognizerHolder
org.apache.seata.sqlparser.druid.kingbase.KingbaseOperateRecognizerHolder
