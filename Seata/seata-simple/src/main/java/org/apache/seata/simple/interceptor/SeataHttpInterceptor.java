/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.apache.seata.simple.interceptor;

import org.apache.seata.core.context.RootContext;
import org.apache.seata.simple.aspect.CloudServiceCallAspect;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpRequest;
import org.springframework.http.client.ClientHttpRequestExecution;
import org.springframework.http.client.ClientHttpRequestInterceptor;
import org.springframework.http.client.ClientHttpResponse;
import org.springframework.stereotype.Component;

import java.io.IOException;

/**
 * Seata HTTP拦截器
 * 
 * 在HTTP调用中自动传递事务上下文，支持去中心化架构下的事务传播
 * 
 * <AUTHOR>
 */
@Component
public class SeataHttpInterceptor implements ClientHttpRequestInterceptor {

    private static final Logger LOGGER = LoggerFactory.getLogger(SeataHttpInterceptor.class);

    /**
     * 事务ID的HTTP头名称
     */
    public static final String TX_XID_HEADER = "TX-XID";

    /**
     * 应用ID的HTTP头名称
     */
    public static final String TX_APPLICATION_ID_HEADER = "TX-APPLICATION-ID";

    /**
     * 事务服务组的HTTP头名称
     */
    public static final String TX_SERVICE_GROUP_HEADER = "TX-SERVICE-GROUP";

    /**
     * 分支事务ID的HTTP头名称
     */
    public static final String TX_BRANCH_ID_HEADER = "TX-BRANCH-ID";

    @Override
    public ClientHttpResponse intercept(
            HttpRequest request, 
            byte[] body, 
            ClientHttpRequestExecution execution) throws IOException {
        
        // 获取当前事务上下文
        String xid = RootContext.getXID();
        
        if (xid != null) {
            // 将事务ID添加到HTTP头中
            request.getHeaders().add(TX_XID_HEADER, xid);

            // 添加TC生成的分支事务ID
            Long branchId = CloudServiceCallAspect.getBranchIdForCurrentThread();
            if (branchId != null) {
                request.getHeaders().add(TX_BRANCH_ID_HEADER, String.valueOf(branchId));
                LOGGER.debug("HTTP请求中添加TC生成的分支事务ID: xid={}, branchId={}, uri={}",
                        xid, branchId, request.getURI());
            }

            // 可以添加更多事务相关信息
            String applicationId = getApplicationId();
            if (applicationId != null) {
                request.getHeaders().add(TX_APPLICATION_ID_HEADER, applicationId);
            }

            String serviceGroup = getServiceGroup();
            if (serviceGroup != null) {
                request.getHeaders().add(TX_SERVICE_GROUP_HEADER, serviceGroup);
            }

            LOGGER.debug("HTTP请求中添加事务上下文: xid={}, branchId={}, uri={}", xid, branchId, request.getURI());
        } else {
            LOGGER.debug("当前无事务上下文，跳过事务传播: uri={}", request.getURI());
        }
        
        try {
            // 执行HTTP请求
            ClientHttpResponse response = execution.execute(request, body);
            
            // 处理响应中的事务信息
            processResponse(response, xid);
            
            return response;
        } catch (IOException e) {
            LOGGER.error("HTTP请求执行失败: uri={}, xid={}", request.getURI(), xid, e);
            throw e;
        }
    }

    /**
     * 处理HTTP响应中的事务信息
     *
     * @param response HTTP响应
     * @param xid 事务ID
     */
    private void processResponse(ClientHttpResponse response, String xid) {
        if (xid == null) {
            return;
        }
        
        try {
            // 检查响应状态
            int statusCode = response.getRawStatusCode();
            
            if (statusCode >= 200 && statusCode < 300) {
                LOGGER.debug("HTTP请求成功: xid={}, statusCode={}", xid, statusCode);
            } else {
                LOGGER.warn("HTTP请求返回错误状态: xid={}, statusCode={}", xid, statusCode);
            }
            
            // 可以从响应头中获取分支事务信息
            String branchId = response.getHeaders().getFirst("TX-BRANCH-ID");
            if (branchId != null) {
                LOGGER.debug("收到分支事务ID: xid={}, branchId={}", xid, branchId);
                // 这里可以记录分支事务信息，用于后续的提交或回滚
            }
            
        } catch (Exception e) {
            LOGGER.error("处理HTTP响应失败: xid={}", xid, e);
        }
    }

    /**
     * 获取应用ID
     * 
     * @return 应用ID
     */
    private String getApplicationId() {
        // 可以从配置中获取，或者从Spring上下文中获取
        // 这里简化实现，实际应该从配置中读取
        return "dlp-service";
    }

    /**
     * 获取事务服务组
     * 
     * @return 事务服务组
     */
    private String getServiceGroup() {
        // 可以从配置中获取，或者从Spring上下文中获取
        // 这里简化实现，实际应该从配置中读取
        return "dlp-group";
    }
}
