/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.apache.seata.simple.autoconfigure;

import org.apache.seata.simple.EmbeddedTransactionCoordinator;
import org.apache.seata.simple.aspect.CloudServiceCallAspect;
import org.apache.seata.simple.context.EmbeddedTransactionContext;
import org.apache.seata.simple.interceptor.GlobalTransactionalInterceptor;
import org.apache.seata.simple.interceptor.SeataHttpInterceptor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.client.ClientHttpRequestInterceptor;
import org.springframework.web.client.RestTemplate;

import javax.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.List;

/**
 * 内嵌Seata自动配置类
 * 
 * 为DLP服务提供内嵌TC的自动配置，支持去中心化架构
 * 
 * <AUTHOR>
 */
@Configuration
@ConditionalOnProperty(prefix = "seata.embedded", name = "enabled", havingValue = "true", matchIfMissing = false)
@EnableConfigurationProperties(EmbeddedSeataProperties.class)
public class EmbeddedSeataAutoConfiguration {

    private static final Logger LOGGER = LoggerFactory.getLogger(EmbeddedSeataAutoConfiguration.class);

    private final EmbeddedSeataProperties properties;

    public EmbeddedSeataAutoConfiguration(EmbeddedSeataProperties properties) {
        this.properties = properties;
    }

    @PostConstruct
    public void init() {
        LOGGER.info("初始化内嵌Seata配置: applicationId={}, txServiceGroup={}", 
                properties.getApplicationId(), properties.getTxServiceGroup());
    }

    /**
     * 创建内嵌事务协调器Bean
     */
    @Bean
    public EmbeddedTransactionCoordinator embeddedTransactionCoordinator() {
        LOGGER.info("创建内嵌事务协调器");
        return new EmbeddedTransactionCoordinator();
    }

    /**
     * 创建内嵌事务上下文管理器Bean
     */
    @Bean
    public EmbeddedTransactionContext embeddedTransactionContext() {
        LOGGER.info("创建内嵌事务上下文管理器");
        return new EmbeddedTransactionContext();
    }

    /**
     * 创建Seata HTTP拦截器Bean
     */
    @Bean
    public SeataHttpInterceptor seataHttpInterceptor() {
        LOGGER.info("创建Seata HTTP拦截器");
        return new SeataHttpInterceptor();
    }

    /**
     * 创建云服务调用切面Bean
     */
    @Bean
    public CloudServiceCallAspect cloudServiceCallAspect() {
        LOGGER.info("创建云服务调用切面");
        return new CloudServiceCallAspect();
    }

    /**
     * 创建全局事务拦截器Bean
     */
    @Bean
    public GlobalTransactionalInterceptor globalTransactionalInterceptor() {
        LOGGER.info("创建全局事务拦截器");
        return new GlobalTransactionalInterceptor();
    }

    /**
     * 配置RestTemplate以支持事务传播
     * 
     * 如果应用中已有RestTemplate Bean，会自动添加Seata拦截器
     */
    @Bean
    @ConditionalOnProperty(prefix = "seata.embedded", name = "auto-configure-rest-template", havingValue = "true", matchIfMissing = true)
    public RestTemplate restTemplate(SeataHttpInterceptor seataHttpInterceptor) {
        RestTemplate restTemplate = new RestTemplate();
        
        // 添加Seata HTTP拦截器
        List<ClientHttpRequestInterceptor> interceptors = new ArrayList<>();
        interceptors.add(seataHttpInterceptor);
        restTemplate.setInterceptors(interceptors);
        
        LOGGER.info("配置RestTemplate支持Seata事务传播");
        return restTemplate;
    }
}
