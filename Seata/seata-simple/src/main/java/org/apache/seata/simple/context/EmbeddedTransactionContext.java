/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.apache.seata.simple.context;

import org.apache.seata.core.context.RootContext;
import org.apache.seata.core.model.GlobalStatus;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 内嵌事务上下文管理器
 * 
 * 管理去中心化架构下的事务上下文信息，包括全局事务和分支事务的状态跟踪
 * 
 * <AUTHOR>
 */
@Component
public class EmbeddedTransactionContext {

    private static final Logger LOGGER = LoggerFactory.getLogger(EmbeddedTransactionContext.class);

    /**
     * 存储事务相关信息的本地缓存
     */
    private final Map<String, TransactionInfo> transactionCache = new ConcurrentHashMap<>();

    /**
     * 事务信息内部类
     */
    public static class TransactionInfo {
        private String xid;
        private String applicationId;
        private String txServiceGroup;
        private String txName;
        private GlobalStatus status;
        private long createTime;
        private int timeout;
        private Map<Long, BranchInfo> branches;

        public TransactionInfo(String xid, String applicationId, String txServiceGroup, String txName, int timeout) {
            this.xid = xid;
            this.applicationId = applicationId;
            this.txServiceGroup = txServiceGroup;
            this.txName = txName;
            this.timeout = timeout;
            this.status = GlobalStatus.Begin;
            this.createTime = System.currentTimeMillis();
            this.branches = new ConcurrentHashMap<>();
        }

        // Getters and Setters
        public String getXid() { return xid; }
        public String getApplicationId() { return applicationId; }
        public String getTxServiceGroup() { return txServiceGroup; }
        public String getTxName() { return txName; }
        public GlobalStatus getStatus() { return status; }
        public void setStatus(GlobalStatus status) { this.status = status; }
        public long getCreateTime() { return createTime; }
        public int getTimeout() { return timeout; }
        public Map<Long, BranchInfo> getBranches() { return branches; }
    }

    /**
     * 分支事务信息内部类
     */
    public static class BranchInfo {
        private long branchId;
        private String resourceId;
        private String lockKeys;
        private long createTime;

        public BranchInfo(long branchId, String resourceId, String lockKeys) {
            this.branchId = branchId;
            this.resourceId = resourceId;
            this.lockKeys = lockKeys;
            this.createTime = System.currentTimeMillis();
        }

        // Getters
        public long getBranchId() { return branchId; }
        public String getResourceId() { return resourceId; }
        public String getLockKeys() { return lockKeys; }
        public long getCreateTime() { return createTime; }
    }

    /**
     * 绑定事务上下文
     *
     * @param xid 全局事务ID
     * @param applicationId 应用ID
     * @param txServiceGroup 事务服务组
     * @param txName 事务名称
     * @param timeout 超时时间
     */
    public void bind(String xid, String applicationId, String txServiceGroup, String txName, int timeout) {
        TransactionInfo txInfo = new TransactionInfo(xid, applicationId, txServiceGroup, txName, timeout);
        transactionCache.put(xid, txInfo);
        
        // 绑定到Seata的根上下文
        RootContext.bind(xid);
        
        LOGGER.info("绑定事务上下文: xid={}, applicationId={}, txServiceGroup={}, txName={}", 
                xid, applicationId, txServiceGroup, txName);
    }

    /**
     * 解绑事务上下文
     *
     * @param xid 全局事务ID
     */
    public void unbind(String xid) {
        TransactionInfo txInfo = transactionCache.remove(xid);
        
        // 从Seata的根上下文中解绑
        String currentXid = RootContext.getXID();
        if (xid.equals(currentXid)) {
            RootContext.unbind();
        }
        
        if (txInfo != null) {
            LOGGER.info("解绑事务上下文: xid={}, status={}, 分支数量={}", 
                    xid, txInfo.getStatus(), txInfo.getBranches().size());
        } else {
            LOGGER.warn("尝试解绑不存在的事务上下文: xid={}", xid);
        }
    }

    /**
     * 获取事务信息
     *
     * @param xid 全局事务ID
     * @return 事务信息
     */
    public TransactionInfo getTransactionInfo(String xid) {
        return transactionCache.get(xid);
    }

    /**
     * 更新事务状态
     *
     * @param xid 全局事务ID
     * @param status 新状态
     */
    public void updateStatus(String xid, GlobalStatus status) {
        TransactionInfo txInfo = transactionCache.get(xid);
        if (txInfo != null) {
            GlobalStatus oldStatus = txInfo.getStatus();
            txInfo.setStatus(status);
            LOGGER.info("更新事务状态: xid={}, {} -> {}", xid, oldStatus, status);
        } else {
            LOGGER.warn("尝试更新不存在的事务状态: xid={}, status={}", xid, status);
        }
    }

    /**
     * 添加分支事务
     *
     * @param xid 全局事务ID
     * @param branchId 分支事务ID
     * @param resourceId 资源ID
     * @param lockKeys 锁定的键
     */
    public void addBranch(String xid, long branchId, String resourceId, String lockKeys) {
        TransactionInfo txInfo = transactionCache.get(xid);
        if (txInfo != null) {
            BranchInfo branchInfo = new BranchInfo(branchId, resourceId, lockKeys);
            txInfo.getBranches().put(branchId, branchInfo);
            LOGGER.info("添加分支事务: xid={}, branchId={}, resourceId={}", xid, branchId, resourceId);
        } else {
            LOGGER.warn("尝试向不存在的事务添加分支: xid={}, branchId={}", xid, branchId);
        }
    }

    /**
     * 移除分支事务
     *
     * @param xid 全局事务ID
     * @param branchId 分支事务ID
     */
    public void removeBranch(String xid, long branchId) {
        TransactionInfo txInfo = transactionCache.get(xid);
        if (txInfo != null) {
            BranchInfo branchInfo = txInfo.getBranches().remove(branchId);
            if (branchInfo != null) {
                LOGGER.info("移除分支事务: xid={}, branchId={}, resourceId={}", 
                        xid, branchId, branchInfo.getResourceId());
            } else {
                LOGGER.warn("尝试移除不存在的分支事务: xid={}, branchId={}", xid, branchId);
            }
        } else {
            LOGGER.warn("尝试从不存在的事务移除分支: xid={}, branchId={}", xid, branchId);
        }
    }

    /**
     * 获取当前事务ID
     *
     * @return 当前事务ID
     */
    public String getCurrentXid() {
        return RootContext.getXID();
    }

    /**
     * 检查是否在事务中
     *
     * @return 是否在事务中
     */
    public boolean inGlobalTransaction() {
        return RootContext.inGlobalTransaction();
    }

    /**
     * 清理超时的事务信息
     */
    public void cleanupTimeoutTransactions() {
        long currentTime = System.currentTimeMillis();
        
        transactionCache.entrySet().removeIf(entry -> {
            TransactionInfo txInfo = entry.getValue();
            long elapsed = currentTime - txInfo.getCreateTime();
            
            if (elapsed > txInfo.getTimeout()) {
                LOGGER.warn("清理超时事务: xid={}, elapsed={}ms, timeout={}ms", 
                        entry.getKey(), elapsed, txInfo.getTimeout());
                return true;
            }
            return false;
        });
    }

    /**
     * 获取所有活跃事务数量
     *
     * @return 活跃事务数量
     */
    public int getActiveTransactionCount() {
        return transactionCache.size();
    }
}
