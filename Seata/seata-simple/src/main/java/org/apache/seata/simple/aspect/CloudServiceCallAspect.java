/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.apache.seata.simple.aspect;

import org.apache.seata.core.context.RootContext;
import org.apache.seata.simple.EmbeddedTransactionCoordinator;
import org.apache.seata.simple.annotation.CloudServiceCall;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import java.lang.reflect.Method;
import java.util.HashMap;
import java.util.Map;

/**
 * 云服务调用切面
 * 
 * 拦截标记了@CloudServiceCall的方法，自动注册分支事务
 * 
 * <AUTHOR>
 */
@Aspect
@Component
public class CloudServiceCallAspect {

    private static final Logger LOGGER = LoggerFactory.getLogger(CloudServiceCallAspect.class);

    @Autowired
    private EmbeddedTransactionCoordinator transactionCoordinator;

    @Autowired
    private RestTemplate restTemplate;

    /**
     * 存储分支事务信息，用于回滚
     */
    private final ThreadLocal<Map<String, String>> branchInfoHolder = new ThreadLocal<>();

    @Around("@annotation(cloudServiceCall)")
    public Object around(ProceedingJoinPoint joinPoint, CloudServiceCall cloudServiceCall) throws Throwable {
        // 检查是否在全局事务中
        String xid = RootContext.getXID();
        if (xid == null) {
            LOGGER.debug("当前不在全局事务中，直接执行云服务调用: {}", joinPoint.getSignature().getName());
            return joinPoint.proceed();
        }

        String resourceId = cloudServiceCall.resourceId();
        if (resourceId.isEmpty()) {
            resourceId = cloudServiceCall.serviceName() + "-" + joinPoint.getSignature().getName();
        }

        LOGGER.info("云服务调用开始: xid={}, resourceId={}, method={}",
                xid, resourceId, joinPoint.getSignature().getName());

        Long branchId = null;
        try {
            // TC注册分支事务并生成branchId
            branchId = transactionCoordinator.branchRegister(xid, resourceId, "");
            LOGGER.info("TC生成分支事务ID: xid={}, branchId={}, resourceId={}", xid, branchId, resourceId);

            // 修改HTTP调用，传递TC生成的branchId
            Object result = proceedWithBranchId(joinPoint, xid, branchId);

            LOGGER.info("云服务调用成功: xid={}, branchId={}, resourceId={}", xid, branchId, resourceId);
            return result;

        } catch (Exception e) {
            LOGGER.error("云服务调用失败: xid={}, branchId={}, resourceId={}", xid, branchId, resourceId, e);
            // 不在这里处理回滚，让异常向上抛出，由TM决定是否回滚整个全局事务
            throw e;
        }
    }

    /**
     * 执行HTTP调用，并在请求中传递TC生成的branchId
     */
    private Object proceedWithBranchId(ProceedingJoinPoint joinPoint, String xid, Long branchId) throws Throwable {
        // 将branchId存储到线程本地变量中，供HTTP拦截器使用
        setBranchIdForCurrentThread(branchId);

        try {
            return joinPoint.proceed();
        } finally {
            // 清理线程本地变量
            clearBranchIdForCurrentThread();
        }
    }

    /**
     * 线程本地变量存储branchId
     */
    private static final ThreadLocal<Long> BRANCH_ID_HOLDER = new ThreadLocal<>();

    /**
     * 设置当前线程的branchId
     */
    public static void setBranchIdForCurrentThread(Long branchId) {
        BRANCH_ID_HOLDER.set(branchId);
    }

    /**
     * 获取当前线程的branchId
     */
    public static Long getBranchIdForCurrentThread() {
        return BRANCH_ID_HOLDER.get();
    }

    /**
     * 清理当前线程的branchId
     */
    public static void clearBranchIdForCurrentThread() {
        BRANCH_ID_HOLDER.remove();
    }
}
