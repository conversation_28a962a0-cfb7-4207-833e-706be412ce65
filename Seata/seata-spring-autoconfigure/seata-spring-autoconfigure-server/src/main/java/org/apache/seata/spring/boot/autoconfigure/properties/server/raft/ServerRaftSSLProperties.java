/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.apache.seata.spring.boot.autoconfigure.properties.server.raft;

import static org.apache.seata.spring.boot.autoconfigure.StarterConstants.SERVER_RAFT_SSL_PREFIX;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@Component
@ConfigurationProperties(prefix = SERVER_RAFT_SSL_PREFIX)
public class ServerRaftSSLProperties {

    private Boolean enabled = false;

    private String kmfAlgorithm = "SunX509";

    private String tmfAlgorithm = "SunX509";

    public Boolean getEnabled() {
        return enabled;
    }

    public void setEnabled(Boolean enabled) {
        this.enabled = enabled;
    }

    public String getKmfAlgorithm() {
        return kmfAlgorithm;
    }

    public void setKmfAlgorithm(String kmfAlgorithm) {
        this.kmfAlgorithm = kmfAlgorithm;
    }

    public String getTmfAlgorithm() {
        return tmfAlgorithm;
    }

    public void setTmfAlgorithm(String tmfAlgorithm) {
        this.tmfAlgorithm = tmfAlgorithm;
    }
}
