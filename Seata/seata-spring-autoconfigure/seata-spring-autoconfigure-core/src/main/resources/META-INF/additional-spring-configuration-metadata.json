{"groups": [], "properties": [{"name": "seata.transport.heartbeat", "type": "java.lang.Bo<PERSON>an", "sourceType": "org.apache.seata.spring.boot.autoconfigure.properties.TransportProperties", "defaultValue": true}, {"name": "seata.transport.enable-client-batch-send-request", "type": "java.lang.Bo<PERSON>an", "sourceType": "org.apache.seata.spring.boot.autoconfigure.properties.TransportProperties", "defaultValue": true}, {"name": "seata.transport.enable-tm-client-batch-send-request", "type": "java.lang.Bo<PERSON>an", "sourceType": "org.apache.seata.spring.boot.autoconfigure.properties.TransportProperties", "defaultValue": false}, {"name": "seata.transport.enable-rm-client-batch-send-request", "type": "java.lang.Bo<PERSON>an", "sourceType": "org.apache.seata.spring.boot.autoconfigure.properties.TransportProperties", "defaultValue": true}, {"name": "seata.transport.enable-tc-server-batch-send-response", "type": "java.lang.Bo<PERSON>an", "sourceType": "org.apache.seata.spring.boot.autoconfigure.properties.TransportProperties", "defaultValue": false}, {"name": "seata.transport.shutdown.wait", "type": "java.lang.Integer", "sourceType": "org.apache.seata.spring.boot.autoconfigure.properties.ShutdownProperties", "defaultValue": 3}, {"name": "seata.transport.thread-factory.boss-thread-prefix", "type": "java.lang.String", "sourceType": "org.apache.seata.spring.boot.autoconfigure.properties.ThreadFactoryProperties", "defaultValue": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "seata.transport.thread-factory.worker-thread-prefix", "type": "java.lang.String", "sourceType": "org.apache.seata.spring.boot.autoconfigure.properties.ThreadFactoryProperties", "defaultValue": "NettyServerNIOWorker"}, {"name": "seata.transport.thread-factory.server-executor-thread-prefix", "type": "java.lang.String", "sourceType": "org.apache.seata.spring.boot.autoconfigure.properties.ThreadFactoryProperties", "defaultValue": "NettyServerBizHandler"}, {"name": "seata.transport.thread-factory.client-selector-thread-prefix", "type": "java.lang.String", "sourceType": "org.apache.seata.spring.boot.autoconfigure.properties.ThreadFactoryProperties", "defaultValue": "NettyClientSelector"}, {"name": "seata.transport.thread-factory.client-selector-thread-size", "type": "java.lang.Integer", "sourceType": "org.apache.seata.spring.boot.autoconfigure.properties.ThreadFactoryProperties", "defaultValue": 1}, {"name": "seata.transport.thread-factory.client-worker-thread-prefix", "type": "java.lang.String", "sourceType": "org.apache.seata.spring.boot.autoconfigure.properties.ThreadFactoryProperties", "defaultValue": "NettyClientWorkerThread"}, {"name": "seata.transport.thread-factory.worker-thread-size", "type": "java.lang.String", "sourceType": "org.apache.seata.spring.boot.autoconfigure.properties.ThreadFactoryProperties", "defaultValue": "<PERSON><PERSON><PERSON>"}, {"name": "seata.transport.thread-factory.boss-thread-size", "type": "java.lang.Integer", "sourceType": "org.apache.seata.spring.boot.autoconfigure.properties.ThreadFactoryProperties", "defaultValue": 1}, {"name": "seata.log.exception-rate", "type": "java.lang.Integer", "sourceType": "org.apache.seata.spring.boot.autoconfigure.properties.LogProperties", "defaultValue": 100}, {"name": "seata.client.log.exception-rate", "type": "java.lang.Integer", "sourceType": "org.apache.seata.spring.boot.autoconfigure.properties.LogProperties", "deprecation": {"level": "error", "replacement": "seata.log.exception-rate", "reason": "Please configure to 'seata.log.exception-rate'."}}, {"name": "seata.transport.rpc-rm-request-timeout", "type": "java.lang.Long", "sourceType": "org.apache.seata.spring.boot.autoconfigure.properties.TransportProperties"}, {"name": "seata.transport.rpc-tm-request-timeout", "type": "java.lang.Long", "sourceType": "org.apache.seata.spring.boot.autoconfigure.properties.TransportProperties"}, {"name": "seata.transport.rpc-tc-request-timeout", "type": "java.lang.Long", "sourceType": "org.apache.seata.spring.boot.autoconfigure.properties.TransportProperties"}], "hints": [{"name": "seata.config.type", "providers": [{"name": "handle-as", "parameters": {"target": "org.apache.seata.config.ConfigType"}}]}, {"name": "seata.config.data-type", "providers": [{"name": "handle-as", "parameters": {"target": "org.apache.seata.config.processor.ConfigDataType"}}]}, {"name": "seata.registry.type", "providers": [{"name": "handle-as", "parameters": {"target": "org.apache.seata.discovery.registry.RegistryType"}}]}, {"name": "seata.transport.type", "providers": [{"name": "handle-as", "parameters": {"target": "org.apache.seata.core.rpc.TransportProtocolType"}}]}, {"name": "seata.transport.server", "providers": [{"name": "handle-as", "parameters": {"target": "org.apache.seata.core.rpc.TransportServerType"}}]}, {"name": "seata.transport.serialization", "providers": [{"name": "handle-as", "parameters": {"target": "org.apache.seata.core.serializer.SerializerType"}}]}, {"name": "seata.transport.compressor", "providers": [{"name": "handle-as", "parameters": {"target": "org.apache.seata.core.compressor.CompressorType"}}]}, {"name": "seata.transport.thread-factory.worker-thread-size", "providers": [{"name": "handle-as", "parameters": {"target": "org.apache.seata.core.rpc.netty.NettyBaseConfig$WorkThreadMode"}}]}]}