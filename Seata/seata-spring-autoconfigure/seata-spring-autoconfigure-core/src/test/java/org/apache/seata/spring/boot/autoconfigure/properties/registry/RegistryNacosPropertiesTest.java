/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.apache.seata.spring.boot.autoconfigure.properties.registry;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

public class RegistryNacosPropertiesTest {

    @Test
    public void testRegistryNacosProperties() {
        RegistryNacosProperties registryNacosProperties = new RegistryNacosProperties();
        registryNacosProperties.setServerAddr("addr");
        registryNacosProperties.setAccessKey("key");
        registryNacosProperties.setSecretKey("key");
        registryNacosProperties.setNamespace("namespace");
        registryNacosProperties.setUsername("username");
        registryNacosProperties.setPassword("password");
        registryNacosProperties.setContextPath("path");
        registryNacosProperties.setRamRoleName("ram");
        registryNacosProperties.setGroup("group");
        registryNacosProperties.setApplication("application");
        registryNacosProperties.setClientApplication("client");
        registryNacosProperties.setCluster("cluster");
        registryNacosProperties.setSlbPattern("slb");

        Assertions.assertEquals("addr", registryNacosProperties.getServerAddr());
        Assertions.assertEquals("key", registryNacosProperties.getAccessKey());
        Assertions.assertEquals("key", registryNacosProperties.getSecretKey());
        Assertions.assertEquals("namespace", registryNacosProperties.getNamespace());
        Assertions.assertEquals("username", registryNacosProperties.getUsername());
        Assertions.assertEquals("password", registryNacosProperties.getPassword());
        Assertions.assertEquals("path", registryNacosProperties.getContextPath());
        Assertions.assertEquals("ram", registryNacosProperties.getRamRoleName());
        Assertions.assertEquals("group", registryNacosProperties.getGroup());
        Assertions.assertEquals("application", registryNacosProperties.getApplication());
        Assertions.assertEquals("client", registryNacosProperties.getClientApplication());
        Assertions.assertEquals("cluster", registryNacosProperties.getCluster());
        Assertions.assertEquals("slb", registryNacosProperties.getSlbPattern());
    }
}
