#
# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#

seata.config.type=file
seata.config.data-type=bbb
seata.config.file.name=file.conf

seata.config.consul.server-addr=aaa
seata.config.consul.acl-token=bbb
seata.config.consul.key=ccc

seata.config.apollo.apollo-access-key-secret=bbb
seata.config.apollo.apollo-meta=aaa
seata.config.apollo.app-id=ccc
seata.config.apollo.namespace=ddd
seata.config.apollo.cluster=eee
seata.config.apollo.apollo-config-service=fff

seata.config.etcd3.server-addr=aaa
seata.config.etcd3.key=bbb

seata.config.nacos.namespace=seata-test-application.yml
seata.config.nacos.server-addr=aaa
seata.config.nacos.group=ccc
seata.config.nacos.username=eee
seata.config.nacos.password=fff
##if use MSE Nacos with auth, mutex with username/password attribute
#seata.config.nacos.access-key=
#seata.config.nacos.secret-key=
seata.config.nacos.data-id=bbb

seata.config.zk.server-addr=bbb
seata.config.zk.session-timeout=2
seata.config.zk.connect-timeout=1
seata.config.zk.username=ccc
seata.config.zk.password=ddd
seata.config.zk.node-path=aaa

seata.config.custom.name=aaa
