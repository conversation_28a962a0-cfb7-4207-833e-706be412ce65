#
# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#

#reduce delay for test
## transaction log store, only used in seata-server
store {
  ## store mode: file、db
  mode = "file"

  ## file store property
  file {
    ## store location dir
    dir = "sessionStore"
  }

  ## database store property
  db {
    ## the implement of javax.sql.DataSource, such as DruidDataSource(druid)/BasicDataSource(dbcp) etc.
    datasource = "dbcp"
    ## mysql/oracle/h2/oceanbase etc.
    dbType = "mysql"
    driverClassName = "com.mysql.jdbc.Driver"
    ## if using mysql to store the data, recommend add rewriteBatchedStatements=true in jdbc connection param
    url = "***************************************************************"
    user = "mysql"
    password = "mysql"
  }
}
server {
  recovery {
    #schedule committing retry period in milliseconds
    committingRetryPeriod = 100
    #schedule asyn committing retry period in milliseconds
    asynCommittingRetryPeriod = 100
    #schedule rollbacking retry period in milliseconds
    rollbackingRetryPeriod = 100
    #schedule timeout retry period in milliseconds
    timeoutRetryPeriod = 100
  }
  undo {
    logSaveDays = 2
    #schedule delete expired undo_log in milliseconds
    logDeletePeriod = 86400000
  }
}
## metrics settings
metrics {
  enabled = true
  registryType = "compact"
  # multi exporters use comma divided
  exporterList = "prometheus"
  exporterPrometheusPort = 9898
}