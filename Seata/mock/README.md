# Seata去中心化架构测试模块

## 概述

本模块提供了完整的分布式事务测试环境，包含两个服务：
- **DLP服务**（端口8081）：事务发起方，使用内嵌TC
- **云服务**（端口8082）：事务参与方，使用简化RM

## 环境准备

### 1. 数据库准备

确保MySQL服务运行，然后执行初始化脚本：

```bash
mysql -u root -p123456 < mock/sql/init.sql
```

这将创建：
- `seata_dlp` 数据库：包含订单表和undo_log表
- `seata_cloud` 数据库：包含库存表和undo_log表

### 2. 启动服务

#### 启动云服务（事务参与方）
```bash
cd mock/cloud-service
mvn spring-boot:run
```

#### 启动DLP服务（事务发起方）
```bash
cd mock/dlp-service
mvn spring-boot:run
```

## 测试场景

### 场景1：正常分布式事务（提交）

创建订单并扣减库存，事务正常提交：

```bash
curl -X POST http://localhost:8081/api/order/create \
  -H "Content-Type: application/json" \
  -d '{
    "userId": 1001,
    "productId": 1001,
    "quantity": 2,
    "amount": 19998.00,
    "shouldFail": false
  }'
```

**预期结果：**
- DLP服务创建订单成功
- 云服务库存扣减成功
- 全局事务提交
- 数据库中有新订单记录，库存减少

### 场景2：分布式事务回滚

模拟库存扣减失败，触发全局事务回滚：

```bash
curl -X POST http://localhost:8081/api/order/create \
  -H "Content-Type: application/json" \
  -d '{
    "userId": 1002,
    "productId": 1002,
    "quantity": 1,
    "amount": 12999.00,
    "shouldFail": true
  }'
```

**预期结果：**
- DLP服务创建订单
- 云服务库存扣减失败
- 全局事务回滚
- 数据库中无新订单记录，库存不变

### 场景3：库存不足回滚

尝试扣减超过库存数量，触发回滚：

```bash
curl -X POST http://localhost:8081/api/order/create \
  -H "Content-Type: application/json" \
  -d '{
    "userId": 1003,
    "productId": 1001,
    "quantity": 999,
    "amount": 999999.00,
    "shouldFail": false
  }'
```

**预期结果：**
- DLP服务创建订单
- 云服务库存不足，扣减失败
- 全局事务回滚
- 数据库中无新订单记录，库存不变

## 验证方法

### 1. 查看订单

```bash
# 查询订单（需要替换实际的订单号）
curl http://localhost:8081/api/order/ORDER_1234567890_abcd1234
```

### 2. 查看库存

```bash
# 查询商品库存
curl http://localhost:8082/api/stock/1001
```

### 3. 重置库存（测试用）

```bash
curl -X POST http://localhost:8082/api/stock/reset \
  -H "Content-Type: application/json" \
  -d '{
    "productId": 1001,
    "quantity": 100
  }'
```

### 4. 健康检查

```bash
# DLP服务健康检查
curl http://localhost:8081/api/order/health

# 云服务健康检查
curl http://localhost:8082/api/stock/health
```

## 日志观察

### DLP服务日志关键信息：
- 全局事务开始：`开始分布式事务: xid=xxx`
- 本地订单创建：`本地订单创建成功`
- HTTP调用：`库存扣减成功`
- 事务提交：`分布式事务提交成功`
- 事务回滚：`分布式事务回滚成功`

### 云服务日志关键信息：
- 事务上下文接收：`绑定事务上下文: xid=xxx`
- 分支事务注册：`注册分支事务成功: branchId=xxx`
- 库存操作：`库存扣减成功`
- 上下文清理：`解绑事务上下文`

## 数据库验证

### 查看订单数据
```sql
USE seata_dlp;
SELECT * FROM t_order ORDER BY create_time DESC LIMIT 10;
SELECT * FROM undo_log;
```

### 查看库存数据
```sql
USE seata_cloud;
SELECT * FROM t_stock;
SELECT * FROM undo_log;
```

## 架构验证要点

1. **去中心化**：无需独立的Seata Server
2. **自动化事务**：
   - DLP服务：使用`@GlobalTransactional`自动开启全局事务
   - 云服务调用：使用`@CloudServiceCall`自动注册分支事务
   - 云服务RM：使用`@Transactional`自动管理本地事务
3. **HTTP协调**：
   - 事务上下文通过HTTP头自动传递
   - TC通过HTTP接口协调云服务RM的提交/回滚
4. **代码侵入性极低**：
   - DLP服务：只需要添加注解，无需手动编程
   - 云服务：只需要正常的Spring事务注解
5. **内嵌TC**：DLP服务内嵌事务协调器，统一决策

## 故障排查

### 常见问题

1. **数据库连接失败**
   - 检查MySQL服务是否启动
   - 验证数据库用户名密码
   - 确认数据库已创建

2. **事务不生效**
   - 检查undo_log表是否存在
   - 验证数据源代理是否启用
   - 查看Seata相关日志

3. **HTTP调用失败**
   - 确认两个服务都已启动
   - 检查端口是否被占用
   - 验证网络连接

4. **事务回滚异常**
   - 查看undo_log表数据
   - 检查数据库事务隔离级别
   - 验证业务逻辑异常处理

## 性能测试

可以使用JMeter或其他工具进行并发测试：

```bash
# 并发创建订单测试
for i in {1..10}; do
  curl -X POST http://localhost:8081/api/order/create \
    -H "Content-Type: application/json" \
    -d "{\"userId\": $i, \"productId\": 1001, \"quantity\": 1, \"amount\": 9999.00, \"shouldFail\": false}" &
done
wait
```

观察：
- 事务并发处理能力
- 数据一致性保证
- 性能指标（响应时间、吞吐量）
