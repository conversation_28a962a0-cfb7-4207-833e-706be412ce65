/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.seata.mock.cloud.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.seata.mock.cloud.entity.Stock;
import com.seata.mock.cloud.mapper.StockMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;

/**
 * 库存服务
 * 
 * <AUTHOR>
 */
@Service
public class StockService {

    private static final Logger LOGGER = LoggerFactory.getLogger(StockService.class);

    @Autowired
    private StockMapper stockMapper;

    /**
     * 扣减库存（自动参与分布式事务）
     *
     * @param productId 商品ID
     * @param quantity 扣减数量
     * @param shouldFail 是否模拟失败
     * @return 是否成功
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean reduceStock(Long productId, Integer quantity, boolean shouldFail) {
        LOGGER.info("开始扣减库存: productId={}, quantity={}, shouldFail={}", productId, quantity, shouldFail);

        // 模拟失败场景
        if (shouldFail) {
            LOGGER.error("模拟库存扣减失败: productId={}, quantity={}", productId, quantity);
            throw new RuntimeException("模拟库存扣减失败");
        }

        // 查询当前库存
        Stock stock = stockMapper.selectOne(
            new QueryWrapper<Stock>().eq("product_id", productId)
        );

        if (stock == null) {
            LOGGER.error("商品不存在: productId={}", productId);
            throw new RuntimeException("商品不存在");
        }

        if (stock.getRemainQuantity() < quantity) {
            LOGGER.error("库存不足: productId={}, 需要={}, 剩余={}",
                    productId, quantity, stock.getRemainQuantity());
            throw new RuntimeException("库存不足");
        }

        // 扣减库存
        int result = stockMapper.reduceStock(productId, quantity);
        if (result <= 0) {
            LOGGER.error("库存扣减失败: productId={}, quantity={}", productId, quantity);
            throw new RuntimeException("库存扣减失败");
        }

        LOGGER.info("库存扣减成功: productId={}, quantity={}, 影响行数={}", productId, quantity, result);
        return true;
    }

    /**
     * 查询库存
     */
    public Stock getStockByProductId(Long productId) {
        return stockMapper.selectOne(
            new QueryWrapper<Stock>().eq("product_id", productId)
        );
    }

    /**
     * 初始化库存数据
     */
    @Transactional
    public void initStock(Long productId, String productName, Integer quantity) {
        Stock existStock = getStockByProductId(productId);
        if (existStock == null) {
            Stock stock = new Stock(productId, productName, quantity);
            stockMapper.insert(stock);
            LOGGER.info("初始化库存成功: {}", stock);
        } else {
            LOGGER.info("库存已存在: {}", existStock);
        }
    }

    /**
     * 重置库存（测试用）
     */
    @Transactional
    public void resetStock(Long productId, Integer quantity) {
        Stock stock = getStockByProductId(productId);
        if (stock != null) {
            stock.setQuantity(quantity);
            stock.setUsedQuantity(0);
            stock.setRemainQuantity(quantity);
            stock.setUpdateTime(LocalDateTime.now());
            stockMapper.updateById(stock);
            LOGGER.info("重置库存成功: {}", stock);
        }
    }
}
