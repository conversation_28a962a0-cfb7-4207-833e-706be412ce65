/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.seata.mock.cloud.controller;

import com.seata.mock.cloud.entity.Stock;
import com.seata.mock.cloud.service.StockService;
import org.apache.seata.simple.client.context.ClientTransactionContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * 库存控制器
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api/stock")
public class StockController {

    private static final Logger LOGGER = LoggerFactory.getLogger(StockController.class);

    @Autowired
    private StockService stockService;

    @Autowired
    private ClientTransactionContext transactionContext;

    /**
     * 扣减库存（分布式事务参与方）
     */
    @PostMapping("/reduce")
    public ResponseEntity<Map<String, Object>> reduceStock(@RequestBody Map<String, Object> request) {
        Map<String, Object> response = new HashMap<>();
        
        try {
            Long productId = Long.valueOf(request.get("productId").toString());
            Integer quantity = Integer.valueOf(request.get("quantity").toString());
            Boolean shouldFail = Boolean.valueOf(request.getOrDefault("shouldFail", false).toString());

            // 获取事务上下文信息
            String xid = transactionContext.getCurrentXid();
            boolean inTransaction = transactionContext.inGlobalTransaction();

            LOGGER.info("接收到库存扣减请求: productId={}, quantity={}, shouldFail={}, xid={}, inTransaction={}",
                    productId, quantity, shouldFail, xid, inTransaction);

            // 扣减库存
            boolean success = stockService.reduceStock(productId, quantity, shouldFail);

            if (success) {
                response.put("success", true);
                response.put("message", "库存扣减成功");
                response.put("productId", productId);
                response.put("quantity", quantity);
                
                // 如果在事务中，返回事务信息
                if (inTransaction) {
                    response.put("xid", xid);
                }
                
                LOGGER.info("库存扣减成功: productId={}, quantity={}", productId, quantity);
                return ResponseEntity.ok(response);
            } else {
                response.put("success", false);
                response.put("message", "库存扣减失败");
                return ResponseEntity.status(500).body(response);
            }

        } catch (Exception e) {
            LOGGER.error("库存扣减失败", e);
            
            response.put("success", false);
            response.put("message", "库存扣减失败: " + e.getMessage());
            
            return ResponseEntity.status(500).body(response);
        }
    }

    /**
     * 查询库存
     */
    @GetMapping("/{productId}")
    public ResponseEntity<Map<String, Object>> getStock(@PathVariable Long productId) {
        Map<String, Object> response = new HashMap<>();
        
        try {
            Stock stock = stockService.getStockByProductId(productId);
            
            if (stock != null) {
                response.put("success", true);
                response.put("stock", stock);
                LOGGER.info("查询库存成功: {}", stock);
            } else {
                response.put("success", false);
                response.put("message", "库存不存在");
                LOGGER.warn("库存不存在: productId={}", productId);
            }
            
            return ResponseEntity.ok(response);

        } catch (Exception e) {
            LOGGER.error("查询库存失败: productId={}", productId, e);
            
            response.put("success", false);
            response.put("message", "查询库存失败: " + e.getMessage());
            
            return ResponseEntity.status(500).body(response);
        }
    }

    /**
     * 初始化库存
     */
    @PostMapping("/init")
    public ResponseEntity<Map<String, Object>> initStock(@RequestBody Map<String, Object> request) {
        Map<String, Object> response = new HashMap<>();
        
        try {
            Long productId = Long.valueOf(request.get("productId").toString());
            String productName = request.get("productName").toString();
            Integer quantity = Integer.valueOf(request.get("quantity").toString());

            stockService.initStock(productId, productName, quantity);

            response.put("success", true);
            response.put("message", "库存初始化成功");
            
            LOGGER.info("库存初始化成功: productId={}, productName={}, quantity={}", 
                    productId, productName, quantity);
            
            return ResponseEntity.ok(response);

        } catch (Exception e) {
            LOGGER.error("库存初始化失败", e);
            
            response.put("success", false);
            response.put("message", "库存初始化失败: " + e.getMessage());
            
            return ResponseEntity.status(500).body(response);
        }
    }

    /**
     * 重置库存（测试用）
     */
    @PostMapping("/reset")
    public ResponseEntity<Map<String, Object>> resetStock(@RequestBody Map<String, Object> request) {
        Map<String, Object> response = new HashMap<>();
        
        try {
            Long productId = Long.valueOf(request.get("productId").toString());
            Integer quantity = Integer.valueOf(request.get("quantity").toString());

            stockService.resetStock(productId, quantity);

            response.put("success", true);
            response.put("message", "库存重置成功");
            
            LOGGER.info("库存重置成功: productId={}, quantity={}", productId, quantity);
            
            return ResponseEntity.ok(response);

        } catch (Exception e) {
            LOGGER.error("库存重置失败", e);
            
            response.put("success", false);
            response.put("message", "库存重置失败: " + e.getMessage());
            
            return ResponseEntity.status(500).body(response);
        }
    }

    /**
     * 健康检查
     */
    @GetMapping("/health")
    public ResponseEntity<Map<String, Object>> health() {
        Map<String, Object> response = new HashMap<>();
        response.put("status", "UP");
        response.put("service", "cloud-service");
        response.put("timestamp", System.currentTimeMillis());
        
        // 显示事务上下文信息
        if (transactionContext.inGlobalTransaction()) {
            response.put("inTransaction", true);
            response.put("xid", transactionContext.getCurrentXid());
        } else {
            response.put("inTransaction", false);
        }
        
        return ResponseEntity.ok(response);
    }
}
