# 云服务配置
server:
  port: 8082

spring:
  application:
    name: cloud-service

  # 数据源配置
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: *****************************************************************************************************************************************************
    username: root
    password: 123456
    hikari:
      minimum-idle: 5
      maximum-pool-size: 20
      idle-timeout: 300000
      connection-timeout: 20000

# MyBatis Plus配置
mybatis-plus:
  configuration:
    map-underscore-to-camel-case: true
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  global-config:
    db-config:
      logic-delete-field: deleted
      logic-delete-value: 1
      logic-not-delete-value: 0

# Seata简化客户端配置
seata:
  client:
    # 启用客户端Seata
    enabled: true
    # 应用ID
    application-id: cloud-service
    # 事务服务组
    tx-service-group: cloud-group
    # 是否启用自动数据源代理
    enable-auto-data-source-proxy: true
    # 数据源代理模式
    data-source-proxy-mode: AT
    # 事务超时时间（毫秒）
    transaction-timeout: 300000
    # 是否启用事务日志
    enable-transaction-log: true

# 日志配置
logging:
  level:
    root: INFO
    com.seata.mock.cloud: DEBUG
    org.apache.seata: INFO
    org.apache.seata.simple.client: DEBUG
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{50} - %msg%n"
