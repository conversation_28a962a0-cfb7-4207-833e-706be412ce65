<?xml version="1.0" encoding="UTF-8"?>
<!--

    Licensed to the Apache Software Foundation (ASF) under one or more
    contributor license agreements.  See the NOTICE file distributed with
    this work for additional information regarding copyright ownership.
    The ASF licenses this file to You under the Apache License, Version 2.0
    (the "License"); you may not use this file except in compliance with
    the License.  You may obtain a copy of the License at

        http://www.apache.org/licenses/LICENSE-2.0

    Unless required by applicable law or agreed to in writing, software
    distributed under the License is distributed on an "AS IS" BASIS,
    WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
    See the License for the specific language governing permissions and
    limitations under the License.

-->
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>org.apache.seata</groupId>
        <artifactId>seata-parent</artifactId>
        <version>${revision}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <artifactId>seata-server</artifactId>
    <packaging>jar</packaging>
    <name>seata-server ${project.version}</name>
    <description>server for Seata built with Maven</description>

    <properties>
        <spring-boot-for-server.version>2.7.18</spring-boot-for-server.version>
        <spring-framework-for-server.version>5.3.39</spring-framework-for-server.version>
        <snakeyaml-for-server.version>2.0</snakeyaml-for-server.version>
        <tomcat-embed.version>9.0.105</tomcat-embed.version>
    </properties>

    <dependencyManagement>
        <dependencies>
            <!-- junit5 -->
            <dependency>
                <groupId>org.junit</groupId>
                <artifactId>junit-bom</artifactId>
                <version>${junit-jupiter.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <!-- spring-framework-->
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-framework-bom</artifactId>
                <version>${spring-framework-for-server.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <!-- spring-boot -->
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>${spring-boot-for-server.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.apache.kafka</groupId>
                        <artifactId>kafka-clients</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.apache.tomcat.embed</groupId>
                        <artifactId>tomcat-embed-core</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.apache.tomcat.embed</groupId>
                        <artifactId>tomcat-embed-websocket</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.apache.tomcat.embed</groupId>
                        <artifactId>tomcat-embed-el</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.springframework</groupId>
                        <artifactId>spring-framework-bom</artifactId>
                    </exclusion>
                </exclusions>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>org.yaml</groupId>
                <artifactId>snakeyaml</artifactId>
                <version>${snakeyaml-for-server.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.tomcat.embed</groupId>
                <artifactId>tomcat-embed-core</artifactId>
                <version>${tomcat-embed.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <dependencies>
        <dependency>
            <groupId>org.apache.tomcat.embed</groupId>
            <artifactId>tomcat-embed-core</artifactId>
            <version>${tomcat-embed.version}</version>
        </dependency>
        <dependency>
            <groupId>org.apache.tomcat.embed</groupId>
            <artifactId>tomcat-embed-el</artifactId>
            <version>${tomcat-embed.version}</version>
        </dependency>
        <dependency>
            <groupId>org.apache.tomcat.embed</groupId>
            <artifactId>tomcat-embed-websocket</artifactId>
            <version>${tomcat-embed.version}</version>
        </dependency>
        <!-- springboot web -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>log4j-to-slf4j</artifactId>
                    <groupId>org.apache.logging.log4j</groupId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.tomcat.embed</groupId>
                    <artifactId>tomcat-embed-core</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.tomcat.embed</groupId>
                    <artifactId>tomcat-embed-websocket</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.tomcat.embed</groupId>
                    <artifactId>tomcat-embed-el</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.yaml</groupId>
                    <artifactId>snakeyaml</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.yaml</groupId>
            <artifactId>snakeyaml</artifactId>
            <version>${snakeyaml.version}</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
            <exclusions>
                <exclusion>
                    <groupId>org.junit.vintage</groupId>
                    <artifactId>junit-vintage-engine</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>${project.groupId}</groupId>
            <artifactId>seata-spring-autoconfigure-server</artifactId>
            <version>${project.version}</version>
        </dependency>

        <dependency>
            <groupId>${project.groupId}</groupId>
            <artifactId>seata-core</artifactId>
            <version>${project.version}</version>
        </dependency>
<!--

        <dependency>
            <groupId>${project.groupId}</groupId>
            <artifactId>seata-console</artifactId>
            <version>${project.version}</version>
        </dependency>

-->
        <!-- for database -->
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>druid</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-dbcp2</artifactId>
        </dependency>
        <dependency>
            <groupId>com.zaxxer</groupId>
            <artifactId>HikariCP</artifactId>
        </dependency>
        <dependency>
            <groupId>com.h2database</groupId>
            <artifactId>h2</artifactId>
            <scope>test</scope>
        </dependency>
       <!--  if you run seata-server in IDE and use mysql8 as session store, please rewrite version to ${mysql8.jdbc.version}-->
        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
            <scope>test</scope>
        </dependency>
<!--        <dependency>
            <groupId>org.mariadb.jdbc</groupId>
            <artifactId>mariadb-java-client</artifactId>
            <scope>provided</scope>
        </dependency>-->
        <dependency>
            <groupId>org.postgresql</groupId>
            <artifactId>postgresql</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dameng</groupId>
            <artifactId>DmJdbcDriver18</artifactId>
        </dependency>
        <!-- Copyright restrictions, do not reference this dependency.
             You can add this dependency to the '/seata/lib' directory of the seata-server when necessary.
        <dependency>
            <groupId>com.oracle.ojdbc</groupId>
            <artifactId>ojdbc8</artifactId>
            <version>${ojdbc.version}</version>
        </dependency>-->

        <dependency>
            <groupId>com.beust</groupId>
            <artifactId>jcommander</artifactId>
        </dependency>

        <!-- only for event bus -->
        <dependency>
            <groupId>com.google.guava</groupId>
            <artifactId>guava</artifactId>
        </dependency>

        <!-- jedis -->
        <dependency>
            <groupId>redis.clients</groupId>
            <artifactId>jedis</artifactId>
        </dependency>

        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
        </dependency>

        <!-- logback -->
        <dependency>
            <groupId>ch.qos.logback</groupId>
            <artifactId>logback-classic</artifactId>
        </dependency>
        <dependency>
            <groupId>ch.qos.logback</groupId>
            <artifactId>logback-core</artifactId>
        </dependency>
        <!-- logback appenders -->
        <dependency>
            <groupId>net.logstash.logback</groupId>
            <artifactId>logstash-logback-encoder</artifactId>
        </dependency>
        <dependency>
            <groupId>com.github.danielwegener</groupId>
            <artifactId>logback-kafka-appender</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.apache.kafka</groupId>
                    <artifactId>kafka-clients</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.apache.kafka</groupId>
            <artifactId>kafka-clients</artifactId>
            <version>${kafka-clients.version}</version>
        </dependency>
        <dependency>
            <groupId>com.alipay.sofa</groupId>
            <artifactId>jraft-core</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.alipay.sofa</groupId>
                    <artifactId>bolt</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.alipay.sofa</groupId>
            <artifactId>bolt</artifactId>
        </dependency>
        <dependency>
            <groupId>org.codehaus.janino</groupId>
            <artifactId>janino</artifactId>
        </dependency>

        <!--rate limit-->
        <dependency>
            <groupId>com.bucket4j</groupId>
            <artifactId>bucket4j_jdk8-core</artifactId>
            <version>${bucket4j.version}</version>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-deploy-plugin</artifactId>
                <configuration>
                    <skip>true</skip>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-dependency-plugin</artifactId>
                <executions>
                    <execution>
                        <id>copy-dependencies</id>
                        <phase>package</phase>
                        <goals>
                            <goal>copy-dependencies</goal>
                        </goals>
                        <configuration>
                            <outputDirectory>${project.build.directory}/lib</outputDirectory>
                            <excludeTransitive>false</excludeTransitive>
                            <stripVersion>false</stripVersion>
                            <silent>true</silent>
                            <overWriteIfNewer>true</overWriteIfNewer>
                            <!--resolve slf4j-simple conflicts-->
                            <includeScope>runtime</includeScope>
                            <excludeGroupIds>org.apache.logging.log4j,log4j,mysql</excludeGroupIds>
                            <skip>${dependencies.copy.skip}</skip>
                        </configuration>
                    </execution>
                    <execution>
                        <id>copy-mysql</id>
                        <phase>package</phase>
                        <goals>
                            <goal>copy</goal>
                        </goals>
                        <configuration>
                            <artifactItems>
                                <artifactItem>
                                    <groupId>mysql</groupId>
                                    <artifactId>mysql-connector-java</artifactId>
                                    <version>${mysql.jdbc.version}</version>
                                </artifactItem>
                                <artifactItem>
                                    <groupId>mysql</groupId>
                                    <artifactId>mysql-connector-java</artifactId>
                                    <version>${mysql8.jdbc.version}</version>
                                </artifactItem>
                            </artifactItems>
                            <outputDirectory>
                                ${project.build.directory}/lib/jdbc
                            </outputDirectory>
                            <skip>${mysql.copy.skip}</skip>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>com.google.cloud.tools</groupId>
                <artifactId>jib-maven-plugin</artifactId>
                <version>${jib-maven-plugin.version}</version>
                <configuration>
                    <from>
                        <image>${image.name}</image>
                        <platforms>
                            <platform>
                                <os>linux</os>
                                <architecture>amd64</architecture>
                            </platform>
                            <platform>
                                <os>linux</os>
                                <architecture>arm64</architecture>
                            </platform>
                        </platforms>
                    </from>
                    <to>
                        <image>docker.io/apache/seata-server</image>
                        <tags>${image.tags}</tags>
                        <auth>
                            <username>${REGISTRY_USERNAME}</username>
                            <password>${REGISTRY_PASSWORD}</password>
                        </auth>
                    </to>
                    <container>
                        <appRoot>/seata-server</appRoot>
                        <workingDirectory>/seata-server</workingDirectory>
                        <ports>
                            <port>8091</port>
                            <port>7091</port>
                        </ports>
                        <labels>
                            <name>seata-server</name>
                            <git.commit.message.full>${git.commit.message.full}</git.commit.message.full>
                            <git.remote.origin.url>${git.remote.origin.url}</git.remote.origin.url>
                            <git.commit.id>${git.commit.id}</git.commit.id>
                            <git.commit.time>${git.commit.time}</git.commit.time>
                            <git.branch>${git.branch}</git.branch>
                            <git.build.time>${git.build.time}</git.build.time>
                            <git.build.version>${git.build.version}</git.build.version>
                            <git.dirty>${git.dirty}</git.dirty>
                            <mvn.build.version>${project.version}</mvn.build.version>
                        </labels>
                        <creationTime>USE_CURRENT_TIMESTAMP</creationTime>
                        <entrypoint>
                            <arg>/bin/bash</arg>
                            <arg>/seata-server-entrypoint.sh</arg>
                        </entrypoint>
                        <environment>
                            <TZ>Asia/Shanghai</TZ>
                        </environment>
                    </container>
                    <extraDirectories>
                        <paths>
                            <path>
                                <from>src/main/resources/jdbc</from>
                                <into>/seata-server/libs/jdbc</into>
                            </path>
                            <path>
                                <from>src/main/resources/docker</from>
                                <includes>seata-server-entrypoint.sh</includes>
                            </path>
                            <path>
                                <from>../distribution/bin</from>
                                <includes>seata-setup.sh</includes>
                            </path>
                        </paths>
                    </extraDirectories>
                    <skip>${image.publish.skip}</skip>
                    <allowInsecureRegistries>true</allowInsecureRegistries>
                </configuration>
                <executions>
                    <execution>
                        <phase>package</phase>
                        <goals>
                            <goal>build</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

    <profiles>
        <profile>
            <id>release-seata</id>
            <properties>
                <mysql.jdbc.version>5.1.42</mysql.jdbc.version>
                <mysql8.jdbc.version>8.0.27</mysql8.jdbc.version>
                <dependencies.copy.skip>false</dependencies.copy.skip>
            </properties>
            <build>
                <finalName>seata-server</finalName>
                <plugins>
                    <plugin>
                        <groupId>org.springframework.boot</groupId>
                        <artifactId>spring-boot-maven-plugin</artifactId>
                        <version>${spring-boot-for-server.version}</version>
                        <configuration>
                            <mainClass>org.apache.seata.server.ServerApplication</mainClass>
                            <layout>ZIP</layout>
                            <attach>false</attach>
                            <classifier>exec</classifier>
                            <includes>
                                <include>
                                    <groupId>null</groupId>
                                    <artifactId>null</artifactId>
                                </include>
                            </includes>
                        </configuration>
                        <executions>
                            <execution>
                                <goals>
                                    <goal>repackage</goal>
                                </goals>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
            </build>
        </profile>
    </profiles>
</project>
