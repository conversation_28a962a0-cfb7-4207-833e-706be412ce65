<?xml version="1.0" encoding="UTF-8"?>
<!--

    Licensed to the Apache Software Foundation (ASF) under one or more
    contributor license agreements.  See the NOTICE file distributed with
    this work for additional information regarding copyright ownership.
    The ASF licenses this file to You under the Apache License, Version 2.0
    (the "License"); you may not use this file except in compliance with
    the License.  You may obtain a copy of the License at

        http://www.apache.org/licenses/LICENSE-2.0

    Unless required by applicable law or agreed to in writing, software
    distributed under the License is distributed on an "AS IS" BASIS,
    WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
    See the License for the specific language governing permissions and
    limitations under the License.

-->
<configuration scan="true" scanPeriod="60 seconds" debug="false">
    <!-- Context listeners -->
    <contextListener class="org.apache.seata.server.logging.listener.SystemPropertyLoggerContextListener"/>

    <!-- The conversion rules are copied from `defaults.xml` in the `spring-boot-xxx.jar` -->
    <conversionRule conversionWord="clr" converterClass="org.springframework.boot.logging.logback.ColorConverter" />
    <conversionRule conversionWord="wex" converterClass="org.springframework.boot.logging.logback.WhitespaceThrowableProxyConverter" />
    <conversionRule conversionWord="wEx" converterClass="org.springframework.boot.logging.logback.ExtendedWhitespaceThrowableProxyConverter" />
    <!-- The custom conversion rules -->
    <conversionRule conversionWord="wEx2" converterClass="org.apache.seata.server.logging.logback.ExtendedArrowThrowableProxyConverter"/>

    <!-- common properties -->
    <springProperty name="PORT" source="server.port" defaultValue="7091"/>
    <springProperty name="LOG_BASH_DIR" source="spring.config.additional-location" defaultValue="" />
    <springProperty name="APPLICATION_NAME" source="spring.application.name" defaultValue="seata-server"/>

    <!-- Appender enable properties -->
    <springProperty name="LOGSTASH_APPENDER_ENABLED" source="logging.extend.logstash-appender.enabled" defaultValue="false"/>
    <springProperty name="KAFKA_APPENDER_ENABLED" source="logging.extend.kafka-appender.enabled" defaultValue="false"/>
    <springProperty name="METRIC_APPENDER_ENABLED" source="logging.extend.metric-appender.enabled" defaultValue="false"/>

    <if condition='property("LOG_BASH_DIR").equals("")' >
        <then>
            <!-- console-appender -->
            <include resource="logback/console-appender.xml"/>

            <!-- file-appender -->
            <include resource="logback/file-appender.xml"/>

            <!-- logstash-appender: off by default -->
            <if condition='property("LOGSTASH_APPENDER_ENABLED").equals("true")'>
                <then>
                    <include resource="logback/logstash-appender.xml"/>
                </then>
            </if>

            <!-- metric-appender: off by default -->
            <if condition='property("METRIC_APPENDER_ENABLED").equals("true")'>
                <then>
                    <include resource="logback/metric-appender.xml"/>
                </then>
            </if>

            <!-- kafka-appender: off by default -->
            <if condition='property("KAFKA_APPENDER_ENABLED").equals("true")'>
                <then>
                    <include resource="logback/kafka-appender.xml"/>
                </then>
            </if>
        </then>
        <else>
            <include file="${LOG_BASH_DIR}/logback/console-appender.xml"/>
            <include file="${LOG_BASH_DIR}/logback/file-appender.xml"/>

            <!-- logstash-appender: off by default -->
            <if condition='property("LOGSTASH_APPENDER_ENABLED").equals("true")'>
                <then>
                    <include resource="logback/logstash-appender.xml"/>
                </then>
            </if>

            <!-- metric-appender: off by default -->
            <if condition='property("METRIC_APPENDER_ENABLED").equals("true")'>
                <then>
                    <include resource="logback/metric-appender.xml"/>
                </then>
            </if>

            <!-- kafka-appender: off by default -->
            <if condition='property("KAFKA_APPENDER_ENABLED").equals("true")'>
                <then>
                    <include resource="logback/kafka-appender.xml"/>
                </then>
            </if>
        </else>
    </if>



    <appender name="ASYNC_CONSOLE" class="ch.qos.logback.classic.AsyncAppender">
        <!-- console-appender -->
        <appender-ref ref="CONSOLE"/>
        <includeCallerData>true</includeCallerData>
        <discardingThreshold>0</discardingThreshold>
        <queueSize>2048</queueSize>
        <neverBlock>true</neverBlock>
    </appender>
    <appender name="ASYNC_FILE_ALL" class="ch.qos.logback.classic.AsyncAppender">
        <!-- file-appender -->
        <appender-ref ref="FILE_ALL"/>
        <includeCallerData>true</includeCallerData>
        <discardingThreshold>0</discardingThreshold>
        <queueSize>2048</queueSize>
        <neverBlock>true</neverBlock>
    </appender>
    <appender name="ASYNC_FILE_WARN" class="ch.qos.logback.classic.AsyncAppender">
        <appender-ref ref="FILE_WARN"/>
        <includeCallerData>true</includeCallerData>
        <discardingThreshold>0</discardingThreshold>
        <queueSize>1024</queueSize>
        <neverBlock>true</neverBlock>
    </appender>
    <appender name="ASYNC_FILE_ERROR" class="ch.qos.logback.classic.AsyncAppender">
        <appender-ref ref="FILE_ERROR"/>
        <includeCallerData>true</includeCallerData>
        <discardingThreshold>0</discardingThreshold>
        <queueSize>1024</queueSize>
        <neverBlock>true</neverBlock>
    </appender>

    <root level="INFO">
        <appender-ref ref="ASYNC_CONSOLE"/>
        <appender-ref ref="ASYNC_FILE_ALL"/>
        <appender-ref ref="ASYNC_FILE_WARN"/>
        <appender-ref ref="ASYNC_FILE_ERROR"/>

        <!-- logstash-appender: off by default -->
        <if condition='property("LOGSTASH_APPENDER_ENABLED").equals("true")'>
            <then>
                <appender-ref ref="LOGSTASH"/>
            </then>
        </if>

        <!-- kafka-appender: off by default -->
        <if condition='property("KAFKA_APPENDER_ENABLED").equals("true")'>
            <then>
                <appender-ref ref="KAFKA"/>
            </then>
        </if>

        <!-- metric-appender: off by default -->
        <if condition='property("METRIC_APPENDER_ENABLED").equals("true")'>
            <then>
                <appender-ref ref="METRIC"/>
            </then>
        </if>

    </root>
    <logger name="org.springframework.security.config.annotation.web.builders.WebSecurity" level="ERROR"/>
    <logger name="org.springframework.security.web.DefaultSecurityFilterChain" level="ERROR"/>
</configuration>
