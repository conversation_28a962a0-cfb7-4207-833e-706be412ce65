/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.apache.seata.rm.datasource.sql.struct;

import java.util.ArrayList;
import java.util.List;


/**
 * The type Row.
 *
 */
public class Row implements java.io.Serializable {

    private static final long serialVersionUID = 6532477221179419451L;

    private List<Field> fields = new ArrayList<Field>();

    /**
     * Instantiates a new Row.
     */
    public Row() {
    }

    /**
     * Gets fields.
     *
     * @return the fields
     */
    public List<Field> getFields() {
        return fields;
    }

    /**
     * Sets fields.
     *
     * @param fields the fields
     */
    public void setFields(List<Field> fields) {
        this.fields = fields;
    }

    /**
     * Add.
     *
     * @param field the field
     */
    public void add(Field field) {
        fields.add(field);
    }

    /**
     * Primary keys list.
     *
     * @return the Primary keys list
     */
    public List<Field> primaryKeys() {
        List<Field> pkFields = new ArrayList<>();
        for (Field field : fields) {
            if (KeyType.PRIMARY_KEY == field.getKeyType()) {
                pkFields.add(field);
            }
        }
        return pkFields;
    }

    /**
     * Non-primary keys list.
     *
     * @return the non-primary list
     */
    public List<Field> nonPrimaryKeys() {
        List<Field> nonPkFields = new ArrayList<>();
        for (Field field : fields) {
            if (KeyType.PRIMARY_KEY != field.getKeyType()) {
                nonPkFields.add(field);
            }
        }
        return nonPkFields;
    }
}
