/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.apache.seata.simple.client.autoconfigure;

import org.springframework.boot.context.properties.ConfigurationProperties;

import java.util.Arrays;
import java.util.List;

/**
 * 客户端Seata配置属性
 * 
 * 定义去中心化架构下简化RM的配置参数
 * 
 * <AUTHOR>
 */
@ConfigurationProperties(prefix = "seata.client")
public class ClientSeataProperties {

    /**
     * 是否启用客户端Seata
     */
    private boolean enabled = false;

    /**
     * 应用ID
     */
    private String applicationId = "cloud-service";

    /**
     * 事务服务组
     */
    private String txServiceGroup = "cloud-group";

    /**
     * 是否启用自动数据源代理
     */
    private boolean enableAutoDataSourceProxy = true;

    /**
     * 数据源代理模式
     */
    private String dataSourceProxyMode = "AT";

    /**
     * 是否启用事务拦截器
     */
    private boolean enableTransactionInterceptor = true;

    /**
     * 拦截器路径模式
     */
    private List<String> interceptorPathPatterns = Arrays.asList("/api/**");

    /**
     * 拦截器排除路径模式
     */
    private List<String> interceptorExcludePatterns = Arrays.asList(
            "/health/**", 
            "/actuator/**", 
            "/swagger-ui/**", 
            "/v3/api-docs/**"
    );

    /**
     * 事务超时清理间隔（毫秒）
     */
    private long transactionTimeoutCleanupInterval = 60000;

    /**
     * 事务超时时间（毫秒）
     */
    private long transactionTimeout = 300000;

    /**
     * 是否启用分支事务自动注册
     */
    private boolean enableAutoBranchRegister = true;

    /**
     * 资源ID前缀
     */
    private String resourceIdPrefix = "cloud-service";

    /**
     * 是否启用事务日志
     */
    private boolean enableTransactionLog = true;

    /**
     * 日志级别
     */
    private String logLevel = "INFO";

    // Getters and Setters
    public boolean isEnabled() { return enabled; }
    public void setEnabled(boolean enabled) { this.enabled = enabled; }
    public String getApplicationId() { return applicationId; }
    public void setApplicationId(String applicationId) { this.applicationId = applicationId; }
    public String getTxServiceGroup() { return txServiceGroup; }
    public void setTxServiceGroup(String txServiceGroup) { this.txServiceGroup = txServiceGroup; }
    public boolean isEnableAutoDataSourceProxy() { return enableAutoDataSourceProxy; }
    public void setEnableAutoDataSourceProxy(boolean enableAutoDataSourceProxy) { this.enableAutoDataSourceProxy = enableAutoDataSourceProxy; }
    public String getDataSourceProxyMode() { return dataSourceProxyMode; }
    public void setDataSourceProxyMode(String dataSourceProxyMode) { this.dataSourceProxyMode = dataSourceProxyMode; }
    public boolean isEnableTransactionInterceptor() { return enableTransactionInterceptor; }
    public void setEnableTransactionInterceptor(boolean enableTransactionInterceptor) { this.enableTransactionInterceptor = enableTransactionInterceptor; }
    public List<String> getInterceptorPathPatterns() { return interceptorPathPatterns; }
    public void setInterceptorPathPatterns(List<String> interceptorPathPatterns) { this.interceptorPathPatterns = interceptorPathPatterns; }
    public List<String> getInterceptorExcludePatterns() { return interceptorExcludePatterns; }
    public void setInterceptorExcludePatterns(List<String> interceptorExcludePatterns) { this.interceptorExcludePatterns = interceptorExcludePatterns; }
    public long getTransactionTimeoutCleanupInterval() { return transactionTimeoutCleanupInterval; }
    public void setTransactionTimeoutCleanupInterval(long transactionTimeoutCleanupInterval) { this.transactionTimeoutCleanupInterval = transactionTimeoutCleanupInterval; }
    public long getTransactionTimeout() { return transactionTimeout; }
    public void setTransactionTimeout(long transactionTimeout) { this.transactionTimeout = transactionTimeout; }
    public boolean isEnableAutoBranchRegister() { return enableAutoBranchRegister; }
    public void setEnableAutoBranchRegister(boolean enableAutoBranchRegister) { this.enableAutoBranchRegister = enableAutoBranchRegister; }
    public String getResourceIdPrefix() { return resourceIdPrefix; }
    public void setResourceIdPrefix(String resourceIdPrefix) { this.resourceIdPrefix = resourceIdPrefix; }
    public boolean isEnableTransactionLog() { return enableTransactionLog; }
    public void setEnableTransactionLog(boolean enableTransactionLog) { this.enableTransactionLog = enableTransactionLog; }
    public String getLogLevel() { return logLevel; }
    public void setLogLevel(String logLevel) { this.logLevel = logLevel; }
}
