/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.apache.seata.simple.client.context;

import org.apache.seata.core.context.RootContext;
import org.apache.seata.simple.client.SimpleResourceManager;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 客户端事务上下文管理器
 * 
 * 管理云服务中的事务上下文，支持临时RM连接和分支事务处理
 * 
 * <AUTHOR>
 */
@Component
public class ClientTransactionContext {

    private static final Logger LOGGER = LoggerFactory.getLogger(ClientTransactionContext.class);

    @Autowired
    private SimpleResourceManager simpleResourceManager;

    /**
     * 线程本地存储，用于保存当前线程的事务信息
     */
    private final ThreadLocal<TransactionInfo> currentTransaction = new ThreadLocal<>();

    /**
     * 事务信息缓存
     */
    private final Map<String, TransactionInfo> transactionCache = new ConcurrentHashMap<>();

    /**
     * 事务信息内部类
     */
    public static class TransactionInfo {
        private String xid;
        private String applicationId;
        private String serviceGroup;
        private Long branchId;
        private String resourceId;
        private long createTime;
        private boolean active;

        public TransactionInfo(String xid, String applicationId, String serviceGroup) {
            this.xid = xid;
            this.applicationId = applicationId;
            this.serviceGroup = serviceGroup;
            this.createTime = System.currentTimeMillis();
            this.active = true;
        }

        // Getters and Setters
        public String getXid() { return xid; }
        public String getApplicationId() { return applicationId; }
        public String getServiceGroup() { return serviceGroup; }
        public Long getBranchId() { return branchId; }
        public void setBranchId(Long branchId) { this.branchId = branchId; }
        public String getResourceId() { return resourceId; }
        public void setResourceId(String resourceId) { this.resourceId = resourceId; }
        public long getCreateTime() { return createTime; }
        public boolean isActive() { return active; }
        public void setActive(boolean active) { this.active = active; }
    }

    /**
     * 绑定事务上下文
     *
     * @param xid 全局事务ID
     * @param applicationId 应用ID
     * @param serviceGroup 事务服务组
     */
    public void bind(String xid, String applicationId, String serviceGroup) {
        if (xid == null || xid.trim().isEmpty()) {
            LOGGER.warn("尝试绑定空的事务ID");
            return;
        }

        try {
            // 创建事务信息
            TransactionInfo txInfo = new TransactionInfo(xid, applicationId, serviceGroup);
            
            // 保存到线程本地存储
            currentTransaction.set(txInfo);
            
            // 保存到缓存
            transactionCache.put(xid, txInfo);
            
            // 绑定到Seata根上下文
            RootContext.bind(xid);
            
            LOGGER.info("绑定客户端事务上下文: xid={}, applicationId={}, serviceGroup={}", 
                    xid, applicationId, serviceGroup);
            
        } catch (Exception e) {
            LOGGER.error("绑定客户端事务上下文失败: xid={}", xid, e);
            throw new RuntimeException("绑定事务上下文失败", e);
        }
    }

    /**
     * 解绑事务上下文
     *
     * @param xid 全局事务ID
     */
    public void unbind(String xid) {
        if (xid == null || xid.trim().isEmpty()) {
            return;
        }

        try {
            // 从线程本地存储中移除
            TransactionInfo txInfo = currentTransaction.get();
            if (txInfo != null && xid.equals(txInfo.getXid())) {
                currentTransaction.remove();
            }
            
            // 从缓存中移除
            TransactionInfo removed = transactionCache.remove(xid);
            
            // 从Seata根上下文中解绑
            String currentXid = RootContext.getXID();
            if (xid.equals(currentXid)) {
                RootContext.unbind();
            }
            
            if (removed != null) {
                LOGGER.info("解绑客户端事务上下文: xid={}, branchId={}", xid, removed.getBranchId());
            } else {
                LOGGER.warn("尝试解绑不存在的事务上下文: xid={}", xid);
            }
            
        } catch (Exception e) {
            LOGGER.error("解绑客户端事务上下文失败: xid={}", xid, e);
        }
    }

    /**
     * 设置分支事务ID（由TC生成并传递）
     *
     * @param branchId TC生成的分支事务ID
     */
    public void setBranchId(Long branchId) {
        TransactionInfo txInfo = currentTransaction.get();
        if (txInfo != null) {
            txInfo.setBranchId(branchId);
            LOGGER.info("设置TC生成的分支事务ID: xid={}, branchId={}", txInfo.getXid(), branchId);
        } else {
            LOGGER.warn("当前无事务上下文，无法设置分支事务ID: branchId={}", branchId);
        }
    }

    /**
     * 获取当前事务信息
     *
     * @return 当前事务信息
     */
    public TransactionInfo getCurrentTransaction() {
        return currentTransaction.get();
    }

    /**
     * 获取当前事务ID
     *
     * @return 当前事务ID
     */
    public String getCurrentXid() {
        TransactionInfo txInfo = currentTransaction.get();
        return txInfo != null ? txInfo.getXid() : null;
    }

    /**
     * 获取当前分支事务ID
     *
     * @return 当前分支事务ID
     */
    public Long getCurrentBranchId() {
        TransactionInfo txInfo = currentTransaction.get();
        return txInfo != null ? txInfo.getBranchId() : null;
    }

    /**
     * 检查是否在事务中
     *
     * @return 是否在事务中
     */
    public boolean inGlobalTransaction() {
        return currentTransaction.get() != null && RootContext.inGlobalTransaction();
    }

    /**
     * 获取事务信息
     *
     * @param xid 全局事务ID
     * @return 事务信息
     */
    public TransactionInfo getTransactionInfo(String xid) {
        return transactionCache.get(xid);
    }

    /**
     * 清理超时的事务信息
     *
     * @param timeoutMillis 超时时间（毫秒）
     */
    public void cleanupTimeoutTransactions(long timeoutMillis) {
        long currentTime = System.currentTimeMillis();
        
        transactionCache.entrySet().removeIf(entry -> {
            TransactionInfo txInfo = entry.getValue();
            long elapsed = currentTime - txInfo.getCreateTime();
            
            if (elapsed > timeoutMillis) {
                LOGGER.warn("清理超时事务: xid={}, elapsed={}ms, timeout={}ms", 
                        entry.getKey(), elapsed, timeoutMillis);
                return true;
            }
            return false;
        });
    }

    /**
     * 获取活跃事务数量
     *
     * @return 活跃事务数量
     */
    public int getActiveTransactionCount() {
        return transactionCache.size();
    }
}
