# 云服务客户端Seata配置示例
seata:
  client:
    # 启用客户端Seata
    enabled: true
    # 应用ID
    application-id: cloud-service
    # 事务服务组
    tx-service-group: cloud-group
    # 是否启用自动数据源代理
    enable-auto-data-source-proxy: true
    # 数据源代理模式
    data-source-proxy-mode: AT
    # 是否启用事务拦截器
    enable-transaction-interceptor: true
    # 拦截器路径模式
    interceptor-path-patterns:
      - "/api/**"
      - "/business/**"
    # 拦截器排除路径模式
    interceptor-exclude-patterns:
      - "/health/**"
      - "/actuator/**"
      - "/swagger-ui/**"
      - "/v3/api-docs/**"
    # 事务超时清理间隔（毫秒）
    transaction-timeout-cleanup-interval: 60000
    # 事务超时时间（毫秒）
    transaction-timeout: 300000
    # 是否启用分支事务自动注册
    enable-auto-branch-register: true
    # 资源ID前缀
    resource-id-prefix: cloud-service
    # 是否启用事务日志
    enable-transaction-log: true
    # 日志级别
    log-level: INFO

# Spring Boot配置
spring:
  application:
    name: cloud-service

# 日志配置
logging:
  level:
    org.apache.seata: INFO
    org.apache.seata.simple.client: DEBUG
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{50} - %msg%n"
