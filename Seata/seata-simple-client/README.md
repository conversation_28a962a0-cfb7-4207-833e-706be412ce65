# Seata Simple Client - 简化RM模块

## 概述

`seata-simple-client` 是为云服务设计的简化资源管理器（RM）模块，支持去中心化Seata架构。该模块提供轻量级的RM功能，支持临时连接和AT模式事务处理。

## 主要特性

- **简化RM功能**：提供轻量级的资源管理器实现
- **临时连接模式**：按需创建和销毁RM连接
- **HTTP事务接收**：自动接收和处理HTTP传递的事务上下文
- **AT模式支持**：专注于AT模式分支事务处理
- **Spring Boot集成**：提供自动配置和拦截器支持

## 核心组件

### SimpleResourceManager
简化资源管理器，提供以下功能：
- 注册分支事务
- 提交/回滚分支事务
- 管理资源和分支事务信息
- 支持锁查询

### TransactionInterceptor
事务拦截器，自动处理HTTP请求中的事务上下文：
- 从HTTP头中提取事务信息
- 绑定事务上下文到当前线程
- 请求完成后自动清理上下文
- 返回分支事务信息

### ClientTransactionContext
客户端事务上下文管理器：
- 管理当前线程的事务信息
- 支持分支事务注册
- 提供事务状态查询
- 支持事务超时清理

## 使用方法

### 1. 添加依赖

```xml
<dependency>
    <groupId>org.apache.seata</groupId>
    <artifactId>seata-simple-client</artifactId>
    <version>${seata.version}</version>
</dependency>
```

### 2. 配置文件

在 `application.yml` 中添加配置：

```yaml
seata:
  client:
    enabled: true
    application-id: cloud-service
    tx-service-group: cloud-group
    enable-auto-data-source-proxy: true
    enable-transaction-interceptor: true
    interceptor-path-patterns:
      - "/api/**"
```

### 3. 使用示例

```java
@RestController
@RequestMapping("/api")
public class BusinessController {
    
    @Autowired
    private ClientTransactionContext transactionContext;
    
    @Autowired
    private BusinessService businessService;
    
    @PostMapping("/business")
    @Transactional
    public ResponseEntity<?> handleBusiness(@RequestBody BusinessRequest request) {
        // 事务上下文已由拦截器自动绑定
        
        if (transactionContext.inGlobalTransaction()) {
            // 在全局事务中执行业务逻辑
            String xid = transactionContext.getCurrentXid();
            
            // 注册分支事务（如果需要）
            Long branchId = transactionContext.registerBranch("cloud-db", "business-table");
            
            // 执行业务逻辑
            businessService.processRequest(request);
            
            return ResponseEntity.ok("处理成功");
        } else {
            // 非事务请求
            businessService.processRequest(request);
            return ResponseEntity.ok("处理成功");
        }
    }
}
```

### 4. 数据源配置

```java
@Configuration
public class DataSourceConfig {
    
    @Bean
    @Primary
    public DataSource dataSource() {
        // 配置数据源
        HikariDataSource dataSource = new HikariDataSource();
        dataSource.setJdbcUrl("************************************");
        dataSource.setUsername("root");
        dataSource.setPassword("password");
        return dataSource;
    }
}
```

## 配置参数

| 参数 | 说明 | 默认值 |
|------|------|--------|
| `seata.client.enabled` | 是否启用客户端Seata | false |
| `seata.client.application-id` | 应用ID | cloud-service |
| `seata.client.tx-service-group` | 事务服务组 | cloud-group |
| `seata.client.enable-auto-data-source-proxy` | 是否启用自动数据源代理 | true |
| `seata.client.enable-transaction-interceptor` | 是否启用事务拦截器 | true |
| `seata.client.interceptor-path-patterns` | 拦截器路径模式 | ["/api/**"] |
| `seata.client.transaction-timeout` | 事务超时时间 | 300000 |

## HTTP头说明

### 请求头
- `TX-XID`：全局事务ID
- `TX-APPLICATION-ID`：发起方应用ID
- `TX-SERVICE-GROUP`：事务服务组

### 响应头
- `TX-CONTEXT-RECEIVED`：表示已接收事务上下文
- `TX-BRANCH-ID`：分支事务ID（如果有）

## 工作流程

1. **接收请求**：TransactionInterceptor拦截HTTP请求
2. **提取事务信息**：从HTTP头中提取事务ID等信息
3. **绑定上下文**：将事务信息绑定到当前线程
4. **执行业务逻辑**：在事务上下文中执行业务代码
5. **注册分支事务**：如果有数据库操作，自动注册分支事务
6. **清理上下文**：请求完成后自动清理事务上下文
7. **返回响应**：在响应头中返回分支事务信息

## 注意事项

1. **数据源代理**：确保数据源被Seata代理，需要创建undo_log表
2. **拦截器配置**：正确配置拦截器路径，避免拦截健康检查等接口
3. **事务传播**：只处理从DLP服务传播过来的事务，不主动发起全局事务
4. **资源清理**：分支事务完成后会自动清理相关资源
5. **异常处理**：业务异常会触发分支事务回滚

## 与传统Seata RM的区别

| 特性 | 传统Seata RM | Seata Simple Client |
|------|--------------|---------------------|
| 连接方式 | 长连接到Server | HTTP短连接 |
| 事务发起 | 可发起全局事务 | 只参与全局事务 |
| 资源管理 | 完整RM功能 | 简化RM功能 |
| 部署复杂度 | 需要配置Server地址 | 无需额外配置 |
| 性能开销 | 长连接维护开销 | HTTP请求开销 |
