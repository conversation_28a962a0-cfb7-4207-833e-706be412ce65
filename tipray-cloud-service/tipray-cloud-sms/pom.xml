<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <!-- 独立微服务，不依赖父工程 -->
    <groupId>com.tipray</groupId>
    <artifactId>tipray-cloud-sms</artifactId>
    <version>1.0.0</version>
    <name>tipray-cloud-sms</name>
    <packaging>pom</packaging>
    <description>Tipray 短信服务 - 独立微服务版本</description>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <maven.compiler.source>1.8</maven.compiler.source>
        <maven.compiler.target>1.8</maven.compiler.target>

        <!-- Tipray组件版本 -->
        <tipray.version>1.0</tipray.version>
        <!-- Spring Boot版本 -->
        <spring-boot.version>2.7.18</spring-boot.version>
    </properties>

    <!-- 依赖管理 -->
    <dependencyManagement>
        <dependencies>
            <!-- 引入Tipray BOM进行统一版本管理 -->
            <dependency>
                <groupId>com.tipray</groupId>
                <artifactId>tipray-cloud-bom</artifactId>
                <version>${tipray.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <!-- Spring Boot BOM - 优先引入，提供Spring Boot生态的版本管理 -->
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>${spring-boot.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <!-- 内部模块版本管理 -->
            <dependency>
                <groupId>com.tipray</groupId>
                <artifactId>tipray-cloud-sms-common</artifactId>
                <version>${project.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <modules>
        <!--基础模块 除了controller 和 特殊的service 其他都在这边-->
        <module>tipray-cloud-sms-common</module>
        <!--对外的api-->
        <module>tipray-cloud-sms-api</module>
        <!--自己后台使用的业务功能-->
        <module>tipray-cloud-sms-biz</module>
    </modules>

    <dependencies>
        <!-- 全局依赖 -->
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <scope>provided</scope>
        </dependency>

        <!-- Tipray通用组件 (通过本地仓库引入) -->
        <dependency>
            <groupId>com.tipray</groupId>
            <artifactId>tipray-cloud-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.tipray</groupId>
            <artifactId>tipray-cloud-mybatis</artifactId>
        </dependency>
    </dependencies>

</project>
