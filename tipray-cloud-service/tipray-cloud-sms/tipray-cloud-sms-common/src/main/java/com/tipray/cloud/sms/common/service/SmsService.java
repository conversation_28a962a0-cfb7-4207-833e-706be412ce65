package com.tipray.cloud.sms.common.service;

import com.tipray.cloud.sms.common.dto.SmsRequest;
import com.tipray.cloud.sms.common.dto.SmsResponse;
import com.tipray.cloud.sms.common.entity.SmsRecord;
import com.tipray.cloud.sms.common.entity.SmsTemplate;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 短信服务接口
 */
public interface SmsService {

    /**
     * 发送短信
     * @param request 短信请求
     * @return 发送结果
     */
    SmsResponse sendSms(SmsRequest request);

    /**
     * 获取短信模板
     * @param id 模板ID
     * @return 短信模板
     */
    SmsTemplate getSmsTemplate(Long id);

    /**
     * 获取短信模板
     * @param code 模板编码
     * @return 短信模板
     */
    SmsTemplate getSmsTemplateByCode(String code);

    /**
     * 保存短信模板
     * @param template 短信模板
     * @return 保存后的短信模板
     */
    SmsTemplate saveSmsTemplate(SmsTemplate template);

    /**
     * 获取短信发送记录
     * @param id 记录ID
     * @return 短信发送记录
     */
    SmsRecord getSmsRecord(Long id);

    /**
     * 根据手机号查询发送记录
     * @param mobile 手机号
     * @return 短信发送记录列表
     */
    List<SmsRecord> getSmsRecordsByMobile(String mobile);

    /**
     * 根据时间范围查询发送记录
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 短信发送记录列表
     */
    List<SmsRecord> getSmsRecordsByTimeRange(LocalDateTime startTime, LocalDateTime endTime);
} 