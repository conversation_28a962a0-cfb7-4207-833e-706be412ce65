package com.tipray.cloud.sms.common.entity;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 短信发送记录实体类
 */
@Data
public class SmsRecord {

    private Long id;

    /**
     * 手机号
     */
    private String mobile;

    /**
     * 短信内容
     */
    private String content;

    /**
     * 短信模板ID
     */
    private Long templateId;

    /**
     * 短信参数（JSON格式）
     */
    private String params;

    /**
     * 发送状态（0-失败，1-成功）
     */
    private Integer status;

    /**
     * 失败原因
     */
    private String failReason;

    /**
     * 短信平台返回的消息ID
     */
    private String messageId;

    /**
     * 平台类型（例如：阿里云、腾讯云等）
     */
    private String platform;

    /**
     * 发送时间
     */
    private LocalDateTime sendTime;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

}
