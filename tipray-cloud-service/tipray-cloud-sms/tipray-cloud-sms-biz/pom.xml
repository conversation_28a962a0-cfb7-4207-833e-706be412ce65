<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.tipray</groupId>
        <artifactId>tipray-cloud-sms</artifactId>
        <version>${revision}</version>
    </parent>

    <artifactId>tipray-cloud-sms-biz</artifactId>

    <dependencies>
        <!-- 引入公共模块 -->
        <dependency>
            <groupId>com.tipray</groupId>
            <artifactId>tipray-cloud-sms-common</artifactId>
        </dependency>
        <dependency>
            <groupId>com.tipray</groupId>
            <artifactId>tipray-cloud-web</artifactId>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <configuration>
                    <excludes>
                        <exclude>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok</artifactId>
                        </exclude>
                    </excludes>
                </configuration>
            </plugin>
        </plugins>
    </build>

</project>
