package com.tipray.cloud.sms.biz.controller;

import com.tipray.cloud.sms.common.dto.SmsRequest;
import com.tipray.cloud.sms.common.dto.SmsResponse;
import com.tipray.cloud.sms.common.entity.SmsRecord;
import com.tipray.cloud.sms.common.entity.SmsTemplate;
import com.tipray.cloud.sms.common.service.SmsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 短信管理后台控制器
 */
@Controller
@RequestMapping("/admin/sms")
public class SmsBizController {

    @Autowired
    private SmsService smsService;

    /**
     * 短信模板管理页面
     */
    @GetMapping("/templates")
    public String templatesPage(Model model) {
        // TODO: 获取模板列表
        return "sms/templates";
    }

    /**
     * 保存短信模板
     */
    @PostMapping("/templates")
    @ResponseBody
    public SmsTemplate saveTemplate(@RequestBody SmsTemplate template) {
        return smsService.saveSmsTemplate(template);
    }

    /**
     * 短信发送页面
     */
    @GetMapping("/send")
    public String sendPage(Model model) {
        // TODO: 获取模板列表
        return "sms/send";
    }

    /**
     * 发送短信
     */
    @PostMapping("/send")
    @ResponseBody
    public SmsResponse sendSms(@RequestBody SmsRequest request) {
        return smsService.sendSms(request);
    }

    /**
     * 短信记录查询页面
     */
    @GetMapping("/records")
    public String recordsPage(Model model) {
        return "sms/records";
    }

    /**
     * 查询短信记录
     */
    @GetMapping("/records/data")
    @ResponseBody
    public List<SmsRecord> getRecords(@RequestParam(required = false) String mobile,
                                      @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime startTime,
                                      @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime endTime) {
        if (mobile != null && !mobile.isEmpty()) {
            return smsService.getSmsRecordsByMobile(mobile);
        } else if (startTime != null && endTime != null) {
            return smsService.getSmsRecordsByTimeRange(startTime, endTime);
        } else {
            // 默认返回近7天的记录
            LocalDateTime now = LocalDateTime.now();
            LocalDateTime sevenDaysAgo = now.minusDays(7);
            return smsService.getSmsRecordsByTimeRange(sevenDaysAgo, now);
        }
    }
} 