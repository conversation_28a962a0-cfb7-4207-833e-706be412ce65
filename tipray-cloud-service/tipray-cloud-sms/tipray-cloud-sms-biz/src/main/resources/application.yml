server:
  port: 8082
  servlet:
    context-path: /sms-admin

spring:
  application:
    name: tipray-cloud-sms-biz
  datasource:
    url: ******************************************************************************************
    username: root
    password: 123456
    driver-class-name: com.mysql.cj.jdbc.Driver
  jpa:
    hibernate:
      ddl-auto: update
    show-sql: true
    properties:
      hibernate:
        format_sql: true
        dialect: org.hibernate.dialect.MySQL5InnoDBDialect
  thymeleaf:
    cache: false
    encoding: UTF-8
    mode: HTML
    prefix: classpath:/templates/
    suffix: .html

management:
  endpoints:
    web:
      exposure:
        include: health,info

# 日志配置
logging:
  level:
    com.tipray.cloud.sms: INFO
    org.springframework: WARN
    org.hibernate: WARN


tipray:
  info:
    version: 1.0
    mapper-base-package: com.tipray.cloud.mapper
    http-base-package: com.tipray.cloud.api
  distributed:
    transaction: true
