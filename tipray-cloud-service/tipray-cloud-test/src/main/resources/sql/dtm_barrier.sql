-- DTM事务屏障表
CREATE TABLE IF NOT EXISTS dtm_barrier (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    trans_type VARCHAR(45) DEFAULT '',
    gid VARCHAR(128) DEFAULT '',
    branch_id VARCHAR(128) DEFAULT '',
    op VARCHAR(45) DEFAULT '',
    barrier_id VARCHAR(128) DEFAULT '',
    reason VARCHAR(45) DEFAULT '' COMMENT 'the branch type who insert this record',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY(gid, branch_id, op, barrier_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 创建索引
CREATE INDEX idx_dtm_barrier_gid ON dtm_barrier(gid);
CREATE INDEX idx_dtm_barrier_create_time ON dtm_barrier(create_time);
