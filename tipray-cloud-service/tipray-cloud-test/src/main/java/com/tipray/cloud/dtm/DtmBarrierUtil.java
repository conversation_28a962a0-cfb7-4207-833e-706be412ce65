package com.tipray.cloud.dtm;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletRequest;

/**
 * DTM事务屏障工具类
 * 用于处理空补偿、悬挂和幂等性问题
 */
@Component
public class DtmBarrierUtil {
    
    private static final Logger log = LoggerFactory.getLogger(DtmBarrierUtil.class);
    
    @Autowired
    private JdbcTemplate jdbcTemplate;
    
    /**
     * 执行带屏障的业务操作
     * 
     * @param request HTTP请求，包含DTM相关头信息
     * @param businessCall 业务回调函数
     * @return 执行结果
     */
    @Transactional(rollbackFor = Exception.class)
    public String callWithBarrier(HttpServletRequest request, BusinessCall businessCall) {
        // 从请求头中获取DTM相关信息
        String gid = request.getHeader("Dtm-Gid");
        String branchId = request.getHeader("Dtm-BranchId");
        String op = request.getHeader("Dtm-Op");
        String barrierId = request.getHeader("Dtm-BarrierId");
        String transType = request.getHeader("Dtm-TransType");
        
        log.info("DTM屏障检查: gid={}, branchId={}, op={}, barrierId={}, transType={}", 
                gid, branchId, op, barrierId, transType);
        
        if (gid == null || branchId == null || op == null) {
            log.warn("DTM头信息不完整，直接执行业务逻辑");
            return businessCall.call();
        }
        
        return executeWithBarrier(gid, branchId, op, barrierId, transType, businessCall);
    }
    
    /**
     * 执行带屏障的业务逻辑
     */
    private String executeWithBarrier(String gid, String branchId, String op, String barrierId, 
                                    String transType, BusinessCall businessCall) {
        
        // 1. 幂等性检查：尝试插入当前操作记录
        if (!insertBarrier(gid, branchId, op, barrierId, transType, op)) {
            log.info("操作已执行过，直接返回成功: gid={}, branchId={}, op={}", gid, branchId, op);
            return "SUCCESS"; // 幂等性：操作已执行过
        }
        
        // 2. 空补偿检查：如果是补偿操作，检查是否为空补偿
        if ("compensate".equals(op)) {
            // 尝试插入对应的正向操作记录
            if (insertBarrier(gid, branchId, "action", barrierId, transType, op)) {
                log.info("检测到空补偿，跳过业务逻辑: gid={}, branchId={}", gid, branchId);
                return "SUCCESS"; // 空补偿：正向操作未执行，直接返回成功
            }
        }
        
        // 3. 悬挂检查：如果是正向操作，检查补偿是否已执行
        if ("action".equals(op)) {
            // 检查是否存在补偿记录
            if (checkCompensateExists(gid, branchId)) {
                log.info("检测到悬挂，跳过业务逻辑: gid={}, branchId={}", gid, branchId);
                return "SUCCESS"; // 悬挂：补偿已执行，直接返回成功
            }
        }
        
        // 4. 执行业务逻辑
        log.info("通过屏障检查，执行业务逻辑: gid={}, branchId={}, op={}", gid, branchId, op);
        return businessCall.call();
    }
    
    /**
     * 插入屏障记录
     * 
     * @return true-插入成功，false-记录已存在
     */
    private boolean insertBarrier(String gid, String branchId, String op, String barrierId, 
                                String transType, String reason) {
        try {
            String sql = "INSERT INTO dtm_barrier (trans_type, gid, branch_id, op, barrier_id, reason) " +
                        "VALUES (?, ?, ?, ?, ?, ?)";
            
            int rows = jdbcTemplate.update(sql, transType, gid, branchId, op, barrierId, reason);
            log.debug("插入屏障记录成功: gid={}, branchId={}, op={}, rows={}", gid, branchId, op, rows);
            return rows > 0;
            
        } catch (DuplicateKeyException e) {
            log.debug("屏障记录已存在: gid={}, branchId={}, op={}", gid, branchId, op);
            return false;
        } catch (Exception e) {
            log.error("插入屏障记录失败: gid={}, branchId={}, op={}, error={}", 
                    gid, branchId, op, e.getMessage(), e);
            throw new RuntimeException("屏障记录插入失败", e);
        }
    }
    
    /**
     * 检查补偿记录是否存在
     */
    private boolean checkCompensateExists(String gid, String branchId) {
        try {
            String sql = "SELECT COUNT(*) FROM dtm_barrier WHERE gid = ? AND branch_id = ? AND op = 'compensate'";
            Integer count = jdbcTemplate.queryForObject(sql, Integer.class, gid, branchId);
            return count != null && count > 0;
        } catch (Exception e) {
            log.error("检查补偿记录失败: gid={}, branchId={}, error={}", gid, branchId, e.getMessage(), e);
            return false;
        }
    }
    
    /**
     * 业务回调接口
     */
    @FunctionalInterface
    public interface BusinessCall {
        String call();
    }
}
