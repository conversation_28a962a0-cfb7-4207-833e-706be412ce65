package com.tipray.cloud.controller;

import com.tipray.cloud.dtm.DtmBarrierUtil;
import com.tipray.cloud.pojo.AccountDO;
import com.tipray.cloud.pojo.TransferRequest;
import com.tipray.cloud.service.AccountService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.util.List;

/**
 * 账户控制器 - 处理DTM Saga的HTTP请求
 */
@RestController
@RequestMapping("/account")
public class AccountController {
    
    private static final Logger log = LoggerFactory.getLogger(AccountController.class);
    
    @Autowired
    private AccountService accountService;

    @Autowired
    private DtmBarrierUtil dtmBarrierUtil;
    
    /**
     * 查询所有账户信息
     */
    @GetMapping("/list")
    public ResponseEntity<List<AccountDO>> getAllAccounts() {
        List<AccountDO> accounts = accountService.getAllAccounts();
        return ResponseEntity.ok(accounts);
    }
    
    /**
     * 初始化测试账户
     */
    @PostMapping("/init")
    public ResponseEntity<String> initAccounts() {
        accountService.initTestAccounts();
        return ResponseEntity.ok("测试账户初始化成功");
    }
    
    /**
     * 执行转账（DTM Saga入口）
     */
    @PostMapping("/transfer")
    public ResponseEntity<String> transfer(@RequestParam String fromAccountId,
                                         @RequestParam String toAccountId,
                                         @RequestParam BigDecimal amount) {
        try {
            log.info("收到转账请求: 从账户={} 到账户={} 金额={}", fromAccountId, toAccountId, amount);
            accountService.executeTransfer(fromAccountId, toAccountId, amount);
            return ResponseEntity.ok("转账请求已提交，请查看日志了解执行结果");
        } catch (Exception e) {
            log.error("转账请求处理失败: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body("转账失败: " + e.getMessage());
        }
    }
    
    /**
     * DTM Saga - 转出操作（带事务屏障）
     */
    @PostMapping("/transferOut")
    public ResponseEntity<String> transferOut(@RequestBody TransferRequest request,
                                            HttpServletRequest httpRequest) {
        log.info("DTM调用转出操作: {}", request);

        try {
            String result = dtmBarrierUtil.callWithBarrier(httpRequest, () -> {
                try {
                    accountService.transferOut(request);
                    log.info("转出操作成功");
                    return "SUCCESS";
                } catch (Exception e) {
                    log.error("转出操作失败: {}", e.getMessage());
                    throw new RuntimeException(e);
                }
            });

            log.info("转出操作完成，返回200状态码，结果: {}", result);
            return ResponseEntity.ok(result);

        } catch (Exception e) {
            log.error("转出操作异常: {}", e.getMessage());
            // 返回409状态码表示永久失败，DTM不会重试
            return ResponseEntity.status(HttpStatus.CONFLICT).body("FAILURE: " + e.getMessage());
        }
    }
    
    /**
     * DTM Saga - 转入操作（带事务屏障）
     */
    @PostMapping("/transferIn")
    public ResponseEntity<String> transferIn(@RequestBody TransferRequest request,
                                           HttpServletRequest httpRequest) {
        log.info("DTM调用转入操作: {}", request);

        try {
            String result = dtmBarrierUtil.callWithBarrier(httpRequest, () -> {
                try {
                    accountService.transferIn(request);
                    log.info("转入操作成功");
                    return "SUCCESS";
                } catch (Exception e) {
                    log.error("转入操作失败: {}", e.getMessage());
                    throw new RuntimeException(e);
                }
            });

            log.info("转入操作完成，返回200状态码，结果: {}", result);
            return ResponseEntity.ok(result);

        } catch (Exception e) {
            log.error("转入操作异常: {}", e.getMessage());
            // 返回409状态码表示永久失败，DTM不会重试，直接执行补偿
            return ResponseEntity.status(HttpStatus.CONFLICT).body("FAILURE: " + e.getMessage());
        }
    }
    
    /**
     * DTM Saga - 转出补偿操作（带事务屏障）
     */
    @PostMapping("/transferOutCompensate")
    public ResponseEntity<String> transferOutCompensate(@RequestBody TransferRequest request,
                                                      HttpServletRequest httpRequest) {
        log.info("DTM调用转出补偿操作: {}", request);

        try {
            String result = dtmBarrierUtil.callWithBarrier(httpRequest, () -> {
                try {
                    accountService.transferOutCompensate(request);
                    log.info("转出补偿操作成功");
                    return "COMPENSATED";
                } catch (Exception e) {
                    log.error("转出补偿操作失败: {}", e.getMessage());
                    // 补偿操作失败也返回成功，避免DTM无限重试
                    return "COMPENSATE_FAILED: " + e.getMessage();
                }
            });

            log.info("转出补偿操作完成，返回200状态码，结果: {}", result);
            return ResponseEntity.ok(result);

        } catch (Exception e) {
            log.error("转出补偿操作异常: {}", e.getMessage());
            // 补偿操作异常也返回200，避免DTM无限重试
            return ResponseEntity.ok("COMPENSATE_ERROR: " + e.getMessage());
        }
    }
    
    /**
     * DTM Saga - 转入补偿操作（带事务屏障）
     */
    @PostMapping("/transferInCompensate")
    public ResponseEntity<String> transferInCompensate(@RequestBody TransferRequest request,
                                                     HttpServletRequest httpRequest) {
        log.info("DTM调用转入补偿操作: {}", request);

        try {
            String result = dtmBarrierUtil.callWithBarrier(httpRequest, () -> {
                try {
                    accountService.transferInCompensate(request);
                    log.info("转入补偿操作成功");
                    return "COMPENSATED";
                } catch (Exception e) {
                    log.error("转入补偿操作失败: {}", e.getMessage());
                    // 补偿操作失败也返回成功，避免DTM无限重试
                    return "COMPENSATE_FAILED: " + e.getMessage();
                }
            });

            log.info("转入补偿操作完成，返回200状态码，结果: {}", result);
            return ResponseEntity.ok(result);

        } catch (Exception e) {
            log.error("转入补偿操作异常: {}", e.getMessage());
            // 补偿操作异常也返回200，避免DTM无限重试
            return ResponseEntity.ok("COMPENSATE_ERROR: " + e.getMessage());
        }
    }
}
