package com.tipray.cloud.service;

import com.tipray.cloud.pojo.AccountDO;
import com.tipray.cloud.pojo.TransferRequest;

import java.math.BigDecimal;
import java.util.List;

/**
 * 账户服务接口
 */
public interface AccountService {
    
    /**
     * 获取所有账户
     */
    List<AccountDO> getAllAccounts();
    
    /**
     * 根据账户ID获取账户
     */
    AccountDO getAccountById(String accountId);
    
    /**
     * 初始化测试账户
     */
    void initTestAccounts();
    
    /**
     * 转出操作（扣款）
     */
    void transferOut(TransferRequest request);
    
    /**
     * 转入操作（加款）
     */
    void transferIn(TransferRequest request);
    
    /**
     * 转出补偿操作（退款）
     */
    void transferOutCompensate(TransferRequest request);
    
    /**
     * 转入补偿操作（扣款）
     */
    void transferInCompensate(TransferRequest request);
    
    /**
     * 执行DTM Saga转账
     */
    void executeTransfer(String fromAccountId, String toAccountId, BigDecimal amount);
}
