package com.tipray.cloud.controller;

import com.dtflys.forest.annotation.Post;
import com.tipray.cloud.pojo.SmsDO;
import com.tipray.cloud.service.SmsService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

@RestController
public class TestController {

    private static final Logger log = LoggerFactory.getLogger(TestController.class);

    @Autowired
    private SmsService smsService;

    @Autowired
    private JdbcTemplate jdbcTemplate;

    @GetMapping("/test")
    public void test1() {
        smsService.list();
    }

    @GetMapping("/test2")
    public void test2() {
        smsService.saveByRemote("123");
    }

    @PostMapping("/test3")
    public void test3(@RequestBody SmsDO smsDO2) {
        smsService.createTest(smsDO2);
    }

    @PostMapping("/testDtmSaga")
    public void testDtmSaga(@RequestBody SmsDO smsDO2) {
        smsService.saveByRemote2("123");
    }

    @PostMapping("/save")
    @Transactional(rollbackFor = Exception.class)
    public ResponseEntity<String> save(@RequestBody SmsDO smsDO) {
        log.info("开始执行保存操作，数据: {}", smsDO);
        try {
            smsService.save(smsDO);
            log.info("保存操作成功完成");
            return ResponseEntity.ok("SUCCESS"); // 200状态码，DTM识别为成功
        } catch (Exception e) {
            log.error("保存操作失败: {}", e.getMessage());
            // 对于data=ee的情况，返回409状态码让DTM识别为永久失败，不再重试
            if (smsDO.getData() != null && smsDO.getData().equals("ee")) {
                log.info("返回409状态码，告诉DTM这是永久失败");
                return ResponseEntity.status(HttpStatus.CONFLICT).body("FAILURE"); // 409状态码
            }
            // 其他异常返回500，DTM会重试
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body("ERROR");
        }
    }

    @PostMapping("/delete")
    public String delete(@RequestBody SmsDO smsDO) {
        log.info("开始执行删除补偿操作，数据: {}", smsDO);
        smsService.delete(smsDO);
        log.info("删除补偿操作成功完成");
        return "true";
    }

    /**
     * 测试DTM Saga事务回滚
     * 访问此接口来验证分布式事务是否正常回滚
     */
    @PostMapping("/testDtmSagaRollback")
    public String testDtmSagaRollback() {
        try {
            smsService.saveByRemote2("123");
            return "事务执行成功";
        } catch (Exception e) {
            return "事务执行失败: " + e.getMessage();
        }
    }

    /**
     * 查询数据库中的记录数量，用于验证补偿是否执行
     */
    @GetMapping("/checkData")
    public String checkData() {
        try {
            int count = smsService.list().size();
            return "当前数据库中记录数量: " + count;
        } catch (Exception e) {
            return "查询失败: " + e.getMessage();
        }
    }

    /**
     * 查询DTM屏障记录
     */
    @GetMapping("/barrier/records")
    public ResponseEntity<List<Map<String, Object>>> getBarrierRecords() {
        try {
            String sql = "SELECT * FROM dtm_barrier ORDER BY create_time DESC LIMIT 50";
            List<Map<String, Object>> records = jdbcTemplate.queryForList(sql);
            return ResponseEntity.ok(records);
        } catch (Exception e) {
            log.error("查询屏障记录失败: {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(null);
        }
    }

    /**
     * 清空DTM屏障记录
     */
    @PostMapping("/barrier/clear")
    public ResponseEntity<String> clearBarrierRecords() {
        try {
            String sql = "DELETE FROM dtm_barrier";
            int deletedRows = jdbcTemplate.update(sql);
            return ResponseEntity.ok("已清空 " + deletedRows + " 条屏障记录");
        } catch (Exception e) {
            log.error("清空屏障记录失败: {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body("清空失败: " + e.getMessage());
        }
    }
}
