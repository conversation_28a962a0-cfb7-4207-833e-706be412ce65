package com.tipray.cloud.offline.strategy.extend.enums;

import com.tipray.cloud.enums.BaseEnum;

/**
     * 日志类型枚举
     */
    public enum LogTypeEnum implements BaseEnum<Integer> {
        /**
         * 配置离线策略时间
         */
        CONFIG_OFFLINE_STRATEGY_TIME(0, "配置离线策略时间"),
        /**
         * 终端同步
         */
        TERMINAL_SYNC(1, "终端同步"),
        /**
         * 同步至云服务
         */
        SYNC_TO_CLOUD_SERVICE(2, "同步至云服务"),
        /**
         * 删除配置
         */
        DELETE_CONFIG(3, "删除离线策略延长配置");

        private final Integer value;
        private final String desc;

        LogTypeEnum(Integer value, String desc) {
            this.value = value;
            this.desc = desc;
        }

        public Integer getValue() {
            return value;
        }

        public String getDesc() {
            return desc;
        }

        public static LogTypeEnum getByCode(Integer code) {
            if (code == null) {
                return null;
            }
            for (LogTypeEnum typeEnum : values()) {
                if (typeEnum.getValue().equals(code)) {
                    return typeEnum;
                }
            }
            return null;
        }
    }
