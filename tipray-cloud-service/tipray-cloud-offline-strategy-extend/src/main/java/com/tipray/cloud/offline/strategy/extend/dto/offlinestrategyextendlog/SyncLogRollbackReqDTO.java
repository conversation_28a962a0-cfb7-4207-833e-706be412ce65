package com.tipray.cloud.offline.strategy.extend.dto.offlinestrategyextendlog;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.List;

/**
 * 回滚同步中的日志状态请求DTO
 *
 * <AUTHOR>
 */
@Schema(description = "回滚同步中的日志状态请求")
@Data
public class SyncLogRollbackReqDTO implements Serializable {

    @Schema(description = "日志ID列表")
    @NotEmpty(message = "日志ID列表不能为空")
    private List<Integer> logIds;
}
