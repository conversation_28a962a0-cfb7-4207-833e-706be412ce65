package com.tipray.cloud.offline.strategy.extend.mapper;


import com.tipray.cloud.offline.strategy.extend.entity.Application;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

/**
 * 应用的 Mapper
 * <AUTHOR> cloud
 */
@Mapper
public interface ApplicationMapper {
    /**
     * 删除应用记录
     * @param id 主键
     * @return 受影响行数
     */
    int deleteByPrimaryKey(String id);

    /**
     * 插入一条应用记录
     * @param record 应用记录
     * @return 受影响行数
     */
    int insert(Application record);

    /**
     * 有选择的插入应用记录
     * @param record 应用记录
     * @return 受影响行数
     */
    int insertSelective(Application record);

    /**
     * 根据主键id查找应用
     * @param id 主键
     * @return 应用记录
     */
    Application selectByPrimaryKey(String id);

    Application selectByCode(String code);

    /**
     * 修改应用记录
     * @param record 应用记录
     * @return 受影响行数
     */
    int updateByPrimaryKey(Application record);

    /**
     * 有选择的修改应用记录
     * @param record 应用记录
     * @return 受影响行数
     */
    int updateByPrimaryKeySelective(Application record);

    /**
     * 查询应用
     * @param m Map集合，查询条件
     * @return 应用记录列表
     */
    List<Application> list(Map<String, Object> m);

    /**
     * 获取所有应用名称
     * @return 应用名称集合
     */
    List<String> getName();
}
