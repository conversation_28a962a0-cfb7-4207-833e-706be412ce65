package com.tipray.cloud.offline.strategy.extend.dto.offlinestrategyextendlog;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 离线策略操作日志创建请求DTO
 *
 * <AUTHOR>
 */
@Data
public class OfflineStrategyExtendLogCreateReqDTO {

    /**
     * 用户编号
     */
    @NotBlank(message = "用户编号不能为空")
    private String userNo;

    /**
     * 关联的配置ID
     */
    @NotNull(message = "关联的配置ID不能为空")
    private Integer offlineStrategyExtendConfigId;

    /**
     * 关联的配置版本
     */
    @NotNull(message = "关联的配置版本不能为空")
    private Integer version;

    /**
     * 日志类型（0配置离线策略时间/1终端同步/2同步至云服务/3删除配置等）
     */
    @NotNull(message = "日志类型不能为空")
    private Integer logType;

    /**
     * 日志内容
     */
    @NotBlank(message = "日志内容不能为空")
    private String logContent;

    /**
     * 是否同步到DLP（0未同步/1已同步/2可删除）
     */
    private Integer syncToDlp = 0;

    /**
     * 操作IP（支持IPv4/IPv6）
     */
    private String ip;

    /**
     * IP归属地/地理位置
     */
    private String ipLocation;

    /**
     * dlp上对应的id，用于同步到dlp本地
     */
    private Long dlpMapId;
}
