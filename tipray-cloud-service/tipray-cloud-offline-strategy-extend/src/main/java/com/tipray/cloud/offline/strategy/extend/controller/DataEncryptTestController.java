package com.tipray.cloud.offline.strategy.extend.controller;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.tipray.cloud.offline.strategy.extend.entity.Application;
import com.tipray.cloud.offline.strategy.extend.service.IApplicationService;
import com.tipray.cloud.offline.strategy.extend.util.AesEncryptUtils;
import com.tipray.cloud.offline.strategy.extend.util.AesUtil;
import com.tipray.cloud.pojo.CommonResult;
import com.tipray.cloud.web.decrypt.pojo.DataParam;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 数据加密测试控制器
 * 用于生成测试数据的加密接口
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/api/test/data-encrypt")
public class DataEncryptTestController {

    @Autowired
    private IApplicationService applicationService;

    /**
     * 加密业务数据，生成DataParam格式的测试数据
     */
    @PostMapping("/encrypt")
    public CommonResult<DataParam> encryptData(@RequestBody EncryptRequest request) {
        try {
            log.info("开始加密数据，应用编码：{}，公司编码：{}",
                    request.getApplicationCode(), request.getCompanyCode());

            // 1. 验证输入参数
            if (StrUtil.isBlank(request.getApplicationCode())) {
                return CommonResult.error("应用编码不能为空");
            }
            if (StrUtil.isBlank(request.getCompanyCode())) {
                return CommonResult.error("公司编码不能为空");
            }
            if (StrUtil.isBlank(request.getBusinessData())) {
                return CommonResult.error("业务数据不能为空");
            }

            // 2. 获取应用信息
            Application application = applicationService.getByCode(request.getApplicationCode());
            if (application == null) {
                return CommonResult.error("应用编码不存在：" + request.getApplicationCode());
            }

            // 3. 生成时间戳（如果未提供）
            String timestamp = StrUtil.isNotBlank(request.getTs()) ?
                              request.getTs() : String.valueOf(System.currentTimeMillis());

            // 4. 生成加密种子
            String seed = AesUtil.md5(application.getKey() + request.getCompanyCode() + timestamp);
            log.debug("生成加密种子，应用密钥长度：{}，时间戳：{}",
                     application.getKey().length(), timestamp);

            // 5. 加密业务数据
            String encryptedData = AesEncryptUtils.encrypt(request.getBusinessData(), seed);
            log.debug("数据加密完成，原始数据长度：{}，加密后长度：{}",
                     request.getBusinessData().length(), encryptedData.length());

            // 6. 构造DataParam对象
            DataParam dataParam = DataParam.builder()
                    .applicationCode(request.getApplicationCode())
                    .companyCode(request.getCompanyCode())
                    .encryptData(encryptedData)
                    .ts(timestamp)
                    .state(StrUtil.isNotBlank(request.getState()) ? request.getState() : "ACTIVE")
                    .seed(StrUtil.isNotBlank(request.getSeed()) ? request.getSeed() : "randomSeed" + System.currentTimeMillis())
                    .build();

            log.info("数据加密成功，应用编码：{}，加密数据长度：{}",
                    request.getApplicationCode(), encryptedData.length());

            return CommonResult.success(dataParam);

        } catch (Exception e) {
            log.error("数据加密失败", e);
            return CommonResult.error("数据加密失败：" + e.getMessage());
        }
    }

    /**
     * 快速生成测试数据
     * 根据预设的业务数据模板生成加密的DataParam
     */
    @PostMapping("/generate-test-data")
    public CommonResult<DataParam> generateTestData(@RequestBody TestDataRequest request) {
        try {
            log.info("开始生成测试数据，模板类型：{}", request.getTemplateType());

            // 1. 根据模板类型生成业务数据
            String businessData = generateBusinessDataByTemplate(request.getTemplateType(), request);

            // 2. 构造加密请求
            EncryptRequest encryptRequest = new EncryptRequest();
            encryptRequest.setApplicationCode(StrUtil.isNotBlank(request.getApplicationCode()) ?
                                            request.getApplicationCode() : "OFFLINE_STRATEGY");
            encryptRequest.setCompanyCode(StrUtil.isNotBlank(request.getCompanyCode()) ?
                                        request.getCompanyCode() : "TIPRAY");
            encryptRequest.setBusinessData(businessData);
            encryptRequest.setState("ACTIVE");

            // 3. 执行加密
            return encryptData(encryptRequest);

        } catch (Exception e) {
            log.error("生成测试数据失败", e);
            return CommonResult.error("生成测试数据失败：" + e.getMessage());
        }
    }

    /**
     * 根据模板类型生成业务数据
     */
    private String generateBusinessDataByTemplate(String templateType, TestDataRequest request) {
        switch (templateType) {
            case "TERMINAL_CONFIG":
                return JSONUtil.toJsonStr(new TerminalConfigData(
                    StrUtil.isNotBlank(request.getTerminalNo()) ? request.getTerminalNo() : "TERMINAL001",
                    StrUtil.isNotBlank(request.getUserNo()) ? request.getUserNo() : "USER001",
                    request.getGroupNoList() != null ? request.getGroupNoList() : java.util.Arrays.asList("GROUP001", "GROUP002")
                ));

            case "USER_INFO":
                return JSONUtil.toJsonStr(new UserInfoData(
                    StrUtil.isNotBlank(request.getUserNo()) ? request.getUserNo() : "USER001",
                    "测试用户",
                    "<EMAIL>"
                ));

            case "CUSTOM":
                return StrUtil.isNotBlank(request.getCustomData()) ? request.getCustomData() : "{}";

            default:
                return JSONUtil.toJsonStr(new TerminalConfigData("TERMINAL001", "USER001",
                                        java.util.Arrays.asList("GROUP001")));
        }
    }

    /**
     * 加密请求参数
     */
    @Data
    public static class EncryptRequest {
        /**
         * 应用编码
         */
        private String applicationCode;

        /**
         * 公司编码
         */
        private String companyCode;

        /**
         * 业务数据（JSON字符串）
         */
        private String businessData;

        /**
         * 时间戳（可选，不提供则自动生成）
         */
        private String ts;

        /**
         * 状态（可选，默认ACTIVE）
         */
        private String state;

        /**
         * 随机种子（可选，不提供则自动生成）
         */
        private String seed;
    }

    /**
     * 测试数据生成请求参数
     */
    @Data
    public static class TestDataRequest {
        /**
         * 模板类型：TERMINAL_CONFIG, USER_INFO, CUSTOM
         */
        private String templateType = "TERMINAL_CONFIG";

        /**
         * 应用编码（可选，默认OFFLINE_STRATEGY）
         */
        private String applicationCode;

        /**
         * 公司编码（可选，默认TIPRAY）
         */
        private String companyCode;

        /**
         * 终端编号（用于TERMINAL_CONFIG模板）
         */
        private String terminalNo;

        /**
         * 用户编号
         */
        private String userNo;

        /**
         * 分组编号列表（用于TERMINAL_CONFIG模板）
         */
        private java.util.List<String> groupNoList;

        /**
         * 自定义数据（用于CUSTOM模板）
         */
        private String customData;
    }

    /**
     * 终端配置数据模板
     */
    @Data
    public static class TerminalConfigData {
        private String terminalNo;
        private String userNo;
        private java.util.List<String> groupNoList;

        public TerminalConfigData(String terminalNo, String userNo, java.util.List<String> groupNoList) {
            this.terminalNo = terminalNo;
            this.userNo = userNo;
            this.groupNoList = groupNoList;
        }
    }

    /**
     * 用户信息数据模板
     */
    @Data
    public static class UserInfoData {
        private String userNo;
        private String userName;
        private String email;

        public UserInfoData(String userNo, String userName, String email) {
            this.userNo = userNo;
            this.userName = userName;
            this.email = email;
        }
    }

}
