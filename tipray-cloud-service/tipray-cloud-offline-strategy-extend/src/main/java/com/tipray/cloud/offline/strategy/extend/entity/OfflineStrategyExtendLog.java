package com.tipray.cloud.offline.strategy.extend.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 离线策略操作日志实体
 *
 * <AUTHOR>
 */
@TableName("ose_offline_strategy_extend_log")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class OfflineStrategyExtendLog {

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Integer id;

    /**
     * 用户编号
     */
    private String userNo;

    /**
     * 配置类型（分支/终端）快照
     */
    private Integer targetType;

    /**
     * 生效目标 分组编号/终端编号 快照
     */
    private String target;

    /**
     * 关联的配置ID
     */
    private Integer offlineStrategyExtendConfigId;

    /**
     * 关联的配置版本
     */
    private Integer version;

    /**
     * 日志类型（0配置离线策略时间/1终端同步/2同步至云服务/3删除配置等）
     */
    private Integer logType;

    /**
     * 日志内容
     */
    private String logContent;

    /**
     * 是否同步到DLP（0未同步/1已同步/2可删除）
     */
    private Integer syncToDlp;

    /**
     * 操作IP（支持IPv4/IPv6）
     */
    private String ip;

    /**
     * IP归属地/地理位置
     */
    private String ipLocation;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * dlp上对应的id，用于同步到dlp本地
     */
    private Long dlpMapId;

    /**
     * 操作时间
     */
    private LocalDateTime operateTime;
}
