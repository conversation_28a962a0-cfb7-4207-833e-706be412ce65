package com.tipray.cloud.offline.strategy.extend.dto.offlinestrategyextendconfig;

import com.tipray.cloud.offline.strategy.extend.dto.quota.QuotaRespDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * 离线延长配置同步的返回结果类
 * <AUTHOR>
 */
@Schema(description = "离线策略延长配置批量同步响应")
@Data
public class OfflineStrategyExtendConfigSyncRespDTO {

    @Schema(description = "总处理数量")
    private Integer totalCount = 0;

    @Schema(description = "总成功数量")
    private Integer totalSuccessCount = 0;

    @Schema(description = "总失败数量")
    private Integer totalFailureCount = 0;

    @Schema(description = "更新成功数量")
    private Integer updateSuccessCount = 0;

    @Schema(description = "更新失败数量")
    private Integer updateFailureCount = 0;

    @Schema(description = "更新失败详情")
    private List<String> updateFailureDetails = new ArrayList<>();

    @Schema(description = "创建成功数量")
    private Integer createSuccessCount = 0;

    @Schema(description = "创建失败数量")
    private Integer createFailureCount = 0;

    @Schema(description = "创建失败详情")
    private List<String> createFailureDetails = new ArrayList<>();

    @Schema(description = "当前配额信息")
    private QuotaRespDTO currentQuota;

    // 兼容旧字段
    @Schema(description = "同步成功的数量（兼容字段）")
    @Deprecated
    private Integer successCount;

    @Schema(description = "同步失败的数量（兼容字段）")
    @Deprecated
    private Integer failCount;

    @Schema(description = "本次同步成功的ids")
    private List<Long> successDlpMapIds = new ArrayList<>();

    @Schema(description = "最新的配额数据（兼容字段）")
    @Deprecated
    private QuotaRespDTO quotaRespDTO;

    @Schema(description = "处理是否完全成功")
    public Boolean isAllSuccess() {
        return totalFailureCount == 0;
    }

    @Schema(description = "成功率")
    public Double getSuccessRate() {
        if (totalCount == 0) {
            return 100.0;
        }
        return (double) totalSuccessCount / totalCount * 100;
    }

    // 兼容性方法
    public Integer getSuccessCount() {
        return totalSuccessCount;
    }

    public void setSuccessCount(Integer successCount) {
        this.successCount = successCount;
        this.totalSuccessCount = successCount;
    }

    public Integer getFailCount() {
        return totalFailureCount;
    }

    public void setFailCount(Integer failCount) {
        this.failCount = failCount;
        this.totalFailureCount = failCount;
    }

    public QuotaRespDTO getQuotaRespDTO() {
        return currentQuota;
    }

    public void setQuotaRespDTO(QuotaRespDTO quotaRespDTO) {
        this.quotaRespDTO = quotaRespDTO;
        this.currentQuota = quotaRespDTO;
    }
}
