package com.tipray.cloud.offline.strategy.extend.dto.quota;

import com.tipray.cloud.pojo.PageParam;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 配额分页查询请求DTO
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class QuotaPageReqDTO extends PageParam {

    /**
     * 用户编号
     */
    private String userNo;

    /**
     * 配额类型
     */
    private Integer quotaType;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 修改人
     */
    private String updatedBy;

    /**
     * 配额使用率阈值（百分比）
     */
    private Double usageRateThreshold;
}
