package com.tipray.cloud.offline.strategy.extend.dto.offlinestrategyextendconfig;

import com.tipray.cloud.offline.strategy.extend.dto.quota.QuotaRespDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * 离线延长配置同步的返回结果类
 * <AUTHOR>
 */
@Schema(description = "离线策略延长配置批量同步响应")
@Data
public class OfflineStrategyExtendConfigDeleteRespDTO {

    @Schema(description = "总处理数量")
    private Integer totalCount = 0;

    @Schema(description = "总成功数量")
    private Integer totalSuccessCount = 0;

    @Schema(description = "本次删除成功的ids")
    private List<Long> successDlpMapIds = new ArrayList<>();

    @Schema(description = "总失败数量")
    private Integer totalFailureCount = 0;

    @Schema(description = "当前配额信息")
    private QuotaRespDTO currentQuota;

    public void incrementSuccess() {
        totalSuccessCount++;
    }

    public void incrementFailure() {
        totalFailureCount++;
    }
}
