package com.tipray.cloud.offline.strategy.extend.dto.offlinestrategyextendconfig;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 离线策略延长配置响应DTO
 *
 * <AUTHOR>
 */
@Data
public class TerminalOfflineStrategyExtendRespDTO {

    /**
     * 主键ID
     */
    @JsonIgnore
    private Integer id;

    /**
     * 用户编号
     */
    @JsonIgnore
    private String userNo;

    /**
     * 配置类型（分支/终端）
     */
    private Integer targetType;

    /**
     * 配置类型描述
     */
    private String typeDesc;

    /**
     * 生效目标 分组编号/终端编号
     */
    private String target;

    /**
     * 密文内容json：effective_time，expire_time
     */
    private String strategyTimeContext;

    /**
     * 当前版本号
     */
    private Integer version;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    /**
     * dlp上对应的id，用于修改、删除
     */
    @JsonIgnore
    private Long dlpMapId;
}
