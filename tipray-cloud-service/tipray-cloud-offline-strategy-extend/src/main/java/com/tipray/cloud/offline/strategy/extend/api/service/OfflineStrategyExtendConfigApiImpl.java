package com.tipray.cloud.offline.strategy.extend.api.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.tipray.cloud.exception.BusinessException;
import com.tipray.cloud.exception.ExceptionUtil;
import com.tipray.cloud.offline.strategy.extend.api.OfflineStrategyExtendConfigApi;
import com.tipray.cloud.offline.strategy.extend.context.DlpContext;
import com.tipray.cloud.offline.strategy.extend.dto.offlinestrategyextendconfig.*;
import com.tipray.cloud.offline.strategy.extend.enums.QuotaTypeEnum;
import com.tipray.cloud.offline.strategy.extend.service.OfflineStrategyExtendConfigService;
import com.tipray.cloud.offline.strategy.extend.service.QuotaService;
import com.tipray.cloud.pojo.CommonResult;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * 具体实现
 * <AUTHOR>
 */
@Slf4j
@RestController
public class OfflineStrategyExtendConfigApiImpl implements OfflineStrategyExtendConfigApi {

    @Resource
    private OfflineStrategyExtendConfigService offlineStrategyExtendConfigService;

    @Resource
    private QuotaService quotaService;

    // 使用专门的锁对象
    private final ConcurrentHashMap<String, Object> userLocks = new ConcurrentHashMap<>();

    @Override
    @Transactional(rollbackFor = Exception.class)
    public CommonResult<Boolean> createConfig(OfflineStrategyExtendConfigCreateReqDTO createReqDTO) {
        // 1. 确保配额存在（带锁的初始化）
        ensureQuotaExists(createReqDTO.getUserNo());

        // 2. 原子性消费配额
        boolean quotaConsumed = quotaService.consumeQuota(
            createReqDTO.getUserNo(),
            QuotaTypeEnum.OFFLINE_STRATEGY_EXTEND_CONFIG.getValue(),
            1
        );

        if (!quotaConsumed) {
            throw new BusinessException("配额不足，无法创建配置");
        }

        // 3. 创建配置
        Integer configId = offlineStrategyExtendConfigService.createConfig(createReqDTO);
        return CommonResult.success(configId > 0);
    }

    /**
     * 确保配额存在（只对初始化加锁）
     * 使用双重检查锁定模式，最小化锁的范围
     */
    private void ensureQuotaExists(String userNo) {
        // 先快速检查，避免不必要的加锁
        if (quotaService.existsByUserNoAndType(userNo, QuotaTypeEnum.OFFLINE_STRATEGY_EXTEND_CONFIG.getValue())) {
            return;
        }

        // 双重检查锁定模式
        Object userLock = userLocks.computeIfAbsent(userNo, k -> new Object());
        try {
            synchronized (userLock) {
                // 再次检查，避免重复初始化
                if (!quotaService.existsByUserNoAndType(userNo, QuotaTypeEnum.OFFLINE_STRATEGY_EXTEND_CONFIG.getValue())) {
                    quotaService.initQuota(userNo);
                }
            }
        } finally {
            // 清理锁对象，避免内存泄漏
            userLocks.remove(userNo);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public CommonResult<Boolean> updateConfig(OfflineStrategyExtendConfigUpdateReqDTO updateReqDTO) {
        // 检查数据是否存在
        OfflineStrategyExtendConfigRespDTO config = offlineStrategyExtendConfigService.getConfigByDlpMapId(updateReqDTO.getDlpMapId());

        if (ObjectUtil.isEmpty(config)) {
            throw BusinessException.dataNotFound("离线策略延长配置");
        }

        // 赋值上云服务的id
        updateReqDTO.setId(config.getId());

        // 更新操作 版本号由dlp那边维护 我们这边只需要做保存就行
        Integer success = offlineStrategyExtendConfigService.updateConfigById(config.getVersion(), updateReqDTO);

        return CommonResult.success(success > 0);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public CommonResult<Boolean> deleteConfig(Long dlpMapId) {
        // 检查数据是否存在
        OfflineStrategyExtendConfigRespDTO config = offlineStrategyExtendConfigService.getConfigByDlpMapId(dlpMapId);

        if (ObjectUtil.isEmpty(config)) {
            throw BusinessException.dataNotFound("离线策略延长配置");
        }

        try {
            // 删除配置
            offlineStrategyExtendConfigService.deleteConfig(config.getId());

            // 释放配额
            boolean quotaReleased = quotaService.releaseQuota(
                config.getUserNo(),
                QuotaTypeEnum.OFFLINE_STRATEGY_EXTEND_CONFIG.getValue(),
                1
            );

            if (!quotaReleased) {
                log.warn("配额释放失败，尝试强制释放，用户：{}", config.getUserNo());
                quotaService.forceReleaseQuota(
                    config.getUserNo(),
                    QuotaTypeEnum.OFFLINE_STRATEGY_EXTEND_CONFIG.getValue(),
                    1
                );
            }

            return CommonResult.success(Boolean.TRUE);

        } catch (Exception e) {
            log.error("删除配置失败，用户：{}，配置ID：{}", config.getUserNo(), config.getId(), e);
            throw e;
        }
    }

    @Override
    public CommonResult<List<TerminalOfflineStrategyExtendRespDTO>> list(TerminalOfflineStrategyExtendReqDTO reqDTO) {
        return CommonResult.success(offlineStrategyExtendConfigService.getConfigByTerminal(
                reqDTO.getUserNo(), reqDTO.getTerminalNo(), reqDTO.getGroupNoList()));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public CommonResult<OfflineStrategyExtendConfigSyncRespDTO> batchSync(List<OfflineStrategyExtendConfigRespDTO> reqDTO) {
        // 从DLP上下文获取当前用户编号
        String userNo = DlpContext.getCurrentUserNo();

        // 校验
        ExceptionUtil.assertNotEmpty(reqDTO, "配置列表不能为空");
        ExceptionUtil.assertNotEmpty(userNo, "用户编号不能为空");

        // 查询这个用户编码所有的dlpMapId
        List<Long> dlpMapIds = offlineStrategyExtendConfigService.listDlpMapIdByUserNo(userNo);

        // 分组
        Map<String, List<OfflineStrategyExtendConfigRespDTO>> groupList = reqDTO.stream()
                .collect(Collectors.groupingBy(config -> dlpMapIds.contains(config.getDlpMapId()) ? "update" : "new"));

        // 初始化结果统计
        OfflineStrategyExtendConfigSyncRespDTO syncResult = new OfflineStrategyExtendConfigSyncRespDTO();
        syncResult.setTotalCount(reqDTO.size());

        try {
            // 更新操作
            List<OfflineStrategyExtendConfigRespDTO> updateList = groupList.getOrDefault("update", Collections.emptyList());
            BatchOperationResult updateResult = batchUpdateConfigs(userNo, updateList);
            syncResult.setUpdateSuccessCount(updateResult.getSuccessCount());
            syncResult.setUpdateFailureCount(updateResult.getFailureCount());
            syncResult.setUpdateFailureDetails(updateResult.getFailureDetails());

            // 新增操作
            List<OfflineStrategyExtendConfigRespDTO> newList = groupList.getOrDefault("new", Collections.emptyList());
            BatchOperationResult createResult = batchCreateConfigs(userNo, newList);
            syncResult.setCreateSuccessCount(createResult.getSuccessCount());
            syncResult.setCreateFailureCount(createResult.getFailureCount());
            syncResult.setCreateFailureDetails(createResult.getFailureDetails());

            // 统计总计
            syncResult.setTotalSuccessCount(syncResult.getUpdateSuccessCount() + syncResult.getCreateSuccessCount());
            syncResult.setTotalFailureCount(syncResult.getUpdateFailureCount() + syncResult.getCreateFailureCount());
            syncResult.setSuccessDlpMapIds(CollUtil.addAllIfNotContains(createResult.getSuccessDlpMapIds(), updateResult.getSuccessDlpMapIds()));

            syncResult.setCurrentQuota(quotaService.getQuotaByUserNoAndType(userNo, QuotaTypeEnum.OFFLINE_STRATEGY_EXTEND_CONFIG.getValue()));
            return CommonResult.success(syncResult);
        } catch (Exception e) {
            log.error("批量同步配置失败", e);
            throw new BusinessException("批量同步配置失败：" + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public CommonResult<OfflineStrategyExtendConfigDeleteRespDTO> batchDelete(List<Long> dlpMapIds) {
        // 从DLP上下文获取当前用户编号
        String userNo = DlpContext.getCurrentUserNo();

        // 校验
        ExceptionUtil.assertNotEmpty(userNo, "用户编号不能为空");


        OfflineStrategyExtendConfigDeleteRespDTO result = new OfflineStrategyExtendConfigDeleteRespDTO();

        if (dlpMapIds.isEmpty()) {
            return CommonResult.success(result);
        }

        for (Long dlpMapId : dlpMapIds) {
            try {
                // 直接调用现有的删除方法
                CommonResult<Boolean> deleteResult = deleteConfig(dlpMapId);

                if (deleteResult.isSuccess() && Boolean.TRUE.equals(deleteResult.getData())) {
                    result.incrementSuccess();
                    result.getSuccessDlpMapIds().add(dlpMapId);
                } else {
                    result.incrementFailure();
                }

            } catch (Exception e) {
                result.incrementFailure();
                log.error("删除配置异常，DLP映射ID：{}", dlpMapId, e);
            }
        }

        result.setCurrentQuota(quotaService.getQuotaByUserNoAndType(userNo, QuotaTypeEnum.OFFLINE_STRATEGY_EXTEND_CONFIG.getValue()));

        return CommonResult.success(result);
    }

    @Override
    public CommonResult<List<OfflineStrategyExtendConfigStatusRespDTO>> batchGetStatus(List<Long> dlpMapIds) {
        return CommonResult.success(offlineStrategyExtendConfigService.selectStatusByDlpMapIds(dlpMapIds));
    }

    /**
     * 批量更新配置
     */
    private BatchOperationResult batchUpdateConfigs(String userNo, List<OfflineStrategyExtendConfigRespDTO> updateList) {
        BatchOperationResult result = new BatchOperationResult();

        if (updateList.isEmpty()) {
            return result;
        }

        for (OfflineStrategyExtendConfigRespDTO config : updateList) {
            try {
                // 构建更新请求
                OfflineStrategyExtendConfigUpdateReqDTO updateReqDTO = new OfflineStrategyExtendConfigUpdateReqDTO();
                updateReqDTO.setDlpMapId(config.getDlpMapId());
                updateReqDTO.setTargetType(config.getTargetType());
                updateReqDTO.setTarget(config.getTarget());
                updateReqDTO.setStrategyTimeContext(config.getStrategyTimeContext());
                updateReqDTO.setVersion(config.getVersion());

                // 直接调用现有的更新方法
                CommonResult<Boolean> updateResult = updateConfig(updateReqDTO);

                if (updateResult.isSuccess() && Boolean.TRUE.equals(updateResult.getData())) {
                    result.incrementSuccess();
                    result.getSuccessDlpMapIds().add(config.getDlpMapId());
                } else {
                    result.incrementFailure();
                    result.getFailureDetails().add("DLP映射ID：" + config.getDlpMapId() + "，更新失败");
                }

            } catch (Exception e) {
                result.incrementFailure();
                String errorMsg = "DLP映射ID：" + config.getDlpMapId() + "，更新异常：" + e.getMessage();
                result.getFailureDetails().add(errorMsg);
                log.error("更新配置异常，DLP映射ID：{}", config.getDlpMapId(), e);
            }
        }


        return result;
    }

    /**
     * 批量创建配置
     */
    private BatchOperationResult batchCreateConfigs(String userNo, List<OfflineStrategyExtendConfigRespDTO> newList) {
        BatchOperationResult result = new BatchOperationResult();

        if (newList.isEmpty()) {
            return result;
        }

        for (OfflineStrategyExtendConfigRespDTO config : newList) {
            try {
                // 构建创建请求
                OfflineStrategyExtendConfigCreateReqDTO createReqDTO = new OfflineStrategyExtendConfigCreateReqDTO();
                createReqDTO.setUserNo(userNo);
                createReqDTO.setTargetType(config.getTargetType());
                createReqDTO.setTarget(config.getTarget());
                createReqDTO.setStrategyTimeContext(config.getStrategyTimeContext());
                createReqDTO.setDlpMapId(config.getDlpMapId());

                // 直接调用现有的创建方法（包含配额检查和消费逻辑）
                CommonResult<Boolean> createResult = createConfig(createReqDTO);

                if (createResult.isSuccess() && Boolean.TRUE.equals(createResult.getData())) {
                    result.incrementSuccess();
                    result.getSuccessDlpMapIds().add(config.getDlpMapId());
                } else {
                    result.incrementFailure();
                    result.getFailureDetails().add("用户：" + config.getUserNo() + "，创建失败");
                }

            } catch (Exception e) {
                result.incrementFailure();
                String errorMsg = "用户：" + config.getUserNo() + "，创建异常：" + e.getMessage();
                result.getFailureDetails().add(errorMsg);
                log.error("创建配置异常，用户：{}", config.getUserNo(), e);
            }
        }

        return result;
    }

    /**
     * 批量操作结果
     */
    @Data
    private static class BatchOperationResult {
        private int successCount = 0;
        private List<Long> successDlpMapIds = new ArrayList<>();
        private int failureCount = 0;
        private List<String> failureDetails = new ArrayList<>();

        public void incrementSuccess() {
            successCount++;
        }

        public void incrementFailure() {
            failureCount++;
        }
    }
}
