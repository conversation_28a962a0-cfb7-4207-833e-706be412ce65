package com.tipray.cloud.offline.strategy.extend.dto.converter;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.tipray.cloud.offline.strategy.extend.dto.quota.*;
import com.tipray.cloud.offline.strategy.extend.entity.Quota;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 配额转换器（静态工具类）
 * 提供各种DTO与实体之间的转换功能
 *
 * <AUTHOR>
 */
@Slf4j
public final class QuotaConverter {

    private QuotaConverter() {
        // 工具类，禁止实例化
    }

    /**
     * 通用DTO转实体方法
     * 支持多种DTO类型转换为Quota实体
     *
     * @param dto DTO对象
     * @return 实体对象
     */
    public static Quota toEntity(Object dto) {
        if (dto == null) {
            return null;
        }

        if (dto instanceof QuotaCreateReqDTO) {
            return fromCreateReqDTO((QuotaCreateReqDTO) dto);
        } else if (dto instanceof QuotaUpdateReqDTO) {
            return fromUpdateReqDTO((QuotaUpdateReqDTO) dto);
        } else if (dto instanceof QuotaPageReqDTO) {
            return fromPageReqDTO((QuotaPageReqDTO) dto);
        } else if (dto instanceof QuotaRespDTO) {
            return fromRespDTO((QuotaRespDTO) dto);
        } else {
            // 使用BeanUtil作为兜底方案
            Quota entity = new Quota();
            BeanUtil.copyProperties(dto, entity);
            log.debug("使用BeanUtil转换，源类型：{}", dto.getClass().getSimpleName());
            return entity;
        }
    }

    /**
     * 创建请求DTO转实体
     */
    public static Quota fromCreateReqDTO(QuotaCreateReqDTO createDTO) {
        if (createDTO == null) {
            return null;
        }

        Quota entity = new Quota();
        entity.setUserNo(createDTO.getUserNo());
        entity.setQuotaType(createDTO.getQuotaType());
        entity.setTotalQuota(createDTO.getTotalQuota());
        entity.setUsedQuota(createDTO.getUsedQuota());
        entity.setCreatedBy(createDTO.getCreatedBy());
        return entity;
    }

    /**
     * 更新请求DTO转实体
     */
    public static Quota fromUpdateReqDTO(QuotaUpdateReqDTO updateDTO) {
        if (updateDTO == null) {
            return null;
        }

        Quota entity = new Quota();
        entity.setId(updateDTO.getId());
        entity.setQuotaType(updateDTO.getQuotaType());
        entity.setTotalQuota(updateDTO.getTotalQuota());
        entity.setUsedQuota(updateDTO.getUsedQuota());
        entity.setUpdatedBy(updateDTO.getUpdatedBy());
        return entity;
    }

    /**
     * 分页请求DTO转实体
     */
    public static Quota fromPageReqDTO(QuotaPageReqDTO pageDTO) {
        if (pageDTO == null) {
            return null;
        }

        Quota entity = new Quota();
        entity.setUserNo(pageDTO.getUserNo());
        entity.setQuotaType(pageDTO.getQuotaType());
        return entity;
    }

    /**
     * 响应DTO转实体
     */
    public static Quota fromRespDTO(QuotaRespDTO respDTO) {
        if (respDTO == null) {
            return null;
        }

        Quota entity = new Quota();
        entity.setId(respDTO.getId());
        entity.setUserNo(respDTO.getUserNo());
        entity.setQuotaType(respDTO.getQuotaType());
        entity.setTotalQuota(respDTO.getTotalQuota());
        entity.setUsedQuota(respDTO.getUsedQuota());
        entity.setCreateTime(respDTO.getCreateTime());
        entity.setCreatedBy(respDTO.getCreatedBy());
        entity.setUpdateTime(respDTO.getUpdateTime());
        entity.setUpdatedBy(respDTO.getUpdatedBy());
        return entity;
    }

    /**
     * 实体转响应DTO
     */
    public static QuotaRespDTO toRespDTO(Quota entity) {
        if (entity == null) {
            return null;
        }

        QuotaRespDTO respDTO = new QuotaRespDTO();
        BeanUtil.copyProperties(entity, respDTO);

        // 设置计算字段
        respDTO.setRemainingQuota(entity.getRemainingQuota());
        respDTO.setUsageRate(entity.getUsageRate());

        return respDTO;
    }

    /**
     * 实体转创建请求DTO
     */
    public static QuotaCreateReqDTO toCreateReqDTO(Quota entity) {
        if (entity == null) {
            return null;
        }

        QuotaCreateReqDTO createDTO = new QuotaCreateReqDTO();
        createDTO.setUserNo(entity.getUserNo());
        createDTO.setQuotaType(entity.getQuotaType());
        createDTO.setTotalQuota(entity.getTotalQuota());
        createDTO.setUsedQuota(entity.getUsedQuota());
        createDTO.setCreatedBy(entity.getCreatedBy());
        return createDTO;
    }

    /**
     * 实体转更新请求DTO
     */
    public static QuotaUpdateReqDTO toUpdateReqDTO(Quota entity) {
        if (entity == null) {
            return null;
        }

        QuotaUpdateReqDTO updateDTO = new QuotaUpdateReqDTO();
        updateDTO.setId(entity.getId());
        updateDTO.setQuotaType(entity.getQuotaType());
        updateDTO.setTotalQuota(entity.getTotalQuota());
        updateDTO.setUsedQuota(entity.getUsedQuota());
        updateDTO.setUpdatedBy(entity.getUpdatedBy());
        return updateDTO;
    }

    /**
     * 批量DTO转实体
     */
    public static List<Quota> toEntityList(List<?> dtoList) {
        if (CollUtil.isEmpty(dtoList)) {
            return CollUtil.newArrayList();
        }

        return dtoList.stream()
                .map(QuotaConverter::toEntity)
                .collect(Collectors.toList());
    }

    /**
     * 批量实体转响应DTO
     */
    public static List<QuotaRespDTO> toRespDTOList(List<Quota> entityList) {
        if (CollUtil.isEmpty(entityList)) {
            return CollUtil.newArrayList();
        }

        return entityList.stream()
                .map(QuotaConverter::toRespDTO)
                .collect(Collectors.toList());
    }

    /**
     * 配额操作请求DTO转实体（用于增减配额操作）
     */
    public static Quota fromOperationReqDTO(QuotaOperationReqDTO operationDTO) {
        if (operationDTO == null) {
            return null;
        }

        Quota entity = new Quota();
        entity.setId(operationDTO.getId());
        entity.setUpdatedBy(operationDTO.getOperator());
        return entity;
    }
}
