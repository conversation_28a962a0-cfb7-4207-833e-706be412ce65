package com.tipray.cloud.offline.strategy.extend.dto.offlinestrategyextendlog;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 立即删除可删除状态的日志请求DTO
 *
 * <AUTHOR>
 */
@Schema(description = "立即删除可删除状态的日志请求")
@Data
public class SyncLogDeleteReqDTO implements Serializable {

    @Schema(description = "日志ID列表（可选，为空时删除该用户所有可删除状态的日志）")
    private List<Integer> logIds;
}
