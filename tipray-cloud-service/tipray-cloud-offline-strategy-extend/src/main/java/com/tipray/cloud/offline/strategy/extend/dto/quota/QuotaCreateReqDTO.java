package com.tipray.cloud.offline.strategy.extend.dto.quota;

import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 配额创建请求DTO
 *
 * <AUTHOR>
 */
@Data
public class QuotaCreateReqDTO {

    /**
     * 用户编号
     */
    @NotBlank(message = "用户编号不能为空")
    private String userNo;

    /**
     * 配额类型
     */
    @NotNull(message = "配额类型不能为空")
    private Integer quotaType;

    /**
     * 分配的配额
     */
    @NotNull(message = "分配的配额不能为空")
    @Min(value = 0, message = "分配的配额不能小于0")
    private Integer totalQuota;

    /**
     * 已使用的配额
     */
    @Min(value = 0, message = "已使用的配额不能小于0")
    private Integer usedQuota = 0;

    /**
     * 创建人
     */
    @NotBlank(message = "创建人不能为空")
    private String createdBy;
}
