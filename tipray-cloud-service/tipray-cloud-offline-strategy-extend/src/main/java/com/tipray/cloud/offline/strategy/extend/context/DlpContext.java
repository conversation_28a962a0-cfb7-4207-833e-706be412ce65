package com.tipray.cloud.offline.strategy.extend.context;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;

/**
 * DLP上下文信息
 * 存储当前请求的DLP相关信息
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-05-20
 */
@Data
@Slf4j
public class DlpContext {

    /**
     * 用户编号
     */
    private String userNo;

    /**
     * 终端编号
     */
    private String terminalNo;

    /**
     * 请求ID
     */
    private String requestId;

    /**
     * 客户端IP
     */
    private String clientIp;

    /**
     * 用户代理
     */
    private String userAgent;

    /**
     * 请求时间戳
     */
    private Long timestamp;

    /**
     * 扩展属性
     */
    private java.util.Map<String, String> extendProperties;

    /**
     * 线程本地变量存储上下文
     */
    private static final ThreadLocal<DlpContext> CONTEXT_HOLDER = new ThreadLocal<>();

    /**
     * 获取当前上下文
     *
     * @return DLP上下文
     */
    public static DlpContext getCurrentContext() {
        return CONTEXT_HOLDER.get();
    }

    /**
     * 设置当前上下文
     *
     * @param context DLP上下文
     */
    public static void setCurrentContext(DlpContext context) {
        CONTEXT_HOLDER.set(context);
        if (context != null) {
            log.debug("设置DLP上下文 - 用户: {}, 终端: {}, 请求ID: {}",
                    context.getUserNo(), context.getTerminalNo(), context.getRequestId());
        }
    }

    /**
     * 清除当前上下文
     */
    public static void clearCurrentContext() {
        DlpContext current = CONTEXT_HOLDER.get();
        if (current != null) {
            log.debug("清除DLP上下文 - 用户: {}, 请求ID: {}",
                    current.getUserNo(), current.getRequestId());
        }
        CONTEXT_HOLDER.remove();
    }

    /**
     * 获取当前用户编号
     *
     * @return 用户编号
     */
    public static String getCurrentUserNo() {
        DlpContext context = getCurrentContext();
        return context != null ? context.getUserNo() : null;
    }

    /**
     * 获取当前终端编号
     *
     * @return 终端编号
     */
    public static String getCurrentTerminalNo() {
        DlpContext context = getCurrentContext();
        return context != null ? context.getTerminalNo() : null;
    }

    /**
     * 获取当前请求ID
     *
     * @return 请求ID
     */
    public static String getCurrentRequestId() {
        DlpContext context = getCurrentContext();
        return context != null ? context.getRequestId() : null;
    }

    /**
     * 判断是否存在上下文
     *
     * @return true表示存在上下文
     */
    public static boolean hasContext() {
        return getCurrentContext() != null;
    }

    /**
     * 添加扩展属性
     *
     * @param key 属性键
     * @param value 属性值
     */
    public void addExtendProperty(String key, String value) {
        if (extendProperties == null) {
            extendProperties = new java.util.HashMap<>();
        }
        extendProperties.put(key, value);
    }

    /**
     * 获取扩展属性
     *
     * @param key 属性键
     * @return 属性值
     */
    public String getExtendProperty(String key) {
        return extendProperties != null ? extendProperties.get(key) : null;
    }

    /**
     * 创建DLP上下文
     *
     * @param userNo 用户编号
     * @param terminalNo 终端编号
     * @param requestId 请求ID
     * @return DLP上下文
     */
    public static DlpContext create(String userNo, String terminalNo, String requestId) {
        DlpContext context = new DlpContext();
        context.setUserNo(userNo);
        context.setTerminalNo(terminalNo);
        context.setRequestId(requestId);
        context.setTimestamp(System.currentTimeMillis());
        return context;
    }

    /**
     * 从请求头创建DLP上下文
     *
     * @param userNo 用户编号（从x-ca-issuer获取）
     * @param terminalNo 终端编号（从x-terminal-no获取）
     * @param requestId 请求ID（从x-request-id获取）
     * @param clientIp 客户端IP
     * @param userAgent 用户代理
     * @return DLP上下文
     */
    public static DlpContext createFromHeaders(String userNo, String terminalNo, String requestId,
                                             String clientIp, String userAgent) {
        DlpContext context = new DlpContext();
        context.setUserNo(userNo);
        context.setTerminalNo(terminalNo);
        context.setRequestId(requestId);
        context.setClientIp(clientIp);
        context.setUserAgent(userAgent);
        context.setTimestamp(System.currentTimeMillis());
        return context;
    }
}
