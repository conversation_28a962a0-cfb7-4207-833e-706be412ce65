package com.tipray.cloud.offline.strategy.extend.service;

import com.tipray.cloud.offline.strategy.extend.dto.quota.*;
import com.tipray.cloud.offline.strategy.extend.entity.Quota;
import com.tipray.cloud.pojo.PageResult;

import java.util.List;

/**
 * 配额服务接口
 *
 * <AUTHOR>
 */
public interface QuotaService {

    /**
     * 创建配额
     *
     * @param createReqDTO 创建请求DTO
     * @return 配额ID
     */
    Integer createQuota(QuotaCreateReqDTO createReqDTO);

    /**
     * 更新配额
     *
     * @param updateReqDTO 更新请求DTO
     */
    void updateQuota(QuotaUpdateReqDTO updateReqDTO);

    /**
     * 删除配额
     *
     * @param id 配额ID
     */
    void deleteQuota(Long id);

    /**
     * 根据ID获取配额
     *
     * @param id 配额ID
     * @return 配额信息
     */
    QuotaRespDTO getQuota(Long id);

    /**
     * 分页查询配额
     *
     * @param pageReqDTO 分页查询请求DTO
     * @return 分页结果
     */
    PageResult<QuotaRespDTO> getQuotaPage(QuotaPageReqDTO pageReqDTO);

    /**
     * 根据用户编号查询配额列表
     *
     * @param userNo 用户编号
     * @return 配额列表
     */
    List<QuotaRespDTO> getQuotaListByUserNo(String userNo);

    /**
     * 根据用户编号和类型查询配额
     *
     * @param userNo 用户编号
     * @param type   配额类型
     * @return 配额信息
     */
    QuotaRespDTO getQuotaByUserNoAndType(String userNo, Integer type);

    /**
     * 根据配额类型查询配额列表
     *
     * @param type 配额类型
     * @return 配额列表
     */
    List<QuotaRespDTO> getQuotaListByType(Integer type);

    /**
     * 增加已使用配额
     *
     * @param operationReqDTO 操作请求DTO
     * @return 是否成功
     */
    boolean increaseUsedQuota(QuotaOperationReqDTO operationReqDTO);

    /**
     * 减少已使用配额
     *
     * @param operationReqDTO 操作请求DTO
     * @return 是否成功
     */
    boolean decreaseUsedQuota(QuotaOperationReqDTO operationReqDTO);

    /**
     * 更新总配额
     *
     * @param id         配额ID
     * @param totalQuota 新的总配额
     * @param updatedBy  更新人
     * @return 是否成功
     */
    boolean updateTotalQuota(Long id, Integer totalQuota, String updatedBy);

    /**
     * 重置已使用配额
     *
     * @param id        配额ID
     * @param updatedBy 更新人
     * @return 是否成功
     */
    boolean resetUsedQuota(Long id, String updatedBy);

    /**
     * 查询配额不足的用户列表
     *
     * @param type           配额类型
     * @param thresholdRate  阈值比例（百分比）
     * @return 配额信息列表
     */
    List<QuotaRespDTO> getQuotaShortageList(Integer type, Double thresholdRate);

    /**
     * 统计用户配额数量
     *
     * @param userNo 用户编号
     * @return 配额数量
     */
    Long countByUserNo(String userNo);

    /**
     * 检查配额是否充足
     *
     * @param userNo       用户编号
     * @param type         配额类型
     * @param requiredQuota 需要的配额
     * @return 是否充足
     */
    Boolean checkQuotaSufficient(String userNo, Integer type, Integer requiredQuota);

    /**
     * 检查配额是否存在
     *
     * @param userNo 用户编号
     * @param type   配额类型
     * @return 是否存在
     */
    boolean existsByUserNoAndType(String userNo, Integer type);

    /**
     * 消费配额（线程安全）
     * 原子操作，只有当剩余配额足够时才会扣减
     *
     * @param userNo 用户编号
     * @param type   配额类型
     * @param amount 消费数量
     * @return 是否成功
     */
    boolean consumeQuota(String userNo, Integer type, Integer amount);

    /**
     * 增加配额（线程安全）
     * 原子操作，增加总配额数量
     *
     * @param userNo 用户编号
     * @param type   配额类型
     * @param amount 增加数量
     * @return 是否成功
     */
    boolean increaseQuota(String userNo, Integer type, Integer amount);

    /**
     * 释放配额（线程安全）
     * 原子操作，减少已使用配额，释放配额供后续使用
     * 只有当已使用配额足够时才会释放，防止过度释放
     *
     * @param userNo 用户编号
     * @param type   配额类型
     * @param amount 释放数量
     * @return 是否成功
     */
    boolean releaseQuota(String userNo, Integer type, Integer amount);

    /**
     * 强制释放配额（线程安全）
     * 原子操作，强制减少已使用配额，确保不会变成负数
     * 用于异常情况下的强制回滚
     *
     * @param userNo 用户编号
     * @param type   配额类型
     * @param amount 释放数量
     * @return 是否成功
     */
    boolean forceReleaseQuota(String userNo, Integer type, Integer amount);

    /**
     * 设置配额（线程安全）
     * 原子操作，直接设置总配额和已使用配额
     *
     * @param userNo     用户编号
     * @param type       配额类型
     * @param totalQuota 总配额
     * @param usedQuota  已使用配额
     * @return 是否成功
     */
    boolean setQuota(String userNo, Integer type, Integer totalQuota, Integer usedQuota);

    /**
     * 使用悲观锁查询配额记录
     *
     * @param userNo 用户编号
     * @param type   配额类型
     * @return 配额记录
     */
    QuotaRespDTO selectForUpdate(String userNo, Integer type);

    /**
     * 初始化用户配额
     *
     * @param userNo 用户编号
     * @return 是否成功
     */
    void initQuota(String userNo);

    /**
     * 消费配额（带自动初始化）
     * 如果配额不存在会自动初始化，然后再消费
     *
     * @param userNo 用户编号
     * @param type   配额类型
     * @param amount 消费数量
     * @return 是否成功
     */
    boolean consumeQuotaWithAutoInit(String userNo, Integer type, Integer amount);

    /**
     * 实体转换为响应DTO
     *
     * @param entity 实体对象
     * @return 响应DTO
     */
    QuotaRespDTO convertToRespDTO(Quota entity);

    /**
     * 实体列表转换为响应DTO列表
     *
     * @param entities 实体列表
     * @return 响应DTO列表
     */
    List<QuotaRespDTO> convertToRespDTOList(List<Quota> entities);
}
