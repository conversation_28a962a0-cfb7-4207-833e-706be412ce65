package com.tipray.cloud.offline.strategy.extend.dto.offlinestrategyextendconfig;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 离线策略延长配置响应DTO
 *
 * <AUTHOR>
 */
@Data
public class OfflineStrategyExtendConfigRespDTO {

    /**
     * 主键ID
     */
    private Integer id;

    /**
     * 用户编号
     */
    private String userNo;

    /**
     * 配置类型（分支/终端）
     */
    private Integer targetType;

    /**
     * 配置类型描述
     */
    private String typeDesc;

    /**
     * 生效目标 分组编号/终端编号
     */
    private String target;

    /**
     * 密文内容json：effective_time，expire_time
     */
    private String strategyTimeContext;

    /**
     * 当前版本号
     */
    private Integer version;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * dlp上对应的id，用于修改、删除
     */
    private Long dlpMapId;
}
