package com.tipray.cloud.offline.strategy.extend.dto.quota;

import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 配额更新请求DTO
 *
 * <AUTHOR>
 */
@Data
public class QuotaUpdateReqDTO {

    /**
     * 主键ID
     */
    @NotNull(message = "配额ID不能为空")
    private Integer id;

    /**
     * 配额类型
     */
    private Integer quotaType;

    /**
     * 分配的配额
     */
    @Min(value = 0, message = "分配的配额不能小于0")
    private Integer totalQuota;

    /**
     * 已使用的配额
     */
    @Min(value = 0, message = "已使用的配额不能小于0")
    private Integer usedQuota;

    /**
     * 修改人
     */
    @NotBlank(message = "修改人不能为空")
    private String updatedBy;
}
