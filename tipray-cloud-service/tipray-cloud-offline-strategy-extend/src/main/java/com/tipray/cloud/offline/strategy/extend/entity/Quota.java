package com.tipray.cloud.offline.strategy.extend.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 配额实体
 *
 * <AUTHOR>
 */
@TableName("ose_quota")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class Quota {

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Integer id;

    /**
     * 用户编号
     */
    private String userNo;

    /**
     * 配额类型（预留）
     */
    private Integer quotaType;

    /**
     * 分配的配额
     */
    private Integer totalQuota;

    /**
     * 已使用的配额
     */
    private Integer usedQuota;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 修改时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 修改人
     */
    private String updatedBy;

    /**
     * 获取剩余配额
     */
    public Integer getRemainingQuota() {
        if (totalQuota == null || usedQuota == null) {
            return 0;
        }
        return Math.max(0, totalQuota - usedQuota);
    }

    /**
     * 获取配额使用率（百分比）
     */
    public Double getUsageRate() {
        if (totalQuota == null || totalQuota == 0 || usedQuota == null) {
            return 0.0;
        }
        return (double) usedQuota / totalQuota * 100;
    }

    /**
     * 检查配额是否充足
     */
    public boolean isQuotaSufficient(Integer requiredQuota) {
        if (requiredQuota == null || requiredQuota <= 0) {
            return true;
        }
        return getRemainingQuota() >= requiredQuota;
    }
}
