package com.tipray.cloud.offline.strategy.extend.api;

import com.tipray.cloud.offline.strategy.extend.dto.quota.QuotaRespDTO;
import com.tipray.cloud.pojo.CommonResult;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;

@RequestMapping("/api/quota")
public interface QuotaApi {

    /**
     * 获取云上配额的使用情况
     *
     */
    @GetMapping("/get")
    CommonResult<QuotaRespDTO> getQuotaByUserNo();
}
