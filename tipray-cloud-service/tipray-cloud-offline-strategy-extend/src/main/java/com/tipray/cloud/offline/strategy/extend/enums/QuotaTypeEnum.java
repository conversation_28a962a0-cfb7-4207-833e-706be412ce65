package com.tipray.cloud.offline.strategy.extend.enums;

import com.tipray.cloud.enums.BaseEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Objects;

/**
 * 配额类型
 */
@Getter
@AllArgsConstructor
public enum QuotaTypeEnum implements BaseEnum<Integer> {

    OFFLINE_STRATEGY_EXTEND_CONFIG("离线策略延长配置配额", 0);

    /**
     * 类型名
     */
    private String desc;
    /**
     * 类型值
     */
    private Integer value;


    public static QuotaTypeEnum getValue(Integer type) {
        return Arrays.stream(QuotaTypeEnum.values()).filter(quotaTypeEnum -> Objects.equals(quotaTypeEnum.getValue(), type)).findAny().orElse(null);
    }
}
