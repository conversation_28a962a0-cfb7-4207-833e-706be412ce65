package com.tipray.cloud.offline.strategy.extend.dto.offlinestrategyextendlog;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 准备同步日志请求DTO
 *
 * <AUTHOR>
 */
@Schema(description = "准备同步日志请求")
@Data
public class SyncLogPrepareReqDTO implements Serializable {

    @Schema(description = "上次查询的最大ID（游标），默认为0", example = "0")
    @Min(value = 0, message = "游标ID不能小于0")
    private Integer lastId = 0;

    @Schema(description = "页大小，默认为100", example = "100")
    @NotNull(message = "页大小不能为空")
    @Min(value = 1, message = "页大小不能小于1")
    @Max(value = 1000, message = "页大小不能大于1000")
    private Integer pageSize = 100;
}
