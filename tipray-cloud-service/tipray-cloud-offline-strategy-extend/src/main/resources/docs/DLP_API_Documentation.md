# DLP离线策略延长配置API文档

## 概述
本文档描述了专门为DLP（数据丢失防护）系统提供的离线策略延长配置API接口。这些接口允许DLP系统创建、更新和删除离线策略延长配置。

## 基础信息
- **基础路径**: `/api/dlp/config`
- **内容类型**: `application/json`
- **字符编码**: `UTF-8`

## API接口

### 1. 创建离线策略延长配置

**接口地址**: `POST /api/dlp/config/create`

**请求参数**:
```json
{
  "userNo": "USER001",                    // 用户编号（必填）
  "type": 1,                             // 配置类型：1-分支配置，2-终端配置（必填）
  "target": "BRANCH001",                 // 生效目标：分组编号/终端编号（必填）
  "strategyTimeContext": "{\"effective_time\":\"2024-01-01 00:00:00\",\"expire_time\":\"2024-12-31 23:59:59\"}", // 密文内容json（必填）
  "version": 1                           // 版本号（可选，默认为1）
}
```

**响应结果**:
```json
{
  "code": "200",
  "message": "操作成功",
  "data": 1,                            // 返回DLP映射ID
  "success": true,
  "timestamp": 1703123456789
}
```

### 2. 更新离线策略延长配置

**接口地址**: `PUT /api/dlp/config/update`

**请求参数**:
```json
{
  "dlpMapId": 1,                        // DLP映射ID（必填）
  "userNo": "USER001",                  // 用户编号（可选）
  "type": 2,                            // 配置类型（可选）
  "target": "TERMINAL001",              // 生效目标（可选）
  "strategyTimeContext": "{\"effective_time\":\"2024-01-01 00:00:00\",\"expire_time\":\"2025-12-31 23:59:59\"}", // 密文内容json（可选）
  "version": 2                          // 版本号（可选）
}
```

**响应结果**:
```json
{
  "code": "200",
  "message": "操作成功",
  "data": true,
  "success": true,
  "timestamp": 1703123456789
}
```

### 3. 删除离线策略延长配置

**接口地址**: `DELETE /api/dlp/config/delete`

**请求参数**:
```json
{
  "dlpMapId": 1                         // DLP映射ID（必填）
}
```

**响应结果**:
```json
{
  "code": "200",
  "message": "操作成功",
  "data": true,
  "success": true,
  "timestamp": 1703123456789
}
```

### 4. 根据DLP映射ID获取配置

**接口地址**: `GET /api/dlp/config/get/{dlpMapId}`

**路径参数**:
- `dlpMapId`: DLP映射ID

**响应结果**:
```json
{
  "code": "200",
  "message": "操作成功",
  "data": {
    "id": 1,                            // 主键ID
    "userNo": "USER001",                // 用户编号
    "type": 1,                          // 配置类型
    "typeDesc": "分支配置",              // 配置类型描述
    "target": "BRANCH001",              // 生效目标
    "strategyTimeContext": "{\"effective_time\":\"2024-01-01 00:00:00\",\"expire_time\":\"2024-12-31 23:59:59\"}", // 密文内容json
    "version": 1,                       // 版本号
    "createTime": "2024-01-01T10:00:00", // 创建时间
    "dlpMapId": 1                       // DLP映射ID
  },
  "success": true,
  "timestamp": 1703123456789
}
```

## 错误码说明

| 错误码 | 错误信息 | 说明 |
|--------|----------|------|
| 200 | 操作成功 | 请求处理成功 |
| 500 | 操作失败 | 服务器内部错误 |

## 常见错误信息

1. **该用户的相同类型和目标配置已存在**
   - 创建配置时，如果用户编号、配置类型、生效目标的组合已存在

2. **配置不存在，DLP映射ID：xxx**
   - 更新或删除配置时，指定的DLP映射ID不存在

3. **用户编号不能为空**
   - 创建配置时未提供用户编号

4. **配置类型不能为空**
   - 创建配置时未提供配置类型

5. **生效目标不能为空**
   - 创建配置时未提供生效目标

6. **策略时间内容不能为空**
   - 创建配置时未提供策略时间内容

7. **DLP映射ID不能为空**
   - 更新或删除配置时未提供DLP映射ID

## 注意事项

1. **DLP映射ID生成规则**: 创建配置成功后，系统会自动生成DLP映射ID，该ID与配置的主键ID相同
2. **唯一性约束**: 用户编号、配置类型、生效目标的组合必须唯一
3. **更新策略**: 更新接口只会更新提供的非空字段，未提供的字段保持原值不变
4. **事务处理**: 所有写操作（创建、更新、删除）都在事务中执行，确保数据一致性
5. **日志记录**: 所有操作都会记录详细的日志信息，便于问题排查

## 使用示例

### 创建配置示例
```bash
curl -X POST "http://localhost:8080/api/dlp/config/create" \
  -H "Content-Type: application/json" \
  -d '{
    "userNo": "USER001",
    "type": 1,
    "target": "BRANCH001",
    "strategyTimeContext": "{\"effective_time\":\"2024-01-01 00:00:00\",\"expire_time\":\"2024-12-31 23:59:59\"}",
    "version": 1
  }'
```

### 更新配置示例
```bash
curl -X PUT "http://localhost:8080/api/dlp/config/update" \
  -H "Content-Type: application/json" \
  -d '{
    "dlpMapId": 1,
    "strategyTimeContext": "{\"effective_time\":\"2024-01-01 00:00:00\",\"expire_time\":\"2025-12-31 23:59:59\"}",
    "version": 2
  }'
```

### 删除配置示例
```bash
curl -X DELETE "http://localhost:8080/api/dlp/config/delete" \
  -H "Content-Type: application/json" \
  -d '{
    "dlpMapId": 1
  }'
```

### 获取配置示例
```bash
curl -X GET "http://localhost:8080/api/dlp/config/get/1"
```
