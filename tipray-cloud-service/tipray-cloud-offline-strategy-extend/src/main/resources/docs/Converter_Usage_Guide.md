# 通用转换器使用指南

## 概述

本项目提供了多种转换器来处理不同对象之间的转换，包括通用转换工具类和专门的业务转换器。

## 转换器类型

### 1. 通用转换工具类 (ConvertUtil)

位置：`tipray-cloud-common/tipray-cloud-core/src/main/java/com/tipray/cloud/util/ConvertUtil.java`

#### 基本使用

```java
// 单个对象转换
UserRespDTO userResp = ConvertUtil.convert(user, UserRespDTO.class);

// 列表转换
List<UserRespDTO> userRespList = ConvertUtil.convertList(userList, UserRespDTO.class);

// 使用自定义转换函数
UserRespDTO userResp = ConvertUtil.convert(user, entity -> {
    UserRespDTO dto = new UserRespDTO();
    dto.setId(entity.getId());
    dto.setName(entity.getName());
    // 自定义转换逻辑
    return dto;
});

// 安全转换（失败时返回默认值）
UserRespDTO userResp = ConvertUtil.convertSafely(user, UserRespDTO.class, new UserRespDTO());

// 条件转换
UserRespDTO userResp = ConvertUtil.convertIf(user, UserRespDTO.class, 
    entity -> entity.getStatus() == UserStatus.ACTIVE);

// 链式转换
String result = ConvertUtil.chain(user)
    .to(UserRespDTO.class)
    .to(dto -> dto.getName())
    .get();
```

### 2. 离线策略延长配置转换器 (OfflineStrategyExtendConfigConverter)

位置：`tipray-cloud-service/tipray-cloud-offline-strategy-extend/src/main/java/com/tipray/cloud/offline/strategy/extend/converter/OfflineStrategyExtendConfigConverter.java`

#### 支持的转换类型

- `OfflineStrategyExtendConfigCreateReqDTO` → `OfflineStrategyExtendConfig`
- `OfflineStrategyExtendConfigUpdateReqDTO` → `OfflineStrategyExtendConfig`
- `OfflineStrategyExtendConfigPageReqDTO` → `OfflineStrategyExtendConfig`
- `OfflineStrategyExtendConfigRespDTO` → `OfflineStrategyExtendConfig`
- `OfflineStrategyExtendLogCreateReqDTO` → `OfflineStrategyExtendConfig`
- `OfflineStrategyExtendLogPageReqDTO` → `OfflineStrategyExtendConfig`
- `OfflineStrategyExtendLogRespDTO` → `OfflineStrategyExtendConfig`

#### 使用示例

```java
@Autowired
private OfflineStrategyExtendConfigConverter configConverter;

// 通用转换方法
OfflineStrategyExtendConfig entity = configConverter.toEntity(createReqDTO);
OfflineStrategyExtendConfig entity = configConverter.toEntity(updateReqDTO);
OfflineStrategyExtendConfig entity = configConverter.toEntity(logCreateReqDTO);

// 专门的转换方法
OfflineStrategyExtendConfig entity = configConverter.fromCreateReqDTO(createReqDTO);
OfflineStrategyExtendConfig entity = configConverter.fromUpdateReqDTO(updateReqDTO);
OfflineStrategyExtendConfig entity = configConverter.fromLogCreateReqDTO(logCreateReqDTO);

// 实体转DTO
OfflineStrategyExtendConfigRespDTO respDTO = configConverter.toRespDTO(entity);
OfflineStrategyExtendConfigCreateReqDTO createDTO = configConverter.toCreateReqDTO(entity);
OfflineStrategyExtendConfigUpdateReqDTO updateDTO = configConverter.toUpdateReqDTO(entity);

// 批量转换
List<OfflineStrategyExtendConfig> entities = configConverter.toEntityList(dtoList);
List<OfflineStrategyExtendConfigRespDTO> respDTOList = configConverter.toRespDTOList(entities);
```

### 3. Service中的通用转换方法

在`OfflineStrategyExtendConfigServiceImpl`中也提供了通用转换方法：

```java
// 通用DTO转实体
OfflineStrategyExtendConfig entity = convertToEntity(dto);

// 批量转换
List<OfflineStrategyExtendConfig> entities = convertToEntityList(dtoList);

// 实体转DTO
OfflineStrategyExtendConfigRespDTO respDTO = convertToRespDTO(entity);
List<OfflineStrategyExtendConfigRespDTO> respDTOList = convertToRespDTOList(entities);

// 通用类型转换
UserRespDTO userResp = convertTo(user, UserRespDTO.class);
List<UserRespDTO> userRespList = convertToList(userList, UserRespDTO.class);
```

## 转换规则说明

### 1. 配置相关DTO转实体

- **CreateReqDTO**: 包含创建配置所需的所有字段
- **UpdateReqDTO**: 包含更新配置的字段，ID必填
- **PageReqDTO**: 包含查询条件字段
- **RespDTO**: 包含完整的配置信息

### 2. 日志相关DTO转实体

- **LogCreateReqDTO**: 提取policyId作为配置ID，userNo转为字符串
- **LogPageReqDTO**: 提取查询条件中的配置相关信息
- **LogRespDTO**: 提取日志中的配置相关信息

### 3. 特殊处理

- **类型描述**: 实体转RespDTO时会自动设置typeDesc字段
- **数据类型转换**: Long类型的userNo会转换为String类型
- **空值处理**: 所有转换方法都会处理null值情况
- **兜底机制**: 不支持的类型会使用BeanUtil进行通用转换

## 最佳实践

### 1. 选择合适的转换器

- **简单转换**: 使用ConvertUtil
- **业务转换**: 使用专门的业务转换器
- **复杂转换**: 使用自定义转换函数

### 2. 性能考虑

- 批量转换时使用批量方法
- 避免在循环中进行单个转换
- 缓存转换结果（如果适用）

### 3. 错误处理

- 使用安全转换方法处理可能失败的转换
- 检查转换结果是否为null
- 记录转换失败的日志

### 4. 代码示例

```java
@Service
public class UserServiceImpl implements UserService {
    
    @Autowired
    private OfflineStrategyExtendConfigConverter configConverter;
    
    public void createConfig(OfflineStrategyExtendConfigCreateReqDTO createReqDTO) {
        // 参数校验
        ExceptionUtil.assertNotNull(createReqDTO, "创建参数不能为空");
        
        // 转换为实体
        OfflineStrategyExtendConfig entity = configConverter.fromCreateReqDTO(createReqDTO);
        
        // 业务处理
        configMapper.insert(entity);
        
        // 返回结果
        return configConverter.toRespDTO(entity);
    }
    
    public List<OfflineStrategyExtendConfigRespDTO> getConfigList(List<Object> dtoList) {
        // 批量转换为实体
        List<OfflineStrategyExtendConfig> entities = configConverter.toEntityList(dtoList);
        
        // 业务处理
        // ...
        
        // 批量转换为响应DTO
        return configConverter.toRespDTOList(entities);
    }
}
```

## 注意事项

1. **类型安全**: 转换器会进行类型检查，确保转换的安全性
2. **性能**: 大量数据转换时注意性能影响
3. **扩展性**: 新增DTO类型时需要更新转换器
4. **一致性**: 保持转换逻辑的一致性
5. **测试**: 为转换逻辑编写单元测试

## 扩展指南

### 添加新的DTO类型支持

1. 在`convertToEntity`方法中添加新的`instanceof`判断
2. 实现具体的转换逻辑
3. 添加对应的专门转换方法
4. 更新文档和测试

### 创建新的业务转换器

1. 继承或参考现有转换器结构
2. 实现业务特定的转换逻辑
3. 注册为Spring Bean
4. 在Service中注入使用
