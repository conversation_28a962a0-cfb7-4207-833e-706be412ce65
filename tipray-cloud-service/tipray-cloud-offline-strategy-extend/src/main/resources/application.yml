server:
  port: 9000

spring:
  mvc:
    servlet:
      path: /
  application:
    name: tipray-cloud-offline-strategy-extend
  datasource:
    url: jdbc:mysql://${offlineStrategyExtendDBHost:localhost}:${offlineStrategyExtendDBPort:3306}/${offlineStrategyExtendDB:tipray-cloud-offline-strategy-extend}?useUnicode=true&characterEncoding=utf8&useSSL=false&serverTimezone=Asia/Shanghai&allowPublicKeyRetrieval=true
    username: ${offlineStrategyExtendDBUserName:root}
    password: ${offlineStrategyExtendDBPassword:123456}
    driver-class-name: com.mysql.cj.jdbc.Driver

tipray:
  info:
    version: 1.0
    mapper-base-package: com.tipray.cloud.offline.strategy.extend.mapper
  transaction:
    client:
      # 是否启用事务客户端
      enabled: true
      # 客户端ID
      client-id: tipray-cloud-offline-strategy-extend
      # 应用名称
      application-name: ${spring.application.name:unknown}
      # AT模式配置
      at:
        enabled: true

