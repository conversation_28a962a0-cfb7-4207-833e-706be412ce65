<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.tipray.cloud.offline.strategy.extend.mapper.QuotaMapper">

    <resultMap id="BaseResultMap" type="com.tipray.cloud.offline.strategy.extend.entity.Quota">
        <id column="id" property="id" jdbcType="INTEGER"/>
        <result column="user_no" property="userNo" jdbcType="VARCHAR"/>
        <result column="quota_type" property="quotaTypeEnum" jdbcType="INTEGER"/>
        <result column="total_quota" property="totalQuota" jdbcType="INTEGER"/>
        <result column="used_quota" property="usedQuota" jdbcType="INTEGER"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="created_by" property="createdBy" jdbcType="VARCHAR"/>
        <result column="updated_by" property="updatedBy" jdbcType="VARCHAR"/>
    </resultMap>



    <sql id="Base_Column_List">
        id, user_no, quota_type, total_quota, used_quota, create_time, update_time, created_by, updated_by
    </sql>

    <!-- 查询配额不足的用户列表 -->
    <select id="selectQuotaShortage" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM ose_quota
        WHERE quota_type = #{type}
        AND (used_quota * 100.0 / total_quota) >= #{thresholdRate}
        ORDER BY (used_quota * 100.0 / total_quota) DESC
    </select>

    <!-- 检查配额是否充足 -->
    <select id="checkQuotaSufficient" resultType="java.lang.Boolean">
        SELECT CASE WHEN (total_quota - used_quota) >= #{requiredQuota} THEN 1 ELSE 0 END
        FROM ose_quota
        WHERE user_no = #{userNo} AND quota_type = #{type}
    </select>

    <!-- 原子操作：消费配额（线程安全） -->
    <update id="consumeQuotaAtomic">
        UPDATE ose_quota
        SET used_quota = used_quota + #{amount}
        WHERE user_no = #{userNo} AND quota_type = #{type}
        AND (total_quota - used_quota) >= #{amount}
    </update>

    <!-- 原子操作：增加配额（线程安全） -->
    <update id="increaseQuotaAtomic">
        UPDATE ose_quota
        SET total_quota = total_quota + #{amount}
        WHERE user_no = #{userNo} AND quota_type = #{type}
        AND total_quota &lt;= (2147483647 - #{amount})
    </update>

    <!-- 原子操作：释放配额（线程安全） -->
    <update id="releaseQuotaAtomic">
        UPDATE ose_quota
        SET used_quota = used_quota - #{amount}
        WHERE user_no = #{userNo} AND quota_type = #{type}
        AND used_quota &gt;= #{amount}
    </update>

    <!-- 原子操作：强制释放配额（线程安全） -->
    <update id="forceReleaseQuotaAtomic">
        UPDATE ose_quota
        SET used_quota = GREATEST(0, used_quota - #{amount})
        WHERE user_no = #{userNo} AND quota_type = #{type}
    </update>

    <!-- 原子操作：设置配额（线程安全） -->
    <update id="setQuotaAtomic">
        UPDATE ose_quota
        SET total_quota = #{totalQuota}, used_quota = #{usedQuota}
        WHERE user_no = #{userNo} AND quota_type = #{type}
    </update>

    <!-- 使用悲观锁查询配额记录 -->
    <select id="selectForUpdate" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM ose_quota
        WHERE user_no = #{userNo} AND quota_type = #{type}
        FOR UPDATE
    </select>

    <!-- 增加已使用配额 -->
    <update id="increaseUsedQuota">
        UPDATE ose_quota
        SET used_quota = used_quota + #{amount},
            updated_by = #{operator},
            update_time = NOW()
        WHERE id = #{id}
        AND (total_quota - used_quota) &gt;= #{amount}
    </update>

    <!-- 减少已使用配额 -->
    <update id="decreaseUsedQuota">
        UPDATE ose_quota
        SET used_quota = used_quota - #{amount},
            updated_by = #{operator},
            update_time = NOW()
        WHERE id = #{id}
        AND used_quota &gt;= #{amount}
    </update>

    <!-- 更新总配额 -->
    <update id="updateTotalQuota">
        UPDATE ose_quota
        SET total_quota = #{totalQuota},
            updated_by = #{updatedBy},
            update_time = NOW()
        WHERE id = #{id}
        AND #{totalQuota} &gt;= used_quota
    </update>

    <!-- 重置已使用配额 -->
    <update id="resetUsedQuota">
        UPDATE ose_quota
        SET used_quota = 0,
            updated_by = #{updatedBy},
            update_time = NOW()
        WHERE id = #{id}
    </update>

</mapper>
