<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.tipray.cloud.offline.strategy.extend.mapper.IpMapper" >
    <resultMap id="BaseResultMap" type="com.tipray.cloud.offline.strategy.extend.entity.Ip" >
        <id column="id" property="id" jdbcType="VARCHAR" />
        <result column="ip" property="ip" jdbcType="VARCHAR" />
        <result column="name" property="name" jdbcType="VARCHAR" />
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
        <result column="deleted" property="deleted" jdbcType="INTEGER" />
        <result column="version" property="version" jdbcType="BIGINT" />
    </resultMap>

    <sql id="Base_Column_List">
        id, ip, name, create_time, update_time, deleted, version
    </sql>

    <insert id="insertSelective" parameterType="com.tipray.cloud.offline.strategy.extend.entity.Ip">
        insert into ip
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="ip != null">
                ip,
            </if>
            <if test="name != null">
                name,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="deleted != null">
                deleted,
            </if>
            <if test="version != null">
                version,
            </if>
        </trim>
        <trim prefix="values(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=VARCHAR},
            </if>
            <if test="ip != null">
                #{ip,jdbcType=VARCHAR},
            </if>
            <if test="name != null">
                #{name,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null" >
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null" >
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="deleted != null" >
                #{deleted,jdbcType=INTEGER},
            </if>
            <if test="version != null" >
                #{version,jdbcType=BIGINT},
            </if>
        </trim>
    </insert>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        delete from ip
        where id = #{id,jdbcType=VARCHAR}
    </delete>

    <update id="updateByIpSelective" parameterType="com.tipray.cloud.offline.strategy.extend.entity.Ip">
        update ip
        <set>
            <if test="ip != null">
                  ip = #{ip,jdbcType=VARCHAR},
            </if>
            <if test="name != null">
                  name = #{name,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null" >
                  create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null" >
                  update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="deleted != null" >
                  deleted = #{deleted,jdbcType=INTEGER},
            </if>
            <if test="version != null" >
                  version = #{version,jdbcType=BIGINT}
            </if>
        </set>
        where ip = #{ip,jdbcType=VARCHAR}
    </update>

    <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from ip
        where id = #{id,jdbcType=VARCHAR}
    </select>

    <select id="list" resultMap="BaseResultMap" >
        select
        <include refid="Base_Column_List" />
        from ip where deleted = 0
        order by create_time desc
    </select>

    <select id="selectByIp" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from ip
        where ip = #{ip,jdbcType=VARCHAR}
    </select>

</mapper>
