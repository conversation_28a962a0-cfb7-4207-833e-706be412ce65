<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.tipray.cloud.offline.strategy.extend.mapper.OfflineStrategyExtendLogMapper">

    <resultMap id="BaseResultMap" type="com.tipray.cloud.offline.strategy.extend.entity.OfflineStrategyExtendLog">
        <id column="id" property="id" jdbcType="INTEGER"/>
        <result column="user_no" property="userNo" jdbcType="VARCHAR"/>
        <result column="offline_strategy_extend_config_id" property="offlineStrategyExtendConfigId" jdbcType="INTEGER"/>
        <result column="version" property="version" jdbcType="INTEGER"/>
        <result column="log_type" property="logType" jdbcType="INTEGER"/>
        <result column="log_content" property="logContent" jdbcType="VARCHAR"/>
        <result column="sync_to_dlp" property="syncToDlp" jdbcType="INTEGER"/>
        <result column="ip" property="ip" jdbcType="VARCHAR"/>
        <result column="ip_location" property="ipLocation" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="dlp_map_id" property="dlpMapId" jdbcType="INTEGER"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, user_no, offline_strategy_extend_config_id, target_type, target, version, log_type, log_content,
        sync_to_dlp, ip, ip_location, create_time, dlp_map_id, operate_time
    </sql>

    <!-- 更新同步状态 -->
    <update id="updateSyncStatus">
        UPDATE ose_offline_strategy_extend_log
        SET sync_to_dlp = #{syncToDlp}
        WHERE id = #{id}
    </update>

    <!-- 游标分页查询未同步的日志ID列表 -->
    <select id="selectUnsyncedLogIds" resultType="java.lang.Integer">
        SELECT id
        FROM ose_offline_strategy_extend_log
        WHERE user_no = #{userNo} AND sync_to_dlp = 0
        AND id &gt; #{lastId}
        ORDER BY id ASC
        LIMIT #{pageSize}
    </select>

    <!-- 游标分页查询未同步的日志ID列表（多查一条用于判断是否还有更多数据） -->
    <select id="selectUnsyncedLogIdsWithMore" resultType="java.lang.Integer">
        SELECT id
        FROM ose_offline_strategy_extend_log
        WHERE user_no = #{userNo} AND sync_to_dlp = 0
        AND id &gt; #{lastId}
        ORDER BY id ASC
        LIMIT #{limitSize}
    </select>

    <!-- 原子操作：批量将未同步状态更新为同步中状态 -->
    <update id="batchUpdateToSyncing">
        UPDATE ose_offline_strategy_extend_log
        SET sync_to_dlp = 3
        WHERE sync_to_dlp = 0 AND id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <!-- 根据ID列表查询日志详情 -->
    <select id="selectByIds" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM ose_offline_strategy_extend_log
        WHERE id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        ORDER BY id ASC
    </select>

</mapper>
