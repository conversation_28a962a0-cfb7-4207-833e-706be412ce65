<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.tipray.cloud.offline.strategy.extend.mapper.ApplicationMapper" >
  <resultMap id="BaseResultMap" type="com.tipray.cloud.offline.strategy.extend.entity.Application" >
    <id column="id" property="id" jdbcType="VARCHAR" />
    <result column="name" property="name" jdbcType="VARCHAR" />
    <result column="code" property="code" jdbcType="VARCHAR" />
    <result column="key" property="key" jdbcType="VARCHAR" />
    <result column="status" property="status" jdbcType="INTEGER" />
    <result column="ln" property="ln" jdbcType="INTEGER" />
    <result column="remark" property="remark" jdbcType="VARCHAR" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
    <result column="deleted" property="deleted" jdbcType="INTEGER" />
    <result column="version" property="version" jdbcType="BIGINT" />
  </resultMap>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String" >
    delete from application
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="com.tipray.cloud.offline.strategy.extend.entity.Application" >
    insert into application (id, `name`, code, `key`, status, ln,
      remark, create_time, update_time, deleted, version)
    values (#{id,jdbcType=VARCHAR}, #{name,jdbcType=VARCHAR},
      #{code,jdbcType=VARCHAR}, #{key,jdbcType=VARCHAR}, #{status,jdbcType=INTEGER},
      #{ln,jdbcType=INTEGER}, #{remark,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP},
      #{updateTime,jdbcType=TIMESTAMP}, #{deleted,jdbcType=INTEGER}, #{version,jdbcType=BIGINT})
  </insert>
  <update id="updateByPrimaryKey" parameterType="com.tipray.cloud.offline.strategy.extend.entity.Application" >
    update application
    set `name` = #{name,jdbcType=VARCHAR},
      code = #{code,jdbcType=VARCHAR},
      `key` = #{key,jdbcType=VARCHAR},
      status = #{status,jdbcType=INTEGER},
      ln = #{ln,jdbcType=INTEGER},
      remark = #{remark,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      deleted = #{deleted,jdbcType=INTEGER},
      version = #{version,jdbcType=BIGINT}
    where deleted = 0 and id = #{id,jdbcType=VARCHAR}
  </update>

  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.String" >
    select id, `name`, code, `key`, status, ln,
           remark, create_time, update_time, deleted, version
    from application
    where deleted = 0 and id = #{id,jdbcType=VARCHAR}
  </select>

  <select id="selectByCode" resultMap="BaseResultMap" parameterType="java.lang.String" >
    select id, `name`, code, `key`, status, ln,
           remark, create_time, update_time, deleted, version
    from application
    where deleted = 0 and code = #{code,jdbcType=VARCHAR}
  </select>

  <select id="getName" resultType="java.lang.String" >
    select `name`
    from application
    where deleted = 0
  </select>

  <insert id="insertSelective" parameterType="com.tipray.cloud.offline.strategy.extend.entity.Application" >
    insert into application
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="name != null" >
        `name`,
      </if>
      <if test="code != null" >
        code,
      </if>
      <if test="key != null" >
        `key`,
      </if>
      <if test="status != null" >
        status,
      </if>
      <if test="ln != null" >
        ln,
      </if>
      <if test="remark != null" >
        remark,
      </if>
      <if test="createTime != null" >
        create_time,
      </if>
      <if test="updateTime != null" >
        update_time,
      </if>
      <if test="deleted != null" >
        deleted,
      </if>
      <if test="version != null" >
        version,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="name != null" >
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="code != null" >
        #{code,jdbcType=VARCHAR},
      </if>
      <if test="key != null" >
        #{key,jdbcType=VARCHAR},
      </if>
      <if test="status != null" >
        #{status,jdbcType=INTEGER},
      </if>
      <if test="ln != null" >
        #{ln,jdbcType=INTEGER},
      </if>
      <if test="remark != null" >
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null" >
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null" >
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="deleted != null" >
        #{deleted,jdbcType=INTEGER},
      </if>
      <if test="version != null" >
        #{version,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>

  <update id="updateByPrimaryKeySelective" parameterType="com.tipray.cloud.offline.strategy.extend.entity.Application" >
    update application
    <set>
      <if test="name != null" >
        `name` = #{name,jdbcType=VARCHAR},
      </if>
      <if test="code != null" >
        code = #{code,jdbcType=VARCHAR},
      </if>
      <if test="key != null" >
        `key` = #{key,jdbcType=VARCHAR},
      </if>
      <if test="status != null" >
        status = #{status,jdbcType=INTEGER},
      </if>
      <if test="ln != null" >
        ln = #{ln,jdbcType=INTEGER},
      </if>
      <if test="remark != null" >
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null" >
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null" >
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="deleted != null" >
        deleted = #{deleted,jdbcType=INTEGER},
      </if>
      <if test="version != null" >
        version = #{version,jdbcType=BIGINT},
      </if>
    </set>
    where deleted = 0 and id = #{id,jdbcType=VARCHAR}
  </update>

  <select id="list" resultMap="BaseResultMap">
    SELECT id, `name`, code, `key`, status, ln,
           remark, create_time, update_time, deleted, version
    FROM application t
    WHERE deleted = 0
    <if test="name != null and name != ''">
      AND t.`name` LIKE CONCAT('%', #{name}, '%')
    </if>
    <if test="status != null and status != 10000">
      AND t.status = #{status}
    </if>
    ORDER BY ln ASC, create_time DESC
  </select>
</mapper>