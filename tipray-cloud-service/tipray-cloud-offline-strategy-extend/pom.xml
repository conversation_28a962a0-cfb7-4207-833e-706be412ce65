<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <!-- 独立微服务，不依赖父工程 -->
    <groupId>com.tipray</groupId>
    <artifactId>tipray-cloud-offline-strategy-extend</artifactId>
    <version>1.0.0</version>
    <name>tipray-cloud-offline-strategy-extend</name>
    <packaging>jar</packaging>
    <description>Tipray 离线配置延长服务 - 独立微服务版本</description>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <maven.compiler.source>1.8</maven.compiler.source>
        <maven.compiler.target>1.8</maven.compiler.target>

        <!-- Tipray组件版本 -->
        <tipray.version>1.0</tipray.version>

        <!-- Spring Boot版本 -->
        <spring-boot.version>2.7.18</spring-boot.version>
    </properties>

    <!-- 依赖管理 -->
    <dependencyManagement>
        <dependencies>
            <!-- 引入Tipray BOM进行统一版本管理 -->
            <dependency>
                <groupId>com.tipray</groupId>
                <artifactId>tipray-cloud-bom</artifactId>
                <version>${tipray.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <!-- Spring Boot BOM - 优先引入，提供Spring Boot生态的版本管理 -->
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>${spring-boot.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <dependencies>

        <!-- 全局依赖 -->
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <scope>provided</scope>
        </dependency>

        <!-- Tipray通用组件 (通过本地仓库引入) -->
        <dependency>
            <groupId>com.tipray</groupId>
            <artifactId>tipray-cloud-web</artifactId>
        </dependency>

        <dependency>
            <groupId>com.tipray</groupId>
            <artifactId>tipray-cloud-mybatis</artifactId>
        </dependency>

        <!-- 工具类 -->
        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-json</artifactId>
        </dependency>

        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-core</artifactId>
        </dependency>

        <dependency>
            <groupId>org.lionsoul</groupId>
            <artifactId>ip2region</artifactId>
            <version>1.7.2</version>
        </dependency>

        <!-- Spring Boot AOP -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-aop</artifactId>
        </dependency>
        <dependency>
            <groupId>commons-codec</groupId>
            <artifactId>commons-codec</artifactId>
        </dependency>

        <!-- 测试依赖 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>

        <!-- 分布式事务客户端 (可选，如果需要的话) -->
        <dependency>
            <groupId>com.tipray</groupId>
            <artifactId>tipray-transaction-client-spring-boot-starter</artifactId>
            <version>1.0</version>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <!-- Maven资源插件配置 -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-resources-plugin</artifactId>
                <version>3.3.1</version>
                <configuration>
                    <encoding>UTF-8</encoding>
                    <!-- 排除二进制文件的过滤 -->
                    <nonFilteredFileExtensions>
                        <nonFilteredFileExtension>db</nonFilteredFileExtension>
                        <nonFilteredFileExtension>bin</nonFilteredFileExtension>
                        <nonFilteredFileExtension>dat</nonFilteredFileExtension>
                        <nonFilteredFileExtension>exe</nonFilteredFileExtension>
                        <nonFilteredFileExtension>dll</nonFilteredFileExtension>
                        <nonFilteredFileExtension>so</nonFilteredFileExtension>
                        <nonFilteredFileExtension>dylib</nonFilteredFileExtension>
                        <nonFilteredFileExtension>zip</nonFilteredFileExtension>
                        <nonFilteredFileExtension>jar</nonFilteredFileExtension>
                        <nonFilteredFileExtension>war</nonFilteredFileExtension>
                        <nonFilteredFileExtension>tar</nonFilteredFileExtension>
                        <nonFilteredFileExtension>gz</nonFilteredFileExtension>
                        <nonFilteredFileExtension>7z</nonFilteredFileExtension>
                        <nonFilteredFileExtension>rar</nonFilteredFileExtension>
                        <nonFilteredFileExtension>pdf</nonFilteredFileExtension>
                        <nonFilteredFileExtension>doc</nonFilteredFileExtension>
                        <nonFilteredFileExtension>docx</nonFilteredFileExtension>
                        <nonFilteredFileExtension>xls</nonFilteredFileExtension>
                        <nonFilteredFileExtension>xlsx</nonFilteredFileExtension>
                        <nonFilteredFileExtension>ppt</nonFilteredFileExtension>
                        <nonFilteredFileExtension>pptx</nonFilteredFileExtension>
                        <nonFilteredFileExtension>jpg</nonFilteredFileExtension>
                        <nonFilteredFileExtension>jpeg</nonFilteredFileExtension>
                        <nonFilteredFileExtension>png</nonFilteredFileExtension>
                        <nonFilteredFileExtension>gif</nonFilteredFileExtension>
                        <nonFilteredFileExtension>bmp</nonFilteredFileExtension>
                        <nonFilteredFileExtension>ico</nonFilteredFileExtension>
                        <nonFilteredFileExtension>svg</nonFilteredFileExtension>
                        <nonFilteredFileExtension>mp3</nonFilteredFileExtension>
                        <nonFilteredFileExtension>mp4</nonFilteredFileExtension>
                        <nonFilteredFileExtension>avi</nonFilteredFileExtension>
                        <nonFilteredFileExtension>mov</nonFilteredFileExtension>
                        <nonFilteredFileExtension>wmv</nonFilteredFileExtension>
                        <nonFilteredFileExtension>flv</nonFilteredFileExtension>
                        <nonFilteredFileExtension>swf</nonFilteredFileExtension>
                    </nonFilteredFileExtensions>
                </configuration>
            </plugin>

            <!-- Spring Boot Maven插件 -->
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>${spring-boot.version}</version>
                <configuration>
                    <excludes>
                        <exclude>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok</artifactId>
                        </exclude>
                    </excludes>
                </configuration>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>

            <!-- Maven Compiler Plugin -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.8.1</version>
                <configuration>
                    <source>${maven.compiler.source}</source>
                    <target>${maven.compiler.target}</target>
                    <encoding>${project.build.sourceEncoding}</encoding>
                </configuration>
            </plugin>
        </plugins>
    </build>

</project>
