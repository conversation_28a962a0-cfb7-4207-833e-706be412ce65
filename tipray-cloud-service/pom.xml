<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.tipray</groupId>
        <artifactId>my-tipray-cloud</artifactId>
        <version>${revision}</version>
    </parent>

    <artifactId>tipray-cloud-service</artifactId>
    <packaging>pom</packaging>

    <description>tipray-cloud 业务服务模块</description>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

    <modules>
        <!-- 预留给新的微服务模块 -->
        <module>tipray-cloud-offline-strategy-extend</module>
        <module>tipray-at-transaction-test</module>
    </modules>

</project>
