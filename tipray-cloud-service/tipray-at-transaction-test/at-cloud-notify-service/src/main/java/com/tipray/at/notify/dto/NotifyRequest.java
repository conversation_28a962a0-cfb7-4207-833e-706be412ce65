package com.tipray.at.notify.dto;

import lombok.Data;

/**
 * 通知请求DTO
 * 
 * <AUTHOR>
 */
@Data
public class NotifyRequest {

    /**
     * 数据ID
     */
    private String dataId;

    /**
     * 数据内容
     */
    private String dataContent;

    /**
     * 数据类型
     */
    private String dataType;

    /**
     * 通知类型：EMAIL-邮件，SMS-短信，PUSH-推送
     */
    private String notifyType = "EMAIL";

    /**
     * 通知目标（邮箱、手机号等）
     */
    private String notifyTarget = "<EMAIL>";

    /**
     * 全局事务ID
     */
    private String globalTxId;

    /**
     * 分支事务ID
     */
    private String branchTxId;

    /**
     * 是否模拟异常
     */
    private Boolean simulateError = false;

    /**
     * 是否模拟慢处理
     */
    private Boolean simulateSlowProcessing = false;

    /**
     * 慢处理延迟时间（毫秒）
     */
    private Long slowProcessingDelayMillis = 3000L;

    /**
     * 备注
     */
    private String remark;
}
