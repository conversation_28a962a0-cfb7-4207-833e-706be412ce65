package com.tipray.at.notify.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 通知记录实体
 * 
 * <AUTHOR>
 */
@Data
@TableName("notify_record")
public class NotifyRecord {

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 数据ID
     */
    private String dataId;

    /**
     * 通知类型：EMAIL-邮件，SMS-短信，PUSH-推送
     */
    private String notifyType;

    /**
     * 通知目标（邮箱、手机号等）
     */
    private String notifyTarget;

    /**
     * 通知内容
     */
    private String notifyContent;

    /**
     * 通知状态：PENDING-待发送，SENT-已发送，FAILED-发送失败
     */
    private String status;

    /**
     * 发送时间
     */
    private LocalDateTime sendTime;

    /**
     * 全局事务ID
     */
    private String globalTxId;

    /**
     * 分支事务ID
     */
    private String branchTxId;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 备注
     */
    private String remark;
}
