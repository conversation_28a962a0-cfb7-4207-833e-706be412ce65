package com.tipray.at.notify.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.tipray.at.notify.dto.NotifyRequest;
import com.tipray.at.notify.entity.NotifyRecord;
import com.tipray.at.notify.mapper.NotifyRecordMapper;
import com.tipray.at.notify.service.NotifyService;
import com.tipray.transaction.client.infrastructure.context.TransactionContextHolder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 通知服务实现
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class NotifyServiceImpl implements NotifyService {

    @Autowired
    private NotifyRecordMapper notifyRecordMapper;

    @Autowired
    private JdbcTemplate jdbcTemplate;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public NotifyRecord sendNotify(NotifyRequest request) {
        log.info("开始发送通知，数据ID：{}，全局事务ID：{}", request.getDataId(), request.getGlobalTxId());

        // 模拟异常
        if (Boolean.TRUE.equals(request.getSimulateError())) {
            log.error("模拟通知异常，数据ID：{}", request.getDataId());
            throw new RuntimeException("模拟通知服务异常");
        }

        // 模拟慢处理
        if (Boolean.TRUE.equals(request.getSimulateSlowProcessing())) {
            Long delay = request.getSlowProcessingDelayMillis() != null ?
                        request.getSlowProcessingDelayMillis() : 3000L;
            log.info("模拟通知服务慢处理，延迟{}毫秒", delay);
            try {
                Thread.sleep(delay);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                throw new RuntimeException("通知服务慢处理被中断", e);
            }
        }

        // 生成通知内容
        String notifyContent = generateNotifyContent(request);
        LocalDateTime sendTime = LocalDateTime.now();

        // 检查是否已存在记录
        NotifyRecord existingRecord = getByDataId(request.getDataId());
        NotifyRecord record;

        if (existingRecord != null) {
            // 更新现有记录
            log.info("发现已存在记录，执行更新操作，数据ID：{}", request.getDataId());

            existingRecord.setNotifyType(request.getNotifyType());
            existingRecord.setNotifyTarget(request.getNotifyTarget());
            existingRecord.setNotifyContent(notifyContent);
            existingRecord.setStatus("RESENT");
            existingRecord.setSendTime(sendTime);
            existingRecord.setGlobalTxId(request.getGlobalTxId());
            existingRecord.setBranchTxId(request.getBranchTxId());
            existingRecord.setUpdateTime(LocalDateTime.now());
            existingRecord.setRemark(request.getRemark());

            // 更新到数据库
            notifyRecordMapper.updateById(existingRecord);

            record = existingRecord;
            log.info("通知重新发送成功，数据ID：{}，通知类型：{}，目标：{}，记录ID：{}",
                    request.getDataId(), request.getNotifyType(), request.getNotifyTarget(), record.getId());
        } else {
            // 创建新通知记录
            log.info("未发现已存在记录，执行插入操作，数据ID：{}", request.getDataId());

            record = new NotifyRecord();
            record.setDataId(request.getDataId());
            record.setNotifyType(request.getNotifyType());
            record.setNotifyTarget(request.getNotifyTarget());
            record.setNotifyContent(notifyContent);
            record.setStatus("SENT");
            record.setSendTime(sendTime);
            record.setGlobalTxId(request.getGlobalTxId());
            record.setBranchTxId(request.getBranchTxId());
            record.setCreateTime(LocalDateTime.now());
            record.setUpdateTime(LocalDateTime.now());
            record.setRemark(request.getRemark());

            // 保存到数据库
            notifyRecordMapper.insert(record);

            log.info("通知发送成功，数据ID：{}，通知类型：{}，目标：{}，记录ID：{}",
                    request.getDataId(), request.getNotifyType(), request.getNotifyTarget(), record.getId());
        }

        // 模拟发送通知（实际项目中这里会调用邮件、短信等服务）
        simulateSendNotification(record);

        return record;
    }

    @Override
    public NotifyRecord getByDataId(String dataId) {
        LambdaQueryWrapper<NotifyRecord> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(NotifyRecord::getDataId, dataId);
        return notifyRecordMapper.selectOne(wrapper);
    }

    /**
     * 生成通知内容
     */
    private String generateNotifyContent(NotifyRequest request) {
        StringBuilder content = new StringBuilder();
        content.append("数据处理通知\n");
        content.append("数据ID：").append(request.getDataId()).append("\n");
        content.append("数据类型：").append(request.getDataType()).append("\n");
        content.append("处理时间：").append(LocalDateTime.now()).append("\n");
        content.append("全局事务ID：").append(request.getGlobalTxId()).append("\n");

        if (request.getRemark() != null && !request.getRemark().trim().isEmpty()) {
            content.append("备注：").append(request.getRemark()).append("\n");
        }

        content.append("数据处理已完成，请查收！");

        return content.toString();
    }

    /**
     * 模拟发送通知
     */
    private void simulateSendNotification(NotifyRecord record) {
        // 这里模拟发送通知的过程
        log.info("模拟发送{}通知到{}：{}",
                record.getNotifyType(), record.getNotifyTarget(), record.getNotifyContent());

        // 实际项目中，这里会调用具体的通知服务
        // 比如邮件服务、短信服务、推送服务等
    }

    @Transactional(rollbackFor = Exception.class)
    public boolean deleteData(NotifyRequest request) {
        log.info("开始删除通知数据，数据ID：{}", request.getDataId());

        // 模拟异常
        if (Boolean.TRUE.equals(request.getSimulateError())) {
            log.error("模拟通知删除异常，数据ID：{}", request.getDataId());
            throw new RuntimeException("模拟通知服务删除异常");
        }

        try {
            // 检查数据是否存在
            NotifyRecord existingRecord = getByDataId(request.getDataId());
            if (existingRecord == null) {
                log.warn("要删除的通知数据不存在，数据ID：{}", request.getDataId());
                return false;
            }

            // 删除数据库记录（这里会被AT数据源代理拦截，生成undo_log）
            log.info("准备执行数据库删除操作，将被AT数据源代理拦截");
            log.info("MyBatis Plus 执行前，当前事务上下文: xid={}",
                     TransactionContextHolder.getGlobalTransactionId());

            LambdaQueryWrapper<NotifyRecord> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(NotifyRecord::getDataId, request.getDataId());
            int deleteCount = notifyRecordMapper.delete(wrapper);

            log.info("MyBatis Plus 执行后，当前事务上下文: xid={}",
                    TransactionContextHolder.getGlobalTransactionId());
            log.info("数据库删除操作完成，应该已生成undo_log");

            if (deleteCount > 0) {
                log.info("通知数据删除成功，数据ID：{}", request.getDataId());
                return true;
            } else {
                log.warn("通知数据删除失败，数据ID：{}", request.getDataId());
                return false;
            }

        } catch (Exception e) {
            log.error("删除通知数据异常，数据ID：{}，错误：{}", request.getDataId(), e.getMessage(), e);
            throw new RuntimeException("删除通知数据失败: " + e.getMessage(), e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Map<String, Object> executeComplexSqlScenarios(NotifyRequest request) {
        log.info("开始执行云通知服务复杂SQL场景测试，数据ID：{}", request.getDataId());

        String globalTxId = TransactionContextHolder.getGlobalTransactionId();
        Long branchId = TransactionContextHolder.getBranchId();

        Map<String, Object> result = new HashMap<>();
        result.put("testId", request.getDataId());
        result.put("startTime", System.currentTimeMillis());
        result.put("service", "cloud-notify");

        // 模拟异常
        if (Boolean.TRUE.equals(request.getSimulateError())) {
            log.error("模拟云通知服务复杂SQL异常，数据ID：{}", request.getDataId());
            throw new RuntimeException("模拟云通知服务复杂SQL场景异常");
        }

        try {
            int totalOperations = 0;

            // 1. 多表INSERT操作 - 插入通知记录和相关数据
            log.info("执行多表INSERT操作...");

            // 插入主通知记录
            NotifyRecord mainRecord = new NotifyRecord();
            mainRecord.setDataId(request.getDataId());
            mainRecord.setNotifyType("COMPLEX_SQL_TEST");
            mainRecord.setNotifyTarget("<EMAIL>");
            mainRecord.setNotifyContent("云通知服务复杂SQL测试通知内容");
            mainRecord.setStatus("COMPLEX_SENT");
            mainRecord.setSendTime(LocalDateTime.now());
            mainRecord.setGlobalTxId(globalTxId);
            mainRecord.setBranchTxId(branchId != null ? branchId.toString() : null);
            mainRecord.setCreateTime(LocalDateTime.now());
            mainRecord.setUpdateTime(LocalDateTime.now());
            mainRecord.setRemark("云通知服务多表操作主记录");

            notifyRecordMapper.insert(mainRecord);
            totalOperations++;

            // 插入处理历史记录
            jdbcTemplate.update(
                "INSERT INTO data_process_history (data_id, process_type, process_status, process_result, process_duration, global_tx_id, branch_tx_id, create_time, update_time) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)",
                request.getDataId(), "NOTIFICATION", "COMPLETED", "云通知服务多表INSERT成功", 1800L, globalTxId, branchId != null ? branchId.toString() : null, LocalDateTime.now(), LocalDateTime.now()
            );
            totalOperations++;

            // 插入多个标签记录
            String[] notifyTags = {"cloud-notify", "email-sent", "complex-sql", "final-step"};
            for (int i = 0; i < notifyTags.length; i++) {
                jdbcTemplate.update(
                    "INSERT INTO data_tags (data_id, tag_name, tag_value, tag_category, priority, global_tx_id, branch_tx_id, create_time, update_time) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)",
                    request.getDataId(), notifyTags[i], "notify_value_" + i, "notification", i + 1, globalTxId, branchId != null ? branchId.toString() : null, LocalDateTime.now(), LocalDateTime.now()
                );
                totalOperations++;
            }

            // 2. 批量INSERT操作 - 插入多个通知记录（模拟群发）
            log.info("执行批量INSERT操作...");
            String[] recipients = {"<EMAIL>", "<EMAIL>", "<EMAIL>"};
            for (int i = 0; i < recipients.length; i++) {
                String batchDataId = request.getDataId() + "_NOTIFY_BATCH_" + i;

                // 插入批量通知记录
                NotifyRecord batchRecord = new NotifyRecord();
                batchRecord.setDataId(batchDataId);
                batchRecord.setNotifyType("BATCH_EMAIL");
                batchRecord.setNotifyTarget(recipients[i]);
                batchRecord.setNotifyContent("批量通知内容 " + i);
                batchRecord.setStatus("BATCH_SENT");
                batchRecord.setSendTime(LocalDateTime.now());
                batchRecord.setGlobalTxId(globalTxId);
                batchRecord.setBranchTxId(branchId != null ? branchId.toString() : null);
                batchRecord.setCreateTime(LocalDateTime.now());
                batchRecord.setUpdateTime(LocalDateTime.now());
                batchRecord.setRemark("云通知服务批量操作 " + i);

                notifyRecordMapper.insert(batchRecord);
                totalOperations++;

                // 为每个批量记录建立关系
                jdbcTemplate.update(
                    "INSERT INTO data_relationship (parent_data_id, child_data_id, relationship_type, relationship_strength, status, global_tx_id, branch_tx_id, create_time, update_time) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)",
                    request.getDataId(), batchDataId, "NOTIFY_BATCH", 1.0, "ACTIVE", globalTxId, branchId != null ? branchId.toString() : null, LocalDateTime.now(), LocalDateTime.now()
                );
                totalOperations++;
            }

            // 3. 复杂UPDATE操作 - 更新配置和统计
            log.info("执行复杂UPDATE操作...");

//            // 更新通知配置
//            int configUpdated = jdbcTemplate.update(
//                "UPDATE data_config SET config_value = ?, global_tx_id = ?, branch_tx_id = ?, update_time = ? WHERE config_key = ?",
//                String.valueOf(System.currentTimeMillis()), globalTxId, branchId != null ? branchId.toString() : null, LocalDateTime.now(), "notification.last_batch_time"
//            );
//
//            if (configUpdated == 0) {
//                // 如果配置不存在，插入新配置
//                jdbcTemplate.update(
//                    "INSERT INTO data_config (config_key, config_value, config_type, config_group, is_encrypted, is_active, global_tx_id, branch_tx_id, create_time, update_time) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)",
//                    "notification.last_batch_time", String.valueOf(System.currentTimeMillis()), "LONG", "NOTIFICATION", 0, 1, globalTxId, branchId != null ? branchId.toString() : null, LocalDateTime.now(), LocalDateTime.now()
//                );
//            }
            totalOperations++;

            // 更新统计表
            int statsUpdated = jdbcTemplate.update(
                "UPDATE data_statistics SET total_count = total_count + ?, success_count = success_count + ?, avg_process_time = (avg_process_time + ?) / 2, total_size = total_size + ?, global_tx_id = ?, branch_tx_id = ?, update_time = ? WHERE stat_date = ? AND data_type = ?",
                recipients.length + 1, recipients.length + 1, 1800.0, 2048L, globalTxId, branchId != null ? branchId.toString() : null, LocalDateTime.now(), LocalDate.now(), "NOTIFICATION"
            );

            if (statsUpdated == 0) {
                // 如果统计记录不存在，插入新记录
                jdbcTemplate.update(
                    "INSERT INTO data_statistics (stat_date, data_type, total_count, success_count, failed_count, avg_process_time, total_size, global_tx_id, branch_tx_id, create_time, update_time) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)",
                    LocalDate.now(), "NOTIFICATION", (long)(recipients.length + 1), (long)(recipients.length + 1), 0L, 1800.0, 2048L, globalTxId, branchId != null ? branchId.toString() : null, LocalDateTime.now(), LocalDateTime.now()
                );
            }
            totalOperations++;

            // 4. 条件DELETE操作 - 清理过期的临时数据
            log.info("执行条件DELETE操作...");

            // 删除过期的临时标签
            int deletedTags = jdbcTemplate.update(
                "DELETE FROM data_tags WHERE tag_category = 'temp' AND create_time < ? AND global_tx_id = ? AND branch_tx_id = ?",
                LocalDateTime.now().minusHours(2), globalTxId, branchId != null ? branchId.toString() : null
            );
            totalOperations++;

            // 5. 复杂查询和基于结果的操作
            log.info("执行复杂查询和基于结果的操作...");

            // 查询需要更新状态的通知记录
            List<Map<String, Object>> notifyQueryResults = jdbcTemplate.queryForList(
                "SELECT nr.id, nr.notify_type, COUNT(dt.id) as tag_count " +
                "FROM notify_record nr " +
                "LEFT JOIN data_tags dt ON nr.data_id = dt.data_id " +
                "WHERE nr.data_id LIKE ? AND nr.global_tx_id = ? " +
                "GROUP BY nr.id, nr.notify_type " +
                "HAVING tag_count > 0",
                request.getDataId() + "%", globalTxId
            );

            // 基于查询结果更新通知状态
            for (Map<String, Object> row : notifyQueryResults) {
                Long recordId = ((Number) row.get("id")).longValue();
                Integer tagCount = ((Number) row.get("tag_count")).intValue();

                String newStatus = tagCount > 2 ? "HIGH_PRIORITY_SENT" : "NORMAL_SENT";

                jdbcTemplate.update(
                    "UPDATE notify_record SET status = ?, update_time = ? WHERE id = ?",
                    newStatus, LocalDateTime.now(), recordId
                );
                totalOperations++;
            }

            result.put("totalOperations", totalOperations);
            result.put("mainRecordInserted", 1);
            result.put("historyRecordsInserted", 1);
            result.put("tagsInserted", notifyTags.length);
            result.put("batchRecordsInserted", recipients.length);
            result.put("relationshipsInserted", recipients.length);
            result.put("configUpdated", 1);
            result.put("statisticsUpdated", 1);
            result.put("tagsDeleted", deletedTags);
            result.put("statusUpdated", notifyQueryResults.size());
            result.put("success", true);
            result.put("endTime", System.currentTimeMillis());

            log.info("云通知服务复杂SQL场景测试完成，数据ID：{}，总操作数：{}", request.getDataId(), totalOperations);
            return result;

        } catch (Exception e) {
            log.error("云通知服务复杂SQL场景测试异常，数据ID：{}，错误：{}", request.getDataId(), e.getMessage(), e);
            result.put("success", false);
            result.put("error", e.getMessage());
            result.put("endTime", System.currentTimeMillis());
            throw new RuntimeException("云通知服务复杂SQL场景测试失败: " + e.getMessage(), e);
        }
    }

    @Override
    public Map<String, Object> checkDataExists(String dataId) {
        Map<String, Object> result = new HashMap<>();

        try {
            // 检查主表
            NotifyRecord notifyRecord = getByDataId(dataId);
            result.put("notify_record", notifyRecord != null);

            // 检查历史表
            int historyCount = jdbcTemplate.queryForObject(
                "SELECT COUNT(*) FROM data_process_history WHERE data_id = ?",
                Integer.class, dataId);
            result.put("process_history_count", historyCount);

            // 检查关系表
            int relationshipCount = jdbcTemplate.queryForObject(
                "SELECT COUNT(*) FROM data_relationship WHERE parent_data_id = ? OR child_data_id = ?",
                Integer.class, dataId, dataId);
            result.put("relationship_count", relationshipCount);

            // 检查标签表
            int tagsCount = jdbcTemplate.queryForObject(
                "SELECT COUNT(*) FROM data_tags WHERE data_id = ?",
                Integer.class, dataId);
            result.put("tags_count", tagsCount);

            // 检查配置表
            int configCount = jdbcTemplate.queryForObject(
                "SELECT COUNT(*) FROM data_config WHERE config_group = 'NOTIFICATION'",
                Integer.class);
            result.put("config_count", configCount);

            // 检查统计表
            int statisticsCount = jdbcTemplate.queryForObject(
                "SELECT COUNT(*) FROM data_statistics WHERE data_type = 'NOTIFICATION'",
                Integer.class);
            result.put("statistics_count", statisticsCount);

            log.info("云通知服务数据存在性检查完成，数据ID: {}, 结果: {}", dataId, result);
            return result;

        } catch (Exception e) {
            log.error("检查云通知服务数据存在性失败，数据ID: {}", dataId, e);
            throw new RuntimeException("检查失败: " + e.getMessage(), e);
        }
    }
}
