package com.tipray.at.notify.controller;

import com.tipray.at.notify.dto.NotifyRequest;
import com.tipray.at.notify.entity.NotifyRecord;
import com.tipray.at.notify.service.NotifyService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * 通知服务控制器
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/api/notify")
public class NotifyController {

    @Autowired
    private NotifyService notifyService;

    /**
     * 发送通知接口
     * 框架会自动处理分支事务
     */
    @PostMapping("/send")
    public Map<String, Object> sendNotify(@RequestBody Map<String, Object> request) {
        Map<String, Object> result = new HashMap<>();

        try {
            String dataId = (String) request.get("dataId");
            String globalTxId = (String) request.get("globalTxId");

            log.info("接收到通知请求，数据ID：{}，全局事务ID：{}", dataId, globalTxId);

            // 转换为NotifyRequest
            NotifyRequest notifyRequest = new NotifyRequest();
            notifyRequest.setDataId((String) request.get("dataId"));
            notifyRequest.setDataContent((String) request.get("dataContent"));
            notifyRequest.setDataType((String) request.get("dataType"));
            notifyRequest.setGlobalTxId(globalTxId);
            notifyRequest.setBranchTxId((String) request.get("branchTxId"));
            notifyRequest.setSimulateError((Boolean) request.get("simulateError"));
            notifyRequest.setRemark((String) request.get("remark"));

            NotifyRecord record = notifyService.sendNotify(notifyRequest);

            result.put("success", true);
            result.put("code", "200");
            result.put("message", "通知发送成功");
            result.put("dataId", record.getDataId());
            result.put("globalTxId", record.getGlobalTxId());
            result.put("branchTxId", record.getBranchTxId());
            result.put("data", "通知发送成功，类型：" + record.getNotifyType());

            if ((Boolean) request.getOrDefault("randomError", false)) {
                if (Math.random()  > 0.5) throw new RuntimeException("随机异常");
            }
        } catch (Exception e) {
            log.error("发送通知失败：{}", e.getMessage(), e);
            result.put("success", false);
            result.put("code", "500");
            result.put("message", "通知发送失败：" + e.getMessage());
            result.put("dataId", request.get("dataId"));
            result.put("globalTxId", request.get("globalTxId"));
            result.put("branchTxId", request.get("branchTxId"));
        }

        return result;
    }

    /**
     * 查询通知记录
     */
    @GetMapping("/{dataId}")
    public Map<String, Object> getNotifyRecord(@PathVariable String dataId) {
        Map<String, Object> result = new HashMap<>();

        try {
            NotifyRecord record = notifyService.getByDataId(dataId);

            if (record != null) {
                result.put("success", true);
                result.put("message", "查询成功");
                result.put("data", record);
            } else {
                result.put("success", false);
                result.put("message", "通知记录不存在");
            }

        } catch (Exception e) {
            log.error("查询通知记录失败：{}", e.getMessage(), e);
            result.put("success", false);
            result.put("message", "查询失败：" + e.getMessage());
        }

        return result;
    }

    /**
     * 复杂SQL场景测试接口
     */
    @PostMapping("/complex-sql")
    public Map<String, Object> complexSqlTest(@RequestBody Map<String, Object> request) {
        Map<String, Object> result = new HashMap<>();

        try {
            String dataId = (String) request.get("dataId");
            String globalTxId = (String) request.get("globalTxId");

            log.info("接收到复杂SQL测试请求，数据ID：{}，全局事务ID：{}", dataId, globalTxId);

            // 转换为NotifyRequest
            NotifyRequest notifyRequest = new NotifyRequest();
            notifyRequest.setDataId(dataId);
            notifyRequest.setDataContent((String) request.get("dataContent"));
            notifyRequest.setDataType((String) request.get("dataType"));
            notifyRequest.setGlobalTxId(globalTxId);
            notifyRequest.setBranchTxId((String) request.get("branchTxId"));
            notifyRequest.setSimulateError((Boolean) request.get("simulateError"));
            notifyRequest.setRemark((String) request.get("remark"));

            Map<String, Object> testResult = notifyService.executeComplexSqlScenarios(notifyRequest);

            result.put("success", true);
            result.put("code", "200");
            result.put("message", "复杂SQL测试执行成功");
            result.put("dataId", dataId);
            result.put("globalTxId", globalTxId);
            result.put("result", testResult);

        } catch (Exception e) {
            log.error("复杂SQL测试失败：{}", e.getMessage(), e);
            result.put("success", false);
            result.put("code", "500");
            result.put("message", "复杂SQL测试失败：" + e.getMessage());
        }

        return result;
    }

    /**
     * 检查数据是否存在（用于回滚验证）
     */
    @GetMapping("/check/{dataId}")
    public Map<String, Object> checkData(@PathVariable String dataId) {
        Map<String, Object> result = new HashMap<>();

        try {
            Map<String, Object> checkResult = notifyService.checkDataExists(dataId);

            result.put("success", true);
            result.put("message", "检查完成");
            result.put("dataId", dataId);
            result.put("exists", checkResult);

        } catch (Exception e) {
            log.error("检查数据失败：{}", e.getMessage(), e);
            result.put("success", false);
            result.put("message", "检查失败：" + e.getMessage());
        }

        return result;
    }

    /**
     * 健康检查
     */
    @GetMapping("/health")
    public Map<String, Object> health() {
        Map<String, Object> result = new HashMap<>();
        result.put("status", "UP");
        result.put("service", "at-cloud-notify-service");
        result.put("timestamp", System.currentTimeMillis());
        return result;
    }
}
