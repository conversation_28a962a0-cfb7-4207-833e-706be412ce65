package com.tipray.at.storage.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.tipray.at.storage.dto.StorageRequest;
import com.tipray.at.storage.entity.StorageRecord;
import com.tipray.at.storage.mapper.StorageRecordMapper;
import com.tipray.at.storage.service.StorageService;
import com.tipray.transaction.client.infrastructure.context.TransactionContextHolder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.sql.DataSource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 存储服务实现
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class StorageServiceImpl implements StorageService {

    @Autowired
    private StorageRecordMapper storageRecordMapper;

    @Autowired
    private DataSource dataSource;

    @Autowired
    private JdbcTemplate jdbcTemplate;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public StorageRecord storeData(StorageRequest request) {
        // 模拟异常
        if (Boolean.TRUE.equals(request.getSimulateError())) {
            log.error("模拟存储异常，数据ID：{}", request.getDataId());
            throw new RuntimeException("模拟存储服务异常");
        }

        // 模拟慢处理
        if (Boolean.TRUE.equals(request.getSimulateSlowProcessing())) {
            Long delay = request.getSlowProcessingDelayMillis() != null ?
                        request.getSlowProcessingDelayMillis() : 3000L;
            log.info("模拟存储服务慢处理，延迟{}毫秒", delay);
            try {
                Thread.sleep(delay);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                throw new RuntimeException("存储服务慢处理被中断", e);
            }
        }

        // 模拟存储路径和文件大小
        String storagePath = "/storage/data/" + request.getDataId() + ".dat";
        Long fileSize = (long) (request.getDataContent() != null ? request.getDataContent().length() : 0);

        // 检查是否已存在相同的data_id
        StorageRecord existingRecord = getByDataId(request.getDataId());
        StorageRecord record;

        if (existingRecord != null) {
            // 更新现有记录
            log.info("发现已存在记录，执行更新操作，数据ID：{}", request.getDataId());

            existingRecord.setDataContent(request.getDataContent());
            existingRecord.setDataType(request.getDataType());
            existingRecord.setStoragePath(storagePath);
            existingRecord.setFileSize(fileSize);
            existingRecord.setStatus("UPDATED");
            existingRecord.setGlobalTxId(request.getGlobalTxId());
            existingRecord.setBranchTxId(request.getBranchTxId());
            existingRecord.setUpdateTime(LocalDateTime.now());
            existingRecord.setRemark(request.getRemark());

            // 使用MyBatis Plus执行更新
            storageRecordMapper.updateById(existingRecord);

            record = existingRecord;
            log.info("存储数据更新完成，数据ID：{}，存储路径：{}", request.getDataId(), storagePath);
        } else {
            // 创建新存储记录
            log.info("未发现已存在记录，执行插入操作，数据ID：{}", request.getDataId());

            record = new StorageRecord();
            record.setDataId(request.getDataId()); // 使用请求中的dataId，不要生成新的UUID
            record.setDataContent(request.getDataContent());
            record.setDataType(request.getDataType());
            record.setStoragePath(storagePath);
            record.setFileSize(fileSize);
            record.setStatus("STORED");
            record.setGlobalTxId(request.getGlobalTxId());
            record.setBranchTxId(request.getBranchTxId());
            record.setCreateTime(LocalDateTime.now());
            record.setUpdateTime(LocalDateTime.now());
            record.setRemark(request.getRemark());

            // 使用MyBatis Plus执行插入
            storageRecordMapper.insert(record);

            log.info("存储数据完成，数据ID：{}，存储路径：{}", request.getDataId(), storagePath);
        }

        return record;
    }

    @Override
    public StorageRecord getByDataId(String dataId) {
        LambdaQueryWrapper<StorageRecord> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(StorageRecord::getDataId, dataId);
        return storageRecordMapper.selectOne(wrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteData(StorageRequest request) {
        log.info("开始删除存储数据，数据ID：{}", request.getDataId());

        // 模拟异常
        if (Boolean.TRUE.equals(request.getSimulateError())) {
            log.error("模拟存储删除异常，数据ID：{}", request.getDataId());
            throw new RuntimeException("模拟存储服务删除异常");
        }

        try {
            // 检查数据是否存在
            StorageRecord existingRecord = getByDataId(request.getDataId());
            if (existingRecord == null) {
                log.warn("要删除的存储数据不存在，数据ID：{}", request.getDataId());
                return false;
            }

            // 检查数据源类型
            log.info("当前使用的数据源类型: {}", dataSource.getClass().getName());

            // 删除数据库记录（这里会被AT数据源代理拦截，生成undo_log）
            log.info("准备执行数据库删除操作，将被AT数据源代理拦截");
            log.info("MyBatis Plus 执行前，当前事务上下文: xid={}",
                     TransactionContextHolder.getGlobalTransactionId());

            LambdaQueryWrapper<StorageRecord> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(StorageRecord::getDataId, request.getDataId());
            int deleteCount = storageRecordMapper.delete(wrapper);

            log.info("MyBatis Plus 执行后，当前事务上下文: xid={}",
                    TransactionContextHolder.getGlobalTransactionId());
            log.info("数据库删除操作完成，应该已生成undo_log");

            if (deleteCount > 0) {
                log.info("存储数据删除成功，数据ID：{}", request.getDataId());
                return true;
            } else {
                log.warn("存储数据删除失败，数据ID：{}", request.getDataId());
                return false;
            }

        } catch (Exception e) {
            log.error("删除存储数据异常，数据ID：{}，错误：{}", request.getDataId(), e.getMessage(), e);
            throw new RuntimeException("删除存储数据失败: " + e.getMessage(), e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Object executeComplexOperations(String dataId, String operationType, Map<String, Object> params) {
        log.info("开始执行复杂多表操作，数据ID: {}, 操作类型: {}", dataId, operationType);

        String globalTxId = TransactionContextHolder.getGlobalTransactionId();
        Long branchId = TransactionContextHolder.getBranchId();

        Map<String, Object> result = new HashMap<>();

        try {
            switch (operationType) {
                case "MULTI_INSERT":
                    result = executeMultiTableInsert(dataId, globalTxId, branchId, params);
                    break;
                case "MIXED_OPS":
                    result = executeMixedOperations(dataId, globalTxId, branchId, params);
                    break;
                case "BATCH_OPS":
                    result = executeBatchOperations(dataId, globalTxId, branchId, params);
                    break;
                case "RELATED_TABLES":
                    result = executeRelatedTablesOperations(dataId, globalTxId, branchId, params);
                    break;
                case "STATISTICS":
                    result = executeStatisticsOperations(dataId, globalTxId, branchId, params);
                    break;
                case "UPDATE_OPERATIONS":
                    result = executeUpdateOperations(dataId, globalTxId, branchId, params);
                    break;
                case "COMPLEX_UPDATE":
                    result = executeComplexUpdateOperations(dataId, globalTxId, branchId, params);
                    break;
                default:
                    throw new IllegalArgumentException("不支持的操作类型: " + operationType);
            }

            log.info("复杂多表操作执行成功，数据ID: {}, 操作类型: {}", dataId, operationType);
            return result;

        } catch (Exception e) {
            log.error("复杂多表操作失败，数据ID: {}, 操作类型: {}", dataId, operationType, e);
            throw new RuntimeException("复杂操作失败: " + e.getMessage(), e);
        }
    }

    @Override
    public Map<String, Object> checkDataExists(String dataId) {
        Map<String, Object> result = new HashMap<>();

        try {
            // 检查主表
            StorageRecord storageRecord = getByDataId(dataId);
            result.put("storage_record", storageRecord != null);

            // 检查历史表
            int historyCount = jdbcTemplate.queryForObject(
                "SELECT COUNT(*) FROM data_process_history WHERE data_id = ?",
                Integer.class, dataId);
            result.put("process_history_count", historyCount);

            // 检查关系表
            int relationshipCount = jdbcTemplate.queryForObject(
                "SELECT COUNT(*) FROM data_relationship WHERE parent_data_id = ? OR child_data_id = ?",
                Integer.class, dataId, dataId);
            result.put("relationship_count", relationshipCount);

            // 检查标签表
            int tagsCount = jdbcTemplate.queryForObject(
                "SELECT COUNT(*) FROM data_tags WHERE data_id = ?",
                Integer.class, dataId);
            result.put("tags_count", tagsCount);

            log.info("数据存在性检查完成，数据ID: {}, 结果: {}", dataId, result);
            return result;

        } catch (Exception e) {
            log.error("检查数据存在性失败，数据ID: {}", dataId, e);
            throw new RuntimeException("检查失败: " + e.getMessage(), e);
        }
    }

    /**
     * 执行多表插入操作
     */
    private Map<String, Object> executeMultiTableInsert(String dataId, String globalTxId, Long branchId, Map<String, Object> params) {
        log.info("执行多表INSERT操作，数据ID: {}", dataId);

        Map<String, Object> result = new HashMap<>();
        int totalInserted = 0;

        // 1. 插入主存储记录
        StorageRecord record = new StorageRecord();
        record.setDataId(dataId);
        record.setDataContent((String) params.get("dataContent"));
        record.setDataType((String) params.get("dataType"));
        record.setStoragePath("/storage/multi/" + dataId + ".dat");
        record.setFileSize(1024L);
        record.setStatus("STORED");
        record.setGlobalTxId(globalTxId);
        record.setBranchTxId(branchId != null ? branchId.toString() : null);
        record.setCreateTime(LocalDateTime.now());
        record.setUpdateTime(LocalDateTime.now());
        record.setRemark("多表INSERT测试");

        storageRecordMapper.insert(record);
        totalInserted++;

        // 2. 插入处理历史记录
        jdbcTemplate.update(
            "INSERT INTO data_process_history (data_id, process_type, process_status, process_result, process_duration, global_tx_id, branch_tx_id, create_time, update_time) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)",
            dataId, "STORAGE", "COMPLETED", "多表插入成功", 1500L, globalTxId, branchId != null ? branchId.toString() : null, LocalDateTime.now(), LocalDateTime.now()
        );
        totalInserted++;

        // 3. 插入多个标签记录
        String[] tags = {"multi-insert", "test-data", "complex-sql"};
        for (int i = 0; i < tags.length; i++) {
            jdbcTemplate.update(
                "INSERT INTO data_tags (data_id, tag_name, tag_value, tag_category, priority, global_tx_id, branch_tx_id, create_time, update_time) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)",
                dataId, tags[i], "value_" + i, "test", i + 1, globalTxId, branchId != null ? branchId.toString() : null, LocalDateTime.now(), LocalDateTime.now()
            );
            totalInserted++;
        }

        result.put("operation", "MULTI_INSERT");
        result.put("total_inserted", totalInserted);
        result.put("tables_affected", new String[]{"storage_record", "data_process_history", "data_tags"});

        return result;
    }

    /**
     * 执行混合操作（INSERT + UPDATE + DELETE）
     */
    private Map<String, Object> executeMixedOperations(String dataId, String globalTxId, Long branchId, Map<String, Object> params) {
        log.info("执行混合操作，数据ID: {}", dataId);

        Map<String, Object> result = new HashMap<>();
        int insertCount = 0, updateCount = 0, deleteCount = 0;

        // 1. INSERT - 插入新的存储记录
        StorageRecord newRecord = new StorageRecord();
        newRecord.setDataId(dataId);
        newRecord.setDataContent("混合操作新数据");
        newRecord.setDataType("MIXED");
        newRecord.setStoragePath("/storage/mixed/" + dataId + ".dat");
        newRecord.setFileSize(2048L);
        newRecord.setStatus("STORED");
        newRecord.setGlobalTxId(globalTxId);
        newRecord.setBranchTxId(branchId != null ? branchId.toString() : null);
        newRecord.setCreateTime(LocalDateTime.now());
        newRecord.setUpdateTime(LocalDateTime.now());
        newRecord.setRemark("混合操作INSERT");

        storageRecordMapper.insert(newRecord);
        insertCount++;

        // 2. UPDATE - 更新现有的配置记录
        updateCount += jdbcTemplate.update(
            "UPDATE data_config SET config_value = ?, global_tx_id = ?, branch_tx_id = ?, update_time = ? WHERE config_key = ?",
            "updated_by_mixed_ops", globalTxId, branchId != null ? branchId.toString() : null, LocalDateTime.now(), "storage.max_file_size"
        );

        // 3. DELETE - 删除旧的标签记录
        deleteCount += jdbcTemplate.update(
            "DELETE FROM data_tags WHERE tag_category = 'test' AND global_tx_id = ? AND branch_tx_id = ?",
            globalTxId, branchId != null ? branchId.toString() : null
        );

        result.put("operation", "MIXED_OPS");
        result.put("insert_count", insertCount);
        result.put("update_count", updateCount);
        result.put("delete_count", deleteCount);

        return result;
    }

    /**
     * 执行批量操作（循环SQL）
     */
    private Map<String, Object> executeBatchOperations(String dataId, String globalTxId, Long branchId, Map<String, Object> params) {
        log.info("执行批量操作，数据ID: {}", dataId);

        Map<String, Object> result = new HashMap<>();
        int batchSize = 5; // 批量处理5条记录
        int totalProcessed = 0;

        // 批量插入处理历史记录
        for (int i = 0; i < batchSize; i++) {
            String batchDataId = dataId + "_BATCH_" + i;

            // 插入历史记录
            jdbcTemplate.update(
                "INSERT INTO data_process_history (data_id, process_type, process_status, process_result, process_duration, global_tx_id, branch_tx_id, create_time, update_time) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)",
                batchDataId, "BATCH_PROCESS", "PROCESSING", "批量处理第" + (i + 1) + "条", (i + 1) * 100L, globalTxId, branchId != null ? branchId.toString() : null, LocalDateTime.now(), LocalDateTime.now()
            );

            // 插入关系记录
            if (i > 0) {
                String parentDataId = dataId + "_BATCH_" + (i - 1);
                jdbcTemplate.update(
                    "INSERT INTO data_relationship (parent_data_id, child_data_id, relationship_type, relationship_strength, status, global_tx_id, branch_tx_id, create_time, update_time) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)",
                    parentDataId, batchDataId, "BATCH_SEQUENCE", 0.8 + i * 0.1, "ACTIVE", globalTxId, branchId != null ? branchId.toString() : null, LocalDateTime.now(), LocalDateTime.now()
                );
            }

            totalProcessed++;
        }

        // 批量更新统计信息（支持UPSERT）
        int statsUpdated = jdbcTemplate.update(
            "UPDATE data_statistics SET total_count = total_count + ?, success_count = success_count + ?, avg_process_time = (avg_process_time + ?) / 2, global_tx_id = ?, branch_tx_id = ?, update_time = ? WHERE stat_date = ? AND data_type = ?",
            batchSize, batchSize, 150.0, globalTxId, branchId != null ? branchId.toString() : null, LocalDateTime.now(), LocalDate.now(), "BATCH"
        );

        if (statsUpdated == 0) {
            // 如果没有更新到记录，插入新的统计记录
            jdbcTemplate.update(
                "INSERT INTO data_statistics (stat_date, data_type, total_count, success_count, failed_count, avg_process_time, total_size, global_tx_id, branch_tx_id, create_time, update_time) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)",
                LocalDate.now(), "BATCH", (long)batchSize, (long)batchSize, 0L, 150.0, (long)(batchSize * 1024), globalTxId, branchId != null ? branchId.toString() : null, LocalDateTime.now(), LocalDateTime.now()
            );
        }

        result.put("operation", "BATCH_OPS");
        result.put("batch_size", batchSize);
        result.put("total_processed", totalProcessed);
        result.put("tables_affected", new String[]{"data_process_history", "data_relationship", "data_statistics"});

        return result;
    }

    /**
     * 执行关联表操作
     */
    private Map<String, Object> executeRelatedTablesOperations(String dataId, String globalTxId, Long branchId, Map<String, Object> params) {
        log.info("执行关联表操作，数据ID: {}", dataId);

        Map<String, Object> result = new HashMap<>();
        int operationCount = 0;

        // 1. 插入主存储记录
        StorageRecord record = new StorageRecord();
        record.setDataId(dataId);
        record.setDataContent("关联表操作测试数据");
        record.setDataType("COMPLEX");
        record.setStoragePath("/storage/related/" + dataId + ".dat");
        record.setFileSize(4096L);
        record.setStatus("STORED");
        record.setGlobalTxId(globalTxId);
        record.setBranchTxId(branchId != null ? branchId.toString() : null);
        record.setCreateTime(LocalDateTime.now());
        record.setUpdateTime(LocalDateTime.now());
        record.setRemark("关联表操作");

        storageRecordMapper.insert(record);
        operationCount++;

        // 2. 插入处理历史
        jdbcTemplate.update(
            "INSERT INTO data_process_history (data_id, process_type, process_status, process_result, process_duration, global_tx_id, branch_tx_id, create_time, update_time) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)",
            dataId, "RELATED_PROCESS", "COMPLETED", "关联表处理完成", 2500L, globalTxId, branchId != null ? branchId.toString() : null, LocalDateTime.now(), LocalDateTime.now()
        );
        operationCount++;

        // 3. 插入多个标签
        String[] tagNames = {"related", "complex", "multi-table"};
        for (String tagName : tagNames) {
            jdbcTemplate.update(
                "INSERT INTO data_tags (data_id, tag_name, tag_value, tag_category, priority, global_tx_id, branch_tx_id, create_time, update_time) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)",
                dataId, tagName, "related_value", "related", 1, globalTxId, branchId != null ? branchId.toString() : null, LocalDateTime.now(), LocalDateTime.now()
            );
            operationCount++;
        }

        // 4. 建立关系链
        String[] relatedDataIds = {"INIT_STORAGE_001", "INIT_ANALYSIS_001"};
        for (String relatedId : relatedDataIds) {
            jdbcTemplate.update(
                "INSERT INTO data_relationship (parent_data_id, child_data_id, relationship_type, relationship_strength, status, global_tx_id, branch_tx_id, create_time, update_time) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)",
                relatedId, dataId, "DEPENDENCY", 0.95, "ACTIVE", globalTxId, branchId != null ? branchId.toString() : null, LocalDateTime.now(), LocalDateTime.now()
            );
            operationCount++;
        }

        result.put("operation", "RELATED_TABLES");
        result.put("operation_count", operationCount);
        result.put("tables_affected", new String[]{"storage_record", "data_process_history", "data_tags", "data_relationship"});

        return result;
    }

    /**
     * 执行统计操作
     */
    private Map<String, Object> executeStatisticsOperations(String dataId, String globalTxId, Long branchId, Map<String, Object> params) {
        log.info("执行统计操作，数据ID: {}", dataId);

        Map<String, Object> result = new HashMap<>();
        int updateCount = 0;

        // 更新多个统计记录
        String[] dataTypes = {"JSON", "TEXT", "STATISTICS"};
        for (String dataType : dataTypes) {
            // 尝试更新现有记录
            int updated = jdbcTemplate.update(
                "UPDATE data_statistics SET total_count = total_count + 1, success_count = success_count + 1, avg_process_time = (avg_process_time + 200) / 2, total_size = total_size + 1024, global_tx_id = ?, branch_tx_id = ?, update_time = ? WHERE stat_date = ? AND data_type = ?",
                globalTxId, branchId != null ? branchId.toString() : null, LocalDateTime.now(), LocalDate.now(), dataType
            );

            if (updated == 0) {
                // 如果没有现有记录，插入新记录
                jdbcTemplate.update(
                    "INSERT INTO data_statistics (stat_date, data_type, total_count, success_count, failed_count, avg_process_time, total_size, global_tx_id, branch_tx_id, create_time, update_time) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)",
                    LocalDate.now(), dataType, 1L, 1L, 0L, 200.0, 1024L, globalTxId, branchId != null ? branchId.toString() : null, LocalDateTime.now(), LocalDateTime.now()
                );
            }
            updateCount++;
        }

        // 插入配置更新记录（带死锁重试）
        try {
            executeWithDeadlockRetry(() -> {
                jdbcTemplate.update(
                    "INSERT INTO data_config (config_key, config_value, config_type, config_group, is_encrypted, is_active, global_tx_id, branch_tx_id, create_time, update_time) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)",
                    "stats.last_update." + dataId, String.valueOf(System.currentTimeMillis()), "LONG", "STATISTICS", 0, 1, globalTxId, branchId != null ? branchId.toString() : null, LocalDateTime.now(), LocalDateTime.now()
                );
                return null;
            });
        } catch (Exception e) {
            log.error("插入配置记录失败: dataId={}", dataId, e);
            throw new RuntimeException("插入配置记录失败", e);
        }
        updateCount++;

        result.put("operation", "STATISTICS");
        result.put("update_count", updateCount);
        result.put("data_types_updated", dataTypes);
        result.put("tables_affected", new String[]{"data_statistics", "data_config"});

        return result;
    }

    /**
     * 执行数据更新操作
     */
    private Map<String, Object> executeUpdateOperations(String dataId, String globalTxId, Long branchId, Map<String, Object> params) {
        log.info("执行数据更新操作，数据ID: {}", dataId);

        Map<String, Object> result = new HashMap<>();
        int updateCount = 0;

        // 1. 更新现有存储记录
        int recordsUpdated = jdbcTemplate.update(
            "UPDATE storage_record SET data_content = ?, data_type = ?, file_size = file_size * 2, status = 'UPDATED', global_tx_id = ?, branch_tx_id = ?, update_time = ? WHERE data_id LIKE ?",
            "更新后的数据内容", "UPDATED_TYPE", globalTxId, branchId != null ? branchId.toString() : null, LocalDateTime.now(), dataId.substring(0, Math.min(dataId.length(), 10)) + "%"
        );
        updateCount += recordsUpdated;

        // 2. 批量更新处理历史状态
        int historyUpdated = jdbcTemplate.update(
            "UPDATE data_process_history SET process_status = 'UPDATED', process_result = ' 批量更新完成', global_tx_id = ?, branch_tx_id = ?, update_time = ? WHERE process_type = 'STORAGE'",
            globalTxId, branchId != null ? branchId.toString() : null, LocalDateTime.now()
        );
        updateCount += historyUpdated;

        // 3. 更新标签优先级
        int tagsUpdated = jdbcTemplate.update(
            "UPDATE data_tags SET priority = priority + 10, tag_value = CONCAT(tag_value, '_UPDATED'), global_tx_id = ?, branch_tx_id = ?, update_time = ? WHERE tag_category = 'test'",
            globalTxId, branchId != null ? branchId.toString() : null, LocalDateTime.now()
        );
        updateCount += tagsUpdated;

        // 4. 条件更新关系强度
        int relationshipsUpdated = jdbcTemplate.update(
            "UPDATE data_relationship SET relationship_strength = relationship_strength * 1.2, status = 'ENHANCED', global_tx_id = ?, branch_tx_id = ?, update_time = ? WHERE relationship_type = 'DEPENDENCY'",
            globalTxId, branchId != null ? branchId.toString() : null, LocalDateTime.now()
        );
        updateCount += relationshipsUpdated;

        result.put("operation", "UPDATE_OPERATIONS");
        result.put("total_updates", updateCount);
        result.put("records_updated", recordsUpdated);
        result.put("history_updated", historyUpdated);
        result.put("tags_updated", tagsUpdated);
        result.put("relationships_updated", relationshipsUpdated);

        return result;
    }

    /**
     * 执行复杂更新操作
     */
    private Map<String, Object> executeComplexUpdateOperations(String dataId, String globalTxId, Long branchId, Map<String, Object> params) {
        log.info("执行复杂更新操作，数据ID: {}", dataId);

        Map<String, Object> result = new HashMap<>();
        int totalOperations = 0;

        // 1. 基于子查询的复杂更新
        int complexUpdate1 = jdbcTemplate.update(
            "UPDATE storage_record sr SET file_size = (SELECT AVG(file_size) FROM (SELECT file_size FROM storage_record WHERE status = 'STORED') AS avg_table), status = 'NORMALIZED', global_tx_id = ?, branch_tx_id = ?, update_time = ? WHERE sr.data_type = 'JSON'",
            globalTxId, branchId != null ? branchId.toString() : null, LocalDateTime.now()
        );
        totalOperations += complexUpdate1;

        // 2. 基于JOIN的更新操作
        int complexUpdate2 = jdbcTemplate.update(
            "UPDATE data_statistics ds JOIN (SELECT data_type, COUNT(*) as cnt FROM storage_record GROUP BY data_type) sr ON ds.data_type = sr.data_type SET ds.total_count = sr.cnt, ds.global_tx_id = ?, ds.branch_tx_id = ?, ds.update_time = ?",
            globalTxId, branchId != null ? branchId.toString() : null, LocalDateTime.now()
        );
        totalOperations += complexUpdate2;

        // 3. 条件批量更新配置
        int configUpdates = 0;
        String[] configKeys = {"storage.max_file_size", "storage.compression_enabled", "storage.backup_enabled"};
        String[] configValues = {"20971520", "true", "true"};

        for (int i = 0; i < configKeys.length; i++) {
            int updated = jdbcTemplate.update(
                "UPDATE data_config SET config_value = ?, global_tx_id = ?, branch_tx_id = ?, update_time = ? WHERE config_key = ?",
                configValues[i], globalTxId, branchId != null ? branchId.toString() : null, LocalDateTime.now(), configKeys[i]
            );

            if (updated == 0) {
                // 如果配置不存在，插入新配置
                jdbcTemplate.update(
                    "INSERT INTO data_config (config_key, config_value, config_type, config_group, is_encrypted, is_active, global_tx_id, branch_tx_id, create_time, update_time) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)",
                    configKeys[i], configValues[i], "STRING", "STORAGE", 0, 1, globalTxId, branchId != null ? branchId.toString() : null, LocalDateTime.now(), LocalDateTime.now()
                );
            }
            configUpdates++;
            totalOperations++;
        }

        // 4. 基于时间条件的批量更新
        int timeBasedUpdate = jdbcTemplate.update(
            "UPDATE data_process_history SET process_duration = process_duration + 1000, process_result = CONCAT(process_result, ' [ENHANCED]'), global_tx_id = ?, branch_tx_id = ?, update_time = ? WHERE create_time > DATE_SUB(NOW(), INTERVAL 1 HOUR)",
            globalTxId, branchId != null ? branchId.toString() : null, LocalDateTime.now()
        );
        totalOperations += timeBasedUpdate;

        result.put("operation", "COMPLEX_UPDATE");
        result.put("total_operations", totalOperations);
        result.put("complex_update_1", complexUpdate1);
        result.put("complex_update_2", complexUpdate2);
        result.put("config_updates", configUpdates);
        result.put("time_based_update", timeBasedUpdate);
        result.put("tables_affected", new String[]{"storage_record", "data_statistics", "data_config", "data_process_history"});

        return result;
    }

    /**
     * 执行带死锁重试的数据库操作
     */
    private <T> T executeWithDeadlockRetry(java.util.concurrent.Callable<T> operation) throws Exception {
        int maxRetries = 3;
        int retryCount = 0;
        long baseDelay = 100L;

        while (retryCount <= maxRetries) {
            try {
                return operation.call();
            } catch (Exception e) {
                if (isDeadlockException(e) && retryCount < maxRetries) {
                    retryCount++;
                    long delay = baseDelay * (1L << (retryCount - 1)) + (long)(Math.random() * 50);

                    log.warn("检测到死锁，第{}次重试，延迟{}ms: {}", retryCount, delay, e.getMessage());

                    try {
                        Thread.sleep(delay);
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        throw new RuntimeException("死锁重试被中断", ie);
                    }
                } else {
                    throw e;
                }
            }
        }

        throw new RuntimeException("死锁重试耗尽");
    }

    /**
     * 检查是否是死锁异常
     */
    private boolean isDeadlockException(Exception e) {
        if (e == null) return false;

        String message = e.getMessage();
        if (message != null) {
            String lowerMessage = message.toLowerCase();
            if (lowerMessage.contains("deadlock") ||
                lowerMessage.contains("lock wait timeout") ||
                lowerMessage.contains("try restarting transaction")) {
                return true;
            }
        }

        Throwable cause = e.getCause();
        if (cause instanceof Exception) {
            return isDeadlockException((Exception) cause);
        }

        return false;
    }
}
