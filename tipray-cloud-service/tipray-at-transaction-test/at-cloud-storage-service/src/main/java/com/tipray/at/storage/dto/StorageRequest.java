package com.tipray.at.storage.dto;

import lombok.Data;

/**
 * 存储请求DTO
 *
 * <AUTHOR>
 */
@Data
public class StorageRequest {

    /**
     * 数据ID
     */
    private String dataId;

    /**
     * 数据内容
     */
    private String dataContent;

    /**
     * 数据类型
     */
    private String dataType;

    /**
     * 全局事务ID
     */
    private String globalTxId;

    /**
     * 分支事务ID
     */
    private String branchTxId;

    /**
     * 是否模拟异常
     */
    private Boolean simulateError = false;

    /**
     * 是否模拟慢处理
     */
    private Boolean simulateSlowProcessing = false;

    /**
     * 慢处理延迟时间（毫秒）
     */
    private Long slowProcessingDelayMillis = 3000L;

    /**
     * 备注
     */
    private String remark;
}
