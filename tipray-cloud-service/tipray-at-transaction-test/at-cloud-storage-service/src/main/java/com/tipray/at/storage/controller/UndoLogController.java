package com.tipray.at.storage.controller;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 存储服务 undo_log 查询控制器
 * 提供undo_log相关的查询和统计接口
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/api/storage/undo-log")
public class UndoLogController {

    @Autowired
    private JdbcTemplate jdbcTemplate;

    /**
     * 获取undo_log统计信息
     */
    @GetMapping("/statistics")
    public Map<String, Object> getUndoLogStatistics() {
        Map<String, Object> response = new HashMap<>();

        try {
            // 总数统计
            int totalCount = jdbcTemplate.queryForObject("SELECT COUNT(*) FROM undo_log", Integer.class);

            // 按状态统计
            List<Map<String, Object>> statusStats = jdbcTemplate.queryForList(
                "SELECT log_status, COUNT(*) as count FROM undo_log GROUP BY log_status"
            );

            // 回滚信息大小统计
            Map<String, Object> sizeStats = new HashMap<>();
            if (totalCount > 0) {
                sizeStats = jdbcTemplate.queryForMap(
                    "SELECT AVG(LENGTH(rollback_info)) as avg_size, " +
                    "MAX(LENGTH(rollback_info)) as max_size, " +
                    "MIN(LENGTH(rollback_info)) as min_size " +
                    "FROM undo_log WHERE rollback_info IS NOT NULL"
                );
            }

            // 最近的记录
            List<Map<String, Object>> recentLogs = jdbcTemplate.queryForList(
                "SELECT id, xid, branch_id, log_status, LENGTH(rollback_info) as rollback_size, log_created " +
                "FROM undo_log ORDER BY log_created DESC LIMIT 10"
            );

            response.put("success", true);
            response.put("serviceName", "storage");
            response.put("totalCount", totalCount);
            response.put("statusStatistics", statusStats);
            response.put("rollbackSizeStatistics", sizeStats);
            response.put("recentLogs", recentLogs);

        } catch (Exception e) {
            log.error("获取存储服务undo_log统计失败", e);
            response.put("success", false);
            response.put("message", "获取统计失败: " + e.getMessage());
        }

        return response;
    }

    /**
     * 获取指定事务的undo_log
     */
    @GetMapping("/by-transaction/{xid}")
    public Map<String, Object> getUndoLogByTransaction(@PathVariable String xid) {
        Map<String, Object> response = new HashMap<>();

        try {
            List<Map<String, Object>> logs = jdbcTemplate.queryForList(
                "SELECT * FROM undo_log WHERE xid = ? ORDER BY log_created", xid
            );

            response.put("success", true);
            response.put("serviceName", "storage");
            response.put("xid", xid);
            response.put("logs", logs);
            response.put("count", logs.size());

        } catch (Exception e) {
            log.error("获取事务{}的undo_log失败", xid, e);
            response.put("success", false);
            response.put("message", "获取失败: " + e.getMessage());
        }

        return response;
    }

    /**
     * 清理undo_log数据
     */
    @PostMapping("/cleanup")
    public Map<String, Object> cleanupUndoLog() {
        Map<String, Object> response = new HashMap<>();

        try {
            int deletedCount = jdbcTemplate.update("DELETE FROM undo_log");

            response.put("success", true);
            response.put("serviceName", "storage");
            response.put("deletedCount", deletedCount);
            response.put("message", "undo_log清理完成");

            log.info("存储服务undo_log清理完成，删除记录数: {}", deletedCount);

        } catch (Exception e) {
            log.error("清理存储服务undo_log失败", e);
            response.put("success", false);
            response.put("message", "清理失败: " + e.getMessage());
        }

        return response;
    }

    /**
     * 获取业务数据统计
     */
    @GetMapping("/business-data-statistics")
    public Map<String, Object> getBusinessDataStatistics() {
        Map<String, Object> response = new HashMap<>();

        try {
            // 存储记录统计
            int totalRecords = jdbcTemplate.queryForObject("SELECT COUNT(*) FROM storage_record", Integer.class);

            // 按状态统计
            List<Map<String, Object>> statusStats = jdbcTemplate.queryForList(
                "SELECT status, COUNT(*) as count FROM storage_record GROUP BY status"
            );

            // 最近的记录
            List<Map<String, Object>> recentRecords = jdbcTemplate.queryForList(
                "SELECT id, data_id, status, create_time FROM storage_record ORDER BY create_time DESC LIMIT 10"
            );

            response.put("success", true);
            response.put("serviceName", "storage");
            response.put("totalRecords", totalRecords);
            response.put("statusStatistics", statusStats);
            response.put("recentRecords", recentRecords);

        } catch (Exception e) {
            log.error("获取存储服务业务数据统计失败", e);
            response.put("success", false);
            response.put("message", "获取统计失败: " + e.getMessage());
        }

        return response;
    }

    /**
     * 清理业务数据
     */
    @PostMapping("/cleanup-business-data")
    public Map<String, Object> cleanupBusinessData() {
        Map<String, Object> response = new HashMap<>();

        try {
            int deletedCount = jdbcTemplate.update("DELETE FROM storage_record");

            response.put("success", true);
            response.put("serviceName", "storage");
            response.put("deletedCount", deletedCount);
            response.put("message", "业务数据清理完成");

            log.info("存储服务业务数据清理完成，删除记录数: {}", deletedCount);

        } catch (Exception e) {
            log.error("清理存储服务业务数据失败", e);
            response.put("success", false);
            response.put("message", "清理失败: " + e.getMessage());
        }

        return response;
    }

    /**
     * 分析rollback_info内容
     */
    @GetMapping("/analyze-rollback-info")
    public Map<String, Object> analyzeRollbackInfo() {
        Map<String, Object> response = new HashMap<>();

        try {
            // 获取有rollback_info的记录
            List<Map<String, Object>> rollbackInfos = jdbcTemplate.queryForList(
                "SELECT id, xid, branch_id, LENGTH(rollback_info) as size, " +
                "SUBSTRING(rollback_info, 1, 200) as preview " +
                "FROM undo_log WHERE rollback_info IS NOT NULL AND LENGTH(rollback_info) > 0 " +
                "ORDER BY log_created DESC LIMIT 20"
            );

            // 统计SQL类型
            Map<String, Integer> sqlTypeStats = new HashMap<>();
            for (Map<String, Object> info : rollbackInfos) {
                String preview = (String) info.get("preview");
                if (preview != null) {
                    if (preview.contains("INSERT")) {
                        sqlTypeStats.merge("INSERT", 1, Integer::sum);
                    } else if (preview.contains("UPDATE")) {
                        sqlTypeStats.merge("UPDATE", 1, Integer::sum);
                    } else if (preview.contains("DELETE")) {
                        sqlTypeStats.merge("DELETE", 1, Integer::sum);
                    }
                }
            }

            response.put("success", true);
            response.put("serviceName", "storage");
            response.put("rollbackInfos", rollbackInfos);
            response.put("sqlTypeStatistics", sqlTypeStats);
            response.put("totalAnalyzed", rollbackInfos.size());

        } catch (Exception e) {
            log.error("分析存储服务rollback_info失败", e);
            response.put("success", false);
            response.put("message", "分析失败: " + e.getMessage());
        }

        return response;
    }

    /**
     * 验证数据一致性
     */
    @GetMapping("/verify-data-consistency")
    public Map<String, Object> verifyDataConsistency() {
        Map<String, Object> response = new HashMap<>();

        try {
            // 检查数据ID唯一性
            Map<String, Object> uniqueCheck = jdbcTemplate.queryForMap(
                "SELECT COUNT(*) as total_records, COUNT(DISTINCT data_id) as unique_data_ids " +
                "FROM storage_record"
            );

            int totalRecords = ((Number) uniqueCheck.get("total_records")).intValue();
            int uniqueDataIds = ((Number) uniqueCheck.get("unique_data_ids")).intValue();

            // 检查状态一致性
            List<Map<String, Object>> statusCheck = jdbcTemplate.queryForList(
                "SELECT status, COUNT(*) as count FROM storage_record GROUP BY status"
            );

            // 检查时间戳一致性
            List<Map<String, Object>> timeCheck = jdbcTemplate.queryForList(
                "SELECT COUNT(*) as count FROM storage_record WHERE update_time < create_time"
            );

            boolean dataConsistent = totalRecords == uniqueDataIds;
            boolean timeConsistent = ((Number) timeCheck.get(0).get("count")).intValue() == 0;

            response.put("success", true);
            response.put("serviceName", "storage");
            response.put("totalRecords", totalRecords);
            response.put("uniqueDataIds", uniqueDataIds);
            response.put("dataIdConsistent", dataConsistent);
            response.put("timeConsistent", timeConsistent);
            response.put("statusDistribution", statusCheck);
            response.put("overallConsistent", dataConsistent && timeConsistent);

        } catch (Exception e) {
            log.error("验证存储服务数据一致性失败", e);
            response.put("success", false);
            response.put("message", "验证失败: " + e.getMessage());
        }

        return response;
    }
}
