package com.tipray.at.storage.controller;

import com.tipray.at.storage.dto.StorageRequest;
import com.tipray.at.storage.entity.StorageRecord;
import com.tipray.at.storage.service.StorageService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * 存储服务控制器
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/api/storage")
public class StorageController {

    @Autowired
    private StorageService storageService;

    /**
     * 存储数据接口
     * 框架会自动处理分支事务
     */
    @PostMapping("/store")
    public Map<String, Object> storeData(@RequestBody Map<String, Object> request) {
        Map<String, Object> result = new HashMap<>();

        try {
            // 转换为StorageRequest
            StorageRequest storageRequest = new StorageRequest();
            storageRequest.setDataContent((String) request.get("dataContent"));
            storageRequest.setDataType((String) request.get("dataType"));
            storageRequest.setBranchTxId((String) request.get("branchTxId"));
            storageRequest.setSimulateError((Boolean) request.get("simulateError"));
            storageRequest.setRemark((String) request.get("remark"));
            storageRequest.setDataId((String) request.get("dataId"));

            StorageRecord record = storageService.storeData(storageRequest);

            result.put("success", true);
            result.put("code", "200");
            result.put("message", "存储成功");
            result.put("dataId", record.getDataId());
            result.put("globalTxId", record.getGlobalTxId());
            result.put("branchTxId", record.getBranchTxId());
            result.put("data", "存储成功，路径：" + record.getStoragePath());

            if ((Boolean) request.getOrDefault("randomError", false)) {
                if (Math.random()  > 0.5) throw new RuntimeException("随机异常");
            }
        } catch (Exception e) {
            log.error("存储数据失败：{}", e.getMessage(), e);
            result.put("success", false);
            result.put("code", "500");
            result.put("message", "存储失败：" + e.getMessage());
            result.put("dataId", request.get("dataId"));
            result.put("globalTxId", request.get("globalTxId"));
            result.put("branchTxId", request.get("branchTxId"));
        }

        return result;
    }

    /**
     * 查询存储记录
     */
    @GetMapping("/{dataId}")
    public Map<String, Object> getStorageRecord(@PathVariable String dataId) {
        Map<String, Object> result = new HashMap<>();

        try {
            StorageRecord record = storageService.getByDataId(dataId);

            if (record != null) {
                result.put("success", true);
                result.put("message", "查询成功");
                result.put("data", record);
            } else {
                result.put("success", false);
                result.put("message", "存储记录不存在");
            }

        } catch (Exception e) {
            log.error("查询存储记录失败：{}", e.getMessage(), e);
            result.put("success", false);
            result.put("message", "查询失败：" + e.getMessage());
        }

        return result;
    }

    /**
     * 删除存储数据
     */
    @DeleteMapping("/delete")
    public ResponseEntity<Map<String, Object>> deleteData(@RequestBody StorageRequest request) {
        log.info("接收到删除请求，数据ID：{}", request.getDataId());

        Map<String, Object> response = new HashMap<>();

        try {
            boolean result = storageService.deleteData(request);

            response.put("success", result);
            response.put("message", result ? "数据删除成功" : "数据不存在或删除失败");
            response.put("dataId", request.getDataId());

            log.info("数据删除{}，数据ID：{}", result ? "成功" : "失败", request.getDataId());
            return ResponseEntity.ok(response);

        } catch (Exception e) {
            log.error("数据删除失败，数据ID：{}，错误：{}", request.getDataId(), e.getMessage(), e);

            response.put("success", false);
            response.put("message", "删除失败： " + e.getMessage());

            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    /**
     * 复杂多表操作接口（用于测试复杂SQL回滚）
     */
    @PostMapping("/complex-operations")
    public Map<String, Object> complexOperations(@RequestBody Map<String, Object> request) {
        Map<String, Object> result = new HashMap<>();

        try {
            String dataId = (String) request.get("dataId");
            String operationType = (String) request.get("operationType");
            Boolean simulateError = (Boolean) request.get("simulateError");

            log.info("开始执行复杂多表操作，数据ID: {}, 操作类型: {}", dataId, operationType);

            // 根据操作类型执行不同的复杂SQL操作
            Object operationResult = storageService.executeComplexOperations(dataId, operationType, request);

            // 模拟异常
            if (Boolean.TRUE.equals(simulateError)) {
                throw new RuntimeException("模拟复杂操作异常");
            }

            result.put("success", true);
            result.put("code", "200");
            result.put("message", "复杂操作执行成功");
            result.put("dataId", dataId);
            result.put("operationType", operationType);
            result.put("result", operationResult);

        } catch (Exception e) {
            log.error("复杂多表操作失败：{}", e.getMessage(), e);
            result.put("success", false);
            result.put("code", "500");
            result.put("message", "复杂操作失败：" + e.getMessage());
        }

        return result;
    }

    /**
     * 检查数据是否存在（用于回滚验证）
     */
    @GetMapping("/check/{dataId}")
    public Map<String, Object> checkData(@PathVariable String dataId) {
        Map<String, Object> result = new HashMap<>();

        try {
            Map<String, Object> checkResult = storageService.checkDataExists(dataId);

            result.put("success", true);
            result.put("message", "检查完成");
            result.put("dataId", dataId);
            result.put("exists", checkResult);

        } catch (Exception e) {
            log.error("检查数据失败：{}", e.getMessage(), e);
            result.put("success", false);
            result.put("message", "检查失败：" + e.getMessage());
        }

        return result;
    }

    /**
     * 获取数据详情（用于回滚验证）
     */
    @GetMapping("/get/{dataId}")
    public Map<String, Object> getData(@PathVariable String dataId) {
        Map<String, Object> result = new HashMap<>();

        try {
            StorageRecord record = storageService.getByDataId(dataId);

            result.put("success", record != null);
            result.put("message", record != null ? "数据存在" : "数据不存在");
            result.put("dataId", dataId);
            result.put("data", record);

        } catch (Exception e) {
            log.error("获取数据失败：{}", e.getMessage(), e);
            result.put("success", false);
            result.put("message", "获取失败：" + e.getMessage());
        }

        return result;
    }

    /**
     * 健康检查
     */
    @GetMapping("/health")
    public Map<String, Object> health() {
        Map<String, Object> result = new HashMap<>();
        result.put("status", "UP");
        result.put("service", "at-cloud-storage-service");
        result.put("timestamp", System.currentTimeMillis());
        return result;
    }
}
