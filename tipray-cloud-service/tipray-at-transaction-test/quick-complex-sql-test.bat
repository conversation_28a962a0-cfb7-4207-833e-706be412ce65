@echo off
chcp 65001 > nul
echo ========================================
echo 快速复杂SQL回滚测试
echo ========================================

echo.
echo 假设云服务已经启动，直接运行测试...
echo.

cd at-dlp-service

echo 运行云服务复杂SQL回滚测试...
mvn test -Dtest=CloudServiceComplexSqlRollbackTest -Dspring.profiles.active=test

if %errorlevel% neq 0 (
    echo.
    echo 测试失败！请检查：
    echo 1. 云服务是否启动（端口8081, 8082, 8083）
    echo 2. 数据库连接是否正常
    echo 3. 测试日志中的错误信息
    echo 4. 云服务的复杂SQL接口是否正常
) else (
    echo.
    echo ========================================
    echo 云服务复杂SQL回滚测试成功！
    echo ========================================
    echo 已验证以下云服务复杂SQL回滚场景：
    echo ✓ 云存储服务复杂SQL回滚（多表INSERT/UPDATE/DELETE）
    echo ✓ 云分析服务复杂SQL回滚（批量INSERT/JOIN查询/UPDATE）
    echo ✓ 云通知服务复杂SQL回滚（批量通知/配置更新/统计）
    echo ✓ 全链路复杂SQL回滚（所有云服务复杂操作）
    echo ✓ 复杂SQL性能回滚测试
    echo ✓ 云服务数据更新回滚测试（UPDATE操作专项测试）
    echo ✓ 云服务大批量数据回滚测试（大数据量场景）
    echo ✓ 云服务并发事务回滚测试（并发场景）
    echo ✓ 云服务跨表关联数据回滚测试（关联表一致性）
    echo.
    echo 🎉 所有云服务的复杂SQL操作都能正确生成UndoLog并成功回滚！
    echo 🎉 验证了与Seata相同的SQL解析和回滚能力！
    echo ========================================
)

echo.
pause
