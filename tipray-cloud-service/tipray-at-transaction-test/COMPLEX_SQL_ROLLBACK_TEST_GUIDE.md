# 云服务复杂SQL回滚测试指南

## 概述

本测试套件专门用于验证**云服务端**复杂SQL操作的UndoLog生成和回滚功能。由于使用了Seata的SQL解析器，理论上应该支持与Seata相同的SQL类型和复杂度。

**重点**：这个测试主要验证AT模式下云服务端的SQL回滚能力，DLP端使用Spring事务管理，不需要特别关注。

## 测试场景

### 1. 云存储服务复杂SQL回滚
- **测试内容**: 云存储服务执行复杂的多表操作
- **涉及表**: `storage_record`, `data_process_history`, `data_tags`, `data_relationship`
- **SQL类型**:
  - 多表INSERT（主记录 + 历史记录 + 标签记录）
  - 批量INSERT（循环插入多条记录）
  - 复杂UPDATE（更新统计表）
  - 条件DELETE（清理旧数据）
  - JOIN查询 + 基于结果的UPDATE
- **验证点**: 所有复杂SQL操作都能正确生成UndoLog并回滚

### 2. 云分析服务复杂SQL回滚
- **测试内容**: 云分析服务执行复杂的分析和统计操作
- **涉及表**: `analysis_record`, `data_process_history`, `data_tags`, `data_relationship`, `data_statistics`
- **SQL类型**:
  - 主记录INSERT + 多个关联表INSERT
  - 批量INSERT（循环插入分析记录）
  - 复杂UPDATE（更新统计表，支持UPSERT逻辑）
  - 条件DELETE（清理临时数据）
  - 复杂JOIN查询 + 基于结果的UPDATE
- **验证点**: 分析服务的复杂SQL操作都能正确回滚

### 3. 云通知服务复杂SQL回滚
- **测试内容**: 云通知服务执行复杂的通知和配置操作
- **涉及表**: `notify_record`, `data_process_history`, `data_tags`, `data_relationship`, `data_config`, `data_statistics`
- **SQL类型**:
  - 主通知记录INSERT + 多个关联表INSERT
  - 批量INSERT（群发通知）
  - 配置UPDATE（支持UPSERT逻辑）
  - 统计UPDATE（支持UPSERT逻辑）
  - 条件DELETE（清理过期数据）
  - 复杂查询 + 基于结果的状态UPDATE
- **验证点**: 通知服务的复杂SQL操作都能正确回滚

### 4. 全链路复杂SQL回滚
- **测试内容**: 所有云服务都执行复杂SQL操作，在最后一步失败
- **涉及服务**: 云存储 + 云分析 + 云通知
- **验证点**: 所有云服务的复杂SQL操作都能正确回滚

### 5. 复杂SQL性能回滚测试
- **测试内容**: 验证复杂SQL回滚的性能表现
- **验证点**: 复杂SQL回滚应该在合理时间内完成

## 数据库表结构

### 扩展表（用于复杂SQL测试）

1. **data_process_history** - 数据处理历史表
2. **data_statistics** - 数据统计表
3. **data_relationship** - 数据关系表
4. **data_tags** - 数据标签表
5. **data_config** - 数据配置表

## 运行测试

### 方式一：完整测试（推荐）
```bash
# 运行完整的测试脚本（包含环境准备）
run-complex-sql-rollback-tests.bat
```

### 方式二：快速测试
```bash
# 假设云服务已启动，直接运行测试
quick-complex-sql-test.bat
```

### 方式三：手动运行
```bash
# 1. 启动云服务
cd at-cloud-storage-service
mvn spring-boot:run -Dspring-boot.run.profiles=test

cd at-cloud-analysis-service  
mvn spring-boot:run -Dspring-boot.run.profiles=test

cd at-cloud-notify-service
mvn spring-boot:run -Dspring-boot.run.profiles=test

# 2. 运行测试
cd at-dlp-service
mvn test -Dtest=CloudServiceComplexSqlRollbackTest -Dspring.profiles.active=test
```

## 测试验证点

### UndoLog生成验证
- 检查`undo_log_storage`表中是否生成了正确的UndoLog记录
- 验证UndoLog包含了所有SQL操作的回滚信息

### 数据回滚验证
- 验证所有插入的数据在回滚后被删除
- 验证所有更新的数据在回滚后恢复原值
- 验证所有删除的数据在回滚后被恢复

### 多表一致性验证
- 验证关联表之间的数据一致性
- 验证外键约束在回滚过程中的正确处理

## 云服务复杂SQL操作详情

### 云存储服务 (at-cloud-storage-service)
**接口**: `POST /api/storage/complex-operations`

**执行的SQL操作**:
```sql
-- 1. 主记录INSERT
INSERT INTO storage_record (data_id, data_content, data_type, ...) VALUES (...)

-- 2. 历史记录INSERT
INSERT INTO data_process_history (data_id, process_type, ...) VALUES (...)

-- 3. 批量标签INSERT（循环）
INSERT INTO data_tags (data_id, tag_name, tag_value, ...) VALUES (...)

-- 4. 批量处理记录INSERT（循环）
INSERT INTO data_process_history (data_id, process_type, ...) VALUES (...)

-- 5. 关系记录INSERT（循环）
INSERT INTO data_relationship (parent_data_id, child_data_id, ...) VALUES (...)

-- 6. 统计UPDATE（支持UPSERT）
UPDATE data_statistics SET total_count = total_count + ?, ... WHERE stat_date = ? AND data_type = ?

-- 7. 复杂JOIN查询 + UPDATE
SELECT ar.id, ar.analysis_score, COUNT(dt.id) as tag_count
FROM analysis_record ar LEFT JOIN data_tags dt ON ar.data_id = dt.data_id
WHERE ar.data_id LIKE ? GROUP BY ar.id HAVING tag_count > 0

UPDATE analysis_record SET analysis_score = ?, status = ? WHERE id = ?
```

### 云分析服务 (at-cloud-analysis-service)
**接口**: `POST /api/analysis/complex-sql`

**执行的SQL操作**:
```sql
-- 1. 主分析记录INSERT
INSERT INTO analysis_record (data_id, data_content, analysis_result, ...) VALUES (...)

-- 2. 处理历史INSERT
INSERT INTO data_process_history (data_id, process_type, ...) VALUES (...)

-- 3. 批量标签INSERT（循环）
INSERT INTO data_tags (data_id, tag_name, tag_value, ...) VALUES (...)

-- 4. 批量分析记录INSERT（循环）
INSERT INTO analysis_record (data_id, data_content, ...) VALUES (...)

-- 5. 关系链INSERT（循环）
INSERT INTO data_relationship (parent_data_id, child_data_id, ...) VALUES (...)

-- 6. 统计UPSERT操作
UPDATE data_statistics SET total_count = total_count + ?, ... WHERE stat_date = ? AND data_type = ?
INSERT INTO data_statistics (stat_date, data_type, ...) VALUES (...) -- 如果UPDATE失败

-- 7. 条件DELETE
DELETE FROM data_tags WHERE tag_category = 'temp' AND create_time < ?

-- 8. 复杂JOIN查询 + 基于结果的UPDATE
SELECT ar.id, ar.analysis_score, COUNT(dt.id) as tag_count
FROM analysis_record ar LEFT JOIN data_tags dt ON ar.data_id = dt.data_id
WHERE ar.data_id LIKE ? GROUP BY ar.id HAVING tag_count > 0

UPDATE analysis_record SET analysis_score = ?, status = ? WHERE id = ?
```

### 云通知服务 (at-cloud-notify-service)
**接口**: `POST /api/notify/complex-sql`

**执行的SQL操作**:
```sql
-- 1. 主通知记录INSERT
INSERT INTO notify_record (data_id, notify_type, notify_target, ...) VALUES (...)

-- 2. 处理历史INSERT
INSERT INTO data_process_history (data_id, process_type, ...) VALUES (...)

-- 3. 批量标签INSERT（循环）
INSERT INTO data_tags (data_id, tag_name, tag_value, ...) VALUES (...)

-- 4. 批量通知记录INSERT（循环，模拟群发）
INSERT INTO notify_record (data_id, notify_type, notify_target, ...) VALUES (...)

-- 5. 关系记录INSERT（循环）
INSERT INTO data_relationship (parent_data_id, child_data_id, ...) VALUES (...)

-- 6. 配置UPSERT操作
UPDATE data_config SET config_value = ?, ... WHERE config_key = ?
INSERT INTO data_config (config_key, config_value, ...) VALUES (...) -- 如果UPDATE失败

-- 7. 统计UPSERT操作
UPDATE data_statistics SET total_count = total_count + ?, ... WHERE stat_date = ? AND data_type = ?
INSERT INTO data_statistics (stat_date, data_type, ...) VALUES (...) -- 如果UPDATE失败

-- 8. 条件DELETE
DELETE FROM data_tags WHERE tag_category = 'temp' AND create_time < ?

-- 9. 复杂JOIN查询 + 基于结果的UPDATE
SELECT nr.id, nr.notify_type, COUNT(dt.id) as tag_count
FROM notify_record nr LEFT JOIN data_tags dt ON nr.data_id = dt.data_id
WHERE nr.data_id LIKE ? GROUP BY nr.id HAVING tag_count > 0

UPDATE notify_record SET status = ?, update_time = ? WHERE id = ?
```

## 支持的SQL类型

基于Seata SQL解析器，支持以下SQL类型：

### INSERT操作
- ✅ 单行插入
- ✅ 批量插入（循环）
- ✅ 带复杂字段的插入
- ✅ 带事务ID的插入

### UPDATE操作
- ✅ 简单UPDATE
- ✅ 条件UPDATE
- ✅ 多字段UPDATE
- ✅ 基于查询结果的UPDATE
- ✅ UPSERT逻辑（UPDATE失败时INSERT）

### DELETE操作
- ✅ 简单DELETE
- ✅ 条件DELETE
- ✅ 基于时间条件的DELETE

### 复杂场景
- ✅ 事务中混合多种SQL类型
- ✅ 循环执行SQL操作
- ✅ 多表关联操作
- ✅ JOIN查询 + 基于结果的操作

## 故障排查

### 常见问题

1. **UndoLog未生成**
   - 检查数据源是否正确配置为AT数据源代理
   - 检查事务上下文是否正确传递

2. **回滚失败**
   - 检查UndoLog数据是否完整
   - 检查数据库连接是否正常
   - 检查SQL解析是否正确

3. **数据不一致**
   - 检查外键约束设置
   - 检查并发访问控制
   - 检查事务隔离级别

### 日志分析
- 查看AT数据源代理的SQL拦截日志
- 查看UndoLog生成和执行日志
- 查看事务状态变更日志

## 性能考虑

### UndoLog存储
- 复杂SQL操作会生成较大的UndoLog
- 建议定期清理已完成事务的UndoLog

### 回滚性能
- 复杂SQL的回滚可能耗时较长
- 建议设置合适的事务超时时间

## 扩展测试

可以基于现有框架扩展更多复杂SQL测试：

1. **存储过程回滚测试**
2. **触发器影响的回滚测试**
3. **大数据量回滚性能测试**
4. **并发事务回滚测试**

## 注意事项

1. 测试前确保数据库中没有重要数据
2. 测试过程中会产生大量临时数据
3. 建议在独立的测试环境中运行
4. 测试完成后及时清理测试数据
