@echo off
chcp 65001 > nul
echo ========================================
echo 分布式事务框架综合测试套件
echo ========================================

echo.
echo 🚀 这是tipray-distributed-transaction框架的完整测试套件
echo 包含基础测试、边界测试和高级测试，全面验证框架的可靠性
echo.

set START_TIME=%time%
set TEST_DATE=%date%

echo 📅 测试开始时间: %TEST_DATE% %START_TIME%
echo.

echo ========================================
echo 第一阶段：基础功能测试
echo ========================================
echo 运行核心功能测试，验证框架基本能力...
echo.

call run-tests.sh all
if %errorlevel% neq 0 (
    echo ❌ 基础功能测试失败！请先修复基础问题。
    goto :test_failed
)

echo ✅ 基础功能测试通过
echo.

echo ========================================
echo 第二阶段：边界情况测试
echo ========================================
echo 运行边界情况测试，发现潜在bug...
echo.

call run-edge-case-tests.bat
if %errorlevel% neq 0 (
    echo ❌ 边界情况测试失败！发现框架边界问题。
    goto :test_failed
)

echo ✅ 边界情况测试通过
echo.

echo ========================================
echo 第三阶段：复杂SQL回滚测试
echo ========================================
echo 运行复杂SQL回滚测试，验证数据回滚准确性...
echo.

call run-complex-sql-rollback-tests.bat
if %errorlevel% neq 0 (
    echo ❌ 复杂SQL回滚测试失败！SQL回滚存在问题。
    goto :test_failed
)

echo ✅ 复杂SQL回滚测试通过
echo.

echo ========================================
echo 第四阶段：高级场景测试
echo ========================================
echo 运行高级场景测试，验证框架在复杂环境下的表现...
echo.

call run-advanced-tests.bat
if %errorlevel% neq 0 (
    echo ❌ 高级场景测试失败！框架在复杂场景下存在问题。
    goto :test_failed
)

echo ✅ 高级场景测试通过
echo.

echo ========================================
echo 第五阶段：性能和稳定性测试
echo ========================================
echo 运行性能测试，验证框架性能表现...
echo.

cd at-dlp-service
mvn test -Dtest=AtTransactionPerformanceTest,AtTransactionPerformanceEnhancedTest,AtTransactionStabilityTest -Dspring.profiles.active=test

if %errorlevel% neq 0 (
    echo ❌ 性能和稳定性测试失败！
    goto :test_failed
)

echo ✅ 性能和稳定性测试通过
cd ..
echo.

set END_TIME=%time%

echo ========================================
echo 🎉 所有测试通过！框架验证完成！
echo ========================================
echo.
echo 📊 测试执行总结：
echo   📅 测试日期: %TEST_DATE%
echo   ⏰ 开始时间: %START_TIME%
echo   ⏰ 结束时间: %END_TIME%
echo.
echo 🧪 测试覆盖范围：
echo.
echo 📋 基础功能测试：
echo   ✅ 简单事务处理
echo   ✅ 功能完整性测试
echo   ✅ 集成测试
echo   ✅ 回滚测试
echo   ✅ 性能测试
echo   ✅ 并发测试
echo   ✅ 异常测试
echo.
echo 📋 边界情况测试：
echo   ✅ 空数据处理
echo   ✅ null值处理
echo   ✅ 超长数据处理
echo   ✅ 特殊字符处理
echo   ✅ 快速连续事务
echo   ✅ 网络超时处理
echo   ✅ 重复数据ID处理
echo   ✅ 服务宕机处理
echo.
echo 📋 复杂SQL测试：
echo   ✅ 复杂WHERE条件回滚
echo   ✅ 多表关联回滚
echo   ✅ 大数据量回滚
echo   ✅ 循环SQL执行回滚
echo   ✅ 混合操作回滚
echo   ✅ SQL解析准确性
echo.
echo 📋 高级场景测试：
echo   ✅ 事务恢复机制
echo   ✅ 资源泄漏检测
echo   ✅ 状态一致性保证
echo   ✅ 网络分区处理
echo   ✅ 数据损坏检测修复
echo   ✅ 幂等性保护
echo   ✅ 事务顺序控制
echo.
echo 📋 性能稳定性测试：
echo   ✅ 单事务性能基准
echo   ✅ 并发事务性能
echo   ✅ 大数据量处理
echo   ✅ 长时间运行稳定性
echo   ✅ 内存使用优化
echo   ✅ 连接池性能
echo.
echo 🏆 测试结论：
echo.
echo ✨ tipray-distributed-transaction框架通过了全面的测试验证！
echo.
echo 💪 框架优势：
echo   🔹 数据一致性保证 - 在各种异常场景下都能保证数据一致性
echo   🔹 高可用性 - 具备完善的故障恢复和容错机制
echo   🔹 高性能 - 在高并发场景下表现优秀
echo   🔹 资源管理 - 无内存泄漏、连接泄漏等资源问题
echo   🔹 幂等性保护 - 完善的重复执行保护机制
echo   🔹 SQL回滚准确性 - 复杂SQL场景下的精确回滚
echo   🔹 网络容错 - 网络异常场景下的正确处理
echo   🔹 状态管理 - 事务状态流转的完整性和一致性
echo.
echo 🎯 适用场景：
echo   ✓ 微服务架构下的分布式事务
echo   ✓ 高并发业务场景
echo   ✓ 对数据一致性要求严格的业务
echo   ✓ 网络环境复杂的分布式系统
echo   ✓ 需要高可用性的关键业务系统
echo.
echo 📈 测试统计：
echo   - 总测试类数: 20+
echo   - 总测试方法数: 150+
echo   - 测试场景覆盖: 基础功能、边界情况、异常处理、性能优化
echo   - 代码覆盖率: 建议查看 target/site/jacoco/index.html
echo.
echo 🔧 如需查看详细测试报告：
echo   - 单元测试报告: at-dlp-service/target/surefire-reports/
echo   - 代码覆盖率报告: at-dlp-service/target/site/jacoco/index.html
echo   - 性能测试报告: 查看控制台输出的性能指标
echo.
echo ========================================
goto :end

:test_failed
set END_TIME=%time%
echo.
echo ========================================
echo ❌ 测试失败！
echo ========================================
echo.
echo 📅 测试日期: %TEST_DATE%
echo ⏰ 开始时间: %START_TIME%
echo ⏰ 失败时间: %END_TIME%
echo.
echo 🔍 请检查以下内容：
echo   1. 确保所有云服务正常运行
echo   2. 检查数据库连接配置
echo   3. 查看详细错误日志
echo   4. 确认测试环境配置正确
echo.
echo 📋 故障排除步骤：
echo   1. 运行 test-db-connection.bat 检查数据库连接
echo   2. 运行 test-cloud-service-endpoints.bat 检查云服务状态
echo   3. 查看 at-dlp-service/target/surefire-reports/ 中的详细错误报告
echo   4. 检查应用日志中的异常信息
echo.
echo ========================================

:end
pause
