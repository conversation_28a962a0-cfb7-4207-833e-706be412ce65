server:
  port: 8080
spring:
  application:
    name: at-dlp-service
  datasource:
    url: ***************************************************************************************************************************************************
    username: root
    password: 123456
    driver-class-name: com.mysql.cj.jdbc.Driver
    hikari:
      minimum-idle: 5
      maximum-pool-size: 20
      auto-commit: true
      idle-timeout: 30000
      pool-name: DlpHikariCP
      max-lifetime: 1800000
      connection-timeout: 30000

# MyBatis Plus 配置
mybatis-plus:
  configuration:
    map-underscore-to-camel-case: true
    cache-enabled: false
    call-setters-on-nulls: true
    jdbc-type-for-null: 'null'
  mapper-locations: classpath*:mapper/**/*Mapper.xml
  type-aliases-package: com.tipray.at.dlp.entity

# Tipray 分布式事务配置
tipray:
  info:
    version: 1.0
    mapper-base-package: com.tipray.at.dlp.mapper
  transaction:
    enabled: true
    at:
      enabled: true
      service:
        mappings:
          cloud-storage-service:
            base-url: "localhost"
            port: 8081
            protocol: "http"
          cloud-analysis-service:
            base-url: "localhost"
            port: 8082
            protocol: "http"
          cloud-notify-service:
            base-url: "localhost"
            port: 8083
            protocol: "http"

    ui:
      enabled: false              # 启用UI模块
# 日志配置
logging:
  level:
    # 分布式事务核心日志 - 只保留最关键的状态变更
    com.tipray.transaction.core.application.manager.DistributedTransactionManager: INFO
    com.tipray.transaction.core.application.manager.TransactionStatusManager: INFO

    # 业务日志
    com.tipray.at.dlp: INFO

    # 屏障管理器使用WARN级别（大幅减少屏障日志）
    com.tipray.transaction.core.infrastructure.barrier: WARN
    com.tipray.transaction.core.infrastructure.persistence.impl.InMemoryTransactionRepository: WARN

    # 事件发布器使用WARN级别
    com.tipray.transaction.core.infrastructure.event: WARN

    # 其他框架组件使用DEBUG
    com.tipray.transaction: DEBUG

    # 配置和初始化使用WARN级别
    com.tipray.transaction.core.config: WARN
    com.tipray.transaction.core.infrastructure.config: WARN

    # MyBatis日志
    com.tipray.at.dlp.mapper: WARN

    root: INFO
  pattern:
    console: "%d{HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"
