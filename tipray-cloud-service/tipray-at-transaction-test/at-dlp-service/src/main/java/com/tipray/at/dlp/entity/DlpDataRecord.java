package com.tipray.at.dlp.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * DLP数据记录实体
 * 
 * <AUTHOR>
 */
@Data
@TableName("dlp_data_record")
public class DlpDataRecord {

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 数据ID
     */
    private String dataId;

    /**
     * 数据内容
     */
    private String dataContent;

    /**
     * 数据类型
     */
    private String dataType;

    /**
     * 处理状态：PENDING-待处理，PROCESSING-处理中，SUCCESS-成功，FAILED-失败
     */
    private String status;

    /**
     * 全局事务ID
     */
    private String globalTxId;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 备注
     */
    private String remark;
}
