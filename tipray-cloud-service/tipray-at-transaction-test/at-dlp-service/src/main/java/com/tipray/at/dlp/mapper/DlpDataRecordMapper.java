package com.tipray.at.dlp.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.tipray.at.dlp.entity.DlpDataRecord;
import org.apache.ibatis.annotations.*;

import java.time.LocalDateTime;

/**
 * DLP数据记录Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface DlpDataRecordMapper extends BaseMapper<DlpDataRecord> {

    /**
     * 复杂WHERE条件更新
     */
    @Update("UPDATE dlp_data_record SET status = #{newStatus}, update_time = #{updateTime} " +
            "WHERE data_id = #{dataId} AND status = #{oldStatus} AND create_time < #{beforeTime} " +
            "AND data_type IN ('JSON', 'XML')")
    int updateByComplexCondition(@Param("newStatus") String newStatus,
                                @Param("updateTime") LocalDateTime updateTime,
                                @Param("dataId") String dataId,
                                @Param("oldStatus") String oldStatus,
                                @Param("beforeTime") LocalDateTime beforeTime);

    /**
     * 根据数据ID更新状态
     */
    @Update("UPDATE dlp_data_record SET status = #{status}, update_time = #{updateTime} WHERE data_id = #{dataId}")
    int updateStatusByDataId(@Param("dataId") String dataId,
                           @Param("status") String status,
                           @Param("updateTime") LocalDateTime updateTime);

    /**
     * 根据数据ID查询
     */
    @Select("SELECT * FROM dlp_data_record WHERE data_id = #{dataId}")
    DlpDataRecord selectByDataId(@Param("dataId") String dataId);

    /**
     * 根据状态和时间删除
     */
    @Delete("DELETE FROM dlp_data_record WHERE status = #{status} AND create_time < #{beforeTime}")
    int deleteByStatusAndTime(@Param("status") String status, @Param("beforeTime") LocalDateTime beforeTime);

    /**
     * 根据数据ID删除
     */
    @Delete("DELETE FROM dlp_data_record WHERE data_id = #{dataId}")
    int deleteByDataId(@Param("dataId") String dataId);

    /**
     * 复杂查询：多条件统计
     */
    @Select("SELECT COUNT(*) FROM dlp_data_record " +
            "WHERE status = #{status} AND create_time BETWEEN #{startTime} AND #{endTime} " +
            "AND data_type = #{dataType}")
    int countByComplexCondition(@Param("status") String status,
                              @Param("startTime") LocalDateTime startTime,
                              @Param("endTime") LocalDateTime endTime,
                              @Param("dataType") String dataType);

    /**
     * 批量更新状态（使用CASE WHEN）
     */
    @Update("<script>" +
            "UPDATE dlp_data_record SET " +
            "status = CASE data_id " +
            "<foreach collection='updates' item='item' separator=' '>" +
            "WHEN #{item.dataId} THEN #{item.status} " +
            "</foreach>" +
            "END, " +
            "update_time = #{updateTime} " +
            "WHERE data_id IN " +
            "<foreach collection='updates' item='item' open='(' separator=',' close=')'>" +
            "#{item.dataId}" +
            "</foreach>" +
            "</script>")
    int batchUpdateStatus(@Param("updates") java.util.List<BatchUpdateParam> updates,
                         @Param("updateTime") LocalDateTime updateTime);

    /**
     * 清理测试数据
     */
    @Delete("DELETE FROM dlp_data_record WHERE data_id LIKE 'TEST_%'")
    int deleteTestData();

    /**
     * 批量更新参数
     */
    class BatchUpdateParam {
        private String dataId;
        private String status;

        public BatchUpdateParam(String dataId, String status) {
            this.dataId = dataId;
            this.status = status;
        }

        public String getDataId() { return dataId; }
        public void setDataId(String dataId) { this.dataId = dataId; }
        public String getStatus() { return status; }
        public void setStatus(String status) { this.status = status; }
    }
}
