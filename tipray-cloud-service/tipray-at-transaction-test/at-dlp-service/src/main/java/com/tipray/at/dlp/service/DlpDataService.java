package com.tipray.at.dlp.service;

import com.tipray.at.dlp.dto.DataProcessRequest;
import com.tipray.at.dlp.dto.DataUpdateRequest;
import com.tipray.at.dlp.entity.DlpDataRecord;

import java.util.List;

/**
 * DLP数据服务接口
 *
 * <AUTHOR>
 */
public interface DlpDataService {

    /**
     * 处理数据 - AT模式分布式事务
     *
     * @param request 数据处理请求
     * @return 处理结果
     */
    DlpDataRecord processData(DataProcessRequest request);

    /**
     * 根据数据ID查询记录
     *
     * @param dataId 数据ID
     * @return 数据记录
     */
    DlpDataRecord getByDataId(String dataId);

    /**
     * 更新数据 - AT模式分布式事务测试
     *
     * @param request 数据更新请求
     * @return 更新结果
     */
    DlpDataRecord updateData(DataUpdateRequest request);

    /**
     * 批量更新数据 - AT模式分布式事务测试
     *
     * @param request 批量更新请求
     * @return 更新的记录列表
     */
    List<DlpDataRecord> batchUpdateData(DataUpdateRequest request);

    /**
     * 复杂条件更新数据 - AT模式分布式事务测试
     *
     * @param request 复杂更新请求
     * @return 更新的记录数量
     */
    int complexUpdateData(DataUpdateRequest request);

    /**
     * 删除数据
     *
     * @param dataId 数据ID
     * @return 是否删除成功
     */
    boolean deleteData(String dataId);
}
