package com.tipray.at.dlp.controller;

import com.tipray.at.dlp.dto.DataUpdateRequest;
import com.tipray.at.dlp.entity.DlpDataRecord;
import com.tipray.at.dlp.service.DlpDataService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 数据更新测试控制器
 * 专门用于测试各种更新场景的分布式事务
 * 
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/api/dlp/update-test")
public class UpdateTestController {

    @Autowired
    private DlpDataService dlpDataService;

    /**
     * 测试1：简单字段更新（成功场景）
     */
    @PostMapping("/test-simple-update-success")
    public Map<String, Object> testSimpleUpdateSuccess(@RequestParam String dataId) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            log.info("=== 测试1：简单字段更新（成功场景） ===");
            
            DataUpdateRequest request = new DataUpdateRequest();
            request.setDataId(dataId);
            request.setNewDataContent("更新后的数据内容_" + System.currentTimeMillis());
            request.setNewStatus("UPDATED");
            request.setNewRemark("简单更新测试");
            request.setCallCloudServices(true);
            request.setSimulateError(false);
            
            DlpDataRecord record = dlpDataService.updateData(request);
            
            result.put("success", true);
            result.put("message", "简单更新测试成功");
            result.put("data", record);
            result.put("testCase", "简单字段更新（成功场景）");
            
        } catch (Exception e) {
            log.error("简单更新测试失败：{}", e.getMessage(), e);
            result.put("success", false);
            result.put("message", "简单更新测试失败：" + e.getMessage());
            result.put("testCase", "简单字段更新（成功场景）");
        }
        
        return result;
    }

    /**
     * 测试2：简单字段更新（云服务异常回滚场景）
     */
    @PostMapping("/test-simple-update-rollback")
    public Map<String, Object> testSimpleUpdateRollback(@RequestParam String dataId) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            log.info("=== 测试2：简单字段更新（云服务异常回滚场景） ===");
            
            DataUpdateRequest request = new DataUpdateRequest();
            request.setDataId(dataId);
            request.setNewDataContent("更新后的数据内容_ROLLBACK_" + System.currentTimeMillis());
            request.setNewStatus("UPDATED_ROLLBACK");
            request.setNewRemark("简单更新回滚测试");
            request.setCallCloudServices(true);
            request.setSimulateError(true);
            request.setErrorStep("storage"); // 在存储服务模拟异常
            
            DlpDataRecord record = dlpDataService.updateData(request);
            
            result.put("success", true);
            result.put("message", "简单更新回滚测试成功");
            result.put("data", record);
            result.put("testCase", "简单字段更新（云服务异常回滚场景）");
            
        } catch (Exception e) {
            log.error("简单更新回滚测试失败：{}", e.getMessage(), e);
            result.put("success", false);
            result.put("message", "简单更新回滚测试失败：" + e.getMessage());
            result.put("testCase", "简单字段更新（云服务异常回滚场景）");
            result.put("expectedBehavior", "本地更新应该被回滚");
        }
        
        return result;
    }

    /**
     * 测试3：批量更新（成功场景）
     */
    @PostMapping("/test-batch-update-success")
    public Map<String, Object> testBatchUpdateSuccess(@RequestParam String batchDataIds) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            log.info("=== 测试3：批量更新（成功场景） ===");
            
            DataUpdateRequest request = new DataUpdateRequest();
            request.setBatchDataIds(batchDataIds);
            request.setNewDataContent("批量更新内容_" + System.currentTimeMillis());
            request.setNewStatus("BATCH_UPDATED");
            request.setNewRemark("批量更新测试");
            request.setCallCloudServices(true);
            request.setSimulateError(false);
            
            List<DlpDataRecord> records = dlpDataService.batchUpdateData(request);
            
            result.put("success", true);
            result.put("message", "批量更新测试成功");
            result.put("data", records);
            result.put("updateCount", records.size());
            result.put("testCase", "批量更新（成功场景）");
            
        } catch (Exception e) {
            log.error("批量更新测试失败：{}", e.getMessage(), e);
            result.put("success", false);
            result.put("message", "批量更新测试失败：" + e.getMessage());
            result.put("testCase", "批量更新（成功场景）");
        }
        
        return result;
    }

    /**
     * 测试4：批量更新（本地异常回滚场景）
     */
    @PostMapping("/test-batch-update-rollback")
    public Map<String, Object> testBatchUpdateRollback(@RequestParam String batchDataIds) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            log.info("=== 测试4：批量更新（本地异常回滚场景） ===");
            
            DataUpdateRequest request = new DataUpdateRequest();
            request.setBatchDataIds(batchDataIds);
            request.setNewDataContent("批量更新回滚内容_" + System.currentTimeMillis());
            request.setNewStatus("BATCH_ROLLBACK");
            request.setNewRemark("批量更新回滚测试");
            request.setCallCloudServices(true);
            request.setSimulateError(true);
            request.setErrorStep("local"); // 在本地模拟异常
            
            List<DlpDataRecord> records = dlpDataService.batchUpdateData(request);
            
            result.put("success", true);
            result.put("message", "批量更新回滚测试成功");
            result.put("data", records);
            result.put("testCase", "批量更新（本地异常回滚场景）");
            
        } catch (Exception e) {
            log.error("批量更新回滚测试失败：{}", e.getMessage(), e);
            result.put("success", false);
            result.put("message", "批量更新回滚测试失败：" + e.getMessage());
            result.put("testCase", "批量更新（本地异常回滚场景）");
            result.put("expectedBehavior", "所有批量更新应该被回滚");
        }
        
        return result;
    }

    /**
     * 测试5：复杂条件更新（成功场景）
     */
    @PostMapping("/test-complex-update-success")
    public Map<String, Object> testComplexUpdateSuccess(@RequestParam String oldStatus) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            log.info("=== 测试5：复杂条件更新（成功场景） ===");
            
            DataUpdateRequest request = new DataUpdateRequest();
            request.setOldStatusCondition(oldStatus);
            request.setNewStatus("COMPLEX_UPDATED");
            request.setNewDataContent("复杂条件更新内容_" + System.currentTimeMillis());
            request.setNewRemark("复杂条件更新测试");
            request.setCallCloudServices(true);
            request.setSimulateError(false);
            
            int updateCount = dlpDataService.complexUpdateData(request);
            
            result.put("success", true);
            result.put("message", "复杂条件更新测试成功");
            result.put("updateCount", updateCount);
            result.put("testCase", "复杂条件更新（成功场景）");
            
        } catch (Exception e) {
            log.error("复杂条件更新测试失败：{}", e.getMessage(), e);
            result.put("success", false);
            result.put("message", "复杂条件更新测试失败：" + e.getMessage());
            result.put("testCase", "复杂条件更新（成功场景）");
        }
        
        return result;
    }

    /**
     * 测试6：复杂条件更新（云服务异常回滚场景）
     */
    @PostMapping("/test-complex-update-rollback")
    public Map<String, Object> testComplexUpdateRollback(@RequestParam String oldStatus) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            log.info("=== 测试6：复杂条件更新（云服务异常回滚场景） ===");
            
            DataUpdateRequest request = new DataUpdateRequest();
            request.setOldStatusCondition(oldStatus);
            request.setNewStatus("COMPLEX_ROLLBACK");
            request.setNewDataContent("复杂条件更新回滚内容_" + System.currentTimeMillis());
            request.setNewRemark("复杂条件更新回滚测试");
            request.setCallCloudServices(true);
            request.setSimulateError(true);
            request.setErrorStep("storage"); // 在存储服务模拟异常
            
            int updateCount = dlpDataService.complexUpdateData(request);
            
            result.put("success", true);
            result.put("message", "复杂条件更新回滚测试成功");
            result.put("updateCount", updateCount);
            result.put("testCase", "复杂条件更新（云服务异常回滚场景）");
            
        } catch (Exception e) {
            log.error("复杂条件更新回滚测试失败：{}", e.getMessage(), e);
            result.put("success", false);
            result.put("message", "复杂条件更新回滚测试失败：" + e.getMessage());
            result.put("testCase", "复杂条件更新（云服务异常回滚场景）");
            result.put("expectedBehavior", "复杂条件更新应该被回滚");
        }
        
        return result;
    }

    /**
     * 初始化测试数据
     */
    @PostMapping("/init-test-data")
    public Map<String, Object> initTestData() {
        Map<String, Object> result = new HashMap<>();

        try {
            log.info("=== 初始化更新测试数据 ===");

            // 创建测试数据
            String[] testDataIds = {
                "UPDATE_TEST_001", "UPDATE_TEST_002", "UPDATE_TEST_003",
                "BATCH_UPDATE_001", "BATCH_UPDATE_002", "BATCH_UPDATE_003",
                "COMPLEX_UPDATE_001", "COMPLEX_UPDATE_002"
            };

            for (String dataId : testDataIds) {
                try {
                    // 检查是否已存在
                    DlpDataRecord existing = dlpDataService.getByDataId(dataId);
                    if (existing == null) {
                        // 创建新的测试数据
                        com.tipray.at.dlp.dto.DataProcessRequest createRequest = new com.tipray.at.dlp.dto.DataProcessRequest();
                        createRequest.setDataId(dataId);
                        createRequest.setDataContent("测试数据内容_" + dataId);
                        createRequest.setDataType("JSON");
                        createRequest.setRemark("更新测试初始数据");
                        createRequest.setSimulateError(false);

                        dlpDataService.processData(createRequest);
                        log.info("创建测试数据：{}", dataId);
                    } else {
                        log.info("测试数据已存在：{}", dataId);
                    }
                } catch (Exception e) {
                    log.warn("创建测试数据失败：{}，错误：{}", dataId, e.getMessage());
                }
            }

            result.put("success", true);
            result.put("message", "测试数据初始化完成");
            result.put("testDataIds", testDataIds);
            result.put("note", "可以使用这些数据ID进行更新测试");

        } catch (Exception e) {
            log.error("初始化测试数据失败：{}", e.getMessage(), e);
            result.put("success", false);
            result.put("message", "初始化测试数据失败：" + e.getMessage());
        }

        return result;
    }

    /**
     * 获取测试说明
     */
    @GetMapping("/test-info")
    public Map<String, Object> getTestInfo() {
        Map<String, Object> result = new HashMap<>();
        result.put("title", "数据更新分布式事务测试");
        result.put("description", "测试各种数据更新场景下的AT模式分布式事务行为");

        Map<String, String> testCases = new HashMap<>();
        testCases.put("test-simple-update-success", "简单字段更新（成功场景）");
        testCases.put("test-simple-update-rollback", "简单字段更新（云服务异常回滚场景）");
        testCases.put("test-batch-update-success", "批量更新（成功场景）");
        testCases.put("test-batch-update-rollback", "批量更新（本地异常回滚场景）");
        testCases.put("test-complex-update-success", "复杂条件更新（成功场景）");
        testCases.put("test-complex-update-rollback", "复杂条件更新（云服务异常回滚场景）");

        result.put("testCases", testCases);

        Map<String, String> usage = new HashMap<>();
        usage.put("1", "首先调用 /init-test-data 初始化测试数据");
        usage.put("2", "使用返回的测试数据ID调用各个测试接口");
        usage.put("3", "观察日志和数据库变化，验证分布式事务行为");
        usage.put("4", "检查 undo_log 表确认回滚日志生成");

        result.put("usage", usage);
        result.put("note", "测试前请确保云服务正常运行");

        return result;
    }
}
