package com.tipray.at.dlp.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.tipray.at.dlp.dto.CloudServiceRequest;
import com.tipray.at.dlp.dto.CloudServiceResponse;
import com.tipray.at.dlp.dto.DataProcessRequest;
import com.tipray.at.dlp.dto.DataUpdateRequest;
import com.tipray.at.dlp.entity.DlpDataRecord;
import com.tipray.at.dlp.mapper.DlpDataRecordMapper;
import com.tipray.at.dlp.service.CloudServiceClient;
import com.tipray.at.dlp.service.DlpDataService;
import com.tipray.transaction.core.annotation.DistributedTransaction;
import com.tipray.transaction.core.enums.TransactionMode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * DLP数据服务实现
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class DlpDataServiceImpl implements DlpDataService {

    @Autowired
    private DlpDataRecordMapper dlpDataRecordMapper;

    @Autowired
    private CloudServiceClient cloudServiceClient;

    @Override
    @DistributedTransaction(mode = TransactionMode.AT, description = "DLP数据处理分布式事务", asyncCommitOrRollback = true)
    public DlpDataRecord processData(DataProcessRequest request) {
        log.info("开始处理DLP数据，数据ID：{}", request.getDataId());

        // 1. 保存本地数据记录
        DlpDataRecord record = new DlpDataRecord();
        record.setDataId(request.getDataId());
        record.setDataContent(request.getDataContent());
        record.setDataType(request.getDataType());
        record.setStatus("PROCESSING");
        record.setCreateTime(LocalDateTime.now());
        record.setUpdateTime(LocalDateTime.now());
        record.setRemark(request.getRemark());

        dlpDataRecordMapper.insert(record);
        log.info("本地数据记录保存成功，记录ID：{}", record.getId());

        // 2. 构建云服务请求
        CloudServiceRequest cloudRequest = new CloudServiceRequest();
        cloudRequest.setDataId(request.getDataId());
        cloudRequest.setDataContent(request.getDataContent());
        cloudRequest.setDataType(request.getDataType());
        cloudRequest.setRemark(request.getRemark());
        cloudRequest.setHttpTimeoutMillis(request.getHttpTimeoutMillis());

//        try {
//            Thread.sleep(2*1000);
//        } catch (InterruptedException e) {
//            throw new RuntimeException(e);
//        }

        // 设置异常模拟
        if (request.getSimulateError() != null && request.getSimulateError()) {
            cloudRequest.setSimulateError(true);
        }

        try {
            // 记录事务开始时间，用于超时检查
            long transactionStartTime = System.currentTimeMillis();
            Long timeoutMillis = request.getTimeoutMillis();

            // 模拟本地慢处理
            if (Boolean.TRUE.equals(request.getSimulateLocalSlowProcessing()) && request.getLocalProcessingDelayMillis() != null) {
                log.info("模拟本地慢处理，延迟{}毫秒", request.getLocalProcessingDelayMillis());
                Thread.sleep(request.getLocalProcessingDelayMillis());

                // 检查是否超时
                if (timeoutMillis != null && (System.currentTimeMillis() - transactionStartTime) > timeoutMillis) {
                    throw new RuntimeException("事务执行超时，超时时间: " + timeoutMillis + "ms");
                }
            }

            // 检查全局事务超时
            if (timeoutMillis != null) {
                log.info("设置全局事务超时时间：{}毫秒", timeoutMillis);

                // 如果已经超时，直接抛出异常
                if ((System.currentTimeMillis() - transactionStartTime) > timeoutMillis) {
                    throw new RuntimeException("事务执行超时，超时时间: " + timeoutMillis + "ms");
                }
            }

            // 只有当需要调用云服务时才调用
            if (Boolean.TRUE.equals(request.getCallCloudServices())) {
                // 检查超时
                if (timeoutMillis != null && (System.currentTimeMillis() - transactionStartTime) > timeoutMillis) {
                    throw new RuntimeException("事务执行超时，超时时间: " + timeoutMillis + "ms");
                }

                // 设置HTTP超时
                if (request.getHttpTimeoutMillis() != null) {
                    // 这里应该设置HTTP客户端的超时时间
                    log.info("设置HTTP调用超时时间：{}毫秒", request.getHttpTimeoutMillis());
                }

                // 模拟云服务延迟
                if (request.getSimulateCloudServiceDelay() != null) {
                    log.info("模拟云服务延迟{}毫秒", request.getSimulateCloudServiceDelay());
                    Thread.sleep(request.getSimulateCloudServiceDelay());

                    // 延迟后再次检查超时
                    if (timeoutMillis != null && (System.currentTimeMillis() - transactionStartTime) > timeoutMillis) {
                        throw new RuntimeException("事务执行超时，超时时间: " + timeoutMillis + "ms");
                    }
                }

                // 根据数据类型决定调用哪种操作
                String operationType = determineOperationType(request.getDataType());
                log.info("确定操作类型: {}", operationType);

                // 调用云存储服务（复杂SQL操作）
                cloudRequest.setSimulateError("cloud_storage".equals(request.getErrorStep()) && Boolean.TRUE.equals(request.getSimulateError()));
                cloudRequest.setSimulateSlowProcessing(Boolean.TRUE.equals(request.getSimulateSlowCloudProcessing()));
                if (request.getSimulateCloudServiceDelay() != null) {
                    cloudRequest.setSlowProcessingDelayMillis(request.getSimulateCloudServiceDelay());
                }
                CloudServiceResponse storageResponse = cloudServiceClient.callStorageComplexOperations(cloudRequest, operationType);
                log.info("云存储服务复杂操作调用成功，操作类型: {}", operationType);

                // 调用云分析服务（复杂SQL操作）
                cloudRequest.setSimulateError("cloud_analysis".equals(request.getErrorStep()) && Boolean.TRUE.equals(request.getSimulateError()));
                cloudRequest.setSimulateSlowProcessing(Boolean.TRUE.equals(request.getSimulateSlowCloudProcessing()));
                CloudServiceResponse analysisResponse = cloudServiceClient.callAnalysisComplexSql(cloudRequest);
                log.info("云分析服务复杂SQL操作调用成功");

                // 调用云通知服务（复杂SQL操作）
                cloudRequest.setSimulateError("cloud_notify".equals(request.getErrorStep()) && Boolean.TRUE.equals(request.getSimulateError()));
                cloudRequest.setSimulateSlowProcessing(Boolean.TRUE.equals(request.getSimulateSlowCloudProcessing()));
                CloudServiceResponse notifyResponse = cloudServiceClient.callNotifyComplexSql(cloudRequest);
                log.info("云通知服务复杂SQL操作调用成功");
            } else {
                log.info("跳过云服务调用，仅处理本地数据");
            }

            // 模拟慢处理（在云服务调用之后）
            if (Boolean.TRUE.equals(request.getSimulateSlowProcessing())) {
                log.info("模拟慢处理，延迟3秒");
                Thread.sleep(3000);

                // 慢处理后检查超时
                if (timeoutMillis != null && (System.currentTimeMillis() - transactionStartTime) > timeoutMillis) {
                    throw new RuntimeException("事务执行超时，超时时间: " + timeoutMillis + "ms");
                }
            }

            // 最终超时检查
            if (timeoutMillis != null && (System.currentTimeMillis() - transactionStartTime) > timeoutMillis) {
                throw new RuntimeException("事务执行超时，超时时间: " + timeoutMillis + "ms");
            }

            // 6. 更新本地记录状态为成功
            record.setStatus("SUCCESS");
            record.setUpdateTime(LocalDateTime.now());
            dlpDataRecordMapper.updateById(record);

            log.info("DLP数据处理完成，数据ID：{}", request.getDataId());

            // 模拟本地异常
            if ("local".equals(request.getErrorStep()) && Boolean.TRUE.equals(request.getSimulateError())) {
                throw new RuntimeException("模拟本地处理异常");
            }

            return record;

        } catch (Exception e) {
            log.error("DLP数据处理失败，数据ID：{}，错误：{}", request.getDataId(), e.getMessage());

            // 更新本地记录状态为失败
            record.setStatus("FAILED");
            record.setUpdateTime(LocalDateTime.now());

            // 限制错误信息长度，避免数据库字段溢出
            String errorMessage = e.getMessage();
            if (errorMessage != null && errorMessage.length() > 500) {
                errorMessage = errorMessage.substring(0, 500) + "...";
            }
            record.setRemark("处理失败：" + errorMessage);

            dlpDataRecordMapper.updateById(record);

            try {
                throw e;
            } catch (InterruptedException ex) {
                throw new RuntimeException(ex);
            }
        }
    }

    @Override
    public DlpDataRecord getByDataId(String dataId) {
        LambdaQueryWrapper<DlpDataRecord> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(DlpDataRecord::getDataId, dataId);
        return dlpDataRecordMapper.selectOne(wrapper);
    }

    @Override
    @DistributedTransaction(mode = TransactionMode.AT, description = "DLP数据更新分布式事务")
    @Transactional(rollbackFor = Exception.class)
    public DlpDataRecord updateData(DataUpdateRequest request) {
        log.info("开始更新DLP数据，数据ID：{}", request.getDataId());

        // 1. 查询现有记录
        DlpDataRecord existingRecord = getByDataId(request.getDataId());
        if (existingRecord == null) {
            throw new RuntimeException("数据记录不存在，数据ID：" + request.getDataId());
        }

        log.info("找到现有记录，当前状态：{}，内容：{}", existingRecord.getStatus(), existingRecord.getDataContent());

        // 2. 更新本地记录
        DlpDataRecord updateRecord = new DlpDataRecord();
        updateRecord.setId(existingRecord.getId());

        // 只更新非空字段
        if (StringUtils.hasText(request.getNewDataContent())) {
            updateRecord.setDataContent(request.getNewDataContent());
        }
        if (StringUtils.hasText(request.getNewDataType())) {
            updateRecord.setDataType(request.getNewDataType());
        }
        if (StringUtils.hasText(request.getNewStatus())) {
            updateRecord.setStatus(request.getNewStatus());
        }
        if (StringUtils.hasText(request.getNewRemark())) {
            updateRecord.setRemark(request.getNewRemark());
        }
        updateRecord.setUpdateTime(LocalDateTime.now());

        int updateCount = dlpDataRecordMapper.updateById(updateRecord);
        if (updateCount == 0) {
            throw new RuntimeException("更新本地记录失败，数据ID：" + request.getDataId());
        }
        log.info("本地记录更新成功，数据ID：{}", request.getDataId());

        // 3. 如果需要调用云服务，则进行分布式更新
        if (Boolean.TRUE.equals(request.getCallCloudServices())) {
            CloudServiceRequest cloudRequest = new CloudServiceRequest();
            cloudRequest.setDataId(request.getDataId());
            cloudRequest.setDataContent(request.getNewDataContent() != null ? request.getNewDataContent() : existingRecord.getDataContent());
            cloudRequest.setDataType(request.getNewDataType() != null ? request.getNewDataType() : existingRecord.getDataType());
            cloudRequest.setRemark("数据更新操作");

            try {
                // 调用云存储服务更新
                if ("storage".equals(request.getErrorStep()) && Boolean.TRUE.equals(request.getSimulateError())) {
                    cloudRequest.setSimulateError(true);
                } else {
                    cloudRequest.setSimulateError(false);
                }
                CloudServiceResponse storageResponse = cloudServiceClient.callStorageService(cloudRequest);
                // AtHttpClient现在会自动检查响应状态和业务结果，无需手动判断
                log.info("云存储服务更新成功");

                // 调用云分析服务更新
                if ("analysis".equals(request.getErrorStep()) && Boolean.TRUE.equals(request.getSimulateError())) {
                    cloudRequest.setSimulateError(true);
                } else {
                    cloudRequest.setSimulateError(false);
                }
                CloudServiceResponse analysisResponse = cloudServiceClient.callAnalysisService(cloudRequest);
                // AtHttpClient现在会自动检查响应状态和业务结果，无需手动判断
                log.info("云分析服务更新成功");

            } catch (Exception e) {
                log.error("云服务更新失败，数据ID：{}，错误：{}", request.getDataId(), e.getMessage());
                throw e;
            }
        }

        // 4. 模拟本地异常
        if ("local".equals(request.getErrorStep()) && Boolean.TRUE.equals(request.getSimulateError())) {
            throw new RuntimeException("模拟本地更新异常");
        }

        // 5. 返回更新后的记录
        DlpDataRecord updatedRecord = getByDataId(request.getDataId());
        log.info("DLP数据更新完成，数据ID：{}", request.getDataId());

        return updatedRecord;
    }

    @Override
    @DistributedTransaction(mode = TransactionMode.AT, description = "DLP数据批量更新分布式事务")
    public List<DlpDataRecord> batchUpdateData(DataUpdateRequest request) {
        log.info("开始批量更新DLP数据，批量ID：{}", request.getBatchDataIds());

        List<String> dataIds;

        // 支持两种方式传入批量ID：List或逗号分隔的字符串
        if (request.getBatchDataIdList() != null && !request.getBatchDataIdList().isEmpty()) {
            dataIds = request.getBatchDataIdList();
        } else if (StringUtils.hasText(request.getBatchDataIds())) {
            dataIds = Arrays.asList(request.getBatchDataIds().split(","));
        } else {
            throw new RuntimeException("批量更新的数据ID列表不能为空");
        }
        List<DlpDataRecord> updatedRecords = new ArrayList<>();

        // 1. 批量更新本地记录
        for (String dataId : dataIds) {
            dataId = dataId.trim();
            if (!StringUtils.hasText(dataId)) {
                continue;
            }

            log.info("批量更新处理数据ID：{}", dataId);

            // 查询现有记录
            DlpDataRecord existingRecord = getByDataId(dataId);
            if (existingRecord == null) {
                log.warn("数据记录不存在，跳过更新，数据ID：{}", dataId);
                continue;
            }

            // 使用LambdaUpdateWrapper进行条件更新
            LambdaUpdateWrapper<DlpDataRecord> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(DlpDataRecord::getDataId, dataId);

            // 只更新非空字段
            if (StringUtils.hasText(request.getNewDataContent())) {
                updateWrapper.set(DlpDataRecord::getDataContent, request.getNewDataContent() + "_BATCH");
            }
            if (StringUtils.hasText(request.getNewDataType())) {
                updateWrapper.set(DlpDataRecord::getDataType, request.getNewDataType());
            }
            if (StringUtils.hasText(request.getNewStatus())) {
                updateWrapper.set(DlpDataRecord::getStatus, request.getNewStatus());
            }
            if (StringUtils.hasText(request.getNewRemark())) {
                updateWrapper.set(DlpDataRecord::getRemark, request.getNewRemark() + "_BATCH_" + dataId);
            }
            updateWrapper.set(DlpDataRecord::getUpdateTime, LocalDateTime.now());

            int updateCount = dlpDataRecordMapper.update(null, updateWrapper);
            if (updateCount > 0) {
                DlpDataRecord updatedRecord = getByDataId(dataId);
                updatedRecords.add(updatedRecord);
                log.info("批量更新成功，数据ID：{}", dataId);
            } else {
                log.warn("批量更新失败，数据ID：{}", dataId);
            }
        }

        log.info("本地批量更新完成，成功更新{}条记录", updatedRecords.size());

        // 2. 模拟本地异常
        if ("local".equals(request.getErrorStep()) && Boolean.TRUE.equals(request.getSimulateError())) {
            throw new RuntimeException("模拟批量更新本地异常");
        }

        // 3. 如果需要调用云服务，进行分布式批量更新
        if (Boolean.TRUE.equals(request.getCallCloudServices()) && !updatedRecords.isEmpty()) {
            try {
                // 为了简化，只对第一条记录调用云服务
                DlpDataRecord firstRecord = updatedRecords.get(0);
                CloudServiceRequest cloudRequest = new CloudServiceRequest();
                cloudRequest.setDataId(firstRecord.getDataId());
                cloudRequest.setDataContent(firstRecord.getDataContent());
                cloudRequest.setDataType(firstRecord.getDataType());
                cloudRequest.setRemark("批量更新操作");

                // 调用云存储服务
                if ("storage".equals(request.getErrorStep()) && Boolean.TRUE.equals(request.getSimulateError())) {
                    cloudRequest.setSimulateError(true);
                } else {
                    cloudRequest.setSimulateError(false);
                }
                CloudServiceResponse storageResponse = cloudServiceClient.callStorageService(cloudRequest);
                // AtHttpClient现在会自动检查响应状态和业务结果，无需手动判断
                log.info("批量更新云存储服务成功");

            } catch (Exception e) {
                log.error("批量更新云服务失败，错误：{}", e.getMessage());
                throw e;
            }
        }

        log.info("DLP数据批量更新完成，更新{}条记录", updatedRecords.size());
        return updatedRecords;
    }

    @Override
    @DistributedTransaction(mode = TransactionMode.AT, description = "DLP数据复杂条件更新分布式事务")
    @Transactional(rollbackFor = Exception.class)
    public int complexUpdateData(DataUpdateRequest request) {
        log.info("开始复杂条件更新DLP数据，条件：旧状态={}", request.getOldStatusCondition());

        if (!StringUtils.hasText(request.getOldStatusCondition())) {
            throw new RuntimeException("复杂更新的旧状态条件不能为空");
        }

        // 1. 使用复杂条件更新本地记录
        LambdaUpdateWrapper<DlpDataRecord> updateWrapper = new LambdaUpdateWrapper<>();

        // 设置WHERE条件：状态等于指定值
        updateWrapper.eq(DlpDataRecord::getStatus, request.getOldStatusCondition());

        // 如果指定了数据ID，也作为条件
        if (StringUtils.hasText(request.getDataId())) {
            updateWrapper.eq(DlpDataRecord::getDataId, request.getDataId());
        }

        // 添加时间条件：只更新1小时前创建的记录
        updateWrapper.lt(DlpDataRecord::getCreateTime, LocalDateTime.now().minusHours(1));

        // 设置更新字段
        if (StringUtils.hasText(request.getNewStatus())) {
            updateWrapper.set(DlpDataRecord::getStatus, request.getNewStatus());
        }
        if (StringUtils.hasText(request.getNewDataContent())) {
            updateWrapper.set(DlpDataRecord::getDataContent, request.getNewDataContent() + "_COMPLEX");
        }
        if (StringUtils.hasText(request.getNewRemark())) {
            updateWrapper.set(DlpDataRecord::getRemark, request.getNewRemark() + "_COMPLEX_UPDATE");
        }
        updateWrapper.set(DlpDataRecord::getUpdateTime, LocalDateTime.now());

        // 执行复杂条件更新
        int updateCount = dlpDataRecordMapper.update(null, updateWrapper);
        log.info("复杂条件更新完成，影响记录数：{}", updateCount);

        // 2. 模拟本地异常
        if ("local".equals(request.getErrorStep()) && Boolean.TRUE.equals(request.getSimulateError())) {
            throw new RuntimeException("模拟复杂更新本地异常");
        }

        // 3. 如果需要调用云服务且有记录被更新
        if (Boolean.TRUE.equals(request.getCallCloudServices()) && updateCount > 0) {
            try {
                // 查询一条被更新的记录用于云服务调用
                LambdaQueryWrapper<DlpDataRecord> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.eq(DlpDataRecord::getStatus, request.getNewStatus());
                queryWrapper.like(DlpDataRecord::getRemark, "COMPLEX_UPDATE");
                queryWrapper.orderByDesc(DlpDataRecord::getUpdateTime);
                queryWrapper.last("LIMIT 1");

                DlpDataRecord sampleRecord = dlpDataRecordMapper.selectOne(queryWrapper);
                if (sampleRecord != null) {
                    CloudServiceRequest cloudRequest = new CloudServiceRequest();
                    cloudRequest.setDataId(sampleRecord.getDataId());
                    cloudRequest.setDataContent(sampleRecord.getDataContent());
                    cloudRequest.setDataType(sampleRecord.getDataType());
                    cloudRequest.setRemark("复杂条件更新操作");

                    // 调用云存储服务
                    if ("storage".equals(request.getErrorStep()) && Boolean.TRUE.equals(request.getSimulateError())) {
                        cloudRequest.setSimulateError(true);
                    } else {
                        cloudRequest.setSimulateError(false);
                    }
                    CloudServiceResponse storageResponse = cloudServiceClient.callStorageService(cloudRequest);
                    // AtHttpClient现在会自动检查响应状态和业务结果，无需手动判断
                    log.info("复杂更新云存储服务成功");
                }

            } catch (Exception e) {
                log.error("复杂更新云服务失败，错误：{}", e.getMessage());
                throw e;
            }
        }

        log.info("DLP数据复杂条件更新完成，影响记录数：{}", updateCount);
        return updateCount;
    }

    @Override
    @DistributedTransaction(mode = TransactionMode.AT, description = "DLP数据删除分布式事务")
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteData(String dataId) {
        log.info("开始删除DLP数据，数据ID：{}", dataId);

        try {
            // 1. 检查数据是否存在
            DlpDataRecord existingRecord = dlpDataRecordMapper.selectOne(
                new LambdaQueryWrapper<DlpDataRecord>()
                    .eq(DlpDataRecord::getDataId, dataId)
            );

            if (existingRecord == null) {
                log.warn("要删除的数据不存在，数据ID：{}", dataId);
                return false;
            }

            // 2. 先调用云服务删除（在本地删除之前，确保云服务也能删除）
            CloudServiceRequest cloudRequest = new CloudServiceRequest();
            cloudRequest.setDataId(dataId);
            cloudRequest.setDataContent(existingRecord.getDataContent());
            cloudRequest.setDataType(existingRecord.getDataType());
            cloudRequest.setRemark("数据删除操作");
            cloudRequest.setSimulateError(false);

            try {
                // 调用云存储服务删除
                CloudServiceResponse storageResponse = cloudServiceClient.callStorageService(cloudRequest);
                log.info("云存储服务删除成功");

                // 调用云分析服务删除
                CloudServiceResponse analysisResponse = cloudServiceClient.callAnalysisService(cloudRequest);
                log.info("云分析服务删除成功");

                // 调用云通知服务删除
                CloudServiceResponse notifyResponse = cloudServiceClient.callNotifyService(cloudRequest);
                log.info("云通知服务删除成功");

            } catch (Exception e) {
                log.error("云服务删除失败，数据ID：{}，错误：{}", dataId, e.getMessage());
                throw new RuntimeException("云服务删除失败: " + e.getMessage(), e);
            }

            // 3. 删除本地数据
            int deleteCount = dlpDataRecordMapper.delete(
                new LambdaQueryWrapper<DlpDataRecord>()
                    .eq(DlpDataRecord::getDataId, dataId)
            );

            if (deleteCount > 0) {
                log.info("DLP数据删除成功，数据ID：{}", dataId);
                return true;
            } else {
                log.warn("DLP数据删除失败，数据ID：{}", dataId);
                throw new RuntimeException("本地数据删除失败，数据ID：" + dataId);
            }

        } catch (Exception e) {
            log.error("删除DLP数据异常，数据ID：{}，错误：{}", dataId, e.getMessage(), e);
            throw new RuntimeException("删除数据失败: " + e.getMessage(), e);
        }
    }

    /**
     * 根据数据类型确定操作类型
     */
    private String determineOperationType(String dataType) {
        if (dataType == null) {
            return "MULTI_INSERT";
        }

        switch (dataType.toUpperCase()) {
            case "JSON":
                return "MULTI_INSERT";
            case "MIXED":
                return "MIXED_OPS";
            case "BATCH":
                return "BATCH_OPS";
            case "COMPLEX":
                return "RELATED_TABLES";
            case "STATISTICS":
                return "STATISTICS";
            case "TEXT":
                return "UPDATE_OPERATIONS";
            case "UPDATE":
                return "UPDATE_OPERATIONS";
            case "COMPLEX_UPDATE":
                return "COMPLEX_UPDATE";
            default:
                return "MULTI_INSERT";
        }
    }
}
