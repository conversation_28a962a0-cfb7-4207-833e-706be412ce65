package com.tipray.at.dlp.service;

import com.tipray.at.dlp.entity.DlpDataRecord;
import com.tipray.at.dlp.mapper.DlpDataRecordMapper;
import com.tipray.transaction.core.annotation.DistributedTransaction;
import com.tipray.transaction.core.enums.TransactionMode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 复杂SQL测试服务
 * 测试各种复杂SQL场景下的AT模式功能
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class ComplexSqlTestService {

    @Autowired
    private DlpDataRecordMapper dlpDataRecordMapper;

    /**
     * 测试1：复杂WHERE条件的UPDATE
     */
    @DistributedTransaction(mode = TransactionMode.AT)
    public void testComplexWhereUpdate(String dataId, String newStatus, String oldStatus, LocalDateTime beforeTime) {
        log.info("测试复杂WHERE条件UPDATE: dataId={}, newStatus={}, oldStatus={}, beforeTime={}",
                dataId, newStatus, oldStatus, beforeTime);

        // 复杂WHERE条件：多个AND条件 + 时间比较
        String sql = "UPDATE dlp_data_record SET status = ?, update_time = ? " +
                    "WHERE data_id = ? AND status = ? AND create_time < ? AND data_type IN ('JSON', 'XML')";

        dlpDataRecordMapper.updateByComplexCondition(newStatus, LocalDateTime.now(), dataId, oldStatus, beforeTime);

        log.info("复杂WHERE条件UPDATE执行完成");
    }

    /**
     * 测试2：批量UPDATE操作
     */
    @DistributedTransaction(mode = TransactionMode.AT)
    public void testBatchUpdate(List<String> dataIds, String newStatus) {
        log.info("测试批量UPDATE: dataIds={}, newStatus={}", dataIds, newStatus);

        for (String dataId : dataIds) {
            // 每个UPDATE都有不同的WHERE条件
            dlpDataRecordMapper.updateStatusByDataId(dataId, newStatus, LocalDateTime.now());
        }

        log.info("批量UPDATE执行完成，更新记录数: {}", dataIds.size());
    }

    /**
     * 测试3：复杂的INSERT操作（包含子查询）
     */
    @DistributedTransaction(mode = TransactionMode.AT)
    public void testComplexInsert(String baseDataId, String newDataId) {
        log.info("测试复杂INSERT: baseDataId={}, newDataId={}", baseDataId, newDataId);

        // 基于现有记录创建新记录
        DlpDataRecord baseRecord = dlpDataRecordMapper.selectByDataId(baseDataId);
        if (baseRecord != null) {
            DlpDataRecord newRecord = new DlpDataRecord();
            newRecord.setDataId(newDataId);
            newRecord.setDataContent(baseRecord.getDataContent() + "_COPY");
            newRecord.setDataType(baseRecord.getDataType());
            newRecord.setStatus("COPIED");
            newRecord.setCreateTime(LocalDateTime.now());
            newRecord.setUpdateTime(LocalDateTime.now());
            newRecord.setRemark("基于 " + baseDataId + " 创建的副本");

            dlpDataRecordMapper.insert(newRecord);
        }

        log.info("复杂INSERT执行完成");
    }

    /**
     * 测试4：条件DELETE操作
     */
    @DistributedTransaction(mode = TransactionMode.AT)
    public void testConditionalDelete(String status, LocalDateTime beforeTime) {
        log.info("测试条件DELETE: status={}, beforeTime={}", status, beforeTime);

        // 删除指定状态且创建时间早于指定时间的记录
        dlpDataRecordMapper.deleteByStatusAndTime(status, beforeTime);

        log.info("条件DELETE执行完成");
    }

    /**
     * 测试5：混合操作（INSERT + UPDATE + DELETE）
     */
    @DistributedTransaction(mode = TransactionMode.AT)
    public void testMixedOperations(String testPrefix) {
        log.info("测试混合操作: testPrefix={}", testPrefix);

        // 1. 插入新记录
        DlpDataRecord newRecord = new DlpDataRecord();
        newRecord.setDataId(testPrefix + "_NEW");
        newRecord.setDataContent("混合操作测试数据");
        newRecord.setDataType("JSON");
        newRecord.setStatus("NEW");
        newRecord.setCreateTime(LocalDateTime.now());
        newRecord.setUpdateTime(LocalDateTime.now());
        newRecord.setRemark("混合操作测试");
        dlpDataRecordMapper.insert(newRecord);

        // 2. 更新现有记录
        dlpDataRecordMapper.updateStatusByDataId(testPrefix + "_NEW", "UPDATED", LocalDateTime.now());

        // 3. 删除临时记录
        dlpDataRecordMapper.deleteByDataId(testPrefix + "_TEMP");

        log.info("混合操作执行完成");
    }

    /**
     * 测试6：事务回滚场景
     */
    @DistributedTransaction(mode = TransactionMode.AT)
    public void testTransactionRollback(String dataId, boolean shouldFail) {
        log.info("测试事务回滚: dataId={}, shouldFail={}", dataId, shouldFail);

        // 1. 插入记录
        DlpDataRecord record = new DlpDataRecord();
        record.setDataId(dataId);
        record.setDataContent("回滚测试数据");
        record.setDataType("JSON");
        record.setStatus("PROCESSING");
        record.setCreateTime(LocalDateTime.now());
        record.setUpdateTime(LocalDateTime.now());
        record.setRemark("回滚测试");
        dlpDataRecordMapper.insert(record);

        // 2. 更新记录
        dlpDataRecordMapper.updateStatusByDataId(dataId, "UPDATED", LocalDateTime.now());

        // 3. 如果需要失败，抛出异常触发回滚
        if (shouldFail) {
            log.info("模拟异常，触发事务回滚");
            throw new RuntimeException("模拟异常，测试事务回滚功能");
        }

        log.info("事务回滚测试执行完成");
    }

    /**
     * 测试7：大数据量操作
     */
    @DistributedTransaction(mode = TransactionMode.AT)
    public void testLargeDataOperation(String batchPrefix, int count) {
        log.info("测试大数据量操作: batchPrefix={}, count={}", batchPrefix, count);

        for (int i = 0; i < count; i++) {
            DlpDataRecord record = new DlpDataRecord();
            record.setDataId(batchPrefix + "_" + String.format("%04d", i));
            record.setDataContent("大数据量测试数据 " + i);
            record.setDataType("JSON");
            record.setStatus("BATCH");
            record.setCreateTime(LocalDateTime.now());
            record.setUpdateTime(LocalDateTime.now());
            record.setRemark("大数据量测试 " + i);

            dlpDataRecordMapper.insert(record);

            // 每10条记录更新一次状态
            if (i % 10 == 0) {
                dlpDataRecordMapper.updateStatusByDataId(record.getDataId(), "BATCH_PROCESSED", LocalDateTime.now());
            }
        }

        log.info("大数据量操作执行完成，处理记录数: {}", count);
    }
}
