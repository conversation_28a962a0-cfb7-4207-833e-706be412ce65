package com.tipray.at.dlp.dto;

import lombok.Data;

/**
 * 数据更新请求DTO
 * 
 * <AUTHOR>
 */
@Data
public class DataUpdateRequest {

    /**
     * 数据ID（必填）
     */
    private String dataId;

    /**
     * 新的数据内容（可选）
     */
    private String newDataContent;

    /**
     * 新的数据类型（可选）
     */
    private String newDataType;

    /**
     * 新的状态（可选）
     */
    private String newStatus;

    /**
     * 新的备注（可选）
     */
    private String newRemark;

    /**
     * 是否模拟异常
     */
    private Boolean simulateError = false;

    /**
     * 异常步骤：storage-存储异常，analysis-分析异常，notify-通知异常，local-本地异常
     */
    private String errorStep;

    /**
     * 更新类型：simple-简单更新，complex-复杂条件更新，batch-批量更新
     */
    private String updateType = "simple";

    /**
     * 批量更新时的数据ID列表（用逗号分隔）
     */
    private String batchDataIds;

    /**
     * 批量更新时的数据ID列表
     */
    private java.util.List<String> batchDataIdList;

    /**
     * 复杂更新的旧状态条件
     */
    private String oldStatusCondition;

    /**
     * 是否同时调用云服务更新
     */
    private Boolean callCloudServices = true;
}
