package com.tipray.at.dlp.service.impl;

import cn.hutool.http.HttpResponse;
import cn.hutool.json.JSONUtil;
import com.tipray.at.dlp.dto.CloudServiceRequest;
import com.tipray.at.dlp.dto.CloudServiceResponse;
import com.tipray.at.dlp.service.CloudServiceClient;
import com.tipray.transaction.at.http.AtHttpClient;
import com.tipray.transaction.core.annotation.DistributedBranchTransaction;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * 云服务客户端实现
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class CloudServiceClientImpl implements CloudServiceClient {

    @Autowired
    private AtHttpClient atHttpClient;

    @Override
    @DistributedBranchTransaction(value = "cloud-storage-service", targetService = "cloud-storage-service",description = "调用云存储服务", timeout = 10000)
    public CloudServiceResponse callStorageService(CloudServiceRequest request) {
        log.info("调用云存储服务，数据ID：{}", request.getDataId());

        // 转换为StorageRequest
        Map<String, Object> storageRequest = new java.util.HashMap<>();
        storageRequest.put("dataId", request.getDataId());
        storageRequest.put("dataContent", request.getDataContent());
        storageRequest.put("dataType", request.getDataType());
        storageRequest.put("globalTxId", request.getGlobalTxId());
        storageRequest.put("branchTxId", request.getBranchTxId());
        storageRequest.put("simulateError", request.getSimulateError());
        storageRequest.put("simulateSlowProcessing", request.getSimulateSlowProcessing());
        storageRequest.put("slowProcessingDelayMillis", request.getSlowProcessingDelayMillis());
        storageRequest.put("remark", request.getRemark());

        // 检查是否有HTTP超时配置
        HttpResponse response;
        if (request.getHttpTimeoutMillis() != null && request.getHttpTimeoutMillis() > 0) {
            // 使用指定的超时时间
            response = atHttpClient.post(
                    "http://localhost:8081/api/storage/store",
                    storageRequest,
                    request.getHttpTimeoutMillis().intValue()
            );
        } else {
            // 使用默认超时配置
            response = atHttpClient.post(
                    "http://localhost:8081/api/storage/store",
                    storageRequest
            );
        }

        String body = response.body();
        log.info("云存储服务调用成功，数据ID：{}", request.getDataId());

        // 解析响应体为Map
        Map<String, Object> responseBody = JSONUtil.toBean(body, Map.class);

        // 转换为CloudServiceResponse
        CloudServiceResponse result = new CloudServiceResponse();
        result.setSuccess(getBoolean(responseBody, "success"));
        result.setCode(getString(responseBody, "code"));
        result.setMessage(getString(responseBody, "message"));
        result.setDataId(getString(responseBody, "dataId"));
        result.setGlobalTxId(getString(responseBody, "globalTxId"));
        result.setBranchTxId(getString(responseBody, "branchTxId"));
        result.setData(responseBody.get("data"));

        return result;
    }



    @Override
    @DistributedBranchTransaction(value = "cloud-analysis-service", targetService = "cloud-analysis-service", description = "调用云分析服务", timeout = 10000)
    public CloudServiceResponse callAnalysisService(CloudServiceRequest request) {
        log.info("调用云分析服务，数据ID：{}", request.getDataId());

        // 转换为AnalysisRequest
        Map<String, Object> analysisRequest = new java.util.HashMap<>();
        analysisRequest.put("dataId", request.getDataId());
        analysisRequest.put("dataContent", request.getDataContent());
        analysisRequest.put("dataType", request.getDataType());
        analysisRequest.put("globalTxId", request.getGlobalTxId());
        analysisRequest.put("branchTxId", request.getBranchTxId());
        analysisRequest.put("simulateError", request.getSimulateError());
        analysisRequest.put("simulateSlowProcessing", request.getSimulateSlowProcessing());
        analysisRequest.put("slowProcessingDelayMillis", request.getSlowProcessingDelayMillis());
        analysisRequest.put("remark", request.getRemark());

        // 使用AtHttpClient发送HTTP请求到云分析服务
        HttpResponse response = atHttpClient.post(
            "http://localhost:8082/api/analysis/analyze",
            analysisRequest
        );

        String body = response.body();
        log.info("云分析服务调用成功，数据ID：{}", request.getDataId());

        // 解析响应体为Map
        Map<String, Object> responseBody = JSONUtil.toBean(body, Map.class);

        // 转换为CloudServiceResponse
        CloudServiceResponse result = new CloudServiceResponse();
        result.setSuccess(getBoolean(responseBody, "success"));
        result.setCode(getString(responseBody, "code"));
        result.setMessage(getString(responseBody, "message"));
        result.setDataId(getString(responseBody, "dataId"));
        result.setGlobalTxId(getString(responseBody, "globalTxId"));
        result.setBranchTxId(getString(responseBody, "branchTxId"));
        result.setData(responseBody.get("data"));

        return result;
    }

    @Override
    @DistributedBranchTransaction(value = "cloud-notify-service", targetService = "cloud-notify-service", description = "调用云通知服务", timeout = 10000)
    public CloudServiceResponse callNotifyService(CloudServiceRequest request) {
        log.info("调用云通知服务，数据ID：{}", request.getDataId());

        // 转换为NotifyRequest
        Map<String, Object> notifyRequest = new java.util.HashMap<>();
        notifyRequest.put("dataId", request.getDataId());
        notifyRequest.put("notifyType", "EMAIL"); // 默认邮件通知
        notifyRequest.put("notifyTarget", "<EMAIL>"); // 默认目标
        notifyRequest.put("globalTxId", request.getGlobalTxId());
        notifyRequest.put("branchTxId", request.getBranchTxId());
        notifyRequest.put("simulateError", request.getSimulateError());
        notifyRequest.put("simulateSlowProcessing", request.getSimulateSlowProcessing());
        notifyRequest.put("slowProcessingDelayMillis", request.getSlowProcessingDelayMillis());
        notifyRequest.put("remark", request.getRemark());

        // 使用AtHttpClient发送HTTP请求到云通知服务
        HttpResponse response = atHttpClient.post(
            "http://localhost:8083/api/notify/send",
            notifyRequest
        );

        String body = response.body();
        log.info("云通知服务调用成功，数据ID：{}", request.getDataId());

        // 解析响应体为Map
        Map<String, Object> responseBody = JSONUtil.toBean(body, Map.class);

        // 转换为CloudServiceResponse
        CloudServiceResponse result = new CloudServiceResponse();
        result.setSuccess(getBoolean(responseBody, "success"));
        result.setCode(getString(responseBody, "code"));
        result.setMessage(getString(responseBody, "message"));
        result.setDataId(getString(responseBody, "dataId"));
        result.setGlobalTxId(getString(responseBody, "globalTxId"));
        result.setBranchTxId(getString(responseBody, "branchTxId"));
        result.setData(responseBody.get("data"));

        return result;
    }

    /**
     * 安全获取字符串值
     */
    private String getString(Map<String, Object> map, String key) {
        Object value = map.get(key);
        if (value == null || "null".equals(String.valueOf(value))) {
            return null;
        }
        // 处理JSONNull类型
        if (value.getClass().getSimpleName().equals("JSONNull")) {
            return null;
        }
        return String.valueOf(value);
    }

    /**
     * 安全获取布尔值
     */
    private Boolean getBoolean(Map<String, Object> map, String key) {
        Object value = map.get(key);
        if (value == null || "null".equals(String.valueOf(value))) {
            return false;
        }
        // 处理JSONNull类型
        if (value.getClass().getSimpleName().equals("JSONNull")) {
            return false;
        }
        if (value instanceof Boolean) {
            return (Boolean) value;
        }
        return Boolean.valueOf(String.valueOf(value));
    }

    @Override
    @DistributedBranchTransaction(value = "cloud-storage-complex-ops", targetService = "cloud-storage-service", description = "调用云存储服务复杂操作", timeout = 15000)
    public CloudServiceResponse callStorageComplexOperations(CloudServiceRequest request, String operationType) {
        log.info("调用云存储服务复杂操作，数据ID：{}，操作类型：{}", request.getDataId(), operationType);

        // 构建复杂操作请求
        Map<String, Object> complexRequest = new java.util.HashMap<>();
        complexRequest.put("dataId", request.getDataId());
        complexRequest.put("dataContent", request.getDataContent());
        complexRequest.put("dataType", request.getDataType());
        complexRequest.put("operationType", operationType);
        complexRequest.put("globalTxId", request.getGlobalTxId());
        complexRequest.put("branchTxId", request.getBranchTxId());
        complexRequest.put("simulateError", request.getSimulateError());
        complexRequest.put("simulateSlowProcessing", request.getSimulateSlowProcessing());
        complexRequest.put("slowProcessingDelayMillis", request.getSlowProcessingDelayMillis());
        complexRequest.put("remark", request.getRemark());

        // 使用AtHttpClient发送HTTP请求到云存储服务的复杂操作接口
        HttpResponse response = atHttpClient.post(
                "http://localhost:8081/api/storage/complex-operations",
                complexRequest
        );

        // 解析响应
        String responseBody = response.body();
        log.info("云存储服务复杂操作响应：{}", responseBody);

        Map<String, Object> responseMap = JSONUtil.toBean(responseBody, Map.class);

        CloudServiceResponse serviceResponse = new CloudServiceResponse();
        serviceResponse.setSuccess(getBoolean(responseMap, "success"));
        serviceResponse.setCode(getString(responseMap, "code"));
        serviceResponse.setMessage(getString(responseMap, "message"));
        serviceResponse.setDataId(getString(responseMap, "dataId"));
        serviceResponse.setGlobalTxId(getString(responseMap, "globalTxId"));
        serviceResponse.setBranchTxId(getString(responseMap, "branchTxId"));
        serviceResponse.setData(responseMap.get("result"));

        if (!serviceResponse.getSuccess()) {
            log.error("云存储服务复杂操作失败：{}", serviceResponse.getMessage());
            throw new RuntimeException("云存储服务复杂操作失败：" + serviceResponse.getMessage());
        }

        log.info("云存储服务复杂操作调用成功，操作类型：{}", operationType);
        return serviceResponse;
    }

    @Override
    @DistributedBranchTransaction(value = "cloud-analysis-complex-sql", targetService = "cloud-analysis-service", description = "调用云分析服务复杂SQL操作", timeout = 15000)
    public CloudServiceResponse callAnalysisComplexSql(CloudServiceRequest request) {
        log.info("调用云分析服务复杂SQL操作，数据ID：{}", request.getDataId());

        // 构建复杂SQL请求
        Map<String, Object> complexRequest = new java.util.HashMap<>();
        complexRequest.put("dataId", request.getDataId());
        complexRequest.put("dataContent", request.getDataContent());
        complexRequest.put("dataType", request.getDataType());
        complexRequest.put("globalTxId", request.getGlobalTxId());
        complexRequest.put("branchTxId", request.getBranchTxId());
        complexRequest.put("simulateError", request.getSimulateError());
        complexRequest.put("simulateSlowProcessing", request.getSimulateSlowProcessing());
        complexRequest.put("slowProcessingDelayMillis", request.getSlowProcessingDelayMillis());
        complexRequest.put("remark", request.getRemark());

        // 使用AtHttpClient发送HTTP请求到云分析服务的复杂SQL接口
        HttpResponse response = atHttpClient.post(
                "http://localhost:8082/api/analysis/complex-sql",
                complexRequest
        );

        // 解析响应
        String responseBody = response.body();
        log.info("云分析服务复杂SQL操作响应：{}", responseBody);

        Map<String, Object> responseMap = JSONUtil.toBean(responseBody, Map.class);

        CloudServiceResponse serviceResponse = new CloudServiceResponse();
        serviceResponse.setSuccess(getBoolean(responseMap, "success"));
        serviceResponse.setCode(getString(responseMap, "code"));
        serviceResponse.setMessage(getString(responseMap, "message"));
        serviceResponse.setDataId(getString(responseMap, "dataId"));
        serviceResponse.setGlobalTxId(getString(responseMap, "globalTxId"));
        serviceResponse.setBranchTxId(getString(responseMap, "branchTxId"));
        serviceResponse.setData(responseMap.get("result"));

        if (!serviceResponse.getSuccess()) {
            log.error("云分析服务复杂SQL操作失败：{}", serviceResponse.getMessage());
            throw new RuntimeException("云分析服务复杂SQL操作失败：" + serviceResponse.getMessage());
        }

        log.info("云分析服务复杂SQL操作调用成功");
        return serviceResponse;
    }

    @Override
    @DistributedBranchTransaction(value = "cloud-notify-complex-sql", targetService = "cloud-notify-service", description = "调用云通知服务复杂SQL操作", timeout = 15000)
    public CloudServiceResponse callNotifyComplexSql(CloudServiceRequest request) {
        log.info("调用云通知服务复杂SQL操作，数据ID：{}", request.getDataId());

        // 构建复杂SQL请求
        Map<String, Object> complexRequest = new java.util.HashMap<>();
        complexRequest.put("dataId", request.getDataId());
        complexRequest.put("dataContent", request.getDataContent());
        complexRequest.put("dataType", request.getDataType());
        complexRequest.put("globalTxId", request.getGlobalTxId());
        complexRequest.put("branchTxId", request.getBranchTxId());
        complexRequest.put("simulateError", request.getSimulateError());
        complexRequest.put("simulateSlowProcessing", request.getSimulateSlowProcessing());
        complexRequest.put("slowProcessingDelayMillis", request.getSlowProcessingDelayMillis());
        complexRequest.put("remark", request.getRemark());

        // 使用AtHttpClient发送HTTP请求到云通知服务的复杂SQL接口
        HttpResponse response = atHttpClient.post(
                "http://localhost:8083/api/notify/complex-sql",
                complexRequest
        );

        // 解析响应
        String responseBody = response.body();
        log.info("云通知服务复杂SQL操作响应：{}", responseBody);

        Map<String, Object> responseMap = JSONUtil.toBean(responseBody, Map.class);

        CloudServiceResponse serviceResponse = new CloudServiceResponse();
        serviceResponse.setSuccess(getBoolean(responseMap, "success"));
        serviceResponse.setCode(getString(responseMap, "code"));
        serviceResponse.setMessage(getString(responseMap, "message"));
        serviceResponse.setDataId(getString(responseMap, "dataId"));
        serviceResponse.setGlobalTxId(getString(responseMap, "globalTxId"));
        serviceResponse.setBranchTxId(getString(responseMap, "branchTxId"));
        serviceResponse.setData(responseMap.get("result"));

        if (!serviceResponse.getSuccess()) {
            log.error("云通知服务复杂SQL操作失败：{}", serviceResponse.getMessage());
            throw new RuntimeException("云通知服务复杂SQL操作失败：" + serviceResponse.getMessage());
        }

        log.info("云通知服务复杂SQL操作调用成功");
        return serviceResponse;
    }
}
