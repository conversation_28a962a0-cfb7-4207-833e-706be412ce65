package com.tipray.at.dlp.test;

import com.tipray.at.dlp.dto.DataProcessRequest;
import com.tipray.at.dlp.entity.DlpDataRecord;
import com.tipray.at.dlp.test.base.BaseAtTransactionTest;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.*;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.util.Map;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;

import static org.junit.jupiter.api.Assertions.*;

/**
 * AT模式分布式事务恢复测试
 *
 * 测试各种故障恢复场景，验证框架的容错能力和数据一致性保证
 *
 * <AUTHOR>
 * @version 2.0
 * @since 2025-05-10
 */
@Slf4j
@SpringBootTest
@ActiveProfiles("test")
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
@DisplayName("AT模式分布式事务恢复测试")
public class AtTransactionRecoveryTest extends BaseAtTransactionTest {

    @Test
    @Order(1)
    @DisplayName("测试服务重启后的事务恢复")
    void testTransactionRecoveryAfterServiceRestart() {
        String dataId = generateTestDataId("SERVICE_RESTART_RECOVERY");
        log.info("开始测试服务重启后的事务恢复，数据ID：{}", dataId);

        try {
            // 1. 启动一个长时间运行的事务
            DataProcessRequest request = new DataProcessRequest();
            request.setDataId(dataId);
            request.setDataContent("服务重启恢复测试数据");
            request.setDataType("JSON");
            request.setCallCloudServices(true);
            request.setSimulateSlowProcessing(true);
            request.setTimeoutMillis(30000L);

            // 2. 模拟服务重启场景（通过异常中断事务）
            assertThrows(Exception.class, () -> {
                dlpDataService.processData(request);
            }, "长时间事务应该被中断");

            // 3. 等待一段时间，模拟服务重启完成
            waitForAsyncOperation(5000);

            // 4. 验证事务状态恢复机制
            // 检查是否有未完成的事务记录
            DlpDataRecord record = dlpDataRecordMapper.selectByDataId(dataId);
            if (record != null) {
                // 如果有记录，状态应该是失败或需要恢复
                assertTrue(
                    "FAILED".equals(record.getStatus()) ||
                    "TIMEOUT".equals(record.getStatus()) ||
                    "MANUAL_INTERVENTION_REQUIRED".equals(record.getStatus()),
                    "中断的事务应该被标记为失败状态"
                );
            }

            // 5. 验证云服务数据一致性
            assertFalse(verifyAllCloudServicesData(dataId), "中断的事务不应该在云服务中留下数据");

            log.info("服务重启恢复测试完成");

        } catch (Exception e) {
            log.error("服务重启恢复测试失败", e);
            fail("服务重启恢复测试不应该失败: " + e.getMessage());
        }
    }

    @Test
    @Order(2)
    @DisplayName("测试数据库连接中断后的事务恢复")
    void testTransactionRecoveryAfterDatabaseDisconnection() {
        String dataId = generateTestDataId("DB_DISCONNECT_RECOVERY");
        log.info("开始测试数据库连接中断后的事务恢复，数据ID：{}", dataId);

        try {
            // 1. 模拟数据库连接问题
            DataProcessRequest request = new DataProcessRequest();
            request.setDataId(dataId);
            request.setDataContent("数据库连接中断恢复测试");
            request.setDataType("JSON");
            request.setSimulateDatabaseError(true);

            // 2. 执行事务，期望失败
            assertThrows(Exception.class, () -> {
                dlpDataService.processData(request);
            }, "数据库连接问题应该导致事务失败");

            // 3. 等待恢复
            waitForAsyncOperation(3000);

            // 4. 验证数据一致性
            DlpDataRecord record = dlpDataRecordMapper.selectByDataId(dataId);
            assertNull(record, "失败的事务不应该留下数据记录");

            // 5. 验证云服务数据也没有残留
            assertFalse(verifyAllCloudServicesData(dataId), "失败的事务不应该在云服务中留下数据");

            log.info("数据库连接中断恢复测试完成");

        } catch (Exception e) {
            log.error("数据库连接中断恢复测试失败", e);
            fail("数据库连接中断恢复测试不应该失败: " + e.getMessage());
        }
    }

    @Test
    @Order(3)
    @DisplayName("测试网络分区后的事务恢复")
    void testTransactionRecoveryAfterNetworkPartition() {
        String dataId = generateTestDataId("NETWORK_PARTITION_RECOVERY");
        log.info("开始测试网络分区后的事务恢复，数据ID：{}", dataId);

        try {
            // 1. 模拟网络分区场景
            DataProcessRequest request = new DataProcessRequest();
            request.setDataId(dataId);
            request.setDataContent("网络分区恢复测试数据");
            request.setDataType("JSON");
            request.setCallCloudServices(true);
            request.setSimulateNetworkPartition(true);

            // 2. 执行事务，期望网络异常
            assertThrows(Exception.class, () -> {
                dlpDataService.processData(request);
            }, "网络分区应该导致事务失败");

            // 3. 等待网络恢复
            waitForAsyncOperation(5000);

            // 4. 验证事务状态
            DlpDataRecord record = dlpDataRecordMapper.selectByDataId(dataId);
            if (record != null) {
                assertTrue(
                    "FAILED".equals(record.getStatus()) ||
                    "TIMEOUT".equals(record.getStatus()),
                    "网络分区导致的失败事务应该被正确标记"
                );
            }

            // 5. 验证数据一致性
            assertFalse(verifyAllCloudServicesData(dataId), "网络分区失败的事务不应该在云服务中留下数据");

            log.info("网络分区恢复测试完成");

        } catch (Exception e) {
            log.error("网络分区恢复测试失败", e);
            fail("网络分区恢复测试不应该失败: " + e.getMessage());
        }
    }

    @Test
    @Order(4)
    @DisplayName("测试并发事务恢复")
    void testConcurrentTransactionRecovery() throws InterruptedException {
        log.info("开始测试并发事务恢复");

        int threadCount = 5;
        CountDownLatch startLatch = new CountDownLatch(1);
        CountDownLatch endLatch = new CountDownLatch(threadCount);

        // 启动多个并发事务，部分成功，部分失败
        for (int i = 0; i < threadCount; i++) {
            final int index = i;
            new Thread(() -> {
                try {
                    startLatch.await();

                    String dataId = generateTestDataId("CONCURRENT_RECOVERY_" + index);
                    DataProcessRequest request = new DataProcessRequest();
                    request.setDataId(dataId);
                    request.setDataContent("并发恢复测试数据" + index);
                    request.setDataType("JSON");
                    request.setCallCloudServices(true);

                    // 模拟部分事务失败
                    if (index % 2 == 0) {
                        request.setSimulateError(true);
                    }

                    try {
                        dlpDataService.processData(request);
                        if (index % 2 == 0) {
                            fail("模拟失败的事务不应该成功");
                        }
                    } catch (Exception e) {
                        if (index % 2 != 0) {
                            fail("正常事务不应该失败: " + e.getMessage());
                        }
                    }

                } catch (Exception e) {
                    log.error("并发事务执行失败", e);
                } finally {
                    endLatch.countDown();
                }
            }).start();
        }

        // 启动所有线程
        startLatch.countDown();

        // 等待所有线程完成
        assertTrue(endLatch.await(30, TimeUnit.SECONDS), "并发事务应该在30秒内完成");

        // 等待异步操作完成
        waitForAsyncOperation(5000);

        // 验证成功的事务数据存在，失败的事务数据不存在
        for (int i = 0; i < threadCount; i++) {
            String dataId = generateTestDataId("CONCURRENT_RECOVERY_" + i);

            if (i % 2 == 0) {
                // 失败的事务
                assertFalse(verifyTransactionSuccess(dataId), "失败的事务不应该有成功数据");
                assertFalse(verifyAllCloudServicesData(dataId), "失败的事务不应该在云服务中留下数据");
            } else {
                // 成功的事务
                assertTrue(verifyTransactionSuccess(dataId), "成功的事务应该有数据");
                assertTrue(verifyAllCloudServicesData(dataId), "成功的事务应该在云服务中有数据");
            }
        }

        log.info("并发事务恢复测试完成");
    }

    @Test
    @Order(5)
    @DisplayName("测试UndoLog损坏后的恢复")
    void testRecoveryAfterUndoLogCorruption() {
        String dataId = generateTestDataId("UNDO_LOG_CORRUPTION_RECOVERY");
        log.info("开始测试UndoLog损坏后的恢复，数据ID：{}", dataId);

        try {
            // 1. 模拟UndoLog损坏场景
            Map<String, Object> result = atComplexTestService.testUndoLogCorruptionScenario(dataId);

            // 2. 验证系统对UndoLog损坏的处理
            if (result != null) {
                String status = (String) result.get("status");
                assertTrue(
                    "MANUAL_INTERVENTION_REQUIRED".equals(status) ||
                    "ROLLBACK_FAILED".equals(status),
                    "UndoLog损坏应该被正确识别和处理"
                );
            }

            // 3. 等待处理完成
            waitForAsyncOperation(3000);

            // 4. 验证系统状态
            // UndoLog损坏时，系统应该能够检测到并标记为需要人工干预
            log.info("UndoLog损坏恢复测试完成，系统正确处理了UndoLog损坏场景");

        } catch (Exception e) {
            // UndoLog损坏可能导致特定异常，这是预期的
            assertTrue(
                e.getMessage().contains("UndoLog") ||
                e.getMessage().contains("rollback") ||
                e.getMessage().contains("回滚"),
                "应该是UndoLog相关的异常"
            );
            log.info("UndoLog损坏恢复测试完成，正确抛出了预期异常");
        }
    }

    @Test
    @Order(6)
    @DisplayName("测试系统资源耗尽后的恢复")
    void testRecoveryAfterResourceExhaustion() {
        String dataId = generateTestDataId("RESOURCE_EXHAUSTION_RECOVERY");
        log.info("开始测试系统资源耗尽后的恢复，数据ID：{}", dataId);

        try {
            // 1. 模拟系统资源耗尽
            DataProcessRequest request = new DataProcessRequest();
            request.setDataId(dataId);
            request.setDataContent("资源耗尽恢复测试数据");
            request.setDataType("JSON");
            request.setSimulateResourceExhaustion(true);

            // 2. 执行事务，期望资源不足异常
            assertThrows(Exception.class, () -> {
                dlpDataService.processData(request);
            }, "资源耗尽应该导致事务失败");

            // 3. 等待系统恢复
            waitForAsyncOperation(5000);

            // 4. 验证系统状态恢复
            // 资源耗尽后，系统应该能够恢复正常状态
            String normalDataId = generateTestDataId("NORMAL_AFTER_EXHAUSTION");
            DataProcessRequest normalRequest = new DataProcessRequest();
            normalRequest.setDataId(normalDataId);
            normalRequest.setDataContent("资源恢复后的正常测试");
            normalRequest.setDataType("JSON");

            // 5. 执行正常事务，验证系统已恢复
            DlpDataRecord result = dlpDataService.processData(normalRequest);
            assertNotNull(result, "系统恢复后应该能够正常处理事务");
            assertEquals("SUCCESS", result.getStatus(), "恢复后的事务应该成功");

            log.info("系统资源耗尽恢复测试完成");

        } catch (Exception e) {
            log.error("系统资源耗尽恢复测试失败", e);
            fail("系统资源耗尽恢复测试不应该失败: " + e.getMessage());
        }
    }
}
