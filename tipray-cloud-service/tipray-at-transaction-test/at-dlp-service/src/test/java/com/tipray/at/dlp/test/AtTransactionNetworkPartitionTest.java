package com.tipray.at.dlp.test;

import com.tipray.at.dlp.AtDlpServiceApplication;
import com.tipray.at.dlp.dto.DataProcessRequest;
import com.tipray.at.dlp.entity.DlpDataRecord;
import com.tipray.at.dlp.test.base.BaseAtTransactionTest;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.*;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.util.Map;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;

import static org.junit.jupiter.api.Assertions.*;

/**
 * AT模式分布式事务网络分区测试
 *
 * 测试网络分区、网络延迟、网络抖动等网络异常场景下的事务行为
 *
 * <AUTHOR>
 * @version 2.0
 * @since 2025-05-10
 */
@Slf4j
@SpringBootTest(
        classes = AtDlpServiceApplication.class,
        webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT
)
@ActiveProfiles("test")
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
@DisplayName("AT模式分布式事务网络分区测试")
public class AtTransactionNetworkPartitionTest extends BaseAtTransactionTest {

    @Test
    @Order(1)
    @DisplayName("测试完全网络分区场景")
    void testCompleteNetworkPartition() {
        String dataId = generateTestDataId("COMPLETE_PARTITION");
        log.info("开始测试完全网络分区场景，数据ID：{}", dataId);

        try {
            // 模拟完全网络分区
            DataProcessRequest request = new DataProcessRequest();
            request.setDataId(dataId);
            request.setDataContent("完全网络分区测试数据");
            request.setDataType("JSON");
            request.setCallCloudServices(true);
            request.setSimulateCompleteNetworkPartition(true);

            // 执行事务，期望网络异常
            assertThrows(Exception.class, () -> {
                dlpDataService.processData(request);
            }, "完全网络分区应该导致事务失败");

            // 等待处理完成
            waitForAsyncOperation(5000);

            // 验证DLP状态
            DlpDataRecord record = dlpDataRecordMapper.selectByDataId(dataId);
            if (record != null) {
                assertTrue(
                    "FAILED".equals(record.getStatus()) ||
                    "NETWORK_ERROR".equals(record.getStatus()) ||
                    "TIMEOUT".equals(record.getStatus()),
                    "网络分区导致的失败应该有正确的状态标记"
                );
            }

            // 验证云服务数据一致性
            assertFalse(verifyAllCloudServicesData(dataId),
                "网络分区失败的事务不应该在云服务中留下数据");

            log.info("完全网络分区测试完成");

        } catch (Exception e) {
            log.error("完全网络分区测试失败", e);
            fail("完全网络分区测试不应该失败: " + e.getMessage());
        }
    }

    @Test
    @Order(2)
    @DisplayName("测试部分网络分区场景")
    void testPartialNetworkPartition() {
        String dataId = generateTestDataId("PARTIAL_PARTITION");
        log.info("开始测试部分网络分区场景，数据ID：{}", dataId);

        try {
            // 模拟部分网络分区（部分云服务不可达）
            DataProcessRequest request = new DataProcessRequest();
            request.setDataId(dataId);
            request.setDataContent("部分网络分区测试数据");
            request.setDataType("JSON");
            request.setCallCloudServices(true);
            request.setSimulatePartialNetworkPartition(true);

            // 执行事务
            try {
                DlpDataRecord result = dlpDataService.processData(request);

                // 部分分区可能导致事务失败或部分成功
                if (result != null) {
                    log.info("部分网络分区下事务状态: {}", result.getStatus());
                }

            } catch (Exception e) {
                log.info("部分网络分区导致事务失败: {}", e.getMessage());
            }

            // 等待处理完成
            waitForAsyncOperation(8000);

            // 验证数据一致性
            DlpDataRecord record = dlpDataRecordMapper.selectByDataId(dataId);
            if (record != null) {
                String status = record.getStatus();

                if ("SUCCESS".equals(status)) {
                    // 如果DLP标记为成功，所有云服务都应该有数据
                    assertTrue(verifyAllCloudServicesData(dataId),
                        "成功的事务应该在所有云服务中都有数据");
                } else {
                    // 如果DLP标记为失败，云服务不应该有数据
                    assertFalse(verifyAllCloudServicesData(dataId),
                        "失败的事务不应该在云服务中留下数据");
                }
            }

            log.info("部分网络分区测试完成");

        } catch (Exception e) {
            log.error("部分网络分区测试失败", e);
            fail("部分网络分区测试不应该失败: " + e.getMessage());
        }
    }

    @Test
    @Order(3)
    @DisplayName("测试网络延迟场景")
    void testNetworkLatency() {
        String dataId = generateTestDataId("NETWORK_LATENCY");
        log.info("开始测试网络延迟场景，数据ID：{}", dataId);

        try {
            // 模拟高网络延迟
            DataProcessRequest request = new DataProcessRequest();
            request.setDataId(dataId);
            request.setDataContent("网络延迟测试数据");
            request.setDataType("JSON");
            request.setCallCloudServices(true);
            request.setSimulateHighLatency(true);
            request.setNetworkLatencyMs(5000L); // 5秒延迟

            long startTime = System.currentTimeMillis();

            try {
                DlpDataRecord result = dlpDataService.processData(request);
                long endTime = System.currentTimeMillis();
                long duration = endTime - startTime;

                log.info("高延迟网络下事务执行时间: {} ms", duration);

                // 验证事务能够在高延迟下正常完成
                assertNotNull(result, "高延迟网络下事务应该能够完成");
                assertEquals("SUCCESS", result.getStatus(), "高延迟网络下事务应该成功");

                // 验证执行时间符合预期（应该大于延迟时间）
                assertTrue(duration >= 5000, "执行时间应该反映网络延迟");

            } catch (Exception e) {
                // 如果超时，也是可以接受的结果
                assertTrue(e.getMessage().contains("timeout") || e.getMessage().contains("超时"),
                    "高延迟可能导致超时异常");
            }

            // 等待处理完成
            waitForAsyncOperation(10000);

            // 验证最终数据一致性
            DlpDataRecord record = dlpDataRecordMapper.selectByDataId(dataId);
            if (record != null && "SUCCESS".equals(record.getStatus())) {
                assertTrue(verifyAllCloudServicesData(dataId),
                    "成功的高延迟事务应该在云服务中有数据");
            }

            log.info("网络延迟测试完成");

        } catch (Exception e) {
            log.error("网络延迟测试失败", e);
            fail("网络延迟测试不应该失败: " + e.getMessage());
        }
    }

    @Test
    @Order(4)
    @DisplayName("测试网络抖动场景")
    void testNetworkJitter() {
        String dataId = generateTestDataId("NETWORK_JITTER");
        log.info("开始测试网络抖动场景，数据ID：{}", dataId);

        try {
            // 模拟网络抖动
            DataProcessRequest request = new DataProcessRequest();
            request.setDataId(dataId);
            request.setDataContent("网络抖动测试数据");
            request.setDataType("JSON");
            request.setCallCloudServices(true);
            request.setSimulateNetworkJitter(true);

            // 执行事务
            DlpDataRecord result = dlpDataService.processData(request);

            // 网络抖动下事务应该能够完成
            assertNotNull(result, "网络抖动下事务应该能够完成");
            assertEquals("SUCCESS", result.getStatus(), "网络抖动下事务应该成功");

            // 等待处理完成
            waitForAsyncOperation(8000);

            // 验证数据一致性
            assertTrue(verifyAllCloudServicesData(dataId),
                "网络抖动下成功的事务应该在云服务中有数据");

            log.info("网络抖动测试完成");

        } catch (Exception e) {
            log.error("网络抖动测试失败", e);
            fail("网络抖动测试不应该失败: " + e.getMessage());
        }
    }

    @Test
    @Order(5)
    @DisplayName("测试网络恢复后的事务重试")
    void testTransactionRetryAfterNetworkRecovery() {
        String dataId = generateTestDataId("NETWORK_RECOVERY_RETRY");
        log.info("开始测试网络恢复后的事务重试，数据ID：{}", dataId);

        try {
            // 1. 模拟网络故障导致事务失败
            DataProcessRequest request = new DataProcessRequest();
            request.setDataId(dataId + "_INITIAL");
            request.setDataContent("网络恢复重试测试数据");
            request.setDataType("JSON");
            request.setCallCloudServices(true);
            request.setSimulateNetworkFailure(true);

            assertThrows(Exception.class, () -> {
                dlpDataService.processData(request);
            }, "网络故障应该导致事务失败");

            // 2. 等待网络恢复
            waitForAsyncOperation(3000);

            // 3. 网络恢复后重试事务
            DataProcessRequest retryRequest = new DataProcessRequest();
            retryRequest.setDataId(dataId + "_RETRY");
            retryRequest.setDataContent("网络恢复后重试数据");
            retryRequest.setDataType("JSON");
            retryRequest.setCallCloudServices(true);
            // 不模拟网络故障

            DlpDataRecord retryResult = dlpDataService.processData(retryRequest);

            // 验证重试成功
            assertNotNull(retryResult, "网络恢复后重试应该成功");
            assertEquals("SUCCESS", retryResult.getStatus(), "重试事务应该成功");

            // 等待处理完成
            waitForAsyncOperation(5000);

            // 验证重试事务的数据一致性
            assertTrue(verifyAllCloudServicesData(dataId + "_RETRY"),
                "重试成功的事务应该在云服务中有数据");

            // 验证失败事务没有留下数据
            assertFalse(verifyAllCloudServicesData(dataId + "_INITIAL"),
                "失败的事务不应该在云服务中留下数据");

            log.info("网络恢复后事务重试测试完成");

        } catch (Exception e) {
            log.error("网络恢复后事务重试测试失败", e);
            fail("网络恢复后事务重试测试不应该失败: " + e.getMessage());
        }
    }

    @Test
    @Order(6)
    @DisplayName("测试并发网络分区场景")
    void testConcurrentNetworkPartition() throws InterruptedException {
        log.info("开始测试并发网络分区场景");

        int threadCount = 5;
        CountDownLatch startLatch = new CountDownLatch(1);
        CountDownLatch endLatch = new CountDownLatch(threadCount);

        // 启动多个并发事务，在网络分区环境下执行
        for (int i = 0; i < threadCount; i++) {
            final int index = i;
            new Thread(() -> {
                try {
                    startLatch.await();

                    String dataId = generateTestDataId("CONCURRENT_PARTITION_" + index);
                    DataProcessRequest request = new DataProcessRequest();
                    request.setDataId(dataId);
                    request.setDataContent("并发网络分区测试" + index);
                    request.setDataType("JSON");
                    request.setCallCloudServices(true);

                    // 模拟不同类型的网络问题
                    switch (index % 3) {
                        case 0:
                            request.setSimulateNetworkPartition(true);
                            break;
                        case 1:
                            request.setSimulateHighLatency(true);
                            request.setNetworkLatencyMs(3000L);
                            break;
                        case 2:
                            // 正常网络
                            break;
                    }

                    try {
                        DlpDataRecord result = dlpDataService.processData(request);
                        log.info("并发网络分区测试{}完成，状态: {}", index,
                            result != null ? result.getStatus() : "null");
                    } catch (Exception e) {
                        log.info("并发网络分区测试{}失败: {}", index, e.getMessage());
                    }

                } catch (Exception e) {
                    log.error("并发网络分区测试执行异常", e);
                } finally {
                    endLatch.countDown();
                }
            }).start();
        }

        // 启动所有线程
        startLatch.countDown();

        // 等待所有线程完成
        assertTrue(endLatch.await(60, TimeUnit.SECONDS), "并发网络分区测试应该在60秒内完成");

        // 等待异步操作完成
        waitForAsyncOperation(10000);

        // 验证每个事务的数据一致性
        for (int i = 0; i < threadCount; i++) {
            String dataId = generateTestDataId("CONCURRENT_PARTITION_" + i);
            DlpDataRecord record = dlpDataRecordMapper.selectByDataId(dataId);

            if (record != null) {
                String status = record.getStatus();

                if ("SUCCESS".equals(status)) {
                    assertTrue(verifyAllCloudServicesData(dataId),
                        "成功的事务应该在云服务中有数据");
                } else {
                    assertFalse(verifyAllCloudServicesData(dataId),
                        "失败的事务不应该在云服务中留下数据");
                }
            }
        }

        log.info("并发网络分区测试完成");
    }

    @Test
    @Order(7)
    @DisplayName("测试网络分区下的事务超时处理")
    void testTransactionTimeoutUnderNetworkPartition() {
        String dataId = generateTestDataId("PARTITION_TIMEOUT");
        log.info("开始测试网络分区下的事务超时处理，数据ID：{}", dataId);

        try {
            // 模拟网络分区导致的超时
            DataProcessRequest request = new DataProcessRequest();
            request.setDataId(dataId);
            request.setDataContent("网络分区超时测试数据");
            request.setDataType("JSON");
            request.setCallCloudServices(true);
            request.setTimeoutMillis(5000L); // 5秒超时
            request.setSimulateNetworkPartition(true);

            long startTime = System.currentTimeMillis();

            assertThrows(Exception.class, () -> {
                dlpDataService.processData(request);
            }, "网络分区应该导致超时异常");

            long endTime = System.currentTimeMillis();
            long duration = endTime - startTime;

            // 验证超时时间是否合理
            assertTrue(duration >= 4000 && duration <= 10000,
                "超时时间应该在合理范围内，实际时间: " + duration + "ms");

            // 等待处理完成
            waitForAsyncOperation(5000);

            // 验证超时事务的状态
            DlpDataRecord record = dlpDataRecordMapper.selectByDataId(dataId);
            if (record != null) {
                assertTrue(
                    "TIMEOUT".equals(record.getStatus()) ||
                    "FAILED".equals(record.getStatus()),
                    "超时事务应该有正确的状态标记"
                );
            }

            // 验证数据一致性
            assertFalse(verifyAllCloudServicesData(dataId),
                "超时的事务不应该在云服务中留下数据");

            log.info("网络分区下事务超时处理测试完成");

        } catch (Exception e) {
            log.error("网络分区下事务超时处理测试失败", e);
            fail("网络分区下事务超时处理测试不应该失败: " + e.getMessage());
        }
    }
}
