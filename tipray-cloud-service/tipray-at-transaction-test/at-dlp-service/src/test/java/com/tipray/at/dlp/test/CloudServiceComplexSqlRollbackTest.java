package com.tipray.at.dlp.test;

import com.tipray.at.dlp.AtDlpServiceApplication;
import com.tipray.at.dlp.dto.DataProcessRequest;
import com.tipray.at.dlp.service.DlpDataService;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.web.client.TestRestTemplate;
import org.springframework.http.ResponseEntity;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.test.context.ActiveProfiles;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;

/**
 * 云服务复杂SQL回滚测试
 * 测试云服务端多表操作的复杂SQL回滚场景
 *
 * <AUTHOR>
 */
@Slf4j
@SpringBootTest(
        classes = AtDlpServiceApplication.class,
        webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT
)
@ActiveProfiles("test")
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
public class CloudServiceComplexSqlRollbackTest {

    @Autowired
    protected TestRestTemplate restTemplate;

    @Autowired
    private DlpDataService dlpDataService;

    @Autowired
    private JdbcTemplate jdbcTemplate;


    private static final String CLOUD_STORAGE_URL = "http://localhost:8081";
    private static final String CLOUD_ANALYSIS_URL = "http://localhost:8082";
    private static final String CLOUD_NOTIFY_URL = "http://localhost:8083";

    @BeforeAll
    static void setUpClass() {
        log.info("=== 开始云服务复杂SQL回滚测试套件 ===");
    }

    @AfterAll
    static void tearDownClass() {
        log.info("=== 云服务复杂SQL回滚测试套件结束 ===");
    }

    @BeforeEach
    void setUp() {
        log.info("--- 开始单个测试 ---");
    }

    @AfterEach
    void tearDown() {
        log.info("--- 单个测试结束 ---");
    }

    @Test
    @Order(1)
    @DisplayName("测试云存储服务复杂SQL回滚")
    void testCloudStorageComplexSqlRollback() {
        String dataId = "STORAGE_COMPLEX_SQL_" + System.currentTimeMillis();
        log.info("开始测试云存储服务复杂SQL回滚，数据ID: {}", dataId);

        try {
            // 准备请求，模拟在云存储服务执行复杂SQL后发生异常
            DataProcessRequest request = new DataProcessRequest();
            request.setDataId(dataId);
            request.setDataContent("云存储服务复杂SQL测试 - 多表INSERT/UPDATE/DELETE操作");
            request.setDataType("COMPLEX");
            request.setSimulateError(true);
            request.setErrorStep("cloud_analysis"); // 在云分析步骤模拟异常，让云存储的复杂SQL需要回滚
            request.setRemark("云存储服务复杂SQL回滚测试");

            // 执行分布式事务，期望异常并回滚
            Exception exception = Assertions.assertThrows(Exception.class, () -> {
                dlpDataService.processData(request);
            }, "应该抛出异常触发回滚");

            log.info("捕获到预期异常: {}", exception.getMessage());

            // 等待回滚完成
            Thread.sleep(3000);

            // 验证云存储服务的复杂SQL操作都已回滚
            verifyCloudServiceDataRollback(dataId, "云存储服务复杂SQL");

            log.info("云存储服务复杂SQL回滚测试成功");

        } catch (Exception e) {
            log.error("云存储服务复杂SQL回滚测试失败", e);
            Assertions.fail("测试失败: " + e.getMessage());
        }
    }

    @Test
    @Order(2)
    @DisplayName("测试云分析服务复杂SQL回滚")
    void testCloudAnalysisComplexSqlRollback() {
        String dataId = "ANALYSIS_COMPLEX_SQL_" + System.currentTimeMillis();
        log.info("开始测试云分析服务复杂SQL回滚，数据ID: {}", dataId);

        try {
            // 准备请求，模拟在云分析服务执行复杂SQL后发生异常
            DataProcessRequest request = new DataProcessRequest();
            request.setDataId(dataId);
            request.setDataContent("云分析服务复杂SQL测试 - 批量INSERT/JOIN查询/UPDATE操作");
            request.setDataType("BATCH");
            request.setSimulateError(true);
            request.setErrorStep("cloud_notify"); // 在云通知步骤模拟异常，让云分析的复杂SQL需要回滚
            request.setRemark("云分析服务复杂SQL回滚测试");

            // 执行分布式事务，期望异常并回滚
            Exception exception = Assertions.assertThrows(Exception.class, () -> {
                dlpDataService.processData(request);
            }, "应该抛出异常触发回滚");

            log.info("捕获到预期异常: {}", exception.getMessage());

            // 等待回滚完成
            Thread.sleep(3000);

            // 验证云分析服务的复杂SQL操作都已回滚
            verifyCloudServiceDataRollback(dataId, "云分析服务复杂SQL");

            log.info("云分析服务复杂SQL回滚测试成功");

        } catch (Exception e) {
            log.error("云分析服务复杂SQL回滚测试失败", e);
            Assertions.fail("测试失败: " + e.getMessage());
        }
    }

    @Test
    @Order(3)
    @DisplayName("测试云通知服务复杂SQL回滚")
    void testCloudNotifyComplexSqlRollback() {
        String dataId = "NOTIFY_COMPLEX_SQL_" + System.currentTimeMillis();
        log.info("开始测试云通知服务复杂SQL回滚，数据ID: {}", dataId);

        try {
            // 准备请求，模拟在云通知服务执行复杂SQL后发生异常
            DataProcessRequest request = new DataProcessRequest();
            request.setDataId(dataId);
            request.setDataContent("云通知服务复杂SQL测试 - 批量通知/配置更新/统计操作");
            request.setDataType("STATISTICS");
            request.setSimulateError(true);
            request.setErrorStep("cloud_notify"); // 在云通知步骤本身模拟异常
            request.setRemark("云通知服务复杂SQL回滚测试");

            // 执行分布式事务，期望异常并回滚
            Exception exception = Assertions.assertThrows(Exception.class, () -> {
                dlpDataService.processData(request);
            }, "应该抛出异常触发回滚");

            log.info("捕获到预期异常: {}", exception.getMessage());

            // 等待回滚完成
            Thread.sleep(3000);

            // 验证云通知服务的复杂SQL操作都已回滚
            verifyCloudServiceDataRollback(dataId, "云通知服务复杂SQL");

            log.info("云通知服务复杂SQL回滚测试成功");

        } catch (Exception e) {
            log.error("云通知服务复杂SQL回滚测试失败", e);
            Assertions.fail("测试失败: " + e.getMessage());
        }
    }

    @Test
    @Order(4)
    @DisplayName("测试全链路复杂SQL回滚")
    void testFullChainComplexSqlRollback() {
        String dataId = "FULL_CHAIN_COMPLEX_SQL_" + System.currentTimeMillis();
        log.info("开始测试全链路复杂SQL回滚，数据ID: {}", dataId);

        try {
            // 准备全链路复杂SQL请求
            DataProcessRequest request = new DataProcessRequest();
            request.setDataId(dataId);
            request.setDataContent("全链路复杂SQL测试 - 所有云服务都执行复杂SQL操作");
            request.setDataType("MIXED");
            request.setSimulateError(true);
            request.setErrorStep("cloud_notify"); // 在最后一步模拟异常，前面所有复杂SQL都需要回滚
            request.setRemark("全链路复杂SQL回滚测试");

            // 执行分布式事务，期望异常并回滚
            Exception exception = Assertions.assertThrows(Exception.class, () -> {
                dlpDataService.processData(request);
            }, "应该抛出异常触发回滚");

            log.info("捕获到预期异常: {}", exception.getMessage());

            // 等待回滚完成
            Thread.sleep(5000);

            // 验证所有云服务的复杂SQL操作都已回滚
            verifyCloudServiceDataRollback(dataId, "全链路复杂SQL");

            log.info("全链路复杂SQL回滚测试成功");

        } catch (Exception e) {
            log.error("全链路复杂SQL回滚测试失败", e);
            Assertions.fail("测试失败: " + e.getMessage());
        }
    }

    @Test
    @Order(5)
    @DisplayName("测试云服务复杂SQL性能回滚")
    void testComplexSqlPerformanceRollback() {
        String dataId = "PERFORMANCE_TEST_" + System.currentTimeMillis();
        log.info("开始测试云服务复杂SQL性能回滚，数据ID: {}", dataId);

        try {
            long startTime = System.currentTimeMillis();

            // 准备性能测试请求
            DataProcessRequest request = new DataProcessRequest();
            request.setDataId(dataId);
            request.setDataContent("复杂SQL性能测试 - 大量数据操作和复杂查询");
            request.setDataType("COMPLEX");
            request.setSimulateError(true);
            request.setErrorStep("cloud_analysis"); // 在云分析步骤模拟异常
            request.setRemark("复杂SQL性能回滚测试");

            // 执行分布式事务，期望异常并回滚
            Exception exception = Assertions.assertThrows(Exception.class, () -> {
                dlpDataService.processData(request);
            }, "应该抛出异常触发回滚");

            log.info("捕获到预期异常: {}", exception.getMessage());

            // 等待回滚完成
            Thread.sleep(5000);

            long endTime = System.currentTimeMillis();
            long totalTime = endTime - startTime;

            log.info("复杂SQL回滚总耗时: {}ms", totalTime);

            // 验证云服务端的复杂SQL操作都已回滚
            verifyCloudServiceDataRollback(dataId, "复杂SQL性能");

            // 验证回滚性能（应该在合理时间内完成）
            Assertions.assertTrue(totalTime < 30000, "复杂SQL回滚应该在30秒内完成，实际耗时: " + totalTime + "ms");

            log.info("复杂SQL性能回滚测试成功，总耗时: {}ms", totalTime);

        } catch (Exception e) {
            log.error("复杂SQL性能回滚测试失败", e);
            Assertions.fail("测试失败: " + e.getMessage());
        }
    }

    @Test
    @Order(6)
    @DisplayName("测试云服务数据更新回滚")
    void testCloudServiceDataUpdateRollback() {
        String dataId = "UPDATE_TEST_" + System.currentTimeMillis();
        log.info("开始测试云服务数据更新回滚，数据ID: {}", dataId);

        try {
            // 第一步：先创建一些初始数据
            DataProcessRequest setupRequest = new DataProcessRequest();
            setupRequest.setDataId(dataId);
            setupRequest.setDataContent("初始数据内容");
            setupRequest.setDataType("TEXT");
            setupRequest.setRemark("创建初始数据用于后续更新测试");

            dlpDataService.processData(setupRequest);
            log.info("初始数据创建成功");

            // 等待初始数据处理完成
            Thread.sleep(2000);

            // 记录更新前的状态
            String storageCheckUrl = CLOUD_STORAGE_URL + "/api/storage/get/" + dataId;
            ResponseEntity<Map> beforeUpdate = restTemplate.getForEntity(storageCheckUrl, Map.class);
            log.info("更新前数据状态: {}", beforeUpdate.getBody());

            // 第二步：执行数据更新操作，然后模拟异常
            DataProcessRequest updateRequest = new DataProcessRequest();
            updateRequest.setDataId(dataId + "_UPDATE");
            updateRequest.setDataContent("更新后的数据内容 - 包含复杂更新逻辑");
            updateRequest.setDataType("MIXED"); // 触发混合操作，包含UPDATE
            updateRequest.setSimulateError(true);
            updateRequest.setErrorStep("cloud_notify"); // 在最后一步模拟异常
            updateRequest.setRemark("数据更新回滚测试");

            // 执行分布式事务，期望异常并回滚
            Exception exception = Assertions.assertThrows(Exception.class, () -> {
                dlpDataService.processData(updateRequest);
            }, "应该抛出异常触发回滚");

            log.info("捕获到预期异常: {}", exception.getMessage());

            // 等待回滚完成
            Thread.sleep(3000);

            // 验证更新操作已回滚
            verifyCloudServiceDataRollback(dataId + "_UPDATE", "数据更新");

            // 验证原始数据仍然存在且未被修改
            ResponseEntity<Map> afterRollback = restTemplate.getForEntity(storageCheckUrl, Map.class);
            log.info("回滚后原始数据状态: {}", afterRollback.getBody());

            // 验证原始数据没有被影响
            Assertions.assertEquals(beforeUpdate.getStatusCodeValue(), afterRollback.getStatusCodeValue());

            log.info("数据更新回滚测试成功");

        } catch (Exception e) {
            log.error("数据更新回滚测试失败", e);
            Assertions.fail("测试失败: " + e.getMessage());
        }
    }

    @Test
    @Order(7)
    @DisplayName("测试云服务大批量数据回滚")
    void testCloudServiceBatchDataRollback() {
        String baseDataId = "BATCH_DATA_" + System.currentTimeMillis();
        log.info("开始测试云服务大批量数据回滚，基础数据ID: {}", baseDataId);

        try {
            // 准备大批量数据请求
            DataProcessRequest request = new DataProcessRequest();
            request.setDataId(baseDataId);
            request.setDataContent("大批量数据测试 - 模拟处理大量数据的场景");
            request.setDataType("BATCH");
            request.setSimulateError(true);
            request.setErrorStep("cloud_analysis"); // 在云分析步骤模拟异常
            request.setRemark("大批量数据回滚测试");

            // 执行分布式事务，期望异常并回滚
            Exception exception = Assertions.assertThrows(Exception.class, () -> {
                dlpDataService.processData(request);
            }, "应该抛出异常触发回滚");

            log.info("捕获到预期异常: {}", exception.getMessage());

            // 等待回滚完成
            Thread.sleep(5000);

            // 验证大批量数据都已回滚
            verifyCloudServiceDataRollback(baseDataId, "大批量数据");

            // 额外验证：检查是否有遗留的批量数据
            verifyNoBatchDataRemains(baseDataId);

            log.info("大批量数据回滚测试成功");

        } catch (Exception e) {
            log.error("大批量数据回滚测试失败", e);
            Assertions.fail("测试失败: " + e.getMessage());
        }
    }

    @Test
    @Order(8)
    @DisplayName("测试云服务并发事务回滚")
    void testCloudServiceConcurrentTransactionRollback() {
        String baseDataId = "CONCURRENT_" + System.currentTimeMillis();
        log.info("开始测试云服务并发事务回滚，基础数据ID: {}", baseDataId);

        try {
            // 准备并发事务请求
            DataProcessRequest request1 = new DataProcessRequest();
            request1.setDataId(baseDataId + "_TX1");
            request1.setDataContent("并发事务1 - 测试并发场景下的回滚");
            request1.setDataType("COMPLEX");
            request1.setSimulateError(true);
            request1.setErrorStep("cloud_notify");
            request1.setRemark("并发事务回滚测试1");

            DataProcessRequest request2 = new DataProcessRequest();
            request2.setDataId(baseDataId + "_TX2");
            request2.setDataContent("并发事务2 - 测试并发场景下的回滚");
            request2.setDataType("STATISTICS");
            request2.setSimulateError(true);
            request2.setErrorStep("cloud_analysis");
            request2.setRemark("并发事务回滚测试2");

            // 并发执行两个事务
            Exception exception1 = Assertions.assertThrows(Exception.class, () -> {
                dlpDataService.processData(request1);
            }, "事务1应该抛出异常触发回滚");

            Exception exception2 = Assertions.assertThrows(Exception.class, () -> {
                dlpDataService.processData(request2);
            }, "事务2应该抛出异常触发回滚");

            log.info("捕获到预期异常1: {}", exception1.getMessage());
            log.info("捕获到预期异常2: {}", exception2.getMessage());

            // 等待回滚完成
            Thread.sleep(5000);

            // 验证两个并发事务都已正确回滚
            verifyCloudServiceDataRollback(baseDataId + "_TX1", "并发事务1");
            verifyCloudServiceDataRollback(baseDataId + "_TX2", "并发事务2");

            log.info("并发事务回滚测试成功");

        } catch (Exception e) {
            log.error("并发事务回滚测试失败", e);
            Assertions.fail("测试失败: " + e.getMessage());
        }
    }

    @Test
    @Order(9)
    @DisplayName("测试云服务跨表关联数据回滚")
    void testCloudServiceCrossTableRollback() {
        String dataId = "CROSS_TABLE_" + System.currentTimeMillis();
        log.info("开始测试云服务跨表关联数据回滚，数据ID: {}", dataId);

        try {
            // 准备跨表关联数据请求
            DataProcessRequest request = new DataProcessRequest();
            request.setDataId(dataId);
            request.setDataContent("跨表关联测试 - 涉及多个表之间的复杂关联关系");
            request.setDataType("COMPLEX");
            request.setSimulateError(true);
            request.setErrorStep("cloud_storage"); // 在云存储步骤模拟异常
            request.setRemark("跨表关联数据回滚测试");

            // 执行分布式事务，期望异常并回滚
            Exception exception = Assertions.assertThrows(Exception.class, () -> {
                dlpDataService.processData(request);
            }, "应该抛出异常触发回滚");

            log.info("捕获到预期异常: {}", exception.getMessage());

            // 等待回滚完成
            Thread.sleep(4000);

            // 验证跨表关联数据都已回滚
            verifyCloudServiceDataRollback(dataId, "跨表关联数据");

            // 额外验证：检查关联表的数据一致性
            verifyCrossTableConsistency(dataId);

            log.info("跨表关联数据回滚测试成功");

        } catch (Exception e) {
            log.error("跨表关联数据回滚测试失败", e);
            Assertions.fail("测试失败: " + e.getMessage());
        }
    }

    /**
     * 验证云服务端数据已回滚
     */
    private void verifyCloudServiceDataRollback(String dataId, String operationType) {
        log.info("开始验证云服务端{}数据回滚，数据ID: {}", operationType, dataId);

        try {
            // 验证云存储服务数据已回滚
            String storageCheckUrl = CLOUD_STORAGE_URL + "/api/storage/check/" + dataId;
            ResponseEntity<Map> storageResult = restTemplate.getForEntity(storageCheckUrl, Map.class);
            log.info("云存储服务回滚验证结果: {}", storageResult);

            // 验证云存储服务回滚结果
            verifyServiceRollbackResult(storageResult, "云存储服务", dataId);

            // 验证云分析服务数据已回滚
            String analysisCheckUrl = CLOUD_ANALYSIS_URL + "/api/analysis/check/" + dataId;
            ResponseEntity<Map> analysisResult = restTemplate.getForEntity(analysisCheckUrl, Map.class);
            log.info("云分析服务回滚验证结果: {}", analysisResult);

            // 验证云分析服务回滚结果
            verifyServiceRollbackResult(analysisResult, "云分析服务", dataId);

            // 验证云通知服务数据已回滚
            String notifyCheckUrl = CLOUD_NOTIFY_URL + "/api/notify/check/" + dataId;
            ResponseEntity<Map> notifyResult = restTemplate.getForEntity(notifyCheckUrl, Map.class);
            log.info("云通知服务回滚验证结果: {}", notifyResult);

            // 验证云通知服务回滚结果
            verifyServiceRollbackResult(notifyResult, "云通知服务", dataId);

            log.info("✅ 云服务端{}数据回滚验证完成 - 所有数据都已成功回滚！", operationType);

        } catch (Exception e) {
            log.error("验证云服务端数据回滚失败", e);
            Assertions.fail("回滚验证失败: " + e.getMessage());
        }
    }

    /**
     * 验证单个服务的回滚结果
     */
    private void verifyServiceRollbackResult(ResponseEntity<Map> result, String serviceName, String dataId) {
        // 验证HTTP状态码
        Assertions.assertEquals(200, result.getStatusCodeValue(),
            serviceName + "接口调用应该成功");

        Map<String, Object> responseBody = result.getBody();
        Assertions.assertNotNull(responseBody, serviceName + "响应体不应该为空");

        // 验证响应格式
        Boolean success = (Boolean) responseBody.get("success");
        Assertions.assertTrue(success, serviceName + "接口调用应该成功");

        String returnedDataId = (String) responseBody.get("dataId");
        Assertions.assertEquals(dataId, returnedDataId, "返回的数据ID应该匹配");

        // 验证exists字段
        Map<String, Object> exists = (Map<String, Object>) responseBody.get("exists");
        Assertions.assertNotNull(exists, serviceName + "的exists字段不应该为空");

        // 验证回滚结果 - 所有数据都应该被清理
        log.info("验证{}的回滚结果: {}", serviceName, exists);

        // 验证主记录已被删除
        if (exists.containsKey("storage_record")) {
            Boolean storageRecord = (Boolean) exists.get("storage_record");
            Assertions.assertFalse(storageRecord, serviceName + "的主存储记录应该已被回滚删除");
        }

        if (exists.containsKey("analysis_record")) {
            Boolean analysisRecord = (Boolean) exists.get("analysis_record");
            Assertions.assertFalse(analysisRecord, serviceName + "的主分析记录应该已被回滚删除");
        }

        if (exists.containsKey("notify_record")) {
            Boolean notifyRecord = (Boolean) exists.get("notify_record");
            Assertions.assertFalse(notifyRecord, serviceName + "的主通知记录应该已被回滚删除");
        }

        // 验证关联表数据已被清理
        if (exists.containsKey("process_history_count")) {
            Integer historyCount = (Integer) exists.get("process_history_count");
            Assertions.assertEquals(0, historyCount,
                serviceName + "的处理历史记录应该已被回滚清理，当前数量: " + historyCount);
        }

        if (exists.containsKey("tags_count")) {
            Integer tagsCount = (Integer) exists.get("tags_count");
            Assertions.assertEquals(0, tagsCount,
                serviceName + "的标签记录应该已被回滚清理，当前数量: " + tagsCount);
        }

        if (exists.containsKey("relationship_count")) {
            Integer relationshipCount = (Integer) exists.get("relationship_count");
            Assertions.assertEquals(0, relationshipCount,
                serviceName + "的关系记录应该已被回滚清理，当前数量: " + relationshipCount);
        }

        // 注意：统计表和配置表的验证需要特殊处理，因为它们可能包含其他数据
        if (exists.containsKey("statistics_count")) {
            Integer statisticsCount = (Integer) exists.get("statistics_count");
            log.info("{}的统计表记录数量: {} (可能包含其他测试数据)", serviceName, statisticsCount);
        }

        if (exists.containsKey("config_count")) {
            Integer configCount = (Integer) exists.get("config_count");
            log.info("{}的配置表记录数量: {} (可能包含其他配置数据)", serviceName, configCount);
        }

        log.info("✅ {}回滚验证通过 - 所有相关数据已成功清理", serviceName);
    }

    /**
     * 验证预设数据仍然存在
     */
    private void verifyPresetDataExists(String dataId) {
        log.info("验证预设数据是否存在，数据ID: {}", dataId);

        try {
            String checkUrl = CLOUD_STORAGE_URL + "/api/storage/get/" + dataId;
            ResponseEntity<Map> result = restTemplate.getForEntity(checkUrl, Map.class);

            Assertions.assertNotNull(result, "预设数据响应不应该为空");
            Assertions.assertEquals(200, result.getStatusCodeValue(), "预设数据查询应该成功");

            Map<String, Object> responseBody = result.getBody();
            Assertions.assertNotNull(responseBody, "预设数据响应体不应该为空");

            Boolean success = (Boolean) responseBody.get("success");
            Assertions.assertTrue(success, "预设数据查询应该成功");

            Object data = responseBody.get("data");
            Assertions.assertNotNull(data, "预设数据应该仍然存在，不应该被回滚影响");

            log.info("✅ 预设数据验证通过: 数据ID {} 仍然存在，未被回滚影响", dataId);

        } catch (Exception e) {
            log.error("验证预设数据失败", e);
            Assertions.fail("预设数据验证失败: " + e.getMessage());
        }
    }

    /**
     * 验证没有批量数据遗留
     */
    private void verifyNoBatchDataRemains(String baseDataId) {
        log.info("验证批量数据是否完全清理，基础数据ID: {}", baseDataId);

        try {
            // 检查云存储服务
            String storageCheckUrl = CLOUD_STORAGE_URL + "/api/storage/check/" + baseDataId;
            ResponseEntity<Map> storageResult = restTemplate.getForEntity(storageCheckUrl, Map.class);

            Map<String, Object> storageBody = storageResult.getBody();
            Map<String, Object> storageExists = (Map<String, Object>) storageBody.get("exists");

            // 验证所有批量相关的数据都已清理
            Integer historyCount = (Integer) storageExists.get("process_history_count");
            Integer relationshipCount = (Integer) storageExists.get("relationship_count");
            Integer tagsCount = (Integer) storageExists.get("tags_count");

            Assertions.assertEquals(0, historyCount, "批量处理历史应该完全清理");
            Assertions.assertEquals(0, relationshipCount, "批量关系数据应该完全清理");
            Assertions.assertEquals(0, tagsCount, "批量标签数据应该完全清理");

            log.info("✅ 批量数据清理验证通过");

        } catch (Exception e) {
            log.error("验证批量数据清理失败", e);
            Assertions.fail("批量数据清理验证失败: " + e.getMessage());
        }
    }

    /**
     * 验证跨表数据一致性
     */
    private void verifyCrossTableConsistency(String dataId) {
        log.info("验证跨表数据一致性，数据ID: {}", dataId);

        try {
            // 检查所有云服务的数据一致性
            String[] services = {"storage", "analysis", "notify"};
            String[] urls = {CLOUD_STORAGE_URL, CLOUD_ANALYSIS_URL, CLOUD_NOTIFY_URL};

            for (int i = 0; i < services.length; i++) {
                String checkUrl = urls[i] + "/api/" + services[i] + "/check/" + dataId;
                ResponseEntity<Map> result = restTemplate.getForEntity(checkUrl, Map.class);

                Map<String, Object> body = result.getBody();
                Map<String, Object> exists = (Map<String, Object>) body.get("exists");

                // 验证关联表数据的一致性
                Integer relationshipCount = (Integer) exists.get("relationship_count");
                if (relationshipCount != null) {
                    Assertions.assertEquals(0, relationshipCount,
                        services[i] + "服务的关系表数据应该已回滚清理");
                }

                Integer tagsCount = (Integer) exists.get("tags_count");
                if (tagsCount != null) {
                    Assertions.assertEquals(0, tagsCount,
                        services[i] + "服务的标签表数据应该已回滚清理");
                }

                log.info("✅ {}服务跨表数据一致性验证通过", services[i]);
            }

            log.info("✅ 所有服务跨表数据一致性验证通过");

        } catch (Exception e) {
            log.error("验证跨表数据一致性失败", e);
            Assertions.fail("跨表数据一致性验证失败: " + e.getMessage());
        }
    }
}
