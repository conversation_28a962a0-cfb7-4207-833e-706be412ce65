package com.tipray.at.dlp.test;

import com.tipray.at.dlp.AtDlpServiceApplication;
import com.tipray.at.dlp.dto.DataProcessRequest;
import com.tipray.at.dlp.entity.DlpDataRecord;
import com.tipray.at.dlp.test.base.BaseAtTransactionTest;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.*;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;
import java.util.Arrays;

import static org.junit.jupiter.api.Assertions.*;

/**
 * AT模式分布式事务顺序测试
 *
 * 测试事务执行顺序、依赖关系和顺序一致性
 *
 * <AUTHOR>
 * @version 2.0
 * @since 2025-05-10
 */
@Slf4j
@SpringBootTest(
        classes = AtDlpServiceApplication.class,
        webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT
)
@ActiveProfiles("test")
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
@DisplayName("AT模式分布式事务顺序测试")
public class AtTransactionOrderingTest extends BaseAtTransactionTest {

    @Test
    @Order(1)
    @DisplayName("测试顺序事务执行")
    void testSequentialTransactionExecution() {
        log.info("开始测试顺序事务执行");

        try {
            List<String> dataIds = new ArrayList<>();
            List<Long> executionTimes = new ArrayList<>();

            // 顺序执行多个事务
            for (int i = 0; i < 5; i++) {
                String dataId = generateTestDataId("SEQUENTIAL_" + i);
                dataIds.add(dataId);

                long startTime = System.currentTimeMillis();

                DataProcessRequest request = new DataProcessRequest();
                request.setDataId(dataId);
                request.setDataContent("顺序事务测试数据" + i);
                request.setDataType("JSON");
                request.setCallCloudServices(true);

                DlpDataRecord result = dlpDataService.processData(request);
                assertNotNull(result, "顺序事务" + i + "应该成功");
                assertEquals("SUCCESS", result.getStatus(), "顺序事务" + i + "状态应该是SUCCESS");

                long endTime = System.currentTimeMillis();
                executionTimes.add(endTime - startTime);

                log.info("顺序事务{}完成，耗时: {} ms", i, endTime - startTime);

                // 短暂等待，确保顺序性
                Thread.sleep(500);
            }

            // 等待所有异步操作完成
            waitForAsyncOperation(8000);

            // 验证所有事务都成功
            for (int i = 0; i < dataIds.size(); i++) {
                String dataId = dataIds.get(i);

                DlpDataRecord record = dlpDataRecordMapper.selectByDataId(dataId);
                assertNotNull(record, "顺序事务" + i + "记录应该存在");
                assertEquals("SUCCESS", record.getStatus(), "顺序事务" + i + "最终状态应该是SUCCESS");

                assertTrue(verifyAllCloudServicesData(dataId),
                    "顺序事务" + i + "应该在云服务中有数据");
            }

            log.info("顺序事务执行测试完成，所有{}个事务都成功", dataIds.size());

        } catch (Exception e) {
            log.error("顺序事务执行测试失败", e);
            fail("顺序事务执行测试不应该失败: " + e.getMessage());
        }
    }

    @Test
    @Order(2)
    @DisplayName("测试事务依赖关系")
    void testTransactionDependencies() {
        log.info("开始测试事务依赖关系");

        try {
            // 创建有依赖关系的事务链
            String parentDataId = generateTestDataId("PARENT_TRANSACTION");
            String childDataId = generateTestDataId("CHILD_TRANSACTION");

            // 1. 执行父事务
            DataProcessRequest parentRequest = new DataProcessRequest();
            parentRequest.setDataId(parentDataId);
            parentRequest.setDataContent("父事务数据");
            parentRequest.setDataType("JSON");
            parentRequest.setCallCloudServices(true);

            DlpDataRecord parentResult = dlpDataService.processData(parentRequest);
            assertNotNull(parentResult, "父事务应该成功");
            assertEquals("SUCCESS", parentResult.getStatus(), "父事务状态应该是SUCCESS");

            // 等待父事务完成
            waitForAsyncOperation(3000);

            // 2. 执行依赖于父事务的子事务
            DataProcessRequest childRequest = new DataProcessRequest();
            childRequest.setDataId(childDataId);
            childRequest.setDataContent("子事务数据");
            childRequest.setDataType("JSON");
            childRequest.setCallCloudServices(true);
            childRequest.setDependsOnTransaction(parentDataId); // 设置依赖

            DlpDataRecord childResult = dlpDataService.processData(childRequest);
            assertNotNull(childResult, "子事务应该成功");
            assertEquals("SUCCESS", childResult.getStatus(), "子事务状态应该是SUCCESS");

            // 等待子事务完成
            waitForAsyncOperation(3000);

            // 3. 验证依赖关系正确处理
            DlpDataRecord parentRecord = dlpDataRecordMapper.selectByDataId(parentDataId);
            DlpDataRecord childRecord = dlpDataRecordMapper.selectByDataId(childDataId);

            assertNotNull(parentRecord, "父事务记录应该存在");
            assertNotNull(childRecord, "子事务记录应该存在");

            // 验证执行时间顺序
            assertTrue(parentRecord.getCreateTime().compareTo(childRecord.getCreateTime()) <= 0,
                "父事务应该在子事务之前或同时创建");

            // 验证云服务数据
            assertTrue(verifyAllCloudServicesData(parentDataId), "父事务应该在云服务中有数据");
            assertTrue(verifyAllCloudServicesData(childDataId), "子事务应该在云服务中有数据");

            log.info("事务依赖关系测试完成");

        } catch (Exception e) {
            log.error("事务依赖关系测试失败", e);
            fail("事务依赖关系测试不应该失败: " + e.getMessage());
        }
    }

    @Test
    @Order(3)
    @DisplayName("测试并发事务的顺序一致性")
    void testConcurrentTransactionOrderConsistency() throws InterruptedException {
        log.info("开始测试并发事务的顺序一致性");

        int threadCount = 10;
        CountDownLatch startLatch = new CountDownLatch(1);
        CountDownLatch endLatch = new CountDownLatch(threadCount);
        List<String> dataIds = new ArrayList<>();

        // 准备数据ID列表
        for (int i = 0; i < threadCount; i++) {
            dataIds.add(generateTestDataId("CONCURRENT_ORDER_" + i));
        }

        // 启动多个并发事务
        for (int i = 0; i < threadCount; i++) {
            final int index = i;
            final String dataId = dataIds.get(i);

            new Thread(() -> {
                try {
                    startLatch.await();

                    DataProcessRequest request = new DataProcessRequest();
                    request.setDataId(dataId);
                    request.setDataContent("并发顺序测试数据" + index);
                    request.setDataType("JSON");
                    request.setCallCloudServices(true);
                    request.setOrderIndex(index); // 设置顺序索引

                    DlpDataRecord result = dlpDataService.processData(request);
                    log.info("并发事务{}完成，状态: {}", index,
                        result != null ? result.getStatus() : "null");

                } catch (Exception e) {
                    log.error("并发事务{}执行异常", index, e);
                } finally {
                    endLatch.countDown();
                }
            }).start();
        }

        // 启动所有线程
        startLatch.countDown();

        // 等待所有线程完成
        assertTrue(endLatch.await(60, TimeUnit.SECONDS), "并发事务应该在60秒内完成");

        // 等待异步操作完成
        waitForAsyncOperation(10000);

        // 验证顺序一致性
        List<DlpDataRecord> records = new ArrayList<>();
        for (String dataId : dataIds) {
            DlpDataRecord record = dlpDataRecordMapper.selectByDataId(dataId);
            if (record != null) {
                records.add(record);
            }
        }

        // 验证所有成功的事务都有对应的云服务数据
        for (DlpDataRecord record : records) {
            if ("SUCCESS".equals(record.getStatus())) {
                assertTrue(verifyAllCloudServicesData(record.getDataId()),
                    "成功的并发事务应该在云服务中有数据");
            }
        }

        log.info("并发事务顺序一致性测试完成，处理了{}个事务", records.size());
    }

    @Test
    @Order(4)
    @DisplayName("测试事务回滚的顺序性")
    void testTransactionRollbackOrdering() {
        log.info("开始测试事务回滚的顺序性");

        try {
            List<String> dataIds = new ArrayList<>();

            // 1. 创建一系列相关的事务
            for (int i = 0; i < 3; i++) {
                String dataId = generateTestDataId("ROLLBACK_ORDER_" + i);
                dataIds.add(dataId);

                DataProcessRequest request = new DataProcessRequest();
                request.setDataId(dataId);
                request.setDataContent("回滚顺序测试数据" + i);
                request.setDataType("JSON");
                request.setCallCloudServices(true);

                DlpDataRecord result = dlpDataService.processData(request);
                assertNotNull(result, "事务" + i + "应该成功");

                // 短暂等待确保顺序
                Thread.sleep(1000);
            }

            // 等待所有事务完成
            waitForAsyncOperation(5000);

            // 2. 触发最后一个事务的回滚，应该按相反顺序回滚相关事务
            String lastDataId = dataIds.get(dataIds.size() - 1);

            try {
                Map<String, Object> rollbackResult =
                    atComplexTestService.testOrderedRollbackScenario(lastDataId, dataIds);

                if (rollbackResult != null) {
                    Boolean rollbackTriggered = (Boolean) rollbackResult.get("rollbackTriggered");
                    assertTrue(rollbackTriggered != null && rollbackTriggered,
                        "应该触发有序回滚");

                    List<String> rollbackOrder = (List<String>) rollbackResult.get("rollbackOrder");
                    if (rollbackOrder != null && !rollbackOrder.isEmpty()) {
                        log.info("回滚顺序: {}", rollbackOrder);

                        // 验证回滚顺序是否正确（应该是创建顺序的逆序）
                        for (int i = 0; i < rollbackOrder.size() - 1; i++) {
                            String current = rollbackOrder.get(i);
                            String next = rollbackOrder.get(i + 1);

                            // 这里可以添加更具体的顺序验证逻辑
                            assertNotNull(current, "回滚顺序中的事务ID不应为空");
                            assertNotNull(next, "回滚顺序中的事务ID不应为空");
                        }
                    }
                }

            } catch (Exception e) {
                log.info("有序回滚测试异常: {}", e.getMessage());
            }

            // 等待回滚完成
            waitForAsyncOperation(8000);

            // 3. 验证回滚结果
            for (String dataId : dataIds) {
                DlpDataRecord record = dlpDataRecordMapper.selectByDataId(dataId);
                if (record != null) {
                    assertTrue(
                        "ROLLBACK_SUCCESS".equals(record.getStatus()) ||
                        "FAILED".equals(record.getStatus()),
                        "回滚后的事务状态应该正确"
                    );
                }

                // 验证云服务数据已清理
                assertFalse(verifyAllCloudServicesData(dataId),
                    "回滚后云服务不应该有数据");
            }

            log.info("事务回滚顺序性测试完成");

        } catch (Exception e) {
            log.error("事务回滚顺序性测试失败", e);
            fail("事务回滚顺序性测试不应该失败: " + e.getMessage());
        }
    }

    @Test
    @Order(5)
    @DisplayName("测试事务优先级排序")
    void testTransactionPriorityOrdering() {
        log.info("开始测试事务优先级排序");

        try {
            List<String> dataIds = new ArrayList<>();
            List<Integer> priorities = Arrays.asList(3, 1, 5, 2, 4); // 不同优先级

            // 创建不同优先级的事务
            for (int i = 0; i < priorities.size(); i++) {
                String dataId = generateTestDataId("PRIORITY_" + priorities.get(i) + "_" + i);
                dataIds.add(dataId);

                DataProcessRequest request = new DataProcessRequest();
                request.setDataId(dataId);
                request.setDataContent("优先级测试数据" + i);
                request.setDataType("JSON");
                request.setCallCloudServices(true);
                request.setPriority(priorities.get(i)); // 设置优先级

                // 快速提交所有事务
                int finalI = i;
                new Thread(() -> {
                    try {
                        DlpDataRecord result = dlpDataService.processData(request);
                        log.info("优先级{}事务完成，状态: {}", priorities.get(finalI),
                            result != null ? result.getStatus() : "null");
                    } catch (Exception e) {
                        log.error("优先级事务执行异常", e);
                    }
                }).start();

                // 短暂间隔
                Thread.sleep(100);
            }

            // 等待所有事务完成
            waitForAsyncOperation(10000);

            // 验证所有事务都成功
            for (String dataId : dataIds) {
                DlpDataRecord record = dlpDataRecordMapper.selectByDataId(dataId);
                if (record != null && "SUCCESS".equals(record.getStatus())) {
                    assertTrue(verifyAllCloudServicesData(dataId),
                        "成功的优先级事务应该在云服务中有数据");
                }
            }

            log.info("事务优先级排序测试完成");

        } catch (Exception e) {
            log.error("事务优先级排序测试失败", e);
            fail("事务优先级排序测试不应该失败: " + e.getMessage());
        }
    }

    @Test
    @Order(6)
    @DisplayName("测试事务链式执行")
    void testTransactionChainExecution() {
        log.info("开始测试事务链式执行");

        try {
            List<String> chainDataIds = new ArrayList<>();

            // 创建事务链
            for (int i = 0; i < 5; i++) {
                String dataId = generateTestDataId("CHAIN_" + i);
                chainDataIds.add(dataId);

                DataProcessRequest request = new DataProcessRequest();
                request.setDataId(dataId);
                request.setDataContent("链式事务数据" + i);
                request.setDataType("JSON");
                request.setCallCloudServices(true);

                // 设置前一个事务作为依赖（除了第一个）
                if (i > 0) {
                    request.setDependsOnTransaction(chainDataIds.get(i - 1));
                }

                DlpDataRecord result = dlpDataService.processData(request);
                assertNotNull(result, "链式事务" + i + "应该成功");
                assertEquals("SUCCESS", result.getStatus(), "链式事务" + i + "状态应该是SUCCESS");

                log.info("链式事务{}完成", i);

                // 等待当前事务完成再执行下一个
                waitForAsyncOperation(2000);
            }

            // 等待所有异步操作完成
            waitForAsyncOperation(5000);

            // 验证链式执行结果
            for (int i = 0; i < chainDataIds.size(); i++) {
                String dataId = chainDataIds.get(i);

                DlpDataRecord record = dlpDataRecordMapper.selectByDataId(dataId);
                assertNotNull(record, "链式事务" + i + "记录应该存在");
                assertEquals("SUCCESS", record.getStatus(), "链式事务" + i + "最终状态应该是SUCCESS");

                assertTrue(verifyAllCloudServicesData(dataId),
                    "链式事务" + i + "应该在云服务中有数据");

                // 验证执行顺序（后面的事务创建时间应该晚于前面的）
                if (i > 0) {
                    DlpDataRecord prevRecord = dlpDataRecordMapper.selectByDataId(chainDataIds.get(i - 1));
                    assertTrue(
                        prevRecord.getCreateTime().compareTo(record.getCreateTime()) <= 0,
                        "链式事务应该按顺序执行"
                    );
                }
            }

            log.info("事务链式执行测试完成，成功执行了{}个链式事务", chainDataIds.size());

        } catch (Exception e) {
            log.error("事务链式执行测试失败", e);
            fail("事务链式执行测试不应该失败: " + e.getMessage());
        }
    }

    @Test
    @Order(7)
    @DisplayName("测试事务批处理顺序")
    void testTransactionBatchProcessingOrder() {
        log.info("开始测试事务批处理顺序");

        try {
            // 创建一批事务
            List<DataProcessRequest> batchRequests = new ArrayList<>();
            List<String> batchDataIds = new ArrayList<>();

            for (int i = 0; i < 10; i++) {
                String dataId = generateTestDataId("BATCH_" + i);
                batchDataIds.add(dataId);

                DataProcessRequest request = new DataProcessRequest();
                request.setDataId(dataId);
                request.setDataContent("批处理测试数据" + i);
                request.setDataType("JSON");
                request.setCallCloudServices(true);
                request.setBatchIndex(i); // 设置批次索引

                batchRequests.add(request);
            }

            // 批量提交事务
            Map<String, Object> batchResult = atComplexTestService.processBatchTransactions(batchRequests);

            assertNotNull(batchResult, "批处理结果不应为空");

            Integer processedCount = (Integer) batchResult.get("processedCount");
            assertNotNull(processedCount, "应该有处理计数");
            assertTrue(processedCount > 0, "应该处理了一些事务");

            // 等待批处理完成
            waitForAsyncOperation(15000);

            // 验证批处理结果
            int successCount = 0;
            for (String dataId : batchDataIds) {
                DlpDataRecord record = dlpDataRecordMapper.selectByDataId(dataId);
                if (record != null && "SUCCESS".equals(record.getStatus())) {
                    successCount++;
                    assertTrue(verifyAllCloudServicesData(dataId),
                        "成功的批处理事务应该在云服务中有数据");
                }
            }

            assertTrue(successCount > 0, "批处理应该有成功的事务");
            log.info("事务批处理顺序测试完成，成功处理了{}个事务", successCount);

        } catch (Exception e) {
            log.error("事务批处理顺序测试失败", e);
            fail("事务批处理顺序测试不应该失败: " + e.getMessage());
        }
    }
}
