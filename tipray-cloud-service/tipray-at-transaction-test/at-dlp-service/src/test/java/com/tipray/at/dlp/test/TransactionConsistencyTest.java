package com.tipray.at.dlp.test;

import com.tipray.at.dlp.AtDlpServiceApplication;
import com.tipray.at.dlp.dto.DataProcessRequest;
import com.tipray.at.dlp.service.DlpDataService;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.test.context.ActiveProfiles;

import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;

/**
 * 事务一致性和UndoLog完整性测试
 * 专门测试框架的核心机制是否正确
 * 
 * <AUTHOR>
 */
@Slf4j
@SpringBootTest(classes = AtDlpServiceApplication.class)
@ActiveProfiles("test")
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
public class TransactionConsistencyTest {

    @Autowired
    private DlpDataService dlpDataService;

    @Autowired
    private JdbcTemplate jdbcTemplate;

    @BeforeAll
    static void setUpClass() {
        log.info("=== 开始事务一致性测试套件 ===");
    }

    @AfterAll
    static void tearDownClass() {
        log.info("=== 事务一致性测试套件结束 ===");
    }

    @Test
    @Order(1)
    @DisplayName("测试事务状态流转的正确性")
    void testTransactionStatusFlow() {
        String dataId = "STATUS_FLOW_" + System.currentTimeMillis();
        log.info("开始测试事务状态流转，数据ID: {}", dataId);

        try {
            DataProcessRequest request = new DataProcessRequest();
            request.setDataId(dataId);
            request.setDataContent("事务状态流转测试");
            request.setDataType("COMPLEX");
            request.setSimulateError(true);
            request.setErrorStep("cloud_notify");
            request.setRemark("事务状态流转测试");

            // 记录开始时间
            long startTime = System.currentTimeMillis();

            Exception exception = Assertions.assertThrows(Exception.class, () -> {
                dlpDataService.processData(request);
            }, "应该抛出异常触发回滚");

            log.info("捕获到预期异常: {}", exception.getMessage());

            // 等待事务完全处理完成
            Thread.sleep(3000);

            // 检查事务状态记录
            List<Map<String, Object>> transactions = jdbcTemplate.queryForList(
                "SELECT global_tx_id, status, create_time, update_time FROM distributed_transaction WHERE global_tx_id LIKE ?",
                "%" + dataId + "%"
            );

            Assertions.assertFalse(transactions.isEmpty(), "应该有事务记录");

            for (Map<String, Object> tx : transactions) {
                String status = (String) tx.get("status");
                log.info("事务状态: {}, 全局事务ID: {}", status, tx.get("global_tx_id"));
                
                // 验证最终状态应该是FAILED
                Assertions.assertTrue(
                    "FAILED".equals(status) || "ROLLBACK_COMPLETED".equals(status),
                    "事务最终状态应该是FAILED或ROLLBACK_COMPLETED，实际: " + status
                );
            }

            // 检查事务步骤状态
            List<Map<String, Object>> steps = jdbcTemplate.queryForList(
                "SELECT step_name, step_status, error_message FROM transaction_step WHERE transaction_id LIKE ?",
                "%" + dataId + "%"
            );

            log.info("事务步骤数量: {}", steps.size());
            for (Map<String, Object> step : steps) {
                log.info("步骤: {}, 状态: {}, 错误: {}", 
                    step.get("step_name"), step.get("step_status"), step.get("error_message"));
            }

            log.info("事务状态流转测试成功");

        } catch (Exception e) {
            log.error("事务状态流转测试失败", e);
            Assertions.fail("测试失败: " + e.getMessage());
        }
    }

    @Test
    @Order(2)
    @DisplayName("测试UndoLog生成的完整性")
    void testUndoLogCompleteness() {
        String dataId = "UNDO_LOG_" + System.currentTimeMillis();
        log.info("开始测试UndoLog生成完整性，数据ID: {}", dataId);

        try {
            DataProcessRequest request = new DataProcessRequest();
            request.setDataId(dataId);
            request.setDataContent("UndoLog完整性测试");
            request.setDataType("BATCH");
            request.setSimulateError(true);
            request.setErrorStep("cloud_analysis");
            request.setRemark("UndoLog完整性测试");

            Exception exception = Assertions.assertThrows(Exception.class, () -> {
                dlpDataService.processData(request);
            }, "应该抛出异常触发回滚");

            log.info("捕获到预期异常: {}", exception.getMessage());

            Thread.sleep(2000);

            // 检查各个云服务的UndoLog
            checkUndoLogInService("storage", dataId);
            checkUndoLogInService("analysis", dataId);
            checkUndoLogInService("notify", dataId);

            log.info("UndoLog完整性测试成功");

        } catch (Exception e) {
            log.error("UndoLog完整性测试失败", e);
            Assertions.fail("测试失败: " + e.getMessage());
        }
    }

    @Test
    @Order(3)
    @DisplayName("测试并发事务的隔离性")
    void testConcurrentTransactionIsolation() {
        String baseDataId = "ISOLATION_" + System.currentTimeMillis();
        log.info("开始测试并发事务隔离性，基础数据ID: {}", baseDataId);

        try {
            int concurrentCount = 3;
            CountDownLatch startLatch = new CountDownLatch(1);
            CountDownLatch finishLatch = new CountDownLatch(concurrentCount);

            // 并发执行多个事务
            for (int i = 0; i < concurrentCount; i++) {
                final int index = i;
                CompletableFuture.runAsync(() -> {
                    try {
                        startLatch.await(); // 等待统一开始信号

                        DataProcessRequest request = new DataProcessRequest();
                        request.setDataId(baseDataId + "_" + index);
                        request.setDataContent("并发隔离测试 " + index);
                        request.setDataType("MIXED");
                        request.setSimulateError(true);
                        request.setErrorStep("cloud_storage");
                        request.setRemark("并发隔离测试 " + index);

                        try {
                            dlpDataService.processData(request);
                        } catch (Exception e) {
                            log.info("并发事务{}预期异常: {}", index, e.getMessage());
                        }

                    } catch (InterruptedException e) {
                        Thread.currentThread().interrupt();
                    } finally {
                        finishLatch.countDown();
                    }
                });
            }

            // 统一开始
            startLatch.countDown();

            // 等待所有事务完成
            finishLatch.await(30, TimeUnit.SECONDS);

            // 等待回滚完成
            Thread.sleep(5000);

            // 验证每个事务的隔离性
            for (int i = 0; i < concurrentCount; i++) {
                String txDataId = baseDataId + "_" + i;
                
                // 检查事务记录
                List<Map<String, Object>> txRecords = jdbcTemplate.queryForList(
                    "SELECT global_tx_id, status FROM distributed_transaction WHERE global_tx_id LIKE ?",
                    "%" + txDataId + "%"
                );

                Assertions.assertFalse(txRecords.isEmpty(), "事务" + i + "应该有记录");
                
                for (Map<String, Object> record : txRecords) {
                    String status = (String) record.get("status");
                    Assertions.assertTrue(
                        "FAILED".equals(status) || "ROLLBACK_COMPLETED".equals(status),
                        "事务" + i + "状态应该是FAILED或ROLLBACK_COMPLETED"
                    );
                }
            }

            log.info("并发事务隔离性测试成功");

        } catch (Exception e) {
            log.error("并发事务隔离性测试失败", e);
            Assertions.fail("测试失败: " + e.getMessage());
        }
    }

    @Test
    @Order(4)
    @DisplayName("测试事务超时处理的正确性")
    void testTransactionTimeoutHandling() {
        String dataId = "TIMEOUT_" + System.currentTimeMillis();
        log.info("开始测试事务超时处理，数据ID: {}", dataId);

        try {
            DataProcessRequest request = new DataProcessRequest();
            request.setDataId(dataId);
            request.setDataContent("事务超时测试");
            request.setDataType("COMPLEX");
            request.setSimulateSlowCloudProcessing(true);
            request.setSimulateCloudServiceDelay(25000L); // 25秒延迟
            request.setRemark("事务超时测试");

            long startTime = System.currentTimeMillis();

            Exception exception = Assertions.assertThrows(Exception.class, () -> {
                dlpDataService.processData(request);
            }, "超时应该触发异常");

            long endTime = System.currentTimeMillis();
            long duration = endTime - startTime;

            log.info("事务超时异常: {}, 耗时: {}ms", exception.getMessage(), duration);

            // 验证超时时间是否合理（应该在超时时间附近，而不是等待全部25秒）
            Assertions.assertTrue(duration < 20000, "超时处理应该在20秒内完成，实际: " + duration + "ms");

            Thread.sleep(3000);

            // 检查超时事务的状态
            List<Map<String, Object>> timeoutTx = jdbcTemplate.queryForList(
                "SELECT global_tx_id, status FROM distributed_transaction WHERE global_tx_id LIKE ?",
                "%" + dataId + "%"
            );

            if (!timeoutTx.isEmpty()) {
                for (Map<String, Object> tx : timeoutTx) {
                    String status = (String) tx.get("status");
                    log.info("超时事务状态: {}", status);
                    Assertions.assertTrue(
                        "TIMEOUT".equals(status) || "FAILED".equals(status) || "ROLLBACK_COMPLETED".equals(status),
                        "超时事务状态应该是TIMEOUT、FAILED或ROLLBACK_COMPLETED"
                    );
                }
            }

            log.info("事务超时处理测试成功");

        } catch (Exception e) {
            log.error("事务超时处理测试失败", e);
            Assertions.fail("测试失败: " + e.getMessage());
        }
    }

    @Test
    @Order(5)
    @DisplayName("测试回滚操作的幂等性")
    void testRollbackIdempotency() {
        String dataId = "IDEMPOTENT_" + System.currentTimeMillis();
        log.info("开始测试回滚操作幂等性，数据ID: {}", dataId);

        try {
            DataProcessRequest request = new DataProcessRequest();
            request.setDataId(dataId);
            request.setDataContent("回滚幂等性测试");
            request.setDataType("STATISTICS");
            request.setSimulateError(true);
            request.setErrorStep("cloud_notify");
            request.setRemark("回滚幂等性测试");

            Exception exception = Assertions.assertThrows(Exception.class, () -> {
                dlpDataService.processData(request);
            }, "应该抛出异常触发回滚");

            log.info("捕获到预期异常: {}", exception.getMessage());

            Thread.sleep(3000);

            // 记录第一次回滚后的状态
            List<Map<String, Object>> firstRollbackState = jdbcTemplate.queryForList(
                "SELECT global_tx_id, status, update_time FROM distributed_transaction WHERE global_tx_id LIKE ?",
                "%" + dataId + "%"
            );

            // 模拟重复回滚（这在实际场景中可能发生）
            // 注意：这里需要根据实际的回滚接口来调用
            Thread.sleep(2000);

            // 记录第二次回滚后的状态
            List<Map<String, Object>> secondRollbackState = jdbcTemplate.queryForList(
                "SELECT global_tx_id, status, update_time FROM distributed_transaction WHERE global_tx_id LIKE ?",
                "%" + dataId + "%"
            );

            // 验证状态没有发生不正确的变化
            Assertions.assertEquals(firstRollbackState.size(), secondRollbackState.size(), 
                "重复回滚不应该改变事务记录数量");

            for (int i = 0; i < firstRollbackState.size(); i++) {
                String firstStatus = (String) firstRollbackState.get(i).get("status");
                String secondStatus = (String) secondRollbackState.get(i).get("status");
                
                Assertions.assertEquals(firstStatus, secondStatus, 
                    "重复回滚不应该改变事务状态");
            }

            log.info("回滚幂等性测试成功");

        } catch (Exception e) {
            log.error("回滚幂等性测试失败", e);
            Assertions.fail("测试失败: " + e.getMessage());
        }
    }

    /**
     * 检查指定服务的UndoLog
     */
    private void checkUndoLogInService(String serviceName, String dataId) {
        try {
            String tableName = "undo_log_" + serviceName;
            List<Map<String, Object>> undoLogs = jdbcTemplate.queryForList(
                "SELECT xid, branch_id, table_name, pk_value, gmt_create FROM " + tableName + " WHERE xid LIKE ?",
                "%" + dataId + "%"
            );

            log.info("{}服务UndoLog记录数: {}", serviceName, undoLogs.size());

            for (Map<String, Object> undoLog : undoLogs) {
                log.info("UndoLog - XID: {}, BranchID: {}, Table: {}, PK: {}", 
                    undoLog.get("xid"), undoLog.get("branch_id"), 
                    undoLog.get("table_name"), undoLog.get("pk_value"));
                
                // 验证UndoLog的基本字段
                Assertions.assertNotNull(undoLog.get("xid"), "XID不应该为空");
                Assertions.assertNotNull(undoLog.get("branch_id"), "BranchID不应该为空");
                Assertions.assertNotNull(undoLog.get("table_name"), "表名不应该为空");
            }

        } catch (Exception e) {
            log.warn("检查{}服务UndoLog时出现异常: {}", serviceName, e.getMessage());
            // 某些情况下UndoLog表可能不存在，这是可以接受的
        }
    }
}
