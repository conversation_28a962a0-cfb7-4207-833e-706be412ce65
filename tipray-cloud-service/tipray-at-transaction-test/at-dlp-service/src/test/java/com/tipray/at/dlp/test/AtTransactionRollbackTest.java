package com.tipray.at.dlp.test;

import com.tipray.at.dlp.dto.DataProcessRequest;
import com.tipray.at.dlp.dto.DataUpdateRequest;
import com.tipray.at.dlp.entity.DlpDataRecord;
import com.tipray.at.dlp.test.base.BaseAtTransactionTest;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestMethodOrder;
import org.junit.jupiter.api.MethodOrderer;
import org.junit.jupiter.api.Order;
import org.springframework.test.annotation.DirtiesContext;

import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

/**
 * AT模式分布式事务回滚测试
 *
 * 测试AT模式的事务回滚功能，包括：
 * 1. 本地事务回滚
 * 2. 分布式事务回滚
 * 3. UndoLog回滚
 * 4. 异常场景回滚
 *
 * <AUTHOR>
 * @version 2.0
 * @since 2025-05-08
 */
@Slf4j
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
@DirtiesContext(classMode = DirtiesContext.ClassMode.AFTER_EACH_TEST_METHOD)
public class AtTransactionRollbackTest extends BaseAtTransactionTest {

    @Test
    @Order(1)
    @DisplayName("测试本地异常触发的事务回滚")
    void testLocalExceptionRollback() {
        String dataId = generateTestDataId("LOCAL_ROLLBACK");

        // 准备会触发本地异常的请求
        DataProcessRequest request = new DataProcessRequest();
        request.setDataId(dataId);
        request.setDataContent("本地异常回滚测试");
        request.setDataType("JSON");
        request.setSimulateError(true); // 模拟本地异常
        request.setErrorStep("local");

        // 执行事务，期望抛出异常
        assertThrows(Exception.class, () -> {
            dlpDataService.processData(request);
        }, "应该抛出异常");

        // 等待回滚操作完成
        waitForAsyncOperation(3000);

        // 验证DLP数据已回滚（数据库中不应该有记录或状态为ROLLBACK）
        DlpDataRecord dbRecord = dlpDataRecordMapper.selectByDataId(dataId);
        if (dbRecord != null) {
            // 如果记录存在，状态应该是ROLLBACK或FAILED
            assertTrue("ROLLBACK".equals(dbRecord.getStatus()) || "FAILED".equals(dbRecord.getStatus()),
                "记录状态应该表明事务已回滚");
        }
        // 如果记录不存在，说明回滚成功

        // 验证云服务数据也已回滚
        assertTrue(verifyCloudServicesRollback(dataId), "云服务数据应该已回滚");
    }

    @Test
    @Order(2)
    @DisplayName("测试云存储服务异常导致的分布式事务回滚")
    void testCloudStorageExceptionRollback() {
        String dataId = generateTestDataId("STORAGE_EXCEPTION");

        // 模拟云存储服务异常
        assertThrows(Exception.class, () -> {
            DataProcessRequest request = new DataProcessRequest();
            request.setDataId(dataId);
            request.setDataContent("云存储异常测试数据");
            request.setDataType("JSON");
            request.setCallCloudServices(true);
            request.setSimulateStorageError(true); // 模拟云存储异常

            dlpDataService.processData(request);
        }, "应该抛出云存储异常");

        // 等待回滚操作完成
        waitForAsyncOperation(5000);

        // 验证分布式事务回滚的一致性：
        // 1. DLP数据应该已回滚
        DlpDataRecord dlpRecord = dlpDataRecordMapper.selectByDataId(dataId);
        assertTrue(dlpRecord == null || !"SUCCESS".equals(dlpRecord.getStatus()),
                "DLP数据应该已回滚或不存在");

        // 2. 所有云服务数据都应该不存在（因为整个分布式事务回滚）
        assertFalse(verifyCloudStorageData(dataId), "云存储数据应该不存在");
        assertFalse(verifyCloudAnalysisData(dataId), "云分析数据应该不存在");
        assertFalse(verifyCloudNotifyData(dataId), "云通知数据应该不存在");

        log.info("云存储服务异常回滚测试完成，分布式事务正确回滚，保证了数据一致性");
    }

    @Test
    @Order(3)
    @DisplayName("测试全局事务超时导致的分布式事务回滚")
    void testGlobalTransactionTimeoutRollback() {
        String dataId = generateTestDataId("GLOBAL_TIMEOUT");

        // 模拟全局事务超时
        assertThrows(Exception.class, () -> {
            DataProcessRequest request = new DataProcessRequest();
            request.setDataId(dataId);
            request.setDataContent("全局事务超时测试数据");
            request.setDataType("JSON");
            request.setCallCloudServices(true);
            request.setTimeoutMillis(1000L); // 设置1秒超时
            request.setSimulateSlowProcessing(true); // 模拟慢处理，超过超时时间

            dlpDataService.processData(request);
        }, "应该抛出全局事务超时异常");

        // 等待回滚操作完成
        waitForAsyncOperation(5000);

        // 验证全局事务超时回滚的一致性：
        // 1. DLP数据应该已回滚
        DlpDataRecord dlpRecord = dlpDataRecordMapper.selectByDataId(dataId);
        assertTrue(dlpRecord == null || !"SUCCESS".equals(dlpRecord.getStatus()),
                "DLP数据应该已回滚或不存在");

        // 2. 所有云服务数据都应该不存在（因为全局事务超时回滚）
        assertFalse(verifyCloudStorageData(dataId), "云存储数据应该不存在");
        assertFalse(verifyCloudAnalysisData(dataId), "云分析数据应该不存在");
        assertFalse(verifyCloudNotifyData(dataId), "云通知数据应该不存在");

        log.info("全局事务超时回滚测试完成，分布式事务正确回滚，保证了数据一致性");
    }

    @Test
    @Order(4)
    @DisplayName("测试分支事务超时导致的分布式事务回滚")
    void testBranchTransactionTimeoutRollback() {
        String dataId = generateTestDataId("BRANCH_TIMEOUT");

        // 模拟分支事务超时
        assertThrows(Exception.class, () -> {
            DataProcessRequest request = new DataProcessRequest();
            request.setDataId(dataId);
            request.setDataContent("分支事务超时测试数据");
            request.setDataType("JSON");
            request.setCallCloudServices(true);
            request.setBranchTimeoutMillis(2000L); // 设置分支事务2秒超时
            request.setSimulateSlowCloudProcessing(true); // 模拟云服务慢处理

            dlpDataService.processData(request);
        }, "应该抛出分支事务超时异常");

        // 等待回滚操作完成
        waitForAsyncOperation(5000);

        // 验证分支事务超时回滚的一致性：
        // 1. DLP数据应该已回滚
        DlpDataRecord dlpRecord = dlpDataRecordMapper.selectByDataId(dataId);
        assertTrue(dlpRecord == null || !"SUCCESS".equals(dlpRecord.getStatus()),
                "DLP数据应该已回滚或不存在");

        // 2. 所有云服务数据都应该不存在（因为分支事务超时回滚）
        assertFalse(verifyCloudStorageData(dataId), "云存储数据应该不存在");
        assertFalse(verifyCloudAnalysisData(dataId), "云分析数据应该不存在");
        assertFalse(verifyCloudNotifyData(dataId), "云通知数据应该不存在");

        log.info("分支事务超时回滚测试完成，分布式事务正确回滚，保证了数据一致性");
    }

    @Test
    @Order(5)
    @DisplayName("测试云服务HTTP调用超时导致的分布式事务回滚")
    void testCloudServiceHttpTimeoutRollback() {
        String dataId = generateTestDataId("HTTP_TIMEOUT");

        // 模拟云服务HTTP调用超时
        assertThrows(Exception.class, () -> {
            DataProcessRequest request = new DataProcessRequest();
            request.setDataId(dataId);
            request.setDataContent("HTTP超时测试数据");
            request.setDataType("JSON");
            request.setCallCloudServices(true);
            request.setHttpTimeoutMillis(1000L); // 设置HTTP调用1秒超时
            request.setSimulateCloudServiceDelay(5000L); // 模拟云服务5秒延迟

            dlpDataService.processData(request);
        }, "应该抛出HTTP调用超时异常");

        // 等待回滚操作完成
        waitForAsyncOperation(5000);

        // 验证HTTP超时回滚的一致性：
        // 1. DLP数据应该已回滚
        DlpDataRecord dlpRecord = dlpDataRecordMapper.selectByDataId(dataId);
        assertTrue(dlpRecord == null || !"SUCCESS".equals(dlpRecord.getStatus()),
                "DLP数据应该已回滚或不存在");

        // 2. 所有云服务数据都应该不存在（因为HTTP超时导致调用失败）
        assertFalse(verifyCloudStorageData(dataId), "云存储数据应该不存在");
        assertFalse(verifyCloudAnalysisData(dataId), "云分析数据应该不存在");
        assertFalse(verifyCloudNotifyData(dataId), "云通知数据应该不存在");

        log.info("云服务HTTP超时回滚测试完成，分布式事务正确回滚，保证了数据一致性");
    }

    @Test
    @Order(6)
    @DisplayName("测试本地事务执行过久导致的分布式事务回滚")
    void testLocalTransactionLongRunningRollback() {
        String dataId = generateTestDataId("LOCAL_LONG_RUNNING");

        // 模拟本地事务执行过久
        assertThrows(Exception.class, () -> {
            DataProcessRequest request = new DataProcessRequest();
            request.setDataId(dataId);
            request.setDataContent("本地长事务测试数据");
            request.setDataType("JSON");
            request.setCallCloudServices(true);
            request.setTimeoutMillis(3000L); // 设置3秒超时
            request.setSimulateLocalSlowProcessing(true); // 模拟本地慢处理
            request.setLocalProcessingDelayMillis(5000L); // 本地处理延迟5秒

            dlpDataService.processData(request);
        }, "应该抛出本地事务超时异常");

        // 等待回滚操作完成
        waitForAsyncOperation(5000);

        // 验证本地长事务超时回滚的一致性：
        // 1. DLP数据应该已回滚
        DlpDataRecord dlpRecord = dlpDataRecordMapper.selectByDataId(dataId);
        assertTrue(dlpRecord == null || !"SUCCESS".equals(dlpRecord.getStatus()),
                "DLP数据应该已回滚或不存在");

        // 2. 所有云服务数据都应该不存在（因为本地事务超时回滚）
        assertFalse(verifyCloudStorageData(dataId), "云存储数据应该不存在");
        assertFalse(verifyCloudAnalysisData(dataId), "云分析数据应该不存在");
        assertFalse(verifyCloudNotifyData(dataId), "云通知数据应该不存在");

        log.info("本地长事务超时回滚测试完成，分布式事务正确回滚，保证了数据一致性");
    }

    @Test
    @Order(7)
    @DisplayName("测试网络异常触发的事务回滚")
    void testNetworkExceptionRollback() {
        String dataId = generateTestDataId("NETWORK_ROLLBACK");

        // 准备会触发网络异常的请求
        DataProcessRequest request = new DataProcessRequest();
        request.setDataId(dataId);
        request.setDataContent("网络异常回滚测试");
        request.setDataType("JSON");
        request.setSimulateNetworkError(true); // 模拟网络异常

        // 执行事务，期望抛出异常或返回失败结果
        try {
            DlpDataRecord result = dlpDataService.processData(request);
            if (result != null) {
                assertNotEquals("SUCCESS", result.getStatus(), "网络异常时事务不应该成功");
            }
        } catch (Exception e) {
            // 期望的网络异常
            assertTrue(true, "网络异常正确触发了回滚");
        }

        // 等待回滚操作完成
        waitForAsyncOperation(3000);

        // 验证DLP数据已回滚
        assertTrue(verifyTransactionRollback(dataId), "DLP事务应该已回滚");

        // 验证云服务数据已回滚
        assertTrue(verifyCloudServicesRollback(dataId), "云服务数据应该已回滚");

        log.info("网络异常回滚测试完成，DLP和云服务数据都已正确回滚");
    }

    @Test
    @Order(5)
    @DisplayName("测试部分成功场景的事务回滚")
    void testPartialSuccessRollback() {
        String dataId = generateTestDataId("PARTIAL_ROLLBACK");

        try {
            // 测试部分步骤成功，部分步骤失败的场景
            Map<String, Object> result = atComplexTestService.testPartialFailureScenario(dataId);

            // 验证整个事务最终失败
            if (result != null) {
                assertFalse((Boolean) result.getOrDefault("success", true), "部分失败场景应该导致整个事务失败");
            }

        } catch (Exception e) {
            // 期望的异常
            assertTrue(true, "部分失败正确触发了整个事务回滚");
        }

        // 等待回滚操作完成
        waitForAsyncOperation(3000);

        // 验证DLP数据已回滚
        assertTrue(verifyTransactionRollback(dataId), "DLP数据应该已回滚");

        // 验证云服务数据已回滚
        assertTrue(verifyCloudServicesRollback(dataId), "云服务数据应该已回滚");

        log.info("部分成功回滚测试完成，DLP和云服务数据都已正确回滚");
    }

    @Test
    @Order(6)
    @DisplayName("测试并发事务的回滚隔离")
    void testConcurrentTransactionRollbackIsolation() throws InterruptedException {
        String successDataId = generateTestDataId("CONCURRENT_SUCCESS");
        String failDataId = generateTestDataId("CONCURRENT_FAIL");

        Thread successThread = new Thread(() -> {
            try {
                DataProcessRequest request = new DataProcessRequest();
                request.setDataId(successDataId);
                request.setDataContent("并发成功事务");
                request.setDataType("JSON");
                request.setSimulateError(false);

                DlpDataRecord result = dlpDataService.processData(request);
                assertNotNull(result, "成功事务结果不应为空");
                assertEquals("SUCCESS", result.getStatus(), "成功事务状态应该为SUCCESS");

            } catch (Exception e) {
                fail("成功事务不应该抛出异常: " + e.getMessage());
            }
        });

        Thread failThread = new Thread(() -> {
            try {
                DataProcessRequest request = new DataProcessRequest();
                request.setDataId(failDataId);
                request.setDataContent("并发失败事务");
                request.setDataType("JSON");
                request.setSimulateError(true);
                request.setErrorStep("local");

                dlpDataService.processData(request);
                fail("失败事务应该抛出异常");

            } catch (Exception e) {
                // 期望的异常
                assertTrue(true, "失败事务正确抛出异常");
            }
        });

        // 启动两个并发事务
        successThread.start();
        failThread.start();

        // 等待两个线程完成
        successThread.join(10000);
        failThread.join(10000);

        // 等待异步操作完成
        waitForAsyncOperation(3000);

        // 验证成功事务的DLP数据仍然存在
        assertTrue(verifyTransactionSuccess(successDataId), "成功事务的DLP数据应该保留");

        // 验证成功事务的云服务数据仍然存在
        assertTrue(verifyAllCloudServicesData(successDataId), "成功事务的云服务数据应该保留");

        // 验证失败事务的DLP数据已回滚
        assertTrue(verifyTransactionRollback(failDataId), "失败事务的DLP数据应该已回滚");

        // 验证失败事务的云服务数据已回滚
        assertTrue(verifyCloudServicesRollback(failDataId), "失败事务的云服务数据应该已回滚");

        log.info("并发事务回滚隔离测试完成，成功事务数据保留，失败事务数据回滚");
    }

    @Test
    @Order(7)
    @DisplayName("测试嵌套事务的回滚")
    void testNestedTransactionRollback() {
        String dataId = generateTestDataId("NESTED_ROLLBACK");

        try {
            // 测试嵌套事务回滚场景
            Map<String, Object> result = atComplexTestService.testNestedTransactionRollback(dataId);

            // 验证嵌套事务回滚结果
            if (result != null) {
                assertFalse((Boolean) result.getOrDefault("success", true), "嵌套事务回滚测试应该失败");
            }

        } catch (Exception e) {
            // 期望的异常
            assertTrue(true, "嵌套事务回滚正确触发");
        }

        // 等待回滚操作完成
        waitForAsyncOperation(3000);

        // 验证嵌套事务的DLP数据都已回滚
        assertTrue(verifyTransactionRollback(dataId + "_OUTER"), "外层事务DLP数据应该已回滚");
        assertTrue(verifyTransactionRollback(dataId + "_INNER"), "内层事务DLP数据应该已回滚");

        // 验证嵌套事务的云服务数据都已回滚
        assertTrue(verifyCloudServicesRollback(dataId + "_OUTER"), "外层事务云服务数据应该已回滚");
        assertTrue(verifyCloudServicesRollback(dataId + "_INNER"), "内层事务云服务数据应该已回滚");

        log.info("嵌套事务回滚测试完成，所有层级的DLP和云服务数据都已正确回滚");
        assertTrue(verifyTransactionRollback(dataId), "嵌套事务的所有数据都应该已回滚");
    }

    @Test
    @Order(8)
    @DisplayName("测试UndoLog的生成和回滚")
    void testUndoLogGenerationAndRollback() {
        String dataId = generateTestDataId("UNDOLOG_ROLLBACK");

        // 先执行一个成功的事务，生成UndoLog
        DataProcessRequest successRequest = new DataProcessRequest();
        successRequest.setDataId(dataId);
        successRequest.setDataContent("UndoLog测试数据");
        successRequest.setDataType("JSON");
        successRequest.setSimulateError(false);

        DlpDataRecord successResult = dlpDataService.processData(successRequest);
        assertNotNull(successResult, "成功事务结果不应为空");
        assertEquals("SUCCESS", successResult.getStatus(), "事务应该成功");

        // 等待UndoLog生成
        waitForAsyncOperation(1000);

        // 然后执行一个会失败的更新操作，触发回滚
        try {
            DataUpdateRequest failRequest = new DataUpdateRequest();
            failRequest.setDataId(dataId);
            failRequest.setNewRemark("测试更新");
            failRequest.setSimulateError(true);
            failRequest.setErrorStep("local");

            dlpDataService.updateData(failRequest);
            fail("失败事务应该抛出异常");

        } catch (Exception e) {
            // 期望的异常
            assertTrue(true, "失败事务正确抛出异常");
        }

        // 等待回滚操作完成
        waitForAsyncOperation(3000);

        // 验证DLP数据已回滚到原始状态
        DlpDataRecord finalRecord = dlpDataRecordMapper.selectByDataId(dataId);
        if (finalRecord != null) {
            assertEquals("UndoLog测试数据", finalRecord.getDataContent(), "DLP数据应该回滚到原始内容");
        }

        // 验证云服务数据状态
        // 由于是回滚测试，云服务数据应该保持原始状态或被回滚
        assertTrue(verifyAllCloudServicesData(dataId), "云服务数据应该保持原始状态");

        log.info("UndoLog回滚机制测试完成，DLP数据回滚到原始状态，云服务数据保持一致");
    }

    @Test
    @Order(9)
    @DisplayName("测试回滚失败的处理")
    void testRollbackFailureHandling() {
        // 这个测试比较复杂，需要模拟回滚失败的场景
        // 例如：UndoLog损坏、数据库连接失败等

        String dataId = generateTestDataId("ROLLBACK_FAILURE");

        try {
            // 测试回滚失败场景
            Map<String, Object> result = atComplexTestService.testRollbackFailureScenario(dataId);

            // 验证回滚失败的处理
            if (result != null) {
                // 检查是否正确处理了回滚失败
                String status = (String) result.get("status");
                assertTrue("MANUAL_INTERVENTION_REQUIRED".equals(status) || "ROLLBACK_FAILED".equals(status),
                    "回滚失败应该标记为需要人工干预");
            }

        } catch (Exception e) {
            // 回滚失败可能抛出特定异常
            assertTrue(e.getMessage().contains("rollback") || e.getMessage().contains("回滚"),
                "应该是回滚相关的异常");
        }

        // 等待处理完成
        waitForAsyncOperation(3000);

        // 验证云服务数据状态
        // 回滚失败时，云服务数据可能处于不一致状态
        log.info("回滚失败处理测试完成，系统正确处理了回滚失败场景");
    }
}
