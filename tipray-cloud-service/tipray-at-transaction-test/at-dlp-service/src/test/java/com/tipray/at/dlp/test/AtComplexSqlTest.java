package com.tipray.at.dlp.test;

import com.tipray.at.dlp.dto.DataProcessRequest;
import com.tipray.at.dlp.dto.DataUpdateRequest;
import com.tipray.at.dlp.entity.DlpDataRecord;
import com.tipray.at.dlp.test.base.BaseAtTransactionTest;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestMethodOrder;
import org.junit.jupiter.api.MethodOrderer;
import org.junit.jupiter.api.Order;
import org.springframework.test.annotation.DirtiesContext;

import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.List;
import java.util.ArrayList;
import java.util.Map;
import java.util.HashMap;
import java.util.Random;

import static org.junit.jupiter.api.Assertions.*;

/**
 * AT模式复杂SQL场景测试
 *
 * 重点测试云服务端的复杂SQL回滚能力：
 * 1. 多表关联操作
 * 2. 批量插入/更新/删除
 * 3. 循环SQL执行
 * 4. 复杂条件查询
 * 5. 事务回滚验证
 *
 * <AUTHOR>
 * @version 2.0
 * @since 2025-01-20
 */
@Slf4j
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
@DirtiesContext(classMode = DirtiesContext.ClassMode.AFTER_EACH_TEST_METHOD)
public class AtComplexSqlTest extends BaseAtTransactionTest {

    @Test
    @Order(1)
    @DisplayName("测试多表关联插入操作")
    void testMultiTableInsertOperations() {
        log.info("开始多表关联插入操作测试");

        String testId = generateTestDataId("MULTI_TABLE_INSERT");

        try {
            // 测试多表关联插入
            Map<String, Object> result = atComplexTestService.testMultiTableInsert(testId, 10, false);

            assertNotNull(result, "多表插入结果不应为空");
            assertTrue((Boolean) result.get("success"), "多表插入应该成功");

            // 等待云服务处理完成
            waitForAsyncOperation(5000);

            // 临时禁用云服务验证，避免卡住
             assertTrue(verifyCloudStorageData(testId), "云服务数据应该正确创建");

            log.info("多表关联插入操作测试成功");

        } catch (Exception e) {
            log.error("多表关联插入操作测试失败", e);
            fail("多表关联插入操作不应该失败: " + e.getMessage());
        }
    }

    @Test
    @Order(2)
    @DisplayName("测试多表关联插入回滚")
    void testMultiTableInsertRollback() {
        log.info("开始多表关联插入回滚测试");

        String testId = generateTestDataId("MULTI_TABLE_INSERT_ROLLBACK");

        try {
            try {
                // 测试多表关联插入（模拟失败）
                Map<String, Object> result = atComplexTestService.testMultiTableInsert(testId, 10, true);

                assertNotNull(result, "多表插入回滚结果不应为空");
                assertFalse((Boolean) result.get("success"), "多表插入应该失败并回滚");
            } catch (Exception e) {

            }


            // 等待云服务处理完成
            waitForAsyncOperation(5000);

            // 验证云服务数据已回滚（不应该存在）
            assertFalse(verifyCloudStorageData(testId), "云服务数据应该已回滚，不应该存在");

            // 验证DLP数据也已回滚
            DlpDataRecord dlpRecord = dlpDataRecordMapper.selectByDataId(testId);
            assertNull(dlpRecord, "DLP数据应该已回滚，不应该存在");

            log.info("多表关联插入回滚测试成功");

        } catch (Exception e) {
            log.error("多表关联插入回滚测试失败", e);
            fail("多表关联插入回滚测试不应该失败: " + e.getMessage());
        }
    }

    @Test
    @Order(3)
    @DisplayName("测试批量更新操作")
    void testBatchUpdateOperations() {
        log.info("开始批量更新操作测试");

        // 先创建一些测试数据
        List<String> testDataIds = new ArrayList<>();
        for (int i = 0; i < 20; i++) {
            String dataId = generateTestDataId("BATCH_UPDATE_" + i);
            DataProcessRequest request = new DataProcessRequest();
            request.setDataId(dataId);
            request.setDataContent("初始数据_" + i);
            request.setDataType("JSON");
            request.setSimulateError(false);

            try {
                DlpDataRecord result = dlpDataService.processData(request);
                assertNotNull(result, "创建测试数据应该成功");
                testDataIds.add(dataId);
            } catch (Exception e) {
                log.error("创建测试数据失败", e);
            }
        }

        // 等待云服务处理完成
        waitForAsyncOperation(5000);

        // 执行批量更新
        String updateTestId = generateTestDataId("BATCH_UPDATE_TEST");
        try {
            Map<String, Object> result = atComplexTestService.testBatchUpdate(updateTestId, testDataIds.size(), false);

            assertNotNull(result, "批量更新结果不应为空");
            assertTrue((Boolean) result.get("success"), "批量更新应该成功");

            // 等待云服务处理完成
            waitForAsyncOperation(5000);

            // 验证云服务数据
            assertTrue(verifyCloudStorageData(updateTestId), "云服务批量更新数据应该正确");

            log.info("批量更新操作测试成功");

        } catch (Exception e) {
            log.error("批量更新操作测试失败", e);
            fail("批量更新操作不应该失败: " + e.getMessage());
        }
    }

    @Test
    @Order(4)
    @DisplayName("测试批量更新回滚")
    void testBatchUpdateRollback() {
        log.info("开始批量更新回滚测试");

        // 先创建一些测试数据
        List<String> testDataIds = new ArrayList<>();
        for (int i = 0; i < 15; i++) {
            String dataId = generateTestDataId("BATCH_UPDATE_ROLLBACK_" + i);
            DataProcessRequest request = new DataProcessRequest();
            request.setDataId(dataId);
            request.setDataContent("原始数据_" + i);
            request.setDataType("JSON");
            request.setSimulateError(false);

            try {
                DlpDataRecord result = dlpDataService.processData(request);
                assertNotNull(result, "创建测试数据应该成功");
                testDataIds.add(dataId);
            } catch (Exception e) {
                log.error("创建测试数据失败", e);
            }
        }

        // 等待云服务处理完成
        waitForAsyncOperation(5000);

        // 记录更新前的数据状态
        Map<String, String> originalData = new HashMap<>();
        for (String dataId : testDataIds) {
            try {
                DlpDataRecord record = dlpDataRecordMapper.selectByDataId(dataId);
                if (record != null) {
                    originalData.put(dataId, record.getDataContent());
                }
            } catch (Exception e) {
                log.error("查询原始数据失败", e);
            }
        }

        // 执行批量更新（模拟失败）
        String updateTestId = generateTestDataId("BATCH_UPDATE_ROLLBACK_TEST");
        try {
            Map<String, Object> result = atComplexTestService.testBatchUpdate(updateTestId, testDataIds.size(), true);

            assertNotNull(result, "批量更新回滚结果不应为空");
            assertFalse((Boolean) result.get("success"), "批量更新应该失败并回滚");

            // 等待云服务处理完成
            waitForAsyncOperation(5000);

            // 验证数据已回滚到原始状态
            for (String dataId : testDataIds) {
                try {
                    DlpDataRecord record = dlpDataRecordMapper.selectByDataId(dataId);
                    if (record != null && originalData.containsKey(dataId)) {
                        assertEquals(originalData.get(dataId), record.getDataContent(),
                                "数据应该回滚到原始状态: " + dataId);
                    }
                } catch (Exception e) {
                    log.error("验证回滚数据失败", e);
                }
            }

            // 验证云服务数据也已回滚
            assertFalse(verifyAllCloudServicesData(updateTestId), "云服务更新数据应该已回滚");

            log.info("批量更新回滚测试成功");

        } catch (Exception e) {
            log.error("批量更新回滚测试失败", e);
            fail("批量更新回滚测试不应该失败: " + e.getMessage());
        }
    }

    @Test
    @Order(5)
    @DisplayName("测试循环SQL执行")
    void testLoopSqlExecution() {
        log.info("开始循环SQL执行测试");

        String testId = generateTestDataId("LOOP_SQL_TEST");

        try {
            // 测试循环SQL执行
            Map<String, Object> result = atComplexTestService.testLoopSqlExecution(testId, 50, false);

            assertNotNull(result, "循环SQL执行结果不应为空");
            assertTrue((Boolean) result.get("success"), "循环SQL执行应该成功");

            // 等待云服务处理完成
            waitForAsyncOperation(8000);

            // 验证云服务数据
            assertTrue(verifyAllCloudServicesData(testId), "云服务循环SQL数据应该正确创建");

            log.info("循环SQL执行测试成功");

        } catch (Exception e) {
            log.error("循环SQL执行测试失败", e);
            fail("循环SQL执行不应该失败: " + e.getMessage());
        }
    }

    @Test
    @Order(6)
    @DisplayName("测试循环SQL执行回滚")
    void testLoopSqlExecutionRollback() {
        log.info("开始循环SQL执行回滚测试");

        String testId = generateTestDataId("LOOP_SQL_ROLLBACK_TEST");

        try {
            try {
                // 测试循环SQL执行（模拟失败）
                Map<String, Object> result = atComplexTestService.testLoopSqlExecution(testId, 30, true);

                assertNotNull(result, "循环SQL执行回滚结果不应为空");
                assertFalse((Boolean) result.get("success"), "循环SQL执行应该失败并回滚");
            } catch (Exception e) {

            }


            // 等待云服务处理完成
            waitForAsyncOperation(8000);

            // 验证云服务数据已回滚
            assertFalse(verifyCloudStorageData(testId), "云服务循环SQL数据应该已回滚");

            // 验证DLP数据也已回滚
            DlpDataRecord dlpRecord = dlpDataRecordMapper.selectByDataId(testId);
            assertNull(dlpRecord, "DLP循环SQL数据应该已回滚");

            log.info("循环SQL执行回滚测试成功");

        } catch (Exception e) {
            log.error("循环SQL执行回滚测试失败", e);
            fail("循环SQL执行回滚测试不应该失败: " + e.getMessage());
        }
    }

    @Test
    @Order(7)
    @DisplayName("测试混合复杂操作")
    void testMixedComplexOperations() {
        log.info("开始混合复杂操作测试");

        String testId = generateTestDataId("MIXED_COMPLEX_TEST");

        try {
            // 测试混合复杂操作（插入+更新+删除）
            Map<String, Object> result = atComplexTestService.testMixedComplexOperations(testId, 25, false);

            assertNotNull(result, "混合复杂操作结果不应为空");
            assertTrue((Boolean) result.get("success"), "混合复杂操作应该成功");

            // 等待云服务处理完成
            waitForAsyncOperation(10000);

            // 验证云服务数据
            assertTrue(verifyAllCloudServicesData(testId), "云服务混合操作数据应该正确");

            log.info("混合复杂操作测试成功");

        } catch (Exception e) {
            log.error("混合复杂操作测试失败", e);
            fail("混合复杂操作不应该失败: " + e.getMessage());
        }
    }

    @Test
    @Order(8)
    @DisplayName("测试混合复杂操作回滚")
    void testMixedComplexOperationsRollback() {
        log.info("开始混合复杂操作回滚测试");

        String testId = generateTestDataId("MIXED_COMPLEX_ROLLBACK_TEST");

        try {
            try {
                // 测试混合复杂操作（模拟失败）
                Map<String, Object> result = atComplexTestService.testMixedComplexOperations(testId, 20, true);

                assertNotNull(result, "混合复杂操作回滚结果不应为空");
                assertFalse((Boolean) result.get("success"), "混合复杂操作应该失败并回滚");
            } catch (Exception e) {

            }

            // 等待云服务处理完成
            waitForAsyncOperation(1000);

            // 验证云服务数据已回滚
            assertFalse(verifyAllCloudServicesData(testId), "云服务混合操作数据应该已回滚");

            // 验证DLP数据也已回滚
            DlpDataRecord dlpRecord = dlpDataRecordMapper.selectByDataId(testId);
            assertNull(dlpRecord, "DLP混合操作数据应该已回滚");

            log.info("混合复杂操作回滚测试成功");

        } catch (Exception e) {
            log.error("混合复杂操作回滚测试失败", e);
            fail("混合复杂操作回滚测试不应该失败: " + e.getMessage());
        }
    }
}
