package com.tipray.at.dlp.test;

import org.junit.platform.suite.api.SelectClasses;
import org.junit.platform.suite.api.Suite;
import org.junit.platform.suite.api.SuiteDisplayName;


/**
 * AT模式分布式事务测试套件
 *
 * 包含所有测试类的完整测试套件
 *
 * <AUTHOR>
 * @version 2.0
 * @since 2025-01-08
 */
@Suite
@SuiteDisplayName("AT模式分布式事务完整测试套件")
@SelectClasses({
    // 基础测试
    com.tipray.at.dlp.test.SimpleAtTransactionTest.class,

    // 功能测试
    com.tipray.at.dlp.test.AtTransactionFunctionalTest.class,
    com.tipray.at.dlp.test.AtTransactionIntegrationTest.class,
    com.tipray.at.dlp.test.AtTransactionRollbackTest.class,

    // 性能测试
    AtTransactionPerformanceTest.class,
    com.tipray.at.dlp.test.AtTransactionConcurrencyTest.class,

    // 异常测试
    com.tipray.at.dlp.test.AtTransactionExceptionTest.class,

    // 新增补充测试
    com.tipray.at.dlp.test.AtTransactionRecoveryTest.class,
    com.tipray.at.dlp.test.AtTransactionResourceLeakTest.class,
    com.tipray.at.dlp.test.AtTransactionStateConsistencyTest.class,
    com.tipray.at.dlp.test.AtTransactionNetworkPartitionTest.class,
    com.tipray.at.dlp.test.AtTransactionDataCorruptionTest.class,
    com.tipray.at.dlp.test.AtTransactionIdempotencyTest.class,
    com.tipray.at.dlp.test.AtTransactionOrderingTest.class

    // 注意：其他测试类需要单独创建
    // 目前只包含已经创建的测试类
})
public class AtTransactionTestSuite {
    // 测试套件入口类
}
