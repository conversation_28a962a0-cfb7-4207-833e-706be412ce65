@echo off
chcp 65001 > nul
echo ========================================
echo 复杂UPDATE操作回滚专项测试
echo ========================================

echo.
echo 假设云服务已经启动，开始测试复杂UPDATE回滚...
echo.

cd at-dlp-service

echo 运行复杂UPDATE回滚测试...
mvn test -Dtest=CloudServiceComplexSqlRollbackTest#testCloudServiceDataUpdateRollback -Dspring.profiles.active=test

if %errorlevel% neq 0 (
    echo.
    echo UPDATE回滚测试失败！请检查：
    echo 1. 云服务是否启动（端口8081, 8082, 8083）
    echo 2. 数据库连接是否正常
    echo 3. UPDATE操作的UndoLog生成是否正确
    echo 4. 回滚逻辑是否正确处理UPDATE操作
) else (
    echo.
    echo ========================================
    echo 复杂UPDATE回滚测试成功！
    echo ========================================
    echo 已验证以下UPDATE回滚场景：
    echo ✓ 单表UPDATE操作回滚
    echo ✓ 多表UPDATE操作回滚
    echo ✓ 条件UPDATE操作回滚
    echo ✓ 基于子查询的UPDATE回滚
    echo ✓ 基于JOIN的UPDATE回滚
    echo ✓ 批量UPDATE操作回滚
    echo ✓ UPSERT操作回滚（UPDATE失败时INSERT）
    echo.
    echo 🎉 所有UPDATE操作都能正确生成UndoLog并成功回滚！
    echo 🎉 验证了复杂UPDATE场景下的数据一致性！
    echo ========================================
)

echo.
echo 继续运行其他复杂测试...
echo.

echo 运行大批量数据回滚测试...
mvn test -Dtest=CloudServiceComplexSqlRollbackTest#testCloudServiceBatchDataRollback -Dspring.profiles.active=test

echo.
echo 运行并发事务回滚测试...
mvn test -Dtest=CloudServiceComplexSqlRollbackTest#testCloudServiceConcurrentTransactionRollback -Dspring.profiles.active=test

echo.
echo 运行跨表关联数据回滚测试...
mvn test -Dtest=CloudServiceComplexSqlRollbackTest#testCloudServiceCrossTableRollback -Dspring.profiles.active=test

echo.
echo ========================================
echo 所有复杂UPDATE和扩展测试完成！
echo ========================================

pause
