-- AT模式分布式事务测试数据库建库建表SQL
-- 数据库账号：root 密码：123456

-- =====================================================
-- 1. DLP服务数据库
-- =====================================================
CREATE DATABASE IF NOT EXISTS `at_dlp_db` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE `at_dlp_db`;

-- DLP数据记录表
CREATE TABLE `dlp_data_record` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `data_id` varchar(64) NOT NULL COMMENT '数据ID',
  `data_content` text COMMENT '数据内容',
  `data_type` varchar(32) DEFAULT NULL COMMENT '数据类型',
  `status` varchar(16) NOT NULL DEFAULT 'PENDING' COMMENT '处理状态：PENDING-待处理，PROCESSING-处理中，SUCCESS-成功，FAILED-失败',
  `global_tx_id` varchar(64) DEFAULT NULL COMMENT '全局事务ID',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `remark` varchar(255) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_data_id` (`data_id`),
  KEY `idx_global_tx_id` (`global_tx_id`),
  KEY `idx_status` (`status`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='DLP数据记录表';

-- =====================================================
-- 2. 云存储服务数据库
-- =====================================================
CREATE DATABASE IF NOT EXISTS `at_cloud_storage_db` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE `at_cloud_storage_db`;

-- 存储记录表
CREATE TABLE `storage_record` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `data_id` varchar(64) NOT NULL COMMENT '数据ID',
  `data_content` text COMMENT '数据内容',
  `data_type` varchar(32) DEFAULT NULL COMMENT '数据类型',
  `storage_path` varchar(255) NOT NULL COMMENT '存储路径',
  `file_size` bigint(20) DEFAULT 0 COMMENT '文件大小（字节）',
  `status` varchar(16) NOT NULL DEFAULT 'STORED' COMMENT '存储状态：STORED-已存储，DELETED-已删除',
  `global_tx_id` varchar(64) DEFAULT NULL COMMENT '全局事务ID',
  `branch_tx_id` varchar(64) DEFAULT NULL COMMENT '分支事务ID',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `remark` varchar(255) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_data_id` (`data_id`),
  KEY `idx_global_tx_id` (`global_tx_id`),
  KEY `idx_branch_tx_id` (`branch_tx_id`),
  KEY `idx_status` (`status`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='存储记录表';

-- AT模式undo_log表
CREATE TABLE `undo_log` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `branch_id` bigint(20) NOT NULL COMMENT '分支事务ID',
  `xid` varchar(100) NOT NULL COMMENT '全局事务ID',
  `context` varchar(128) NOT NULL COMMENT '上下文',
  `rollback_info` longblob NOT NULL COMMENT '回滚信息',
  `log_status` int(11) NOT NULL COMMENT '日志状态：0-正常，1-已回滚',
  `log_created` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `log_modified` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `ext` varchar(100) DEFAULT NULL COMMENT '扩展字段',
  PRIMARY KEY (`id`),
  UNIQUE KEY `ux_undo_log` (`xid`,`branch_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='AT模式undo_log表';

-- =====================================================
-- 3. 云分析服务数据库
-- =====================================================
CREATE DATABASE IF NOT EXISTS `at_cloud_analysis_db` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE `at_cloud_analysis_db`;

-- 分析记录表
CREATE TABLE `analysis_record` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `data_id` varchar(64) NOT NULL COMMENT '数据ID',
  `data_content` text COMMENT '数据内容',
  `data_type` varchar(32) DEFAULT NULL COMMENT '数据类型',
  `analysis_result` text COMMENT '分析结果',
  `analysis_score` decimal(5,2) DEFAULT NULL COMMENT '分析得分',
  `status` varchar(16) NOT NULL DEFAULT 'ANALYZING' COMMENT '分析状态：ANALYZING-分析中，COMPLETED-已完成，FAILED-失败',
  `global_tx_id` varchar(64) DEFAULT NULL COMMENT '全局事务ID',
  `branch_tx_id` varchar(64) DEFAULT NULL COMMENT '分支事务ID',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `remark` varchar(255) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_data_id` (`data_id`),
  KEY `idx_global_tx_id` (`global_tx_id`),
  KEY `idx_branch_tx_id` (`branch_tx_id`),
  KEY `idx_status` (`status`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='分析记录表';

-- AT模式undo_log表
CREATE TABLE `undo_log` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `branch_id` bigint(20) NOT NULL COMMENT '分支事务ID',
  `xid` varchar(100) NOT NULL COMMENT '全局事务ID',
  `context` varchar(128) NOT NULL COMMENT '上下文',
  `rollback_info` longblob NOT NULL COMMENT '回滚信息',
  `log_status` int(11) NOT NULL COMMENT '日志状态：0-正常，1-已回滚',
  `log_created` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `log_modified` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `ext` varchar(100) DEFAULT NULL COMMENT '扩展字段',
  PRIMARY KEY (`id`),
  UNIQUE KEY `ux_undo_log` (`xid`,`branch_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='AT模式undo_log表';

-- =====================================================
-- 4. 云通知服务数据库
-- =====================================================
CREATE DATABASE IF NOT EXISTS `at_cloud_notify_db` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE `at_cloud_notify_db`;

-- 通知记录表
CREATE TABLE `notify_record` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `data_id` varchar(64) NOT NULL COMMENT '数据ID',
  `notify_type` varchar(16) NOT NULL DEFAULT 'EMAIL' COMMENT '通知类型：EMAIL-邮件，SMS-短信，PUSH-推送',
  `notify_target` varchar(128) NOT NULL COMMENT '通知目标（邮箱、手机号等）',
  `notify_content` text COMMENT '通知内容',
  `status` varchar(16) NOT NULL DEFAULT 'PENDING' COMMENT '通知状态：PENDING-待发送，SENT-已发送，FAILED-发送失败',
  `send_time` datetime DEFAULT NULL COMMENT '发送时间',
  `global_tx_id` varchar(64) DEFAULT NULL COMMENT '全局事务ID',
  `branch_tx_id` varchar(64) DEFAULT NULL COMMENT '分支事务ID',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `remark` varchar(255) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_data_id` (`data_id`),
  KEY `idx_global_tx_id` (`global_tx_id`),
  KEY `idx_branch_tx_id` (`branch_tx_id`),
  KEY `idx_notify_type` (`notify_type`),
  KEY `idx_status` (`status`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='通知记录表';

-- AT模式undo_log表
CREATE TABLE `undo_log` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `branch_id` bigint(20) NOT NULL COMMENT '分支事务ID',
  `xid` varchar(100) NOT NULL COMMENT '全局事务ID',
  `context` varchar(128) NOT NULL COMMENT '上下文',
  `rollback_info` longblob NOT NULL COMMENT '回滚信息',
  `log_status` int(11) NOT NULL COMMENT '日志状态：0-正常，1-已回滚',
  `log_created` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `log_modified` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `ext` varchar(100) DEFAULT NULL COMMENT '扩展字段',
  PRIMARY KEY (`id`),
  UNIQUE KEY `ux_undo_log` (`xid`,`branch_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='AT模式undo_log表';

-- =====================================================
-- 5. 插入测试数据
-- =====================================================

-- 插入DLP测试数据
USE `at_dlp_db`;
INSERT INTO `dlp_data_record` (`data_id`, `data_content`, `data_type`, `status`, `remark`) VALUES
('TEST_DATA_001', '这是一条测试数据内容', 'JSON', 'SUCCESS', '测试数据1'),
('TEST_DATA_002', '这是另一条测试数据内容', 'XML', 'SUCCESS', '测试数据2');

-- 插入存储测试数据
USE `at_cloud_storage_db`;
INSERT INTO `storage_record` (`data_id`, `data_content`, `data_type`, `storage_path`, `file_size`, `status`, `remark`) VALUES
('TEST_DATA_001', '这是一条测试数据内容', 'JSON', '/storage/data/TEST_DATA_001.dat', 1024, 'STORED', '测试存储1'),
('TEST_DATA_002', '这是另一条测试数据内容', 'XML', '/storage/data/TEST_DATA_002.dat', 2048, 'STORED', '测试存储2');

-- 插入分析测试数据
USE `at_cloud_analysis_db`;
INSERT INTO `analysis_record` (`data_id`, `data_content`, `data_type`, `analysis_result`, `analysis_score`, `status`, `remark`) VALUES
('TEST_DATA_001', '这是一条测试数据内容', 'JSON', '数据类型：JSON；数据长度：12字符；分析完成', 85.5, 'COMPLETED', '测试分析1'),
('TEST_DATA_002', '这是另一条测试数据内容', 'XML', '数据类型：XML；数据长度：13字符；分析完成', 92.3, 'COMPLETED', '测试分析2');

-- 插入通知测试数据
USE `at_cloud_notify_db`;
INSERT INTO `notify_record` (`data_id`, `notify_type`, `notify_target`, `notify_content`, `status`, `send_time`, `remark`) VALUES
('TEST_DATA_001', 'EMAIL', '<EMAIL>', '数据处理通知\n数据ID：TEST_DATA_001\n处理完成', 'SENT', NOW(), '测试通知1'),
('TEST_DATA_002', 'EMAIL', '<EMAIL>', '数据处理通知\n数据ID：TEST_DATA_002\n处理完成', 'SENT', NOW(), '测试通知2');
