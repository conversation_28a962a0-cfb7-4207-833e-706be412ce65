-- ========================================
-- 云服务数据库建库建表脚本
-- 包含：云存储、云分析、云通知服务的数据库表
-- 执行方式：mysql -u root -p < CREATE_CLOUD_SERVICES_DATABASE.sql
-- ========================================

-- 使用测试数据库
USE tipray_cloud_test;

-- ========================================
-- 云存储服务表
-- ========================================
DROP TABLE IF EXISTS storage_record;
CREATE TABLE storage_record (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    data_id VARCHAR(100) NOT NULL COMMENT '数据ID',
    data_content TEXT COMMENT '数据内容',
    data_type VARCHAR(50) COMMENT '数据类型',
    storage_path VARCHAR(500) COMMENT '存储路径',
    file_size BIGINT COMMENT '文件大小',
    status VARCHAR(50) DEFAULT 'STORED' COMMENT '状态',
    global_tx_id VARCHAR(100) COMMENT '全局事务ID',
    branch_tx_id VARCHAR(100) COMMENT '分支事务ID',
    remark VARCHAR(500) COMMENT '备注',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    UNIQUE KEY uk_data_id (data_id),
    INDEX idx_status (status),
    INDEX idx_global_tx_id (global_tx_id),
    INDEX idx_create_time (create_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='云存储记录表';

-- ========================================
-- 云分析服务表
-- ========================================
DROP TABLE IF EXISTS analysis_record;
CREATE TABLE analysis_record (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    data_id VARCHAR(100) NOT NULL COMMENT '数据ID',
    data_content TEXT COMMENT '数据内容',
    data_type VARCHAR(50) COMMENT '数据类型',
    analysis_result TEXT COMMENT '分析结果',
    analysis_score DOUBLE COMMENT '分析得分',
    status VARCHAR(50) DEFAULT 'COMPLETED' COMMENT '状态',
    global_tx_id VARCHAR(100) COMMENT '全局事务ID',
    branch_tx_id VARCHAR(100) COMMENT '分支事务ID',
    remark VARCHAR(500) COMMENT '备注',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    UNIQUE KEY uk_data_id (data_id),
    INDEX idx_status (status),
    INDEX idx_global_tx_id (global_tx_id),
    INDEX idx_create_time (create_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='云分析记录表';

-- ========================================
-- 云通知服务表
-- ========================================
DROP TABLE IF EXISTS notify_record;
CREATE TABLE notify_record (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    data_id VARCHAR(100) NOT NULL COMMENT '数据ID',
    notify_type VARCHAR(50) COMMENT '通知类型',
    notify_target VARCHAR(200) COMMENT '通知目标',
    notify_content TEXT COMMENT '通知内容',
    status VARCHAR(50) DEFAULT 'SENT' COMMENT '状态',
    send_time TIMESTAMP NULL COMMENT '发送时间',
    global_tx_id VARCHAR(100) COMMENT '全局事务ID',
    branch_tx_id VARCHAR(100) COMMENT '分支事务ID',
    remark VARCHAR(500) COMMENT '备注',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    UNIQUE KEY uk_data_id (data_id),
    INDEX idx_status (status),
    INDEX idx_notify_type (notify_type),
    INDEX idx_global_tx_id (global_tx_id),
    INDEX idx_create_time (create_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='云通知记录表';

-- ========================================
-- 云服务扩展表（用于复杂SQL回滚测试）
-- ========================================

-- 数据处理历史表
DROP TABLE IF EXISTS data_process_history;
CREATE TABLE data_process_history (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    data_id VARCHAR(100) NOT NULL COMMENT '数据ID',
    process_type VARCHAR(50) NOT NULL COMMENT '处理类型',
    process_status VARCHAR(50) DEFAULT 'PROCESSING' COMMENT '处理状态',
    process_result TEXT COMMENT '处理结果',
    process_duration BIGINT COMMENT '处理耗时(毫秒)',
    error_message TEXT COMMENT '错误信息',
    global_tx_id VARCHAR(100) COMMENT '全局事务ID',
    branch_tx_id VARCHAR(100) COMMENT '分支事务ID',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_data_id (data_id),
    INDEX idx_process_type (process_type),
    INDEX idx_process_status (process_status),
    INDEX idx_global_tx_id (global_tx_id),
    INDEX idx_create_time (create_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='数据处理历史表';

-- 数据统计表
DROP TABLE IF EXISTS data_statistics;
CREATE TABLE data_statistics (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    stat_date DATE NOT NULL COMMENT '统计日期',
    data_type VARCHAR(50) NOT NULL COMMENT '数据类型',
    total_count BIGINT DEFAULT 0 COMMENT '总数量',
    success_count BIGINT DEFAULT 0 COMMENT '成功数量',
    failed_count BIGINT DEFAULT 0 COMMENT '失败数量',
    avg_process_time DOUBLE DEFAULT 0 COMMENT '平均处理时间',
    total_size BIGINT DEFAULT 0 COMMENT '总大小',
    global_tx_id VARCHAR(100) COMMENT '全局事务ID',
    branch_tx_id VARCHAR(100) COMMENT '分支事务ID',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    UNIQUE KEY uk_stat_date_type (stat_date, data_type),
    INDEX idx_stat_date (stat_date),
    INDEX idx_data_type (data_type),
    INDEX idx_global_tx_id (global_tx_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='数据统计表';

-- 数据关系表
DROP TABLE IF EXISTS data_relationship;
CREATE TABLE data_relationship (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    parent_data_id VARCHAR(100) NOT NULL COMMENT '父数据ID',
    child_data_id VARCHAR(100) NOT NULL COMMENT '子数据ID',
    relationship_type VARCHAR(50) NOT NULL COMMENT '关系类型',
    relationship_strength DOUBLE DEFAULT 1.0 COMMENT '关系强度',
    status VARCHAR(50) DEFAULT 'ACTIVE' COMMENT '状态',
    global_tx_id VARCHAR(100) COMMENT '全局事务ID',
    branch_tx_id VARCHAR(100) COMMENT '分支事务ID',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    UNIQUE KEY uk_parent_child_type (parent_data_id, child_data_id, relationship_type),
    INDEX idx_parent_data_id (parent_data_id),
    INDEX idx_child_data_id (child_data_id),
    INDEX idx_relationship_type (relationship_type),
    INDEX idx_global_tx_id (global_tx_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='数据关系表';

-- 数据标签表
DROP TABLE IF EXISTS data_tags;
CREATE TABLE data_tags (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    data_id VARCHAR(100) NOT NULL COMMENT '数据ID',
    tag_name VARCHAR(100) NOT NULL COMMENT '标签名称',
    tag_value VARCHAR(500) COMMENT '标签值',
    tag_category VARCHAR(50) COMMENT '标签分类',
    priority INT DEFAULT 0 COMMENT '优先级',
    global_tx_id VARCHAR(100) COMMENT '全局事务ID',
    branch_tx_id VARCHAR(100) COMMENT '分支事务ID',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    UNIQUE KEY uk_data_tag (data_id, tag_name),
    INDEX idx_data_id (data_id),
    INDEX idx_tag_name (tag_name),
    INDEX idx_tag_category (tag_category),
    INDEX idx_global_tx_id (global_tx_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='数据标签表';

-- 数据配置表
DROP TABLE IF EXISTS data_config;
CREATE TABLE data_config (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    config_key VARCHAR(100) NOT NULL COMMENT '配置键',
    config_value TEXT COMMENT '配置值',
    config_type VARCHAR(50) DEFAULT 'STRING' COMMENT '配置类型',
    config_group VARCHAR(50) DEFAULT 'DEFAULT' COMMENT '配置组',
    is_encrypted TINYINT(1) DEFAULT 0 COMMENT '是否加密',
    is_active TINYINT(1) DEFAULT 1 COMMENT '是否激活',
    global_tx_id VARCHAR(100) COMMENT '全局事务ID',
    branch_tx_id VARCHAR(100) COMMENT '分支事务ID',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    UNIQUE KEY uk_config_key (config_key),
    INDEX idx_config_group (config_group),
    INDEX idx_is_active (is_active),
    INDEX idx_global_tx_id (global_tx_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='数据配置表';

-- ========================================
-- 云服务UndoLog表（每个云服务一个）
-- ========================================

-- 云存储服务UndoLog表
DROP TABLE IF EXISTS undo_log_storage;
CREATE TABLE undo_log_storage (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    branch_id BIGINT NOT NULL COMMENT '分支事务ID',
    xid VARCHAR(100) NOT NULL COMMENT '全局事务ID',
    context VARCHAR(128) NOT NULL COMMENT '上下文',
    rollback_info LONGBLOB NOT NULL COMMENT '回滚信息',
    log_status INT NOT NULL COMMENT '日志状态',
    log_created TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    log_modified TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
    UNIQUE KEY ux_undo_log (xid, branch_id),
    INDEX idx_log_created (log_created)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='云存储UndoLog表';

-- 云分析服务UndoLog表
DROP TABLE IF EXISTS undo_log_analysis;
CREATE TABLE undo_log_analysis (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    branch_id BIGINT NOT NULL COMMENT '分支事务ID',
    xid VARCHAR(100) NOT NULL COMMENT '全局事务ID',
    context VARCHAR(128) NOT NULL COMMENT '上下文',
    rollback_info LONGBLOB NOT NULL COMMENT '回滚信息',
    log_status INT NOT NULL COMMENT '日志状态',
    log_created TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    log_modified TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
    UNIQUE KEY ux_undo_log (xid, branch_id),
    INDEX idx_log_created (log_created)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='云分析UndoLog表';

-- 云通知服务UndoLog表
DROP TABLE IF EXISTS undo_log_notify;
CREATE TABLE undo_log_notify (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    branch_id BIGINT NOT NULL COMMENT '分支事务ID',
    xid VARCHAR(100) NOT NULL COMMENT '全局事务ID',
    context VARCHAR(128) NOT NULL COMMENT '上下文',
    rollback_info LONGBLOB NOT NULL COMMENT '回滚信息',
    log_status INT NOT NULL COMMENT '日志状态',
    log_created TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    log_modified TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
    UNIQUE KEY ux_undo_log (xid, branch_id),
    INDEX idx_log_created (log_created)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='云通知UndoLog表';

-- ========================================
-- 插入测试数据
-- ========================================
INSERT INTO storage_record (data_id, data_content, data_type, storage_path, file_size, status, remark) VALUES
('INIT_STORAGE_001', '初始化存储数据1', 'JSON', '/storage/init/001.json', 1024, 'STORED', '测试初始化数据')
ON DUPLICATE KEY UPDATE
    data_content = VALUES(data_content),
    update_time = CURRENT_TIMESTAMP;

INSERT INTO analysis_record (data_id, data_content, data_type, analysis_result, analysis_score, status, remark) VALUES
('INIT_ANALYSIS_001', '初始化分析数据1', 'JSON', '分析结果：正常', 85.5, 'COMPLETED', '测试初始化数据')
ON DUPLICATE KEY UPDATE
    data_content = VALUES(data_content),
    update_time = CURRENT_TIMESTAMP;

INSERT INTO notify_record (data_id, notify_type, notify_target, notify_content, status, send_time, remark) VALUES
('INIT_NOTIFY_001', 'EMAIL', '<EMAIL>', '初始化通知内容', 'SENT', NOW(), '测试初始化数据')
ON DUPLICATE KEY UPDATE
    notify_content = VALUES(notify_content),
    update_time = CURRENT_TIMESTAMP;

-- 插入扩展表初始化数据
INSERT INTO data_process_history (data_id, process_type, process_status, process_result, process_duration, remark) VALUES
('INIT_STORAGE_001', 'STORAGE', 'COMPLETED', '存储成功', 1500, '初始化存储历史'),
('INIT_ANALYSIS_001', 'ANALYSIS', 'COMPLETED', '分析完成', 3000, '初始化分析历史')
ON DUPLICATE KEY UPDATE
    process_result = VALUES(process_result),
    update_time = CURRENT_TIMESTAMP;

INSERT INTO data_statistics (stat_date, data_type, total_count, success_count, failed_count, avg_process_time, total_size) VALUES
(CURDATE(), 'JSON', 10, 8, 2, 2500.5, 10240),
(CURDATE(), 'TEXT', 5, 5, 0, 1200.0, 5120)
ON DUPLICATE KEY UPDATE
    total_count = VALUES(total_count),
    success_count = VALUES(success_count),
    failed_count = VALUES(failed_count),
    update_time = CURRENT_TIMESTAMP;

INSERT INTO data_relationship (parent_data_id, child_data_id, relationship_type, relationship_strength, status) VALUES
('INIT_STORAGE_001', 'INIT_ANALYSIS_001', 'PROCESS_FLOW', 0.9, 'ACTIVE')
ON DUPLICATE KEY UPDATE
    relationship_strength = VALUES(relationship_strength),
    update_time = CURRENT_TIMESTAMP;

INSERT INTO data_tags (data_id, tag_name, tag_value, tag_category, priority) VALUES
('INIT_STORAGE_001', 'environment', 'test', 'system', 1),
('INIT_STORAGE_001', 'version', '1.0', 'metadata', 2),
('INIT_ANALYSIS_001', 'algorithm', 'ml_basic', 'technical', 1)
ON DUPLICATE KEY UPDATE
    tag_value = VALUES(tag_value),
    update_time = CURRENT_TIMESTAMP;

INSERT INTO data_config (config_key, config_value, config_type, config_group, is_encrypted, is_active) VALUES
('storage.max_file_size', '10485760', 'LONG', 'STORAGE', 0, 1),
('analysis.timeout_seconds', '300', 'INT', 'ANALYSIS', 0, 1),
('notification.retry_count', '3', 'INT', 'NOTIFICATION', 0, 1)
ON DUPLICATE KEY UPDATE
    config_value = VALUES(config_value),
    update_time = CURRENT_TIMESTAMP;

-- ========================================
-- 显示创建完成信息
-- ========================================
SELECT '云服务数据库表创建完成！' AS status,
       NOW() AS create_time,
       (SELECT COUNT(*) FROM storage_record) AS storage_count,
       (SELECT COUNT(*) FROM analysis_record) AS analysis_count,
       (SELECT COUNT(*) FROM notify_record) AS notify_count,
       (SELECT COUNT(*) FROM data_process_history) AS history_count,
       (SELECT COUNT(*) FROM data_statistics) AS statistics_count,
       (SELECT COUNT(*) FROM data_relationship) AS relationship_count,
       (SELECT COUNT(*) FROM data_tags) AS tags_count,
       (SELECT COUNT(*) FROM data_config) AS config_count;
