# AT模式分布式事务测试套件

## 概述

这是一个全面的AT模式分布式事务测试套件，包含功能测试、性能测试、并发测试、异常测试等多个维度的测试用例，确保tipray-distributed-transaction框架的稳定性和可靠性。

## 测试架构

```
tipray-at-transaction-test/
├── at-dlp-service/                 # DLP服务（事务发起方）
│   ├── src/main/                   # 主要代码
│   └── src/test/                   # 测试代码
│       ├── java/com/tipray/at/dlp/test/
│       │   ├── base/               # 测试基础类
│       │   ├── AtTransactionTestSuite.java          # 测试套件
│       │   ├── AtTransactionFunctionalTest.java     # 功能测试
│       │   ├── AtTransactionIntegrationTest.java    # 集成测试
│       │   ├── AtTransactionRollbackTest.java       # 回滚测试
│       │   ├── AtTransactionPerformanceTest.java    # 性能测试
│       │   ├── AtTransactionConcurrencyTest.java    # 并发测试
│       │   ├── AtTransactionExceptionTest.java      # 异常测试
│       │   ├── AtTransactionTimeoutTest.java        # 超时测试
│       │   ├── AtTransactionStabilityTest.java      # 稳定性测试
│       │   ├── EdgeCaseRollbackTest.java            # 边界情况回滚测试
│       │   ├── TransactionConsistencyTest.java      # 事务一致性测试
│       │   ├── SqlParsingAccuracyTest.java          # SQL解析准确性测试
│       │   ├── AtTransactionRecoveryTest.java       # 事务恢复测试
│       │   ├── AtTransactionResourceLeakTest.java   # 资源泄漏测试
│       │   ├── AtTransactionStateConsistencyTest.java # 状态一致性测试
│       │   ├── AtTransactionNetworkPartitionTest.java # 网络分区测试
│       │   ├── AtTransactionDataCorruptionTest.java # 数据损坏测试
│       │   ├── AtTransactionIdempotencyTest.java    # 幂等性测试
│       │   └── AtTransactionOrderingTest.java       # 事务顺序测试
│       └── resources/
│           ├── application-test.yml    # 测试配置
│           └── sql/test-schema.sql     # 测试数据库脚本
├── at-cloud-storage-service/       # 云存储服务
├── at-cloud-analysis-service/      # 云分析服务
├── at-cloud-notify-service/        # 云通知服务
├── run-tests.sh                    # 测试运行脚本
└── README.md                       # 本文档
```

## 测试分类

### 1. 功能测试 (AtTransactionFunctionalTest)
- **基本数据处理功能**：测试基础的CRUD操作
- **数据更新功能**：测试数据更新和状态变更
- **批量数据更新**：测试批量操作的事务一致性
- **数据查询功能**：测试查询操作的正确性
- **数据删除功能**：测试删除操作的事务性
- **事务上下文传递**：测试事务上下文在调用链中的传递
- **并发数据处理**：测试基本的并发处理能力

### 2. 集成测试 (AtTransactionIntegrationTest)
- **完整分布式事务流程**：端到端事务测试
- **云存储服务集成**：测试与云存储服务的集成
- **云分析服务集成**：测试与云分析服务的集成
- **云通知服务集成**：测试与云通知服务的集成
- **多云服务集成**：测试多个云服务的协调
- **复杂SQL场景集成**：测试复杂SQL操作的集成
- **长时间运行事务集成**：测试长时间事务的稳定性
- **大数据量事务集成**：测试大数据量的处理能力
- **HTTP接口集成**：测试REST API的集成
- **健康检查集成**：测试服务健康状态

### 3. 回滚测试 (AtTransactionRollbackTest)
- **本地异常回滚**：测试本地异常触发的回滚
- **云服务异常回滚**：测试远程服务异常的回滚
- **超时回滚**：测试超时场景的回滚
- **网络异常回滚**：测试网络问题的回滚
- **部分成功回滚**：测试部分步骤成功时的回滚
- **并发事务回滚隔离**：测试并发回滚的隔离性
- **嵌套事务回滚**：测试嵌套事务的回滚
- **UndoLog回滚**：测试UndoLog的生成和回滚
- **回滚失败处理**：测试回滚失败的处理机制

### 4. 性能测试 (AtTransactionPerformanceTest)
- **单事务性能基准**：测试单个事务的性能指标
- **并发事务性能**：测试并发场景下的性能表现
- **大数据量事务性能**：测试不同数据量级的性能
- **长时间运行事务性能**：测试长时间事务的性能
- **内存使用性能**：测试内存使用情况
- **数据库连接池性能**：测试连接池的性能表现

### 5. 并发测试 (AtTransactionConcurrencyTest)
- **并发事务隔离性**：测试事务间的隔离
- **并发访问同一资源**：测试资源竞争处理
- **死锁检测**：测试死锁的检测和处理
- **并发事务回滚**：测试并发回滚的正确性
- **高并发资源竞争**：测试高并发下的稳定性
- **并发事务内存一致性**：测试内存一致性

### 6. 高级测试场景

#### 6.1 事务恢复测试 (AtTransactionRecoveryTest)
- **服务重启后事务恢复**：测试服务重启后的事务状态恢复
- **数据库连接中断恢复**：测试数据库连接问题后的恢复
- **网络分区后事务恢复**：测试网络分区场景的恢复机制
- **并发事务恢复**：测试并发环境下的事务恢复
- **UndoLog损坏恢复**：测试UndoLog损坏后的处理
- **系统资源耗尽恢复**：测试资源耗尽后的系统恢复

#### 6.2 资源泄漏测试 (AtTransactionResourceLeakTest)
- **内存泄漏检测**：长时间运行后的内存使用监控
- **数据库连接泄漏检测**：连接池连接数监控
- **线程泄漏检测**：后台线程生命周期管理
- **事务上下文泄漏检测**：ThreadLocal变量清理
- **长时间运行资源清理**：持续运行后的资源状态

#### 6.3 状态一致性测试 (AtTransactionStateConsistencyTest)
- **事务状态流转正确性**：状态转换的正确性验证
- **失败事务状态一致性**：失败场景下的状态一致性
- **并发事务状态隔离性**：并发环境下的状态独立性
- **事务状态持久化一致性**：内存与数据库状态一致性
- **异常状态处理一致性**：各种异常场景的状态处理
- **状态转换原子性**：状态转换的原子性保证

#### 6.4 网络分区测试 (AtTransactionNetworkPartitionTest)
- **完全网络分区场景**：完全网络不可达的处理
- **部分网络分区场景**：部分服务不可达的处理
- **网络延迟场景**：高延迟网络环境的适应性
- **网络抖动场景**：网络不稳定环境的处理
- **网络恢复后重试**：网络恢复后的事务重试机制
- **并发网络分区**：并发环境下的网络分区处理

## 快速开始

### 环境要求
- Java 8+
- Maven 3.6+
- MySQL 8.0+ (必需，测试使用MySQL数据库)

### 数据库准备
1. 确保MySQL服务正在运行
2. 创建测试数据库：
   ```sql
   CREATE DATABASE IF NOT EXISTS tipray_cloud_test
   CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
   ```
3. 确认连接配置：用户名`root`，密码`123456`，端口`3306`

### 数据库连接测试
```bash
# Windows用户
test-db-connection.bat

# 或者手动测试
mvn test -Dtest=SimpleAtTransactionTest#testContextLoads
```

### 运行所有测试
```bash
# 给脚本执行权限 (Linux/Mac)
chmod +x run-tests.sh

# 运行所有测试
./run-tests.sh all

# 运行所有测试并生成代码覆盖率报告
./run-tests.sh all --coverage

# Windows用户可以直接使用Maven
mvn test
```

### 运行特定类型的测试
```bash
# 只运行功能测试
./run-tests.sh functional

# 只运行性能测试
./run-tests.sh performance

# 只运行并发测试
./run-tests.sh concurrency

# 只运行回滚测试
./run-tests.sh rollback

# 只运行集成测试
./run-tests.sh integration
```

### 使用Maven直接运行
```bash
# 运行所有测试
mvn clean verify

# 只运行单元测试
mvn clean test

# 运行特定测试类
mvn test -Dtest=AtTransactionFunctionalTest

# 运行特定测试方法
mvn test -Dtest=AtTransactionFunctionalTest#testBasicDataProcessing

# 生成代码覆盖率报告
mvn clean verify jacoco:report
```

## 测试配置

### 数据库配置
测试使用MySQL数据库，配置在`application-test.yml`中：
```yaml
spring:
  datasource:
    url: **********************************************************************************************************************************************************
    driver-class-name: com.mysql.cj.jdbc.Driver
    username: root
    password: 123456
```

### 事务配置
```yaml
tipray:
  transaction:
    enabled: true
    at:
      enabled: true
      undo-log-table: undo_log_test
    timeout:
      default: 30000
      max: 60000
```

### 性能测试配置
```yaml
test:
  performance:
    warmup-iterations: 10
    test-iterations: 100
    concurrent-threads: 10
    timeout-seconds: 60
```

## 测试报告

### 测试结果位置
- **单元测试报告**: `target/surefire-reports/`
- **集成测试报告**: `target/failsafe-reports/`
- **代码覆盖率报告**: `target/site/jacoco/index.html`

### 性能测试指标
- **平均执行时间**: 单个事务的平均执行时间
- **95%分位数**: 95%的事务在此时间内完成
- **TPS**: 每秒事务处理数
- **成功率**: 事务成功完成的比例
- **内存使用**: 测试过程中的内存使用情况

## 故障排除

### 常见问题

1. **测试失败：连接被拒绝**
   - 检查云服务是否启动
   - 确认端口配置正确

2. **内存不足错误**
   - 增加JVM内存：`export MAVEN_OPTS="-Xmx2g"`
   - 减少并发线程数

3. **测试超时**
   - 增加超时时间配置
   - 检查系统资源使用情况

4. **数据库连接错误**
   - 确认MySQL服务正在运行
   - 确认数据库tipray_cloud_test存在
   - 检查用户名密码配置 (root/123456)
   - 运行数据库连接测试：`test-db-connection.bat`

### 调试技巧

1. **启用详细日志**
   ```bash
   ./run-tests.sh all --verbose
   ```

2. **单独运行失败的测试**
   ```bash
   mvn test -Dtest=FailedTestClass#failedTestMethod
   ```

3. **查看测试日志**
   ```bash
   tail -f target/surefire-reports/*.txt
   ```

## 扩展测试

### 添加新的测试类
1. 继承`BaseAtTransactionTest`基础类
2. 使用`@TestMethodOrder`和`@Order`注解控制执行顺序
3. 添加到`AtTransactionTestSuite`中

### 自定义测试配置
1. 修改`application-test.yml`配置文件
2. 添加新的测试profile
3. 使用`@TestPropertySource`覆盖特定配置

## 持续集成

### Jenkins集成
```groovy
pipeline {
    agent any
    stages {
        stage('Test') {
            steps {
                sh './run-tests.sh all --coverage'
            }
            post {
                always {
                    publishHTML([
                        allowMissing: false,
                        alwaysLinkToLastBuild: true,
                        keepAll: true,
                        reportDir: 'target/site/jacoco',
                        reportFiles: 'index.html',
                        reportName: 'Code Coverage Report'
                    ])
                }
            }
        }
    }
}
```

### GitHub Actions集成
```yaml
name: AT Transaction Tests
on: [push, pull_request]
jobs:
  test:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v2
    - name: Set up JDK 8
      uses: actions/setup-java@v2
      with:
        java-version: '8'
        distribution: 'adopt'
    - name: Run tests
      run: ./run-tests.sh all --coverage
    - name: Upload coverage reports
      uses: codecov/codecov-action@v1
```

## 贡献指南

1. 添加新测试时，确保测试名称清晰描述测试目的
2. 使用适当的断言和错误消息
3. 确保测试的独立性和可重复性
4. 添加必要的文档和注释
5. 遵循现有的代码风格和命名约定
