package com.tipray.at.analysis.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 分析记录实体
 * 
 * <AUTHOR>
 */
@Data
@TableName("analysis_record")
public class AnalysisRecord {

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 数据ID
     */
    private String dataId;

    /**
     * 数据内容
     */
    private String dataContent;

    /**
     * 数据类型
     */
    private String dataType;

    /**
     * 分析结果
     */
    private String analysisResult;

    /**
     * 分析得分
     */
    private Double analysisScore;

    /**
     * 分析状态：ANALYZING-分析中，COMPLETED-已完成，FAILED-失败
     */
    private String status;

    /**
     * 全局事务ID
     */
    private String globalTxId;

    /**
     * 分支事务ID
     */
    private String branchTxId;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 备注
     */
    private String remark;
}
