package com.tipray.at.analysis.service;

import com.tipray.at.analysis.dto.AnalysisRequest;
import com.tipray.at.analysis.entity.AnalysisRecord;

import java.util.Map;

/**
 * 分析服务接口
 *
 * <AUTHOR>
 */
public interface AnalysisService {

    /**
     * 分析数据
     *
     * @param request 分析请求
     * @return 分析记录
     */
    AnalysisRecord analyzeData(AnalysisRequest request);

    /**
     * 根据数据ID查询分析记录
     *
     * @param dataId 数据ID
     * @return 分析记录
     */
    AnalysisRecord getByDataId(String dataId);

    /**
     * 删除分析数据
     *
     * @param request 删除请求
     * @return 是否删除成功
     */
    boolean deleteData(AnalysisRequest request);

    /**
     * 执行复杂SQL场景测试（在云端）
     *
     * @param request 分析请求
     * @return 测试结果
     */
    Map<String, Object> executeComplexSqlScenarios(AnalysisRequest request);

    /**
     * 检查数据是否存在（用于回滚验证）
     *
     * @param dataId 数据ID
     * @return 检查结果
     */
    Map<String, Object> checkDataExists(String dataId);
}
