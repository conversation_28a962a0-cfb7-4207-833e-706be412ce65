server:
  port: 8082

spring:
  application:
    name: at-cloud-analysis-service
  datasource:
    url: **************************************************************************************************************************************************************
    username: root
    password: 123456
    driver-class-name: com.mysql.cj.jdbc.Driver
    hikari:
      minimum-idle: 10
      maximum-pool-size: 50
      auto-commit: true
      idle-timeout: 60000
      pool-name: AnalysisHikariCP
      max-lifetime: 1800000
      connection-timeout: 10000
      validation-timeout: 5000
      leak-detection-threshold: 60000

# MyBatis Plus 配置
mybatis-plus:
  configuration:
    map-underscore-to-camel-case: true
    cache-enabled: false
    call-setters-on-nulls: true
    jdbc-type-for-null: 'null'
  mapper-locations: classpath*:mapper/**/*Mapper.xml
  type-aliases-package: com.tipray.at.analysis.entity

# Tipray 分布式事务客户端配置
tipray:
  info:
    version: 1.0
    mapper-base-package: com.tipray.at.analysis.mapper
  transaction:
    client:
      # 是否启用事务客户端
      enabled: true

      # 客户端ID
      client-id: tipray-transaction-client

      # 应用名称
      application-name: ${spring.application.name:unknown}

      # AT模式配置
      at:
        enabled: true
        resource-id: tipray-at-resource
        sql-parse-cache: true
        sql-parse-cache-size: 1000
        table-meta-cache: true
        table-meta-cache-expire-minutes: 30

      # UndoLog配置
      undo-log:
        table-name: undo_log
        compression-enabled: true
        compression-threshold: 4096
        serializer: jackson
        auto-create-table: true
        cleanup:
          enabled: true
          interval-hours: 24
          retention-days: 7
          batch-size: 1000

      # 数据源代理配置
      data-source-proxy:
        enabled: true
        global-lock-enabled: true
        global-lock-timeout: 30000

      # 网络配置
      network:
        connect-timeout: 5000
        read-timeout: 10000
        retry-count: 3
        retry-interval: 1000

      # 监控配置
      monitor:
        enabled: true
        port: 8080
        path: /transaction
        health-check-enabled: true
        metrics-enabled: true

# 日志配置
logging:
  level:
    # 业务日志
    com.tipray.at.analysis: INFO

    # 事务客户端日志 - 大幅减少
    com.tipray.transaction.client.infrastructure.datasource: WARN
    com.tipray.transaction.client.infrastructure.undo: WARN
    com.tipray.transaction.client: DEBUG
    com.tipray.cloud.transaction.client: DEBUG

    # MyBatis日志
    com.tipray.at.analysis.mapper: WARN

    root: INFO
  pattern:
    console: "%d{HH:mm:ss.SSS} [%thread] %-5level [分析服务] %logger{36} - %msg%n"
