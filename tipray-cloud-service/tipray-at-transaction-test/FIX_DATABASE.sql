-- ========================================
-- 数据库修复脚本
-- 用于在现有数据库中添加缺失的字段
-- 执行方式：mysql -u root -p tipray_cloud_test < FIX_DATABASE.sql
-- ========================================

USE tipray_cloud_test;

-- 检查并添加 global_tx_id 字段到 dlp_data_record 表
SET @sql = (
    SELECT IF(
        (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
         WHERE TABLE_SCHEMA = 'tipray_cloud_test' 
         AND TABLE_NAME = 'dlp_data_record' 
         AND COLUMN_NAME = 'global_tx_id') = 0,
        'ALTER TABLE dlp_data_record ADD COLUMN global_tx_id VARCHAR(100) COMMENT ''全局事务ID'' AFTER status',
        'SELECT ''global_tx_id字段已存在'' AS message'
    )
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 检查并添加索引
SET @sql = (
    SELECT IF(
        (SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS 
         WHERE TABLE_SCHEMA = 'tipray_cloud_test' 
         AND TABLE_NAME = 'dlp_data_record' 
         AND INDEX_NAME = 'idx_global_tx_id') = 0,
        'ALTER TABLE dlp_data_record ADD INDEX idx_global_tx_id (global_tx_id)',
        'SELECT ''idx_global_tx_id索引已存在'' AS message'
    )
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 显示表结构确认
DESCRIBE dlp_data_record;

-- 显示修复完成信息
SELECT 'dlp_data_record表结构修复完成！' AS status,
       NOW() AS fix_time;
