@echo off
chcp 65001 > nul
echo ========================================
echo 高级分布式事务测试套件
echo ========================================

echo.
echo 这个测试套件包含了专门用于发现框架深层bug的高级测试场景：
echo.
echo 🔍 测试覆盖范围：
echo   📋 事务恢复测试 - 各种故障恢复场景
echo   📋 资源泄漏测试 - 内存、连接、线程泄漏检测
echo   📋 状态一致性测试 - 事务状态流转一致性
echo   📋 网络分区测试 - 网络异常场景处理
echo   📋 数据损坏测试 - 数据损坏检测和修复
echo   📋 幂等性测试 - 重复执行保护机制
echo   📋 事务顺序测试 - 执行顺序和依赖关系
echo.
echo 假设云服务已经启动，开始运行高级测试...
echo.

cd at-dlp-service

echo ========================================
echo 1. 运行事务恢复测试
echo ========================================
echo 测试各种故障恢复场景...
mvn test -Dtest=AtTransactionRecoveryTest -Dspring.profiles.active=test

if %errorlevel% neq 0 (
    echo.
    echo ❌ 事务恢复测试发现问题！
    echo 可能的框架bug：
    echo - 服务重启后事务状态不一致
    echo - 数据库连接中断后恢复异常
    echo - 网络分区后数据不一致
    echo - 并发恢复时状态冲突
    echo - UndoLog损坏后无法恢复
    echo - 资源耗尽后恢复失败
    echo.
    pause
    exit /b 1
) else (
    echo ✅ 事务恢复测试通过
)

echo.
echo ========================================
echo 2. 运行资源泄漏检测测试
echo ========================================
echo 检测内存、连接、线程泄漏...
mvn test -Dtest=AtTransactionResourceLeakTest -Dspring.profiles.active=test

if %errorlevel% neq 0 (
    echo.
    echo ❌ 资源泄漏检测发现问题！
    echo 可能的框架bug：
    echo - 内存泄漏（事务上下文未清理）
    echo - 数据库连接泄漏（连接未正确释放）
    echo - 线程泄漏（后台线程未正确关闭）
    echo - 事务上下文泄漏（ThreadLocal未清理）
    echo - 长时间运行后资源累积
    echo.
    pause
    exit /b 1
) else (
    echo ✅ 资源泄漏检测测试通过
)

echo.
echo ========================================
echo 3. 运行状态一致性测试
echo ========================================
echo 测试事务状态流转一致性...
mvn test -Dtest=AtTransactionStateConsistencyTest -Dspring.profiles.active=test

if %errorlevel% neq 0 (
    echo.
    echo ❌ 状态一致性测试发现问题！
    echo 可能的框架bug：
    echo - 事务状态流转不正确
    echo - 并发事务状态相互影响
    echo - 状态持久化不一致
    echo - 异常状态处理错误
    echo - 状态转换非原子性
    echo - 状态不一致检测失效
    echo.
    pause
    exit /b 1
) else (
    echo ✅ 状态一致性测试通过
)

echo.
echo ========================================
echo 4. 运行网络分区测试
echo ========================================
echo 测试网络异常场景处理...
mvn test -Dtest=AtTransactionNetworkPartitionTest -Dspring.profiles.active=test

if %errorlevel% neq 0 (
    echo.
    echo ❌ 网络分区测试发现问题！
    echo 可能的框架bug：
    echo - 完全网络分区处理异常
    echo - 部分网络分区数据不一致
    echo - 网络延迟导致超时处理错误
    echo - 网络抖动影响事务稳定性
    echo - 网络恢复后重试机制失效
    echo - 并发网络分区处理冲突
    echo.
    pause
    exit /b 1
) else (
    echo ✅ 网络分区测试通过
)

echo.
echo ========================================
echo 5. 运行数据损坏测试
echo ========================================
echo 测试数据损坏检测和修复...
mvn test -Dtest=AtTransactionDataCorruptionTest -Dspring.profiles.active=test

if %errorlevel% neq 0 (
    echo.
    echo ❌ 数据损坏测试发现问题！
    echo 可能的框架bug：
    echo - UndoLog数据损坏检测失效
    echo - 事务日志损坏处理异常
    echo - 数据库数据不一致未检测
    echo - 云服务数据损坏未修复
    echo - 事务元数据损坏恢复失败
    echo - 并发数据损坏检测冲突
    echo - 自动修复机制失效
    echo.
    pause
    exit /b 1
) else (
    echo ✅ 数据损坏测试通过
)

echo.
echo ========================================
echo 6. 运行幂等性测试
echo ========================================
echo 测试重复执行保护机制...
mvn test -Dtest=AtTransactionIdempotencyTest -Dspring.profiles.active=test

if %errorlevel% neq 0 (
    echo.
    echo ❌ 幂等性测试发现问题！
    echo 可能的框架bug：
    echo - 相同事务ID重复执行
    echo - 回滚操作非幂等
    echo - 并发重复请求处理冲突
    echo - 部分失败场景幂等性失效
    echo - 超时重试幂等性问题
    echo - 幂等性键唯一性失效
    echo - 长时间间隔幂等性失效
    echo.
    pause
    exit /b 1
) else (
    echo ✅ 幂等性测试通过
)

echo.
echo ========================================
echo 7. 运行事务顺序测试
echo ========================================
echo 测试执行顺序和依赖关系...
mvn test -Dtest=AtTransactionOrderingTest -Dspring.profiles.active=test

if %errorlevel% neq 0 (
    echo.
    echo ❌ 事务顺序测试发现问题！
    echo 可能的框架bug：
    echo - 顺序事务执行顺序错乱
    echo - 事务依赖关系处理异常
    echo - 并发事务顺序一致性问题
    echo - 事务回滚顺序错误
    echo - 事务优先级排序失效
    echo - 事务链式执行中断
    echo - 批处理顺序混乱
    echo.
    pause
    exit /b 1
) else (
    echo ✅ 事务顺序测试通过
)

echo.
echo ========================================
echo 🎉 所有高级测试通过！
echo ========================================
echo.
echo 📊 测试总结：
echo   ✅ 事务恢复测试 - 验证各种故障恢复场景
echo   ✅ 资源泄漏检测 - 确保无内存/连接/线程泄漏
echo   ✅ 状态一致性测试 - 保证事务状态流转正确
echo   ✅ 网络分区测试 - 处理各种网络异常场景
echo   ✅ 数据损坏测试 - 检测和修复数据损坏
echo   ✅ 幂等性测试 - 保护重复执行场景
echo   ✅ 事务顺序测试 - 维护执行顺序和依赖
echo.
echo 🔍 这些高级测试专门设计用来发现框架的深层问题：
echo.
echo 💡 容错能力测试：
echo   - 服务重启、数据库中断、网络分区等故障场景
echo   - 各种异常情况下的数据一致性保证
echo   - 故障恢复后的系统状态正确性
echo.
echo 💡 资源管理测试：
echo   - 长时间运行后的内存使用情况
echo   - 数据库连接池的正确管理
echo   - 后台线程的生命周期管理
echo   - 事务上下文的正确清理
echo.
echo 💡 并发安全测试：
echo   - 高并发场景下的状态一致性
echo   - 并发事务的隔离性保证
echo   - 竞态条件的检测和处理
echo   - 死锁和活锁的避免
echo.
echo 💡 数据完整性测试：
echo   - 数据损坏的检测和修复
echo   - UndoLog的完整性验证
echo   - 分布式数据的一致性检查
echo   - 元数据的正确性保证
echo.
echo 💡 业务逻辑测试：
echo   - 幂等性保护机制的有效性
echo   - 事务执行顺序的正确性
echo   - 依赖关系的正确处理
echo   - 优先级和批处理的支持
echo.
echo 如果所有高级测试都通过，说明框架在复杂场景下表现优秀！
echo 这些测试能够有效发现生产环境中可能遇到的各种边界情况和异常场景。
echo.
echo ========================================

pause
