@echo off
chcp 65001 >nul
echo ========================================
echo tipray-transaction-group 注解式事务测试
echo ========================================

echo.
echo 🚀 开始运行注解式分布式事务测试套件
echo.

echo 📋 测试内容：
echo   1. 基础功能测试 (5个测试)
echo   2. 重试机制测试 (4个测试)
echo   3. 并发测试 (2个测试)
echo   4. 参数传递测试 (4个测试)
echo   5. 事务屏障测试 (7个测试)
echo   总计：22个测试用例
echo.

echo ⏰ 开始时间: %date% %time%
echo.

echo 🔧 运行环境检查...
java -version
echo.

echo 📦 Maven版本检查...
mvn -version
echo.

echo 🧪 开始执行测试...
echo.

REM 设置Maven选项
set MAVEN_OPTS=-Xmx1024m -XX:MaxPermSize=256m

echo ========================================
echo 1. 运行基础功能测试
echo ========================================
echo mvn test -Dtest=AnnotationBasicTest
mvn test -Dtest=AnnotationBasicTest
if %errorlevel% neq 0 (
    echo ❌ 基础功能测试失败
    goto :error
) else (
    echo ✅ 基础功能测试通过
)
echo.

echo ========================================
echo 2. 运行重试机制测试
echo ========================================
echo mvn test -Dtest=AnnotationRetryTest
mvn test -Dtest=AnnotationRetryTest
if %errorlevel% neq 0 (
    echo ❌ 重试机制测试失败
    goto :error
) else (
    echo ✅ 重试机制测试通过
)
echo.

echo ========================================
echo 3. 运行参数传递测试
echo ========================================
echo mvn test -Dtest=AnnotationParameterTest
mvn test -Dtest=AnnotationParameterTest
if %errorlevel% neq 0 (
    echo ❌ 参数传递测试失败
    goto :error
) else (
    echo ✅ 参数传递测试通过
)
echo.

echo ========================================
echo 4. 运行并发测试
echo ========================================
echo mvn test -Dtest=AnnotationConcurrentTest
mvn test -Dtest=AnnotationConcurrentTest
if %errorlevel% neq 0 (
    echo ❌ 并发测试失败
    goto :error
) else (
    echo ✅ 并发测试通过
)
echo.

echo ========================================
echo 5. 运行完整测试套件
echo ========================================
echo mvn test -Dtest=AnnotationTestSuite
mvn test -Dtest=AnnotationTestSuite
if %errorlevel% neq 0 (
    echo ❌ 完整测试套件失败
    goto :error
) else (
    echo ✅ 完整测试套件通过
)
echo.

echo ========================================
echo 🎉 所有测试执行完成！
echo ========================================
echo.
echo ⏰ 结束时间: %date% %time%
echo.
echo 📊 测试总结：
echo   ✅ 基础功能测试: 通过
echo   ✅ 重试机制测试: 通过  
echo   ✅ 参数传递测试: 通过
echo   ✅ 并发测试: 通过
echo   ✅ 完整测试套件: 通过
echo.
echo 🎯 所有注解式事务功能验证完成！
echo.
goto :end

:error
echo.
echo ========================================
echo ❌ 测试执行失败！
echo ========================================
echo.
echo 💡 故障排查建议：
echo   1. 检查 tipray-transaction-group 依赖是否正确
echo   2. 检查 Spring AOP 配置是否启用
echo   3. 检查测试环境配置是否正确
echo   4. 查看详细错误日志
echo.
echo 📝 查看日志：
echo   - 控制台输出
echo   - target/surefire-reports/ 目录下的测试报告
echo   - logs/ 目录下的应用日志
echo.

:end
echo 按任意键退出...
pause >nul
