-- Tipray 事务组测试数据库初始化脚本
-- 数据库: tipray_transaction_test

-- 创建数据库（如果不存在）
CREATE DATABASE IF NOT EXISTS tipray_transaction_test 
DEFAULT CHARACTER SET utf8mb4 
DEFAULT COLLATE utf8mb4_unicode_ci;

USE tipray_transaction_test;

-- 用户表
CREATE TABLE IF NOT EXISTS t_user (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '用户ID',
    username VARCHAR(50) NOT NULL UNIQUE COMMENT '用户名',
    email VARCHAR(100) NOT NULL UNIQUE COMMENT '邮箱',
    phone VARCHAR(20) COMMENT '手机号',
    balance DECIMAL(10,2) NOT NULL DEFAULT 0.00 COMMENT '余额',
    frozen_balance DECIMAL(10,2) NOT NULL DEFAULT 0.00 COMMENT '冻结余额',
    status INT NOT NULL DEFAULT 1 COMMENT '状态：1-正常，0-禁用',
    version INT NOT NULL DEFAULT 0 COMMENT '版本号（乐观锁）',
    create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_username (username),
    INDEX idx_email (email),
    INDEX idx_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户表';

-- 商品表
CREATE TABLE IF NOT EXISTS t_product (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '商品ID',
    product_code VARCHAR(50) NOT NULL UNIQUE COMMENT '商品编码',
    product_name VARCHAR(100) NOT NULL COMMENT '商品名称',
    price DECIMAL(10,2) NOT NULL COMMENT '价格',
    stock INT NOT NULL DEFAULT 0 COMMENT '库存',
    frozen_stock INT NOT NULL DEFAULT 0 COMMENT '冻结库存',
    category VARCHAR(50) COMMENT '分类',
    description VARCHAR(500) COMMENT '描述',
    status INT NOT NULL DEFAULT 1 COMMENT '状态：1-上架，0-下架',
    version INT NOT NULL DEFAULT 0 COMMENT '版本号（乐观锁）',
    create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_product_code (product_code),
    INDEX idx_category (category),
    INDEX idx_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='商品表';

-- 订单表
CREATE TABLE IF NOT EXISTS t_order (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '订单ID',
    order_no VARCHAR(50) NOT NULL UNIQUE COMMENT '订单号',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    product_id BIGINT NOT NULL COMMENT '商品ID',
    product_code VARCHAR(50) NOT NULL COMMENT '商品编码',
    product_name VARCHAR(100) NOT NULL COMMENT '商品名称',
    quantity INT NOT NULL COMMENT '数量',
    unit_price DECIMAL(10,2) NOT NULL COMMENT '单价',
    total_amount DECIMAL(10,2) NOT NULL COMMENT '总金额',
    status INT NOT NULL COMMENT '状态：1-待支付，2-已支付，3-已发货，4-已完成，5-已取消',
    payment_id VARCHAR(50) COMMENT '支付ID',
    logistics_id VARCHAR(50) COMMENT '物流ID',
    remark VARCHAR(500) COMMENT '备注',
    version INT NOT NULL DEFAULT 0 COMMENT '版本号（乐观锁）',
    create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_order_no (order_no),
    INDEX idx_user_id (user_id),
    INDEX idx_product_id (product_id),
    INDEX idx_status (status),
    INDEX idx_create_time (create_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='订单表';

-- 事务日志表
CREATE TABLE IF NOT EXISTS t_transaction_log (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '日志ID',
    group_id VARCHAR(100) NOT NULL COMMENT '事务组ID',
    step_name VARCHAR(100) COMMENT '步骤名称',
    operation_type VARCHAR(20) NOT NULL COMMENT '操作类型：START,STEP_START,STEP_SUCCESS,STEP_FAILED,COMPENSATE,SUCCESS,FAILED',
    business_type VARCHAR(50) COMMENT '业务类型：ORDER,PAYMENT,INVENTORY,LOGISTICS',
    business_id VARCHAR(100) COMMENT '业务ID',
    status VARCHAR(20) NOT NULL COMMENT '状态',
    result VARCHAR(1000) COMMENT '执行结果',
    error_message VARCHAR(1000) COMMENT '错误信息',
    execution_time BIGINT COMMENT '执行耗时（毫秒）',
    create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    INDEX idx_group_id (group_id),
    INDEX idx_operation_type (operation_type),
    INDEX idx_business_type (business_type),
    INDEX idx_status (status),
    INDEX idx_create_time (create_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='事务日志表';

-- 插入测试用户数据
INSERT IGNORE INTO t_user (username, email, phone, balance) VALUES
('admin', '<EMAIL>', '***********', 10000.00),
('test001', '<EMAIL>', '***********', 5000.00),
('test002', '<EMAIL>', '***********', 3000.00),
('test003', '<EMAIL>', '***********', 8000.00),
('test004', '<EMAIL>', '***********', 2000.00);

-- 插入测试商品数据
INSERT IGNORE INTO t_product (product_code, product_name, price, stock, category, description) VALUES
('PROD0001', '智能手机-iPhone15', 6999.00, 100, '电子产品', '最新款智能手机'),
('PROD0002', '笔记本电脑-MacBook', 12999.00, 50, '电子产品', '高性能笔记本电脑'),
('PROD0003', '运动鞋-Nike', 899.00, 200, '服装', '舒适运动鞋'),
('PROD0004', '咖啡豆-蓝山', 299.00, 500, '食品', '优质咖啡豆'),
('PROD0005', '技术书籍-Java编程', 89.00, 300, '图书', 'Java编程入门书籍');

-- 创建用户和权限（可选）
-- CREATE USER IF NOT EXISTS 'tipray_test'@'%' IDENTIFIED BY 'tipray123456';
-- GRANT ALL PRIVILEGES ON tipray_transaction_test.* TO 'tipray_test'@'%';
-- FLUSH PRIVILEGES;

-- 显示表结构
SHOW TABLES;
SELECT 'Database initialization completed!' as message;
