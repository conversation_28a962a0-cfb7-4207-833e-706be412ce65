package com.tipray.cloud.test.service;


import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 通知AT服务
 * 本地通知服务，不使用@AtService注解
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class NotificationAtService {

    // 调用统计
    private final AtomicInteger callCount = new AtomicInteger(0);
    private final AtomicInteger successCount = new AtomicInteger(0);
    private final AtomicInteger failureCount = new AtomicInteger(0);

    /**
     * 发送订单通知
     */
    public void sendOrderNotification(String orderId, Long userId) {
        callCount.incrementAndGet();

        try {
            log.info("发送订单通知: orderId={}, userId={}", orderId, userId);

            // 模拟发送邮件通知
            sendEmailNotification(orderId, userId);

            // 模拟发送短信通知
            sendSmsNotification(orderId, userId);

            successCount.incrementAndGet();
            log.info("发送订单通知成功: orderId={}", orderId);

        } catch (Exception e) {
            failureCount.incrementAndGet();
            log.error("发送订单通知失败: orderId={}", orderId, e);
            throw e;
        }
    }

    /**
     * 发送订单通知的补偿方法
     */
    public void compensateSendOrderNotification(String orderId, Long userId) {
        log.info("补偿发送订单通知: orderId={}, userId={}", orderId, userId);

        try {
            // 发送取消通知
            log.info("发送订单取消通知: orderId={}", orderId);
        } catch (Exception e) {
            log.error("补偿发送订单通知失败: orderId={}", orderId, e);
        }
    }

    /**
     * 发送多种通知
     */
    public Map<String, Object> sendMultipleNotifications(String orderId, Long userId) {
        callCount.incrementAndGet();
        Map<String, Object> result = new HashMap<>();

        try {
            log.info("发送多种通知: orderId={}, userId={}", orderId, userId);

            // 发送邮件
            boolean emailSent = sendEmailNotification(orderId, userId);
            result.put("emailSent", emailSent);

            // 发送短信
            boolean smsSent = sendSmsNotification(orderId, userId);
            result.put("smsSent", smsSent);

            // 发送推送通知
            boolean pushSent = sendPushNotification(orderId, userId);
            result.put("pushSent", pushSent);

            result.put("orderId", orderId);
            result.put("userId", userId);
            result.put("notificationTime", LocalDateTime.now());
            result.put("success", true);

            successCount.incrementAndGet();
            log.info("发送多种通知成功: orderId={}", orderId);

        } catch (Exception e) {
            failureCount.incrementAndGet();
            result.put("success", false);
            result.put("error", e.getMessage());
            log.error("发送多种通知失败: orderId={}", orderId, e);
            throw e;
        }

        return result;
    }

    /**
     * 发送多种通知的补偿方法
     */
    public void compensateSendMultipleNotifications(String orderId, Long userId) {
        log.info("补偿发送多种通知: orderId={}, userId={}", orderId, userId);
        compensateSendOrderNotification(orderId, userId);
    }

    /**
     * 发送异步通知
     */
    public void sendAsyncNotifications(String orderId, Long userId) throws InterruptedException {
        callCount.incrementAndGet();

        try {
            log.info("发送异步通知: orderId={}, userId={}", orderId, userId);

            // 异步发送通知，不阻塞主流程
            Thread.sleep(100); // 模拟异步处理时间

            sendEmailNotification(orderId, userId);
            sendSmsNotification(orderId, userId);

            successCount.incrementAndGet();
            log.info("发送异步通知成功: orderId={}", orderId);

        } catch (Exception e) {
            failureCount.incrementAndGet();
            log.error("发送异步通知失败: orderId={}", orderId, e);
            throw e;
        }
    }

    /**
     * 发送异步通知的补偿方法
     */
    public void compensateSendAsyncNotifications(String orderId, Long userId) {
        log.info("补偿发送异步通知: orderId={}, userId={}", orderId, userId);
        compensateSendOrderNotification(orderId, userId);
    }

    /**
     * 带补偿的发送通知
     */
    public void sendNotificationWithCompensation(String orderId, Long userId) {
        callCount.incrementAndGet();

        try {
            log.info("带补偿的发送通知: orderId={}, userId={}", orderId, userId);
            sendOrderNotification(orderId, userId);
            successCount.incrementAndGet();

        } catch (Exception e) {
            failureCount.incrementAndGet();
            log.error("带补偿的发送通知失败: orderId={}", orderId, e);
            throw e;
        }
    }

    /**
     * 带补偿的发送通知补偿方法
     */
    public void compensateSendNotificationWithCompensation(String orderId, Long userId) {
        log.info("执行发送通知补偿: orderId={}, userId={}", orderId, userId);
        compensateSendOrderNotification(orderId, userId);
    }

    /**
     * 发送邮件通知
     */
    private boolean sendEmailNotification(String orderId, Long userId) {
        try {
            log.info("发送邮件通知: orderId={}, userId={}", orderId, userId);
            // 模拟邮件发送
            Thread.sleep(50);
            return true;
        } catch (Exception e) {
            log.error("发送邮件通知失败: orderId={}", orderId, e);
            return false;
        }
    }

    /**
     * 发送短信通知
     */
    private boolean sendSmsNotification(String orderId, Long userId) {
        try {
            log.info("发送短信通知: orderId={}, userId={}", orderId, userId);
            // 模拟短信发送
            Thread.sleep(30);
            return true;
        } catch (Exception e) {
            log.error("发送短信通知失败: orderId={}", orderId, e);
            return false;
        }
    }

    /**
     * 发送推送通知
     */
    private boolean sendPushNotification(String orderId, Long userId) {
        try {
            log.info("发送推送通知: orderId={}, userId={}", orderId, userId);
            // 模拟推送通知
            Thread.sleep(20);
            return true;
        } catch (Exception e) {
            log.error("发送推送通知失败: orderId={}", orderId, e);
            return false;
        }
    }

    /**
     * 获取调用统计
     */
    public Map<String, Object> getCallStatistics() {
        Map<String, Object> stats = new HashMap<>();
        stats.put("totalCalls", callCount.get());
        stats.put("successCalls", successCount.get());
        stats.put("failureCalls", failureCount.get());
        stats.put("successRate", callCount.get() > 0 ? (double) successCount.get() / callCount.get() * 100 : 0);
        return stats;
    }

    /**
     * 重置统计
     */
    public void resetStatistics() {
        callCount.set(0);
        successCount.set(0);
        failureCount.set(0);
    }
}
