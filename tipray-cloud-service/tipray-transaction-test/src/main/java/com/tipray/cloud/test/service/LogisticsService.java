package com.tipray.cloud.test.service;


import com.tipray.cloud.test.entity.TransactionLog;
import com.tipray.cloud.transaction.core.common.annotation.DistributedTransactionStep;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Random;

/**
 * 物流服务
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class LogisticsService {

    @Autowired
    private BaseService baseService;

    private final Random random = new Random();

    /**
     * 安排物流（事务步骤）
     */
    @DistributedTransactionStep(
        name = "arrange-logistics",
        description = "安排物流",
        compensateMethod = "cancelLogistics"
    )
    @Transactional
    public String arrangeLogistics(String orderNo, Long userId, Long productId, Integer quantity) throws InterruptedException {
        long startTime = System.currentTimeMillis();

        try {
            log.info("开始安排物流: orderNo={}, userId={}, productId={}, quantity={}", orderNo, userId, productId, quantity);

            // 记录开始日志
            baseService.saveTransactionLog(TransactionLog.stepStart(orderNo, "arrange-logistics", "LOGISTICS", orderNo));

            // 模拟物流处理时间
            Thread.sleep(150 + random.nextInt(300));

            // 模拟物流安排失败场景（5%概率）
            if (random.nextInt(100) < 5) {
                throw new RuntimeException("物流系统异常，安排物流失败");
            }

            // 生成物流单号
            String logisticsId = "LOGISTICS-" + System.currentTimeMillis() + "-" + orderNo;

            // 模拟调用第三方物流接口
            boolean logisticsSuccess = callThirdPartyLogistics(logisticsId, orderNo, userId, productId, quantity);
            if (!logisticsSuccess) {
                throw new RuntimeException("第三方物流安排失败");
            }

            String result = logisticsId;
            long executionTime = System.currentTimeMillis() - startTime;

            // 记录成功日志
            baseService.saveTransactionLog(TransactionLog.stepSuccess(orderNo, "arrange-logistics", "LOGISTICS", orderNo, result, executionTime));

            log.info("安排物流成功: orderNo={}, logisticsId={}, 耗时={}ms", orderNo, logisticsId, executionTime);
            return result;

        } catch (Exception e) {
            long executionTime = System.currentTimeMillis() - startTime;

            // 记录失败日志
            baseService.saveTransactionLog(TransactionLog.stepFailed(orderNo, "arrange-logistics", "LOGISTICS", orderNo, e.getMessage(), executionTime));

            log.error("安排物流失败: orderNo={}, userId={}, productId={}, quantity={}, 耗时={}ms", orderNo, userId, productId, quantity, executionTime, e);
            throw e;
        }
    }

    /**
     * 取消物流（补偿方法）
     */
    @Transactional
    public void cancelLogistics(String orderNo, Long userId, Long productId, Integer quantity, String logisticsId) {
        try {
            log.info("开始取消物流: orderNo={}, userId={}, productId={}, quantity={}, logisticsId={}", orderNo, userId, productId, quantity, logisticsId);

            // 模拟取消物流处理时间
            Thread.sleep(50 + random.nextInt(100));

            // 模拟调用第三方物流取消接口
            String cancelId = "CANCEL-" + System.currentTimeMillis() + "-" + orderNo;
            boolean cancelSuccess = callThirdPartyLogisticsCancel(cancelId, logisticsId);

            if (cancelSuccess) {
                // 记录补偿日志
                baseService.saveTransactionLog(TransactionLog.compensate(orderNo, "arrange-logistics", "LOGISTICS", orderNo, "CANCEL-" + cancelId));

                log.info("取消物流成功: orderNo={}, logisticsId={}, cancelId={}", orderNo, logisticsId, cancelId);
            } else {
                log.warn("取消物流失败: orderNo={}, logisticsId={}", orderNo, logisticsId);
            }

        } catch (Exception e) {
            log.error("取消物流失败: orderNo={}, logisticsId={}", orderNo, logisticsId, e);
            // 补偿失败不抛异常，避免影响其他补偿
        }
    }

    /**
     * 模拟调用第三方物流接口
     */
    private boolean callThirdPartyLogistics(String logisticsId, String orderNo, Long userId, Long productId, Integer quantity) {
        try {
            log.debug("调用第三方物流接口: logisticsId={}, orderNo={}, userId={}, productId={}, quantity={}",
                     logisticsId, orderNo, userId, productId, quantity);

            // 模拟网络延迟
            Thread.sleep(100 + random.nextInt(200));

            // 模拟物流安排成功率92%
            boolean success = random.nextInt(100) < 92;

            if (success) {
                log.debug("第三方物流安排成功: logisticsId={}", logisticsId);
            } else {
                log.warn("第三方物流安排失败: logisticsId={}", logisticsId);
            }

            return success;

        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.error("第三方物流接口调用被中断: logisticsId={}", logisticsId);
            return false;
        }
    }

    /**
     * 模拟调用第三方物流取消接口
     */
    private boolean callThirdPartyLogisticsCancel(String cancelId, String logisticsId) {
        try {
            log.debug("调用第三方物流取消接口: cancelId={}, logisticsId={}", cancelId, logisticsId);

            // 模拟网络延迟
            Thread.sleep(50 + random.nextInt(100));

            // 模拟物流取消成功率96%
            boolean success = random.nextInt(100) < 96;

            if (success) {
                log.debug("第三方物流取消成功: cancelId={}", cancelId);
            } else {
                log.warn("第三方物流取消失败: cancelId={}", cancelId);
            }

            return success;

        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.error("第三方物流取消接口调用被中断: cancelId={}", cancelId);
            return false;
        }
    }
}
