package com.tipray.cloud.test.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.tipray.cloud.test.entity.Product;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;

/**
 * 商品Mapper
 * 
 * <AUTHOR>
 */
@Mapper
public interface ProductMapper extends BaseMapper<Product> {
    
    /**
     * 冻结商品库存
     */
    @Update("UPDATE t_product SET stock = stock - #{quantity}, frozen_stock = frozen_stock + #{quantity}, version = version + 1 " +
            "WHERE id = #{productId} AND stock >= #{quantity} AND version = #{version}")
    int freezeStock(@Param("productId") Long productId, @Param("quantity") Integer quantity, @Param("version") Integer version);
    
    /**
     * 扣减商品库存（从冻结库存中扣减）
     */
    @Update("UPDATE t_product SET frozen_stock = frozen_stock - #{quantity}, version = version + 1 " +
            "WHERE id = #{productId} AND frozen_stock >= #{quantity} AND version = #{version}")
    int deductStock(@Param("productId") Long productId, @Param("quantity") Integer quantity, @Param("version") Integer version);
    
    /**
     * 解冻商品库存
     */
    @Update("UPDATE t_product SET stock = stock + #{quantity}, frozen_stock = frozen_stock - #{quantity}, version = version + 1 " +
            "WHERE id = #{productId} AND frozen_stock >= #{quantity} AND version = #{version}")
    int unfreezeStock(@Param("productId") Long productId, @Param("quantity") Integer quantity, @Param("version") Integer version);
    
    /**
     * 直接扣减商品库存
     */
    @Update("UPDATE t_product SET stock = stock - #{quantity}, version = version + 1 " +
            "WHERE id = #{productId} AND stock >= #{quantity} AND version = #{version}")
    int directDeductStock(@Param("productId") Long productId, @Param("quantity") Integer quantity, @Param("version") Integer version);
    
    /**
     * 增加商品库存
     */
    @Update("UPDATE t_product SET stock = stock + #{quantity}, version = version + 1 " +
            "WHERE id = #{productId} AND version = #{version}")
    int addStock(@Param("productId") Long productId, @Param("quantity") Integer quantity, @Param("version") Integer version);
}
