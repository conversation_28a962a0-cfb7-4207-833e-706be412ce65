package com.tipray.cloud.test.service;


import com.tipray.cloud.test.entity.Product;
import com.tipray.cloud.test.entity.TransactionLog;
import com.tipray.cloud.test.mapper.ProductMapper;
import com.tipray.cloud.transaction.core.common.annotation.DistributedTransactionStep;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Optional;

/**
 * 商品服务（使用MyBatis Plus）
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class ProductService {

    @Autowired
    private ProductMapper productMapper;

    @Autowired
    private BaseService baseService;

    /**
     * 冻结商品库存（事务步骤）
     */
    @DistributedTransactionStep(
        name = "freeze-product-stock",
        description = "冻结商品库存",
        compensateMethod = "unfreezeProductStock"
    )
    @Transactional
    public String freezeProductStock(Long productId, Integer quantity, String businessId) {
        long startTime = System.currentTimeMillis();

        try {
            log.info("开始冻结商品库存: productId={}, quantity={}, businessId={}", productId, quantity, businessId);

            // 记录开始日志
            baseService.saveTransactionLog(TransactionLog.stepStart(businessId, "freeze-product-stock", "INVENTORY", productId.toString()));

            // 查找商品
            Optional<Product> productOpt = baseService.findProductById(productId);
            if (!productOpt.isPresent()) {
                throw new RuntimeException("商品不存在: " + productId);
            }

            Product product = productOpt.get();

            // 检查库存是否足够
            if (product.getStock() < quantity) {
                throw new RuntimeException("商品库存不足: 当前库存=" + product.getStock() + ", 需要冻结=" + quantity);
            }

            // 冻结库存
            int updated = productMapper.freezeStock(productId, quantity, product.getVersion());
            if (updated == 0) {
                throw new RuntimeException("冻结商品库存失败，可能是并发冲突");
            }

            String result = "FREEZE-STOCK-" + productId + "-" + quantity + "-" + System.currentTimeMillis();
            long executionTime = System.currentTimeMillis() - startTime;

            // 记录成功日志
            baseService.saveTransactionLog(TransactionLog.stepSuccess(businessId, "freeze-product-stock", "INVENTORY", productId.toString(), result, executionTime));

            log.info("冻结商品库存成功: productId={}, quantity={}, result={}, 耗时={}ms", productId, quantity, result, executionTime);
            return result;

        } catch (Exception e) {
            long executionTime = System.currentTimeMillis() - startTime;

            // 记录失败日志
            baseService.saveTransactionLog(TransactionLog.stepFailed(businessId, "freeze-product-stock", "INVENTORY", productId.toString(), e.getMessage(), executionTime));

            log.error("冻结商品库存失败: productId={}, quantity={}, businessId={}, 耗时={}ms", productId, quantity, businessId, executionTime, e);
            throw e;
        }
    }

    /**
     * 解冻商品库存（补偿方法）
     */
    @Transactional
    public void unfreezeProductStock(Long productId, Integer quantity, String businessId, String freezeResult) {
        try {
            log.info("开始解冻商品库存: productId={}, quantity={}, businessId={}, freezeResult={}", productId, quantity, businessId, freezeResult);

            // 查找商品
            Optional<Product> productOpt = baseService.findProductById(productId);
            if (!productOpt.isPresent()) {
                log.warn("补偿时商品不存在: {}", productId);
                return;
            }

            Product product = productOpt.get();

            // 解冻库存
            int updated = productMapper.unfreezeStock(productId, quantity, product.getVersion());
            if (updated == 0) {
                log.warn("解冻商品库存失败，可能是并发冲突: productId={}, quantity={}", productId, quantity);
                // 重试一次
                product = baseService.findProductById(productId).orElse(null);
                if (product != null) {
                    productMapper.unfreezeStock(productId, quantity, product.getVersion());
                }
            }

            // 记录补偿日志
            baseService.saveTransactionLog(TransactionLog.compensate(businessId, "freeze-product-stock", "INVENTORY", productId.toString(), "UNFREEZE-" + freezeResult));

            log.info("解冻商品库存成功: productId={}, quantity={}", productId, quantity);

        } catch (Exception e) {
            log.error("解冻商品库存失败: productId={}, quantity={}, businessId={}", productId, quantity, businessId, e);
            // 补偿失败不抛异常，避免影响其他补偿
        }
    }

    /**
     * 扣减商品库存（事务步骤）
     */
    @DistributedTransactionStep(
        name = "deduct-product-stock",
        description = "扣减商品库存",
        compensateMethod = "restoreProductStock"
    )
    @Transactional
    public String deductProductStock(Long productId, Integer quantity, String businessId) {
        long startTime = System.currentTimeMillis();

        try {
            log.info("开始扣减商品库存: productId={}, quantity={}, businessId={}", productId, quantity, businessId);

            // 记录开始日志
            baseService.saveTransactionLog(TransactionLog.stepStart(businessId, "deduct-product-stock", "INVENTORY", productId.toString()));

            // 查找商品
            Optional<Product> productOpt = baseService.findProductById(productId);
            if (!productOpt.isPresent()) {
                throw new RuntimeException("商品不存在: " + productId);
            }

            Product product = productOpt.get();

            // 从冻结库存中扣减
            int updated = productMapper.deductStock(productId, quantity, product.getVersion());
            if (updated == 0) {
                throw new RuntimeException("扣减商品库存失败，可能是并发冲突或冻结库存不足");
            }

            String result = "DEDUCT-STOCK-" + productId + "-" + quantity + "-" + System.currentTimeMillis();
            long executionTime = System.currentTimeMillis() - startTime;

            // 记录成功日志
            baseService.saveTransactionLog(TransactionLog.stepSuccess(businessId, "deduct-product-stock", "INVENTORY", productId.toString(), result, executionTime));

            log.info("扣减商品库存成功: productId={}, quantity={}, result={}, 耗时={}ms", productId, quantity, result, executionTime);
            return result;

        } catch (Exception e) {
            long executionTime = System.currentTimeMillis() - startTime;

            // 记录失败日志
            baseService.saveTransactionLog(TransactionLog.stepFailed(businessId, "deduct-product-stock", "INVENTORY", productId.toString(), e.getMessage(), executionTime));

            log.error("扣减商品库存失败: productId={}, quantity={}, businessId={}, 耗时={}ms", productId, quantity, businessId, executionTime, e);
            throw e;
        }
    }

    /**
     * 恢复商品库存（补偿方法）
     */
    @Transactional
    public void restoreProductStock(Long productId, Integer quantity, String businessId, String deductResult) {
        try {
            log.info("开始恢复商品库存: productId={}, quantity={}, businessId={}, deductResult={}", productId, quantity, businessId, deductResult);

            // 查找商品
            Optional<Product> productOpt = baseService.findProductById(productId);
            if (!productOpt.isPresent()) {
                log.warn("补偿时商品不存在: {}", productId);
                return;
            }

            Product product = productOpt.get();

            // 增加库存
            int updated = productMapper.addStock(productId, quantity, product.getVersion());
            if (updated == 0) {
                log.warn("恢复商品库存失败，可能是并发冲突: productId={}, quantity={}", productId, quantity);
                // 重试一次
                product = baseService.findProductById(productId).orElse(null);
                if (product != null) {
                    productMapper.addStock(productId, quantity, product.getVersion());
                }
            }

            // 记录补偿日志
            baseService.saveTransactionLog(TransactionLog.compensate(businessId, "deduct-product-stock", "INVENTORY", productId.toString(), "RESTORE-" + deductResult));

            log.info("恢复商品库存成功: productId={}, quantity={}", productId, quantity);

        } catch (Exception e) {
            log.error("恢复商品库存失败: productId={}, quantity={}, businessId={}", productId, quantity, businessId, e);
            // 补偿失败不抛异常，避免影响其他补偿
        }
    }

    /**
     * 根据ID查找商品
     */
    public Optional<Product> findById(Long productId) {
        return baseService.findProductById(productId);
    }

    /**
     * 根据商品编码查找商品
     */
    public Optional<Product> findByProductCode(String productCode) {
        return baseService.findProductByCode(productCode);
    }
}
