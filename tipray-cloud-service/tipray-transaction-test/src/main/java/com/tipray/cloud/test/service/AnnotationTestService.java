package com.tipray.cloud.test.service;

import com.tipray.cloud.transaction.core.common.annotation.DistributedTransactionStep;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 注解式事务测试服务
 * 包含所有事务步骤方法，通过Spring Bean代理调用以支持AOP
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class AnnotationTestService {

    // 用于跟踪重试次数的静态变量
    private static final Map<String, AtomicInteger> retryCounters = new HashMap<>();

    // ==================== 基础功能测试步骤 ====================

    /**
     * 验证用户步骤
     */
    @DistributedTransactionStep(
            name = "validate-user",
            description = "验证用户信息",
            compensateMethod = "annotationTestService.compensateValidateUser",
            retryCount = 1,
            retryInterval = 500
    )
    public String validateUser(String testId, Long userId) {
        log.info("执行用户验证: testId={}, userId={}", testId, userId);

        // 模拟验证逻辑
        if (userId == null || userId <= 0) {
            throw new RuntimeException("用户ID无效: " + userId);
        }

        String result = "USER-VALIDATED-" + userId;
        log.info("用户验证成功: testId={}, result={}", testId, result);
        return result;
    }

    /**
     * 验证用户补偿方法
     */
    public void compensateValidateUser(String testId, Long userId, String validationResult) {
        log.info("补偿用户验证: testId={}, userId={}, result={}", testId, userId, validationResult);
        // 用户验证的补偿通常不需要做什么，只记录日志
    }

    /**
     * 验证商品步骤
     */
    @DistributedTransactionStep(
            name = "validate-product",
            description = "验证商品信息和库存",
            compensateMethod = "compensateValidateProduct",
            retryCount = 1,
            retryInterval = 500
    )
    public String validateProduct(String testId, Long productId, Integer quantity) {
        log.info("执行商品验证: testId={}, productId={}, quantity={}", testId, productId, quantity);

        // 模拟验证逻辑
        if (productId == null || productId <= 0) {
            throw new RuntimeException("商品ID无效: " + productId);
        }
        if (quantity == null || quantity <= 0) {
            throw new RuntimeException("商品数量无效: " + quantity);
        }

        String result = "PRODUCT-VALIDATED-" + productId + "-QTY-" + quantity;
        log.info("商品验证成功: testId={}, result={}", testId, result);
        return result;
    }

    /**
     * 验证商品补偿方法
     */
    public void compensateValidateProduct(String testId, Long productId, Integer quantity, String validationResult) {
        log.info("补偿商品验证: testId={}, productId={}, quantity={}, result={}", testId, productId, quantity, validationResult);
        // 商品验证的补偿通常不需要做什么，只记录日志
    }

    /**
     * 创建订单步骤
     */
    @DistributedTransactionStep(
            name = "create-basic-order",
            description = "创建基础订单",
            compensateMethod = "compensateCreateBasicOrder",
            retryCount = 2,
            retryInterval = 1000
    )
    public String createBasicOrder(String testId, String orderNo, Long userId, Long productId, Integer quantity) {
        log.info("执行创建订单: testId={}, orderNo={}, userId={}, productId={}, quantity={}",
                testId, orderNo, userId, productId, quantity);

        // 模拟创建订单逻辑
        try {
            Thread.sleep(100); // 模拟处理时间
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }

        String result = "ORDER-CREATED-" + orderNo;
        log.info("订单创建成功: testId={}, result={}", testId, result);
        return result;
    }

    /**
     * 创建订单补偿方法
     */
    public void compensateCreateBasicOrder(String testId, String orderNo, Long userId, Long productId, Integer quantity, String orderResult) {
        log.info("补偿创建订单: testId={}, orderNo={}, result={}", testId, orderNo, orderResult);
        // 模拟删除订单逻辑
        log.info("订单已删除: orderNo={}", orderNo);
    }

    /**
     * 处理支付步骤
     */
    @DistributedTransactionStep(
            name = "process-basic-payment",
            description = "处理基础支付",
            compensateMethod = "compensateProcessBasicPayment",
            retryCount = 3,
            retryInterval = 1000,
            backoffMultiplier = 2.0
    )
    public String processBasicPayment(String testId, String orderNo, Long userId, boolean shouldFail) {
        log.info("执行支付处理: testId={}, orderNo={}, userId={}, shouldFail={}", testId, orderNo, userId, shouldFail);

        // 模拟支付处理时间
        try {
            Thread.sleep(200);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }

        if (shouldFail) {
            throw new RuntimeException("支付失败：余额不足或支付网关异常");
        }

        String result = "PAYMENT-SUCCESS-" + System.currentTimeMillis();
        log.info("支付处理成功: testId={}, result={}", testId, result);
        return result;
    }

    /**
     * 处理支付补偿方法
     */
    public void compensateProcessBasicPayment(String testId, String orderNo, Long userId, boolean shouldFail, String paymentResult) {
        log.info("补偿支付处理: testId={}, orderNo={}, result={}", testId, orderNo, paymentResult);
        // 模拟退款逻辑
        log.info("支付已退款: orderNo={}, paymentResult={}", orderNo, paymentResult);
    }

    /**
     * 更新订单状态步骤
     */
    @DistributedTransactionStep(
            name = "update-basic-order-status",
            description = "更新基础订单状态",
            compensateMethod = "compensateUpdateBasicOrderStatus",
            retryCount = 1,
            retryInterval = 500
    )
    public String updateBasicOrderStatus(String testId, String orderNo, String status) {
        log.info("执行订单状态更新: testId={}, orderNo={}, status={}", testId, orderNo, status);

        // 模拟状态更新逻辑
        try {
            Thread.sleep(50);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }

        String result = "STATUS-UPDATED-" + status;
        log.info("订单状态更新成功: testId={}, result={}", testId, result);
        return result;
    }

    /**
     * 更新订单状态补偿方法
     */
    public void compensateUpdateBasicOrderStatus(String testId, String orderNo, String status, String updateResult) {
        log.info("补偿订单状态更新: testId={}, orderNo={}, status={}, result={}", testId, orderNo, status, updateResult);
        // 模拟状态回滚逻辑
        log.info("订单状态已回滚: orderNo={}, 从 {} 回滚到 PENDING", orderNo, status);
    }

    // ==================== 重试机制测试步骤 ====================

    /**
     * 创建重试订单步骤
     */
    @DistributedTransactionStep(
            name = "create-retry-order",
            description = "创建重试测试订单",
            compensateMethod = "compensateCreateRetryOrder",
            retryCount = 1,
            retryInterval = 500
    )
    public String createRetryOrder(String testId, String orderNo) {
        log.info("执行创建重试订单: testId={}, orderNo={}", testId, orderNo);

        // 模拟创建订单逻辑
        try {
            Thread.sleep(100);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }

        String result = "RETRY-ORDER-CREATED-" + orderNo;
        log.info("重试订单创建成功: testId={}, result={}", testId, result);
        return result;
    }

    /**
     * 创建重试订单补偿方法
     */
    public void compensateCreateRetryOrder(String testId, String orderNo, String orderResult) {
        log.info("补偿创建重试订单: testId={}, orderNo={}, result={}", testId, orderNo, orderResult);
        log.info("重试订单已删除: orderNo={}", orderNo);
    }

    /**
     * 不稳定的网络调用步骤（用于测试重试）
     */
    @DistributedTransactionStep(
            name = "unstable-network-call",
            description = "不稳定的网络调用",
            compensateMethod = "compensateUnstableNetworkCall",
            retryCount = 5,
            retryInterval = 1000,
            backoffMultiplier = 1.5
    )
    public String unstableNetworkCall(String testId, String orderNo, Integer maxRetries, boolean shouldSucceedAfterRetry) {
        String key = testId + "-" + orderNo;
        AtomicInteger counter = retryCounters.computeIfAbsent(key, k -> new AtomicInteger(0));
        int currentAttempt = counter.incrementAndGet();

        log.info("执行不稳定网络调用: testId={}, orderNo={}, 第{}次尝试, maxRetries={}, shouldSucceedAfterRetry={}",
                testId, orderNo, currentAttempt, maxRetries, shouldSucceedAfterRetry);

        // 模拟网络延迟
        try {
            Thread.sleep(300);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }

        // 根据测试场景决定是否成功
        if (shouldSucceedAfterRetry) {
            // 重试成功场景：前几次失败，最后一次成功
            if (currentAttempt <= maxRetries) {
                log.warn("网络调用失败，将进行重试: testId={}, 第{}次尝试", testId, currentAttempt);
                throw new RuntimeException("网络超时，连接失败 (第" + currentAttempt + "次尝试)");
            }
        } else {
            // 重试失败场景：所有尝试都失败
            log.warn("网络调用失败: testId={}, 第{}次尝试", testId, currentAttempt);
            throw new RuntimeException("网络持续异常，无法连接 (第" + currentAttempt + "次尝试)");
        }

        // 清理计数器
        retryCounters.remove(key);

        String result = "NETWORK-CALL-SUCCESS-" + currentAttempt;
        log.info("网络调用成功: testId={}, result={}, 总尝试次数={}", testId, result, currentAttempt);
        return result;
    }

    /**
     * 不稳定网络调用补偿方法
     */
    public void compensateUnstableNetworkCall(String testId, String orderNo, Integer maxRetries, boolean shouldSucceedAfterRetry, String networkResult) {
        log.info("补偿不稳定网络调用: testId={}, orderNo={}, result={}", testId, orderNo, networkResult);

        // 清理重试计数器
        String key = testId + "-" + orderNo;
        retryCounters.remove(key);

        log.info("网络调用已回滚: orderNo={}", orderNo);
    }

    /**
     * 更新重试订单状态步骤
     */
    @DistributedTransactionStep(
            name = "update-retry-order-status",
            description = "更新重试订单状态",
            compensateMethod = "compensateUpdateRetryOrderStatus",
            retryCount = 1,
            retryInterval = 500
    )
    public String updateRetryOrderStatus(String testId, String orderNo, String status) {
        log.info("执行重试订单状态更新: testId={}, orderNo={}, status={}", testId, orderNo, status);

        try {
            Thread.sleep(50);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }

        String result = "RETRY-STATUS-UPDATED-" + status;
        log.info("重试订单状态更新成功: testId={}, result={}", testId, result);
        return result;
    }

    /**
     * 更新重试订单状态补偿方法
     */
    public void compensateUpdateRetryOrderStatus(String testId, String orderNo, String status, String updateResult) {
        log.info("补偿重试订单状态更新: testId={}, orderNo={}, status={}, result={}", testId, orderNo, status, updateResult);
        log.info("重试订单状态已回滚: orderNo={}", orderNo);
    }

    // ==================== 非关键步骤测试 ====================

    /**
     * 创建非关键测试订单步骤
     */
    @DistributedTransactionStep(
            name = "create-non-critical-order",
            description = "创建非关键测试订单",
            compensateMethod = "compensateCreateNonCriticalOrder",
            critical = true
    )
    public String createNonCriticalOrder(String testId, String orderNo, Long userId, Long productId, Integer quantity) {
        log.info("执行创建非关键测试订单: testId={}, orderNo={}", testId, orderNo);

        try {
            Thread.sleep(100);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }

        String result = "NON-CRITICAL-ORDER-CREATED-" + orderNo;
        log.info("非关键测试订单创建成功: testId={}, result={}", testId, result);
        return result;
    }

    /**
     * 创建非关键测试订单补偿方法
     */
    public void compensateCreateNonCriticalOrder(String testId, String orderNo, Long userId, Long productId, Integer quantity, String orderResult) {
        log.info("补偿创建非关键测试订单: testId={}, orderNo={}, result={}", testId, orderNo, orderResult);
        log.info("非关键测试订单已删除: orderNo={}", orderNo);
    }

    /**
     * 处理非关键测试支付步骤
     */
    @DistributedTransactionStep(
            name = "process-non-critical-payment",
            description = "处理非关键测试支付",
            compensateMethod = "compensateProcessNonCriticalPayment",
            critical = true
    )
    public String processNonCriticalPayment(String testId, String orderNo, Long userId) {
        log.info("执行非关键测试支付: testId={}, orderNo={}, userId={}", testId, orderNo, userId);

        try {
            Thread.sleep(200);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }

        String result = "NON-CRITICAL-PAYMENT-SUCCESS-" + System.currentTimeMillis();
        log.info("非关键测试支付成功: testId={}, result={}", testId, result);
        return result;
    }

    /**
     * 处理非关键测试支付补偿方法
     */
    public void compensateProcessNonCriticalPayment(String testId, String orderNo, Long userId, String paymentResult) {
        log.info("补偿非关键测试支付: testId={}, orderNo={}, result={}", testId, orderNo, paymentResult);
        log.info("非关键测试支付已退款: orderNo={}", orderNo);
    }

    /**
     * 发送邮件通知步骤（非关键，会失败）
     */
    @DistributedTransactionStep(
            name = "send-email-notification",
            description = "发送邮件通知",
            compensateMethod = "compensateSendEmailNotification",
            critical = false,  // 非关键步骤
            retryCount = 1,
            retryInterval = 500
    )
    public String sendEmailNotification(String testId, String orderNo, Long userId) {
        log.info("执行发送邮件通知: testId={}, orderNo={}, userId={}", testId, orderNo, userId);

        try {
            Thread.sleep(100);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }

        // 模拟邮件服务失败
        throw new RuntimeException("邮件服务暂时不可用，发送失败");
    }

    /**
     * 发送邮件通知补偿方法
     */
    public void compensateSendEmailNotification(String testId, String orderNo, Long userId, String emailResult) {
        log.info("补偿发送邮件通知: testId={}, orderNo={}, result={}", testId, orderNo, emailResult);
        // 邮件通知的补偿通常不需要做什么
    }

    /**
     * 发送短信通知步骤（非关键，会失败）
     */
    @DistributedTransactionStep(
            name = "send-sms-notification",
            description = "发送短信通知",
            compensateMethod = "compensateSendSmsNotification",
            critical = false,  // 非关键步骤
            retryCount = 2,
            retryInterval = 1000
    )
    public String sendSmsNotification(String testId, String orderNo, Long userId) {
        log.info("执行发送短信通知: testId={}, orderNo={}, userId={}", testId, orderNo, userId);

        try {
            Thread.sleep(150);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }

        // 模拟短信服务失败
        throw new RuntimeException("短信网关异常，发送失败");
    }

    /**
     * 发送短信通知补偿方法
     */
    public void compensateSendSmsNotification(String testId, String orderNo, Long userId, String smsResult) {
        log.info("补偿发送短信通知: testId={}, orderNo={}, result={}", testId, orderNo, smsResult);
        // 短信通知的补偿通常不需要做什么
    }

    /**
     * 更新非关键测试订单状态步骤
     */
    @DistributedTransactionStep(
            name = "update-non-critical-order-status",
            description = "更新非关键测试订单状态",
            compensateMethod = "compensateUpdateNonCriticalOrderStatus",
            critical = true
    )
    public String updateNonCriticalOrderStatus(String testId, String orderNo, String status) {
        log.info("执行非关键测试订单状态更新: testId={}, orderNo={}, status={}", testId, orderNo, status);

        try {
            Thread.sleep(50);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }

        String result = "NON-CRITICAL-STATUS-UPDATED-" + status;
        log.info("非关键测试订单状态更新成功: testId={}, result={}", testId, result);
        return result;
    }

    /**
     * 更新非关键测试订单状态补偿方法
     */
    public void compensateUpdateNonCriticalOrderStatus(String testId, String orderNo, String status, String updateResult) {
        log.info("补偿非关键测试订单状态更新: testId={}, orderNo={}, status={}, result={}", testId, orderNo, status, updateResult);
        log.info("非关键测试订单状态已回滚: orderNo={}", orderNo);
    }
}
