package com.tipray.cloud.test.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.time.LocalDateTime;

/**
 * 事务日志实体
 * 用于记录分布式事务的执行过程
 *
 * <AUTHOR>
 */
@TableName("t_transaction_log")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class TransactionLog {

    @TableId(type = IdType.AUTO)
    private Long id;

    private String groupId;

    private String stepName;

    private String operationType; // START, STEP_START, STEP_SUCCESS, STEP_FAILED, COMPENSATE, SUCCESS, FAILED

    private String businessType; // ORDER, PAYMENT, INVENTORY, LOGISTICS

    private String businessId;

    private String status;

    private String result;

    private String errorMessage;

    private Long executionTime; // 执行耗时（毫秒）

    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;
    
    public TransactionLog(String groupId, String stepName, String operationType, 
                         String businessType, String businessId, String status) {
        this.groupId = groupId;
        this.stepName = stepName;
        this.operationType = operationType;
        this.businessType = businessType;
        this.businessId = businessId;
        this.status = status;
    }
    
    public static TransactionLog start(String groupId, String businessType, String businessId) {
        return new TransactionLog(groupId, null, "START", businessType, businessId, "STARTED");
    }
    
    public static TransactionLog stepStart(String groupId, String stepName, String businessType, String businessId) {
        return new TransactionLog(groupId, stepName, "STEP_START", businessType, businessId, "EXECUTING");
    }
    
    public static TransactionLog stepSuccess(String groupId, String stepName, String businessType, 
                                           String businessId, String result, Long executionTime) {
        TransactionLog log = new TransactionLog(groupId, stepName, "STEP_SUCCESS", businessType, businessId, "SUCCESS");
        log.setResult(result);
        log.setExecutionTime(executionTime);
        return log;
    }
    
    public static TransactionLog stepFailed(String groupId, String stepName, String businessType, 
                                          String businessId, String errorMessage, Long executionTime) {
        TransactionLog log = new TransactionLog(groupId, stepName, "STEP_FAILED", businessType, businessId, "FAILED");
        log.setErrorMessage(errorMessage);
        log.setExecutionTime(executionTime);
        return log;
    }
    
    public static TransactionLog compensate(String groupId, String stepName, String businessType, 
                                          String businessId, String result) {
        TransactionLog log = new TransactionLog(groupId, stepName, "COMPENSATE", businessType, businessId, "COMPENSATED");
        log.setResult(result);
        return log;
    }
}
