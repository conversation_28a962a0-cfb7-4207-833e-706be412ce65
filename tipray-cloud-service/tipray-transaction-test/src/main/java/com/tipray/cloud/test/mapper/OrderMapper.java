package com.tipray.cloud.test.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.tipray.cloud.test.entity.Order;
import org.apache.ibatis.annotations.*;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 订单Mapper
 * 
 * <AUTHOR>
 */
@Mapper
public interface OrderMapper extends BaseMapper<Order> {
    
    /**
     * 查找指定时间范围内的订单
     */
    @Select("SELECT * FROM t_order WHERE create_time BETWEEN #{startTime} AND #{endTime}")
    List<Order> findByCreateTimeBetween(@Param("startTime") LocalDateTime startTime, 
                                       @Param("endTime") LocalDateTime endTime);
    
    /**
     * 统计用户订单数量
     */
    @Select("SELECT COUNT(*) FROM t_order WHERE user_id = #{userId}")
    long countByUserId(@Param("userId") Long userId);
    
    /**
     * 统计指定状态的订单数量
     */
    @Select("SELECT COUNT(*) FROM t_order WHERE status = #{status}")
    long countByStatus(@Param("status") Integer status);

    /**
     * 根据订单号查询订单
     */
    @Select("SELECT * FROM t_order WHERE order_no = #{orderNo}")
    Order selectByOrderNo(@Param("orderNo") String orderNo);

    /**
     * 根据订单号删除订单
     */
    @Delete("DELETE FROM t_order WHERE order_no = #{orderNo}")
    int deleteByOrderNo(@Param("orderNo") String orderNo);

    /**
     * 删除测试订单
     */
    @Delete("DELETE FROM t_order WHERE order_no LIKE 'AT-ORDER-%' OR order_no LIKE 'ORDER-%'")
    int deleteTestOrders();
}
