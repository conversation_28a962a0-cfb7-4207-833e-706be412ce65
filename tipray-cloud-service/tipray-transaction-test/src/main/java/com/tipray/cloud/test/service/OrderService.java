package com.tipray.cloud.test.service;

import cn.hutool.extra.spring.SpringUtil;
import com.tipray.cloud.test.entity.Order;
import com.tipray.cloud.test.entity.Product;
import com.tipray.cloud.test.entity.TransactionLog;
import com.tipray.cloud.test.entity.User;
import com.tipray.cloud.transaction.core.common.annotation.DistributedTransaction;
import com.tipray.cloud.transaction.core.common.annotation.DistributedTransactionStep;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.Optional;

/**
 * 订单服务（使用MyBatis Plus）
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class OrderService {

    @Autowired
    private UserService userService;

    @Autowired
    private ProductService productService;

    @Autowired
    private PaymentService paymentService;

    @Autowired
    private LogisticsService logisticsService;

    @Autowired
    private BaseService baseService;

    /**
     * 创建订单（注解式事务组）
     */
    @DistributedTransaction(
        description = "创建订单流程",
        timeout = 30000
    )
    public String createOrder(Long userId, Long productId, Integer quantity) throws InterruptedException {
        String orderNo = "ORDER-" + System.currentTimeMillis() + "-" + userId;

        try {
            log.info("=== 开始创建订单: orderNo={}, userId={}, productId={}, quantity={} ===", orderNo, userId, productId, quantity);

            // 记录事务组开始
            baseService.saveTransactionLog(TransactionLog.start(orderNo, "ORDER", orderNo));

            // 1. 验证用户和商品
            User user = userService.findById(userId)
                    .orElseThrow(() -> new RuntimeException("用户不存在: " + userId));

            Product product = productService.findById(productId)
                    .orElseThrow(() -> new RuntimeException("商品不存在: " + productId));

            BigDecimal totalAmount = product.getPrice().multiply(new BigDecimal(quantity));

            OrderService bean = SpringUtil.getBean(OrderService.class);

            // 2. 创建订单记录
            String orderResult = bean.createOrderRecord(orderNo, userId, productId, product.getProductCode(),
                                                  product.getProductName(), quantity, product.getPrice(), totalAmount);

            // 3. 冻结用户余额
            String freezeBalanceResult = userService.freezeUserBalance(userId, totalAmount, orderNo);

            // 4. 冻结商品库存
            String freezeStockResult = productService.freezeProductStock(productId, quantity, orderNo);

            // 5. 执行支付
            String paymentResult = paymentService.processPayment(userId, totalAmount, orderNo);

            // 6. 扣减用户余额
            String deductBalanceResult = userService.deductUserBalance(userId, totalAmount, orderNo);

            // 7. 扣减商品库存
            String deductStockResult = productService.deductProductStock(productId, quantity, orderNo);

            // 8. 安排物流
            String logisticsResult = logisticsService.arrangeLogistics(orderNo, userId, productId, quantity);

            // 9. 更新订单状态为已完成
            bean.updateOrderStatus(orderNo, 4, paymentResult, logisticsResult);

            log.info("=== 订单创建成功: orderNo={} ===", orderNo);
            return orderNo;

        } catch (Exception e) {
            log.error("=== 订单创建失败: orderNo={}, error={} ===", orderNo, e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 创建订单记录（事务步骤）
     */
    @DistributedTransactionStep(
        name = "create-order-record",
        description = "创建订单记录",
        compensateMethod = "cancelOrderRecord"
    )
    @Transactional
    public String createOrderRecord(String orderNo, Long userId, Long productId, String productCode,
                                   String productName, Integer quantity, BigDecimal unitPrice, BigDecimal totalAmount) {
        long startTime = System.currentTimeMillis();

        try {
            log.info("开始创建订单记录: orderNo={}", orderNo);

            // 记录开始日志
            baseService.saveTransactionLog(TransactionLog.stepStart(orderNo, "create-order-record", "ORDER", orderNo));

            // 创建订单
            Order order = new Order(orderNo, userId, productId, productCode, productName, quantity, unitPrice, totalAmount);
            order = baseService.saveOrder(order);

            String result = "ORDER-RECORD-" + order.getId();
            long executionTime = System.currentTimeMillis() - startTime;

            // 记录成功日志
            baseService.saveTransactionLog(TransactionLog.stepSuccess(orderNo, "create-order-record", "ORDER", orderNo, result, executionTime));

            log.info("创建订单记录成功: orderNo={}, orderId={}, 耗时={}ms", orderNo, order.getId(), executionTime);
            return result;

        } catch (Exception e) {
            long executionTime = System.currentTimeMillis() - startTime;

            // 记录失败日志
            baseService.saveTransactionLog(TransactionLog.stepFailed(orderNo, "create-order-record", "ORDER", orderNo, e.getMessage(), executionTime));

            log.error("创建订单记录失败: orderNo={}, 耗时={}ms", orderNo, executionTime, e);
            throw e;
        }
    }

    /**
     * 取消订单记录（补偿方法）
     */
    @Transactional
    public void cancelOrderRecord(String orderNo, Long userId, Long productId, String productCode,
                                 String productName, Integer quantity, BigDecimal unitPrice, BigDecimal totalAmount, String orderResult) {
        try {
            log.info("开始取消订单记录: orderNo={}, orderResult={}", orderNo, orderResult);

            // 查找订单
            Optional<Order> orderOpt = baseService.findOrderByOrderNo(orderNo);
            if (orderOpt.isPresent()) {
                Order order = orderOpt.get();
                order.setStatus(5); // 设置为已取消
                order.setRemark("事务补偿取消");
                baseService.saveOrder(order);

                // 记录补偿日志
                baseService.saveTransactionLog(TransactionLog.compensate(orderNo, "create-order-record", "ORDER", orderNo, "CANCEL-" + orderResult));

                log.info("取消订单记录成功: orderNo={}", orderNo);
            } else {
                log.warn("补偿时订单不存在: {}", orderNo);
            }

        } catch (Exception e) {
            log.error("取消订单记录失败: orderNo={}", orderNo, e);
            // 补偿失败不抛异常，避免影响其他补偿
        }
    }

    /**
     * 更新订单状态（事务步骤）
     */
    @DistributedTransactionStep(
        name = "update-order-status",
        description = "更新订单状态",
        compensateMethod = "revertOrderStatus"
    )
    @Transactional
    public String updateOrderStatus(String orderNo, Integer status, String paymentId, String logisticsId) {
        long startTime = System.currentTimeMillis();

        try {
            log.info("开始更新订单状态: orderNo={}, status={}", orderNo, status);

            // 记录开始日志
            baseService.saveTransactionLog(TransactionLog.stepStart(orderNo, "update-order-status", "ORDER", orderNo));

            // 查找订单
            Optional<Order> orderOpt = baseService.findOrderByOrderNo(orderNo);
            if (!orderOpt.isPresent()) {
                throw new RuntimeException("订单不存在: " + orderNo);
            }

            Order order = orderOpt.get();
            Integer oldStatus = order.getStatus();

            // 更新订单状态
            order.setStatus(status);
            order.setPaymentId(paymentId);
            order.setLogisticsId(logisticsId);
            baseService.saveOrder(order);

            String result = "UPDATE-STATUS-" + oldStatus + "-TO-" + status;
            long executionTime = System.currentTimeMillis() - startTime;

            // 记录成功日志
            baseService.saveTransactionLog(TransactionLog.stepSuccess(orderNo, "update-order-status", "ORDER", orderNo, result, executionTime));

            log.info("更新订单状态成功: orderNo={}, oldStatus={}, newStatus={}, 耗时={}ms", orderNo, oldStatus, status, executionTime);
            return result;

        } catch (Exception e) {
            long executionTime = System.currentTimeMillis() - startTime;

            // 记录失败日志
            baseService.saveTransactionLog(TransactionLog.stepFailed(orderNo, "update-order-status", "ORDER", orderNo, e.getMessage(), executionTime));

            log.error("更新订单状态失败: orderNo={}, status={}, 耗时={}ms", orderNo, status, executionTime, e);
            throw e;
        }
    }

    /**
     * 恢复订单状态（补偿方法）
     */
    @Transactional
    public void revertOrderStatus(String orderNo, Integer status, String paymentId, String logisticsId, String updateResult) {
        try {
            log.info("开始恢复订单状态: orderNo={}, updateResult={}", orderNo, updateResult);

            // 查找订单
            Optional<Order> orderOpt = baseService.findOrderByOrderNo(orderNo);
            if (orderOpt.isPresent()) {
                Order order = orderOpt.get();
                order.setStatus(1); // 恢复为待支付状态
                order.setPaymentId(null);
                order.setLogisticsId(null);
                order.setRemark("事务补偿恢复状态");
                baseService.saveOrder(order);

                // 记录补偿日志
                baseService.saveTransactionLog(TransactionLog.compensate(orderNo, "update-order-status", "ORDER", orderNo, "REVERT-" + updateResult));

                log.info("恢复订单状态成功: orderNo={}", orderNo);
            } else {
                log.warn("补偿时订单不存在: {}", orderNo);
            }

        } catch (Exception e) {
            log.error("恢复订单状态失败: orderNo={}", orderNo, e);
            // 补偿失败不抛异常，避免影响其他补偿
        }
    }

    /**
     * 根据订单号查找订单
     */
    public Optional<Order> findByOrderNo(String orderNo) {
        return baseService.findOrderByOrderNo(orderNo);
    }
}
