package com.tipray.cloud.test.service;

import com.tipray.cloud.test.entity.TransactionLog;
import com.tipray.cloud.transaction.core.common.annotation.DistributedTransactionStep;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.Random;

/**
 * 支付服务
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class PaymentService {

    @Autowired
    private BaseService baseService;

    private final Random random = new Random();

    /**
     * 处理支付（事务步骤）
     */
    @DistributedTransactionStep(
        name = "process-payment",
        description = "处理支付",
        compensateMethod = "refundPayment"
    )
    @Transactional
    public String processPayment(Long userId, BigDecimal amount, String businessId) throws InterruptedException {
        long startTime = System.currentTimeMillis();

        try {
            log.info("开始处理支付: userId={}, amount={}, businessId={}", userId, amount, businessId);

            // 记录开始日志
            baseService.saveTransactionLog(TransactionLog.stepStart(businessId, "process-payment", "PAYMENT", userId.toString()));

            // 模拟支付处理时间
            Thread.sleep(100 + random.nextInt(200));

            // 模拟支付失败场景（10%概率）
            if (random.nextInt(100) < 10) {
                throw new RuntimeException("支付网关异常，支付失败");
            }

            // 生成支付ID
            String paymentId = "PAY-" + System.currentTimeMillis() + "-" + userId;

            // 模拟调用第三方支付接口
            boolean paymentSuccess = callThirdPartyPayment(paymentId, userId, amount);
            if (!paymentSuccess) {
                throw new RuntimeException("第三方支付失败");
            }

            String result = paymentId;
            long executionTime = System.currentTimeMillis() - startTime;

            // 记录成功日志
            baseService.saveTransactionLog(TransactionLog.stepSuccess(businessId, "process-payment", "PAYMENT", userId.toString(), result, executionTime));

            log.info("处理支付成功: userId={}, amount={}, paymentId={}, 耗时={}ms", userId, amount, paymentId, executionTime);
            return result;

        } catch (Exception e) {
            long executionTime = System.currentTimeMillis() - startTime;

            // 记录失败日志
            baseService.saveTransactionLog(TransactionLog.stepFailed(businessId, "process-payment", "PAYMENT", userId.toString(), e.getMessage(), executionTime));

            log.error("处理支付失败: userId={}, amount={}, businessId={}, 耗时={}ms", userId, amount, businessId, executionTime, e);
            throw e;
        }
    }

    /**
     * 退款（补偿方法）
     */
    @Transactional
    public void refundPayment(Long userId, BigDecimal amount, String businessId, String paymentId) {
        try {
            log.info("开始退款: userId={}, amount={}, businessId={}, paymentId={}", userId, amount, businessId, paymentId);

            // 模拟退款处理时间
            Thread.sleep(50 + random.nextInt(100));

            // 模拟调用第三方退款接口
            String refundId = "REFUND-" + System.currentTimeMillis() + "-" + userId;
            boolean refundSuccess = callThirdPartyRefund(refundId, paymentId, amount);

            if (refundSuccess) {
                // 记录补偿日志
                baseService.saveTransactionLog(TransactionLog.compensate(businessId, "process-payment", "PAYMENT", userId.toString(), "REFUND-" + refundId));

                log.info("退款成功: userId={}, amount={}, paymentId={}, refundId={}", userId, amount, paymentId, refundId);
            } else {
                log.warn("退款失败: userId={}, amount={}, paymentId={}", userId, amount, paymentId);
            }

        } catch (Exception e) {
            log.error("退款失败: userId={}, amount={}, businessId={}, paymentId={}", userId, amount, businessId, paymentId, e);
            // 补偿失败不抛异常，避免影响其他补偿
        }
    }

    /**
     * 模拟调用第三方支付接口
     */
    private boolean callThirdPartyPayment(String paymentId, Long userId, BigDecimal amount) {
        try {
            log.debug("调用第三方支付接口: paymentId={}, userId={}, amount={}", paymentId, userId, amount);

            // 模拟网络延迟
            Thread.sleep(50 + random.nextInt(100));

            // 模拟支付成功率95%
            boolean success = random.nextInt(100) < 95;

            if (success) {
                log.debug("第三方支付成功: paymentId={}", paymentId);
            } else {
                log.warn("第三方支付失败: paymentId={}", paymentId);
            }

            return success;

        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.error("第三方支付接口调用被中断: paymentId={}", paymentId);
            return false;
        }
    }

    /**
     * 模拟调用第三方退款接口
     */
    private boolean callThirdPartyRefund(String refundId, String paymentId, BigDecimal amount) {
        try {
            log.debug("调用第三方退款接口: refundId={}, paymentId={}, amount={}", refundId, paymentId, amount);

            // 模拟网络延迟
            Thread.sleep(30 + random.nextInt(70));

            // 模拟退款成功率98%
            boolean success = random.nextInt(100) < 98;

            if (success) {
                log.debug("第三方退款成功: refundId={}", refundId);
            } else {
                log.warn("第三方退款失败: refundId={}", refundId);
            }

            return success;

        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.error("第三方退款接口调用被中断: refundId={}", refundId);
            return false;
        }
    }
}
