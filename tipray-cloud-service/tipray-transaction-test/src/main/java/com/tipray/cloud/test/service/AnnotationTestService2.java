package com.tipray.cloud.test.service;

import com.tipray.cloud.transaction.core.common.annotation.DistributedTransactionStep;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 注解式事务测试服务2
 * 包含事务屏障和并发测试的事务步骤方法
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class AnnotationTestService2 {

    // ==================== 事务屏障测试步骤 ====================

    /**
     * 屏障测试步骤
     */
    @DistributedTransactionStep(
            name = "execute-barrier-step",
            description = "执行屏障测试步骤",
            compensateMethod = "compensateExecuteBarrierStep",
            retryCount = 1,
            retryInterval = 500
    )
    public String executeBarrierStep(String testId, String orderNo, String groupId) {
        log.info("执行屏障测试步骤: testId={}, orderNo={}, groupId={}", testId, orderNo, groupId);

        try {
            Thread.sleep(200);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }

        String result = "BARRIER-STEP-EXECUTED-" + System.currentTimeMillis();
        log.info("屏障测试步骤执行成功: testId={}, result={}", testId, result);
        return result;
    }

    /**
     * 屏障测试步骤补偿方法
     */
    public void compensateExecuteBarrierStep(String testId, String orderNo, String groupId, String stepResult) {
        log.info("补偿屏障测试步骤: testId={}, orderNo={}, groupId={}, result={}", testId, orderNo, groupId, stepResult);
        log.info("屏障测试步骤已回滚: orderNo={}", orderNo);
    }

    // ==================== 并发测试步骤 ====================

    /**
     * 创建并发订单步骤
     */
    @DistributedTransactionStep(
            name = "create-concurrent-order",
            description = "创建并发测试订单",
            compensateMethod = "compensateCreateConcurrentOrder",
            retryCount = 1,
            retryInterval = 500
    )
    public String createConcurrentOrder(String testId, String orderNo, Long userId, Long productId, Integer quantity) {
        log.info("执行创建并发订单: testId={}, orderNo={}, userId={}", testId, orderNo, userId);

        try {
            Thread.sleep(100);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }

        String result = "CONCURRENT-ORDER-CREATED-" + orderNo;
        log.info("并发订单创建成功: testId={}, result={}", testId, result);
        return result;
    }

    /**
     * 创建并发订单补偿方法
     */
    public void compensateCreateConcurrentOrder(String testId, String orderNo, Long userId, Long productId, Integer quantity, String orderResult) {
        log.info("补偿创建并发订单: testId={}, orderNo={}, result={}", testId, orderNo, orderResult);
        log.info("并发订单已删除: orderNo={}", orderNo);
    }

    /**
     * 处理并发支付步骤
     */
    @DistributedTransactionStep(
            name = "process-concurrent-payment",
            description = "处理并发测试支付",
            compensateMethod = "compensateProcessConcurrentPayment",
            retryCount = 2,
            retryInterval = 1000
    )
    public String processConcurrentPayment(String testId, String orderNo, Long userId) {
        log.info("执行并发支付处理: testId={}, orderNo={}, userId={}", testId, orderNo, userId);

        try {
            Thread.sleep(200);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }

        String result = "CONCURRENT-PAYMENT-SUCCESS-" + System.currentTimeMillis();
        log.info("并发支付处理成功: testId={}, result={}", testId, result);
        return result;
    }

    /**
     * 处理并发支付补偿方法
     */
    public void compensateProcessConcurrentPayment(String testId, String orderNo, Long userId, String paymentResult) {
        log.info("补偿并发支付处理: testId={}, orderNo={}, result={}", testId, orderNo, paymentResult);
        log.info("并发支付已退款: orderNo={}", orderNo);
    }
}
