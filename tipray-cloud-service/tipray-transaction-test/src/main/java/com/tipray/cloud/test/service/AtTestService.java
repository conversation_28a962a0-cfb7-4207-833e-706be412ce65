package com.tipray.cloud.test.service;

import com.tipray.cloud.test.entity.Order;
import com.tipray.cloud.test.entity.Product;
import com.tipray.cloud.test.entity.TransactionLog;
import com.tipray.cloud.test.entity.User;
import com.tipray.cloud.test.mapper.OrderMapper;
import com.tipray.cloud.test.mapper.ProductMapper;
import com.tipray.cloud.test.mapper.TransactionLogMapper;
import com.tipray.cloud.test.mapper.UserMapper;
import com.tipray.cloud.transaction.client.core.rm.at.AtResourceManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * AT模式测试服务
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class AtTestService {

    @Autowired
    private UserMapper userMapper;

    @Autowired
    private ProductMapper productMapper;

    @Autowired
    private OrderMapper orderMapper;

    @Autowired
    private TransactionLogMapper transactionLogMapper;

    @Autowired
    private AtResourceManager atResourceManager;

    /**
     * 使用AT模式创建订单
     */
    @Transactional(rollbackFor = Exception.class)
    public String createOrderWithAtTransaction(Long userId, Long productId, Integer quantity) {
        String globalTxId = generateGlobalTxId();
        String branchTxId = generateBranchTxId();

        try {
            // 开始AT分支事务
            boolean started = atResourceManager.beginBranchTransaction(globalTxId, branchTxId);
            if (!started) {
                throw new RuntimeException("开始AT分支事务失败");
            }

            log.info("AT事务开始: globalTxId={}, branchTxId={}", globalTxId, branchTxId);

            // 1. 检查用户是否存在
            User user = userMapper.selectById(userId);
            if (user == null) {
                throw new RuntimeException("用户不存在: " + userId);
            }

            // 2. 检查产品库存
            Product product = productMapper.selectById(productId);
            if (product == null) {
                throw new RuntimeException("产品不存在: " + productId);
            }
            if (product.getStock() < quantity) {
                throw new RuntimeException("库存不足: 需要" + quantity + "，库存" + product.getStock());
            }

            // 3. 扣减库存
            product.setStock(product.getStock() - quantity);
            productMapper.updateById(product);
            log.info("扣减库存成功: productId={}, quantity={}, 剩余库存={}", productId, quantity, product.getStock());

            // 4. 创建订单
            String orderId = generateOrderId();
            Order order = new Order();
            order.setOrderNo(orderId);
            order.setUserId(userId);
            order.setProductId(productId);
            order.setProductName(product.getProductName());
            order.setQuantity(quantity);
            order.setUnitPrice(product.getPrice());
            order.setTotalAmount(product.getPrice().multiply(BigDecimal.valueOf(quantity)));
            order.setStatus(1); // 1-待支付
            order.setCreateTime(LocalDateTime.now());
            orderMapper.insert(order);
            log.info("创建订单成功: orderId={}", orderId);

            // 5. 记录事务日志
            TransactionLog txLog = new TransactionLog();
            txLog.setGroupId(globalTxId);
            txLog.setStepName("CREATE_ORDER");
            txLog.setOperationType("AT");
            txLog.setBusinessType("CREATE_ORDER");
            txLog.setBusinessId(orderId);
            txLog.setStatus("SUCCESS");
            txLog.setResult("AT模式创建订单: " + orderId);
            txLog.setCreateTime(LocalDateTime.now());
            transactionLogMapper.insert(txLog);

            // 提交AT分支事务
            boolean committed = atResourceManager.commitBranchTransaction(globalTxId, branchTxId);
            if (!committed) {
                throw new RuntimeException("提交AT分支事务失败");
            }

            log.info("AT事务提交成功: globalTxId={}, orderId={}", globalTxId, orderId);
            return orderId;

        } catch (Exception e) {
            log.error("AT事务执行失败: globalTxId={}", globalTxId, e);

            // 回滚AT分支事务
            try {
                boolean rollbacked = atResourceManager.rollbackBranchTransaction(globalTxId, branchTxId);
                log.info("AT事务回滚{}: globalTxId={}", rollbacked ? "成功" : "失败", globalTxId);
            } catch (Exception rollbackEx) {
                log.error("AT事务回滚异常: globalTxId={}", globalTxId, rollbackEx);
            }

            throw e;
        }
    }

    /**
     * 带延迟的AT事务测试（用于超时测试）
     */
    @Transactional(rollbackFor = Exception.class)
    public String createOrderWithDelay(Long userId, Long productId, Integer quantity, Long delayMs) throws InterruptedException {
        String globalTxId = generateGlobalTxId();
        String branchTxId = generateBranchTxId();

        try {
            // 开始AT分支事务
            atResourceManager.beginBranchTransaction(globalTxId, branchTxId);

            log.info("AT延迟事务开始: globalTxId={}, delayMs={}", globalTxId, delayMs);

            // 模拟延迟
            Thread.sleep(delayMs);

            // 执行业务逻辑
            String orderId = createOrderWithAtTransaction(userId, productId, quantity);

            log.info("AT延迟事务完成: globalTxId={}, orderId={}", globalTxId, orderId);
            return orderId;

        } catch (Exception e) {
            log.error("AT延迟事务失败: globalTxId={}", globalTxId, e);
            atResourceManager.rollbackBranchTransaction(globalTxId, branchTxId);
            throw e;
        }
    }

    /**
     * AT模式并发测试
     */
    public Map<String, Object> concurrentAtTransactionTest(Integer threadCount, Long userId, Long productId) {
        Map<String, Object> result = new HashMap<>();
        ExecutorService executor = Executors.newFixedThreadPool(threadCount);
        CountDownLatch latch = new CountDownLatch(threadCount);

        int successCount = 0;
        int failureCount = 0;

        try {
            CompletableFuture<Void>[] futures = new CompletableFuture[threadCount];

            for (int i = 0; i < threadCount; i++) {
                final int threadIndex = i;
                futures[i] = CompletableFuture.runAsync(() -> {
                    try {
                        String orderId = createOrderWithAtTransaction(userId + threadIndex, productId, 1);
                        log.info("并发线程{}成功: orderId={}", threadIndex, orderId);
                    } catch (Exception e) {
                        log.warn("并发线程{}失败: {}", threadIndex, e.getMessage());
                    } finally {
                        latch.countDown();
                    }
                }, executor);
            }

            // 等待所有线程完成
            latch.await();

            // 统计结果
            for (CompletableFuture<Void> future : futures) {
                try {
                    future.get();
                    successCount++;
                } catch (Exception e) {
                    failureCount++;
                }
            }

        } catch (Exception e) {
            log.error("并发测试异常", e);
        } finally {
            executor.shutdown();
        }

        result.put("threadCount", threadCount);
        result.put("successCount", successCount);
        result.put("failureCount", failureCount);
        result.put("successRate", (double) successCount / threadCount * 100);

        return result;
    }

    /**
     * AT模式嵌套事务测试
     */
    @Transactional(rollbackFor = Exception.class)
    public Map<String, Object> nestedAtTransactionTest(Long userId, Long productId, Integer quantity) {
        Map<String, Object> result = new HashMap<>();
        String parentGlobalTxId = generateGlobalTxId();

        try {
            log.info("开始嵌套AT事务测试: parentGlobalTxId={}", parentGlobalTxId);

            // 父事务：创建用户
            User user = new User();
            user.setUsername("test_user_" + userId);
            user.setEmail("test" + userId + "@example.com");
            user.setPhone("1380000" + String.format("%04d", userId % 10000));
            user.setBalance(BigDecimal.valueOf(1000.00));
            user.setCreateTime(LocalDateTime.now());
            userMapper.insert(user);

            // 子事务：创建订单
            String orderId = createOrderWithAtTransaction(userId, productId, quantity);

            result.put("parentGlobalTxId", parentGlobalTxId);
            result.put("userId", userId);
            result.put("orderId", orderId);
            result.put("nestedSuccess", true);

            log.info("嵌套AT事务测试成功: parentGlobalTxId={}, orderId={}", parentGlobalTxId, orderId);

        } catch (Exception e) {
            log.error("嵌套AT事务测试失败: parentGlobalTxId={}", parentGlobalTxId, e);
            result.put("nestedSuccess", false);
            result.put("error", e.getMessage());
            throw e;
        }

        return result;
    }

    /**
     * 获取AT事务状态
     */
    public Map<String, Object> getAtTransactionStatus(String globalTxId) {
        Map<String, Object> result = new HashMap<>();

        // 查询事务日志
        TransactionLog txLog = transactionLogMapper.selectByGlobalTxId(globalTxId);
        if (txLog != null) {
            result.put("found", true);
            result.put("operationType", txLog.getOperationType());
            result.put("businessType", txLog.getBusinessType());
            result.put("status", txLog.getStatus());
            result.put("createTime", txLog.getCreateTime());
            result.put("result", txLog.getResult());
        } else {
            result.put("found", false);
            result.put("message", "未找到事务记录");
        }

        return result;
    }

    /**
     * 清理测试数据
     */
    @Transactional(rollbackFor = Exception.class)
    public int cleanupTestData() {
        int count = 0;

        // 清理订单
        count += orderMapper.deleteTestOrders();

        // 清理用户
        count += userMapper.deleteTestUsers();

        // 清理事务日志
        count += transactionLogMapper.deleteTestLogs();

        log.info("清理测试数据完成: count={}", count);
        return count;
    }

    /**
     * 获取AT统计信息
     */
    public Map<String, Object> getAtStatistics() {
        Map<String, Object> result = new HashMap<>();

        // 获取资源管理器统计信息
        String statistics = atResourceManager.getStatistics();
        result.put("resourceManagerStats", statistics);

        // 获取事务日志统计
        int totalTransactions = transactionLogMapper.countByType("AT");
        int successTransactions = transactionLogMapper.countByTypeAndStatus("AT", "SUCCESS");

        result.put("totalAtTransactions", totalTransactions);
        result.put("successAtTransactions", successTransactions);
        result.put("failureAtTransactions", totalTransactions - successTransactions);
        result.put("successRate", totalTransactions > 0 ? (double) successTransactions / totalTransactions * 100 : 0);

        return result;
    }

    /**
     * AT模式健康检查
     */
    public boolean isAtTransactionHealthy() {
        try {
            // 检查资源管理器健康状态
            boolean resourceManagerHealthy = atResourceManager.isHealthy();

            // 检查数据库连接
            User testUser = userMapper.selectById(1L);

            return resourceManagerHealthy;

        } catch (Exception e) {
            log.error("AT模式健康检查失败", e);
            return false;
        }
    }

    /**
     * 生成全局事务ID
     */
    private String generateGlobalTxId() {
        return "AT-" + System.currentTimeMillis() + "-" + UUID.randomUUID().toString().substring(0, 8);
    }

    /**
     * 生成分支事务ID
     */
    private String generateBranchTxId() {
        return "BRANCH-" + System.currentTimeMillis() + "-" + Thread.currentThread().getId();
    }

    /**
     * 生成订单ID
     */
    private String generateOrderId() {
        return "ORDER-" + System.currentTimeMillis() + "-" + UUID.randomUUID().toString().substring(0, 6);
    }
}
