package com.tipray.cloud.test.service;

import com.tipray.cloud.test.entity.Product;
import com.tipray.cloud.test.mapper.ProductMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 产品AT服务
 * 本地产品服务，不使用@AtService注解
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class ProductAtService {

    @Autowired
    private ProductMapper productMapper;

    // 调用统计
    private final AtomicInteger callCount = new AtomicInteger(0);
    private final AtomicInteger successCount = new AtomicInteger(0);
    private final AtomicInteger failureCount = new AtomicInteger(0);

    /**
     * 扣减库存
     */
    public void deductStock(Long productId, Integer quantity) {
        callCount.incrementAndGet();

        try {
            log.info("扣减库存: productId={}, quantity={}", productId, quantity);

            Product product = productMapper.selectById(productId);
            if (product == null) {
                // 创建测试产品
                product = new Product();
                product.setId(productId);
                product.setProductName("测试产品_" + productId);
                product.setDescription("AT模式测试产品");
                product.setPrice(BigDecimal.valueOf(99.99));
                product.setStock(100);
                product.setCategory("TEST");
                product.setCreateTime(LocalDateTime.now());
                productMapper.insert(product);
                log.info("创建测试产品: productId={}", productId);
            }

            if (product.getStock() < quantity) {
                throw new RuntimeException("库存不足: 需要" + quantity + "，库存" + product.getStock());
            }

            // 扣减库存
            product.setStock(product.getStock() - quantity);
            product.setUpdateTime(LocalDateTime.now());
            productMapper.updateById(product);

            successCount.incrementAndGet();
            log.info("扣减库存成功: productId={}, quantity={}, 剩余库存={}",
                    productId, quantity, product.getStock());

        } catch (Exception e) {
            failureCount.incrementAndGet();
            log.error("扣减库存失败: productId={}, quantity={}", productId, quantity, e);
            throw e;
        }
    }

    /**
     * 扣减库存的补偿方法
     */
    public void compensateDeductStock(Long productId, Integer quantity) {
        log.info("补偿库存扣减: productId={}, quantity={}", productId, quantity);

        try {
            Product product = productMapper.selectById(productId);
            if (product != null) {
                // 恢复库存
                product.setStock(product.getStock() + quantity);
                product.setUpdateTime(LocalDateTime.now());
                productMapper.updateById(product);
                log.info("补偿恢复库存成功: productId={}, quantity={}, 当前库存={}",
                        productId, quantity, product.getStock());
            }
        } catch (Exception e) {
            log.error("补偿恢复库存失败: productId={}, quantity={}", productId, quantity, e);
        }
    }

    /**
     * 检查并扣减库存（本地服务方法）
     */
    public Map<String, Object> checkAndDeductStock(Long productId, Integer quantity) {
        callCount.incrementAndGet();
        Map<String, Object> result = new HashMap<>();

        try {
            log.info("检查并扣减库存: productId={}, quantity={}", productId, quantity);

            Product product = productMapper.selectById(productId);
            if (product == null) {
                throw new RuntimeException("产品不存在: " + productId);
            }

            Integer originalStock = product.getStock();

            // 检查库存
            if (originalStock < quantity) {
                throw new RuntimeException("库存不足: 需要" + quantity + "，库存" + originalStock);
            }

            // 扣减库存
            deductStock(productId, quantity);

            result.put("productId", productId);
            result.put("originalStock", originalStock);
            result.put("deductedQuantity", quantity);
            result.put("remainingStock", originalStock - quantity);
            result.put("success", true);

            successCount.incrementAndGet();
            log.info("检查并扣减库存成功: productId={}", productId);

        } catch (Exception e) {
            failureCount.incrementAndGet();
            result.put("success", false);
            result.put("error", e.getMessage());
            log.error("检查并扣减库存失败: productId={}", productId, e);
            throw e;
        }

        return result;
    }

    /**
     * 检查并扣减库存的补偿方法
     */
    public void compensateCheckAndDeductStock(Long productId, Integer quantity) {
        log.info("补偿检查并扣减库存: productId={}, quantity={}", productId, quantity);
        compensateDeductStock(productId, quantity);
    }

    /**
     * 带重试的库存扣减（本地服务方法）
     */
    public void deductStockWithRetry(Long productId, Integer quantity, Integer maxRetries) {
        callCount.incrementAndGet();

        try {
            log.info("带重试的库存扣减: productId={}, quantity={}, maxRetries={}",
                    productId, quantity, maxRetries);

            // 模拟可能失败的操作
            if (Math.random() < 0.2) { // 20%概率失败
                throw new RuntimeException("库存扣减临时失败，需要重试");
            }

            deductStock(productId, quantity);
            successCount.incrementAndGet();

        } catch (Exception e) {
            failureCount.incrementAndGet();
            log.error("带重试的库存扣减失败: productId={}", productId, e);
            throw e;
        }
    }

    /**
     * 带重试的库存扣减补偿方法
     */
    public void compensateDeductStockWithRetry(Long productId, Integer quantity, Integer maxRetries) {
        log.info("补偿带重试的库存扣减: productId={}, quantity={}", productId, quantity);
        compensateDeductStock(productId, quantity);
    }

    /**
     * 带补偿的库存扣减（本地服务方法）
     */
    public void deductStockWithCompensation(Long productId, Integer quantity) {
        callCount.incrementAndGet();

        try {
            log.info("带补偿的库存扣减: productId={}, quantity={}", productId, quantity);
            deductStock(productId, quantity);
            successCount.incrementAndGet();

        } catch (Exception e) {
            failureCount.incrementAndGet();
            log.error("带补偿的库存扣减失败: productId={}", productId, e);
            throw e;
        }
    }

    /**
     * 带补偿的库存扣减补偿方法
     */
    public void compensateDeductStockWithCompensation(Long productId, Integer quantity) {
        log.info("执行库存扣减补偿: productId={}, quantity={}", productId, quantity);
        compensateDeductStock(productId, quantity);
    }

    /**
     * 锁定库存（预留）（本地服务方法）
     */
    public Map<String, Object> lockStock(Long productId, Integer quantity) {
        callCount.incrementAndGet();
        Map<String, Object> result = new HashMap<>();

        try {
            log.info("锁定库存: productId={}, quantity={}", productId, quantity);

            Product product = productMapper.selectById(productId);
            if (product == null) {
                throw new RuntimeException("产品不存在: " + productId);
            }

            if (product.getStock() < quantity) {
                throw new RuntimeException("库存不足，无法锁定: 需要" + quantity + "，库存" + product.getStock());
            }

            // 这里应该有锁定库存的逻辑，简化处理
            result.put("productId", productId);
            result.put("lockedQuantity", quantity);
            result.put("lockTime", LocalDateTime.now());
            result.put("success", true);

            successCount.incrementAndGet();
            log.info("锁定库存成功: productId={}, quantity={}", productId, quantity);

        } catch (Exception e) {
            failureCount.incrementAndGet();
            result.put("success", false);
            result.put("error", e.getMessage());
            log.error("锁定库存失败: productId={}", productId, e);
            throw e;
        }

        return result;
    }

    /**
     * 锁定库存的补偿方法
     */
    public void compensateLockStock(Long productId, Integer quantity) {
        log.info("补偿库存锁定: productId={}, quantity={}", productId, quantity);
        // 补偿逻辑：释放锁定的库存
    }

    /**
     * 获取调用统计
     */
    public Map<String, Object> getCallStatistics() {
        Map<String, Object> stats = new HashMap<>();
        stats.put("totalCalls", callCount.get());
        stats.put("successCalls", successCount.get());
        stats.put("failureCalls", failureCount.get());
        stats.put("successRate", callCount.get() > 0 ? (double) successCount.get() / callCount.get() * 100 : 0);
        return stats;
    }

    /**
     * 重置统计
     */
    public void resetStatistics() {
        callCount.set(0);
        successCount.set(0);
        failureCount.set(0);
    }
}
