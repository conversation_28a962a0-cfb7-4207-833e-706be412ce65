package com.tipray.cloud.test.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 商品实体
 *
 * <AUTHOR>
 */
@TableName("t_product")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class Product {

    @TableId(type = IdType.AUTO)
    private Long id;

    private String productCode;

    private String productName;

    private BigDecimal price;

    private Integer stock;

    private Integer frozenStock = 0;

    private String category;

    private String description;

    private Integer status = 1; // 1-上架, 0-下架

    private Integer version = 0;

    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;
    
    public Product(String productCode, String productName, BigDecimal price, Integer stock, String category) {
        this.productCode = productCode;
        this.productName = productName;
        this.price = price;
        this.stock = stock;
        this.frozenStock = 0;
        this.category = category;
        this.status = 1;
        this.version = 0;
    }
}
