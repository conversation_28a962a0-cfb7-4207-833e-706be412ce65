# tipray-transaction-group 框架问题报告

## 🐛 问题1：非关键步骤失败导致事务组失败

### 问题描述
当 `@TransactionStep` 注解的 `critical = false` 的非关键步骤失败时，整个事务组仍然会失败并执行补偿，这不符合非关键步骤的设计预期。

### 预期行为
- 非关键步骤失败时，应该记录日志但不影响整个事务组的成功
- 事务组应该继续执行并最终成功
- 只有关键步骤失败才应该导致事务组失败

### 实际行为
- 非关键步骤失败时，步骤状态被标记为 `FAILED`
- `TransactionGroupAspect` 检查所有步骤状态，发现有 `FAILED` 状态
- 事务组被认为失败，执行补偿逻辑

### 测试日志证据
```
[notification] 非关键步骤执行失败, 耗时: 3ms, 异常: 通知发送失败 (继续执行)
[TXG-...] 事务组中有步骤失败，开始执行补偿
```

### 根本原因
在 `TransactionGroupAspect.java` 第96-97行：
```java
boolean allStepsSuccess = groupInfo.getSteps().stream()
        .allMatch(step -> step.getStatus().isSuccessState());
```

这个检查没有区分关键步骤和非关键步骤，只要有任何步骤失败就认为事务组失败。

### 修复建议

#### 方案1：修改成功检查逻辑
在 `TransactionGroupAspect.java` 中修改成功检查逻辑：

```java
// 检查关键步骤是否都成功（忽略非关键步骤的失败）
boolean allCriticalStepsSuccess = groupInfo.getSteps().stream()
        .filter(step -> step.isCritical()) // 只检查关键步骤
        .allMatch(step -> step.getStatus().isSuccessState());

if (allCriticalStepsSuccess) {
    // 事务组成功
    long executionTime = System.currentTimeMillis() - groupInfo.getStartTime();
    groupInfo.updateStatus(TransactionGroupStatus.SUCCESS);
    
    // 记录非关键步骤失败的统计
    long failedNonCriticalSteps = groupInfo.getSteps().stream()
            .filter(step -> !step.isCritical() && !step.getStatus().isSuccessState())
            .count();
    
    if (failedNonCriticalSteps > 0) {
        log.info("[{}] 事务组执行成功 - 步骤数: {}, 耗时: {}ms, 非关键步骤失败: {}个",
                groupId, groupInfo.getSteps().size(), executionTime, failedNonCriticalSteps);
    } else {
        log.info("[{}] 事务组执行成功 - 步骤数: {}, 耗时: {}ms",
                groupId, groupInfo.getSteps().size(), executionTime);
    }
    return methodResult;
} else {
    // 有关键步骤失败，需要补偿
    // ...
}
```

#### 方案2：修改步骤状态管理
在 `TransactionStepAspect.java` 中，为非关键步骤失败设置特殊状态：

```java
// 在 TransactionStepAspect 的异常处理中
if (distributedTransactionStep.critical()) {
    log.error("  [{}] 关键步骤执行失败, 耗时: {}ms, 异常: {}", stepName, executionTime, e.getMessage());
    stepInfo.updateStatus(StepStatus.FAILED);
    throw e; // 关键步骤失败，抛出异常中断事务组
} else {
    log.warn("  [{}] 非关键步骤执行失败, 耗时: {}ms, 异常: {} (继续执行)", stepName, executionTime, e.getMessage());
    stepInfo.updateStatus(StepStatus.NON_CRITICAL_FAILED); // 新增状态
    return null; // 非关键步骤失败，返回null继续执行
}
```

然后在 `StepStatus` 枚举中添加新状态，并在成功检查中忽略这个状态。

### 推荐方案
推荐使用 **方案1**，因为：
1. 改动最小，风险最低
2. 不需要修改枚举和状态管理
3. 逻辑清晰，易于理解和维护

### 测试验证
修复后，`test05_NonCriticalStepFailureHandling` 测试应该：
1. 非关键步骤失败时不抛出异常
2. 事务组整体成功
3. 返回正确的订单结果
4. 不执行补偿逻辑

## 🔧 其他潜在问题

### 问题2：步骤信息中缺少 critical 标识
当前 `TransactionStepInfo` 类中可能缺少 `critical` 字段，需要添加以支持上述修复。

### 问题3：补偿逻辑可能需要优化
非关键步骤失败时，是否需要补偿？这个需要根据业务场景决定。

## 📝 总结
这是一个影响框架核心功能的重要 BUG，建议优先修复。修复后可以大大提高框架的可用性和符合预期的行为。
