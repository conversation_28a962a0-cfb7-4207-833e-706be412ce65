@echo off
echo ========================================
echo 补偿失败降级测试
echo ========================================

cd /d %~dp0

echo 正在编译项目...
call mvn clean compile test-compile -q

if %errorlevel% neq 0 (
    echo 编译失败！
    pause
    exit /b 1
)

echo 正在运行补偿失败降级测试...
echo.

call mvn test -Dtest=CompensationFallbackTest -q

echo.
echo ========================================
echo 测试完成
echo ========================================
pause
